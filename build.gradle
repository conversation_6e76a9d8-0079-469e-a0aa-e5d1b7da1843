plugins {
	id 'java'
	id 'org.springframework.boot' version '3.3.5'
	id 'io.spring.dependency-management' version '1.1.6'
	id 'com.google.cloud.tools.jib' version '3.4.4'
	id 'org.jsonschema2pojo' version '1.2.1'
	id 'jacoco'
	id "org.sonarqube" version "4.4.1.3373"
}

allprojects {
	group = 'uk.co.flexi.ri-api'
	version = '0.0.1-SNAPSHOT'
	repositories {
		mavenCentral()
	}
}

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

jacoco {
	toolVersion = '0.8.13'
}

subprojects {
	apply plugin: 'java'
	group = 'uk.co.flexi.oms-sdk'
	version = '0.0.1-SNAPSHOT'
	java {
		toolchain {
			languageVersion = JavaLanguageVersion.of(21)
		}
	}

	dependencies {
		testImplementation 'org.junit.jupiter:junit-jupiter:5.9.2'
	}

	test {
		useJUnitPlatform()
	}
}

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

repositories {
	mavenCentral()
}

dependencies {
	// Spring Boot Starters
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-batch'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.boot:spring-boot-docker-compose'

	// JSON Web Token (JWT)
	implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
	implementation 'io.jsonwebtoken:jjwt-impl:0.11.5'
	implementation 'io.jsonwebtoken:jjwt-jackson:0.11.5'

	// OpenAPI
	implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.6.0'

	// Flyway
	implementation 'org.flywaydb:flyway-core'
	implementation 'org.flywaydb:flyway-mysql'

	// MinIO
	implementation 'io.minio:minio:8.5.14'

	// ModelMapper
	implementation 'org.modelmapper:modelmapper:2.3.8'

	// Apache Camel
	implementation 'org.apache.camel.springboot:camel-spring-boot-starter:4.11.0'
	implementation 'org.apache.camel.springboot:camel-kafka-starter:4.11.0'
	implementation 'org.apache.camel.springboot:camel-http-starter:4.11.0'
	implementation 'org.apache.camel.springboot:camel-mail-starter:4.11.0'
	implementation 'org.apache.camel.springboot:camel-jackson-starter:4.11.0'
	implementation 'org.apache.camel.springboot:camel-aws2-sqs-starter:4.11.0'
	implementation 'org.apache.camel.springboot:camel-google-pubsub-starter:4.11.0'

	// Jackson
	implementation 'com.fasterxml.jackson.core:jackson-databind'

	// Rhino (JavaScript engine)
	implementation 'org.mozilla:rhino:1.8.0'

	// Apache POI for Excel
	implementation 'org.apache.poi:poi-ooxml:5.4.0'

	// Hibernate Envers
	implementation 'org.springframework.data:spring-data-envers:3.4.2'
	implementation 'org.hibernate.orm:hibernate-envers:6.6.13.Final'

	// Lombok
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'

	// Database
	runtimeOnly 'com.mysql:mysql-connector-j'

	// Thumbnailator for image compression
	implementation 'net.coobird:thumbnailator:0.4.20'

	// Javacv for video compression
	implementation 'org.bytedeco:javacv-platform:1.5.12'

	//Cron Utils
	implementation 'com.cronutils:cron-utils:9.2.1'

	//Template rendering
	implementation 'com.github.spullara.mustache.java:compiler:0.9.14'

	// Test dependencies
	implementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.security:spring-security-test'
	testImplementation 'org.testcontainers:testcontainers'
	testImplementation 'org.testcontainers:mysql:1.19.0'
	testImplementation 'org.testcontainers:junit-jupiter'
	testImplementation 'io.rest-assured:rest-assured'
	testImplementation 'com.squareup.okhttp3:mockwebserver:4.12.0'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'

	// Project modules
	implementation project(':oms-sdk')
}

jib {
	from {
		image = "eclipse-temurin:21-jdk"
	}
}

jsonSchema2Pojo {
	source = files("${projectDir}/src/main/resources/schema/ri-event-dto.json")
	targetPackage = 'uk.co.flexi.ri.event.dto'
	targetDirectory = layout.buildDirectory.dir("generated/sources/java").get().asFile
	includeJsr303Annotations = true
	useJakartaValidation = true
	dateTimeType = "java.time.OffsetDateTime"
}
sourceSets.main.java.srcDirs += layout.buildDirectory.dir("generated/sources/java").get().asFile
compileJava.dependsOn generateJsonSchema2Pojo

tasks.named('test') {
	useJUnitPlatform()
	finalizedBy jacocoTestReport
}

jacocoTestReport {
	dependsOn test
	reports {
		xml.required.set(true)
		xml.outputLocation.set(layout.buildDirectory.file("reports/jacoco/test/jacoco.xml"))
		html.required.set(true)
	}
	classDirectories.setFrom(
			files(classDirectories.files.collect { fileTree(dir: it, exclude: [
					'**/config/**',
					'**/dto/**',
					'**/model/**',
					'**/validation/**',
					'**/repository/**',
					'**/exception/**',
					'**/util/Constants.class',
					'**/integration/controller/SimulatorController.class',
					'**/integration/batch/route/EventRoutingRoute.class',
					'**/ReturnInsightsApplication.class'
			]) })
	)
}

tasks.register("resetDB") {
	description = "Stops and removes the MySQL container, then pulls the MySQL Docker image again."
	group = "DB"
	doFirst {
		println "Stopping and removing MySQL container..."
	}
	doLast {
		exec {
			executable = "docker-compose"
			args = ["down", "-v"]
		}
		println "MySQL container stopped and removed."
		exec {
			executable = "docker-compose"
			args = ["pull", "mysqldb"]
		}
		println "MySQL image pulled."
		exec {
			executable = "docker-compose"
			args = ["up", "-d", "mysqldb"]
		}
		println "MySQL Docker container is running."
	}
}

sonar {
    properties {
        property "sonar.projectKey", "RI-API"
        property "sonar.host.url", "http://**************:9100"
		property "sonar.sources", "src/main/java"
		property "sonar.inclusions", "**/*.java"
		property "sonar.exclusions", "oms-sdk/**,**/test/**, **/generated/**"
		property "sonar.java.binaries", "build/classes"
    }
}



