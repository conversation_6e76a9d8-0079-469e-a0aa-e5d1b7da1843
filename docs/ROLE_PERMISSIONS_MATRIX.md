# Role Permissions Matrix

This document outlines the role-based access control (RBAC) system for the Return Insights application, detailing which permissions are assigned to each role group.

## Role Groups Overview

The system defines 5 main role groups, each with specific responsibilities and corresponding permissions:

1. **ADMIN_ROLES (1000)** - Full administrative access
2. **WO_ROLES (1001)** - Warehouse Operator permissions
3. **SU_ROLES (1002)** - Supervisor permissions  
4. **CS_ROLES (1003)** - Client Services permissions
5. **MP_ROLES (1004)** - Marketplace Users permissions

## Available Roles/Permissions

| Role ID | Role Name | Description |
|---------|-----------|-------------|
| 1000 | RI_INSP_PCKG_SCN | Package scanning capabilities |
| 1001 | RI_INSP_ODP_EDIT | Order details page editing |
| 1002 | RI_INSP_MEDIA_UPLD_EXT | External media upload |
| 1003 | RI_INSP_SND_REVIEW | Send items for review |
| 1004 | RI_INSP_APRV | Approve inspection items |
| 1005 | RI_INSP_WRITE_CMNT | Write internal comments |
| 1006 | RI_INSP_WRITE_CMNT_EXT | Write external comments |
| 1007 | RI_INSP_MEDIA_UPLD | Upload media files |
| 1008 | RI_INSP_CREATE_INSP | Create new inspections |
| 1009 | RI_INSP_RJCT | Reject inspection items |
| 1010 | RI_INSP_SLCT_RETN_RSN | Select return reasons |
| 1011 | RI_INSP_LAND_SCAN | Landing page scanning |
| 1012 | RI_INSP_SLA | SLA management |
| 1013 | RI_INSP_RETN_RSN | Return reason management |
| 1014 | RI_INSP_AUDIT | Audit log access |
| 1015 | RI_INSP_OMS_CONFIG | OMS configuration |
| 1016 | RI_INSP_CREATE_USER | User creation |
| 1017 | RI_INSP_CREATE_USER_ROLE_GRP | User role group creation |
| 1018 | RI_INSP_QI_INSTRUCTIONS | Quality inspection instructions |
| 1019 | RI_INSP_MY_PROFILE | Profile management |
| 1020 | RI_INSP_ACCESS_INSP_PAGE | Access inspection pages |
| 1021 | RI_INSP_ACCESS_DSHBRD_PAGE | Access dashboard |
| 1022 | RI_INSP_INTGRN_LOG | Integration logs |
| 1023 | RI_INSP_INTGRN_LOG_VIEW | View integration logs |
| 1024 | RI_INSP_ITEM_CONDN | Item condition management |
| 1025 | RI_INSP_USER_GRP | User group management |
| 1026 | RI_INSP_REVIEWERS | Reviewer management |
| 1027 | RI_INSP_SSO_CONFIG | SSO configuration |
| 1028 | RI_INSP_EDIT_INSTRUCTIONS | Edit instructions |
| 1029 | RI_INSP_PREVIEW_HIDE | Preview hide functionality |
| 1030 | RI_INSP_CUSTOMER_DETAILS | Customer details access |
| 1031 | RI_INSP_MEDIA_QR_CODE | QR code media functionality |

## Role Group Permissions Matrix

### ADMIN_ROLES (1000) - Full Administrative Access
**Users:** System administrators, IT managers
**Permissions:** All available permissions (28 roles)

- ✅ Dashboard and inspection page access
- ✅ User and role management
- ✅ System configuration (OMS, SSO, SLA)
- ✅ Audit and integration logs
- ✅ Full inspection workflow capabilities
- ✅ All media and comment functionalities

### WO_ROLES (1001) - Warehouse Operator
**Users:** Warehouse staff, package handlers
**Permissions:** Basic operational permissions (11 roles)

- ✅ Dashboard and inspection page access
- ✅ Package scanning and landing scan
- ✅ Create inspections
- ✅ Upload media and write comments
- ✅ Select return reasons
- ✅ Customer details access
- ✅ QR code functionality
- ❌ Approval/rejection capabilities
- ❌ Administrative functions
- ❌ Configuration access

### SU_ROLES (1002) - Supervisor
**Users:** Team supervisors, quality managers
**Permissions:** Supervisory and review permissions (16 roles)

- ✅ All warehouse operator permissions
- ✅ Approve and reject items
- ✅ Send items for review
- ✅ Return reason management
- ✅ Reviewer management
- ✅ Package scanning capabilities
- ❌ User creation and system configuration
- ❌ Integration and audit logs
- ❌ SSO and OMS configuration

### CS_ROLES (1003) - Client Services
**Users:** Customer service representatives, client liaisons
**Permissions:** Client-facing permissions (14 roles)

- ✅ Dashboard and inspection page access
- ✅ Approve and reject items
- ✅ Send items for review
- ✅ Write internal and external comments
- ✅ Upload media (internal and external)
- ✅ Return reason management
- ✅ Customer details access
- ✅ QR code functionality
- ❌ Package scanning and creation
- ❌ Administrative functions
- ❌ System configuration

### MP_ROLES (1004) - Marketplace Users
**Users:** External marketplace partners (Amazon, Zalando, etc.)
**Permissions:** Limited partner permissions (12 roles)

- ✅ Dashboard and inspection page access
- ✅ Approve and reject items
- ✅ Write internal and external comments
- ✅ Upload media (internal and external)
- ✅ Return reason management
- ✅ Customer details access
- ❌ Package scanning and creation
- ❌ Send for review capabilities
- ❌ QR code functionality
- ❌ Administrative functions

## Permission Hierarchy

```
ADMIN_ROLES (Full Access)
    ├── SU_ROLES (Supervisory + Operational)
    │   └── WO_ROLES (Operational Only)
    ├── CS_ROLES (Client-facing + Review)
    └── MP_ROLES (Limited Partner Access)
```

## Usage Guidelines

### Role Assignment Best Practices

1. **Principle of Least Privilege**: Assign the minimum permissions necessary for job function
2. **Role Segregation**: Separate operational, supervisory, and administrative roles
3. **Regular Review**: Periodically review and update role assignments
4. **Audit Trail**: Monitor role usage through audit logs

### Adding New Roles

When adding new role groups:

1. Define the role group in the `role_group` table
2. Create users and assign them to the role group
3. Add appropriate role assignments in `role_role_groups`
4. Update this documentation

### Customization for Different Merchants

Different merchants may require different permission sets:

- **High-security merchants**: More restrictive permissions
- **Self-service merchants**: Broader permissions for efficiency
- **Partner-heavy merchants**: Enhanced external user permissions

## Security Considerations

- **Data Isolation**: All permissions respect tenant boundaries
- **Session Management**: Role permissions are checked on each request
- **Audit Logging**: All permission-based actions are logged
- **External Access**: Marketplace users have limited, controlled access

## Implementation Notes

- Role assignments are stored in the `role_role_groups` table
- User-to-role-group mapping is in `user_group_rel` table
- Permissions are enforced at the service and controller level
- Frontend components respect role-based visibility rules
