pipeline {
    agent any

    environment {
        DOCKER_REGISTRY = 'repos.flexicommerce.co.uk'
        DOCKER_USERNAME = "$DOCKER_USER"
        DOCKER_PASSWORD = "$DOCKER_PASS"
    }

    stages {
        stage('Checkout') {
            steps {
                echo 'Checking out code...'
                checkout scmGit(
                    branches: [[name: '*/main']],
                    extensions: [],
                    userRemoteConfigs: [[
                        credentialsId: '1f82540b-f8b4-43de-a674-c8c25da2f8fe',
                        url: 'https://<EMAIL>/flexicom/ri-api.git'
                    ]]
                )
            }
        }

        stage('Build') {
            steps {
                echo 'Setting executable permissions for Gradle wrapper...'
                sh 'chmod +x ./gradlew'
                echo 'Logging into Docker registry...'
                sh "echo $DOCKER_PASSWORD | docker login $DOCKER_REGISTRY -u $DOCKER_USERNAME --password-stdin"
                echo 'Building the project...'
                sh './gradlew clean build jacocoTestReport'
            }
        }

//         stage('SonarQube Analysis') {
//             steps {
//                 withSonarQubeEnv('RI-API') {
//                     sh './gradlew sonar'
//                 }
//             }
//         }

        stage('Publish') {
            steps {
                echo 'Publishing Docker image to registry...'
                sh "./gradlew -Djib.to.image=$DOCKER_REGISTRY/ri-api:prd -Djib.container.environment=ACTIVE_PROFILE=prod jib "
                echo 'Docker image pushed successfully.'
            }
        }
    }

    post {
        always {
            echo 'Archiving JaCoCo HTML report...'
            archiveArtifacts artifacts: 'build/reports/jacoco/test/html/**', allowEmptyArchive: true

            echo 'Publishing JaCoCo coverage to Jenkins...'
            recordCoverage(tools: [[parser: 'JACOCO']],
                id: 'jacoco', name: 'JaCoCo Coverage',
                sourceCodeRetention: 'EVERY_BUILD',
                qualityGates: [
                       [threshold: 90.0, metric: 'LINE', baseline: 'PROJECT', unstable: true],
                       [threshold: 50.0, metric: 'BRANCH', baseline: 'PROJECT', unstable: true]])

            echo 'Cleaning up Docker credentials...'
            sh "docker logout $DOCKER_REGISTRY"
        }
        success {
            echo 'Pipeline completed successfully!'
        }
        failure {
            echo 'Pipeline failed.'
        }
    }
}
