services:
  minio:
    image: minio/minio
    container_name: minio
    ports:
      - "9000:9000"
      - "9090:9090"
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: admin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9090"

  metabase:
    image: metabase/metabase:latest
    container_name: metabase
    ports:
      - "4000:3000"
    networks:
      - flexi_dev
    volumes:
      - metabase_data:/metabase-data
    environment:
      - MB_DB_FILE=/metabase-data/metabase.db

  wiremock:
    image: "wiremock/wiremock:latest"
    container_name: my_wiremock
    ports:
      - "8090:8080"
    volumes:
      - ./wiremock/mappings:/home/<USER>/mappings
      - ./wiremock/__files:/home/<USER>/__files
      - ./wiremock/extensions:/var/wiremock/extensions
    entrypoint: ["/docker-entrypoint.sh", "--global-response-templating", "--disable-gzip", "--verbose",
                 "--extensions", "uk.co.flexi.ri.ProductCountTransformer"]


  zookeeper:
    image: wurstmeister/zookeeper
    container_name: zookeeper
    ports:
      - "2181:2181"
  kafka:
    image: wurstmeister/kafka
    container_name: kafka
    ports:
      - "9092:9092"
    environment:
      KAFKA_ADVERTISED_HOST_NAME: localhost
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181


  localstack:
    image: localstack/localstack
    container_name: localstack
    ports:
      - "4566:4566"
    environment:
      - SERVICES=sqs
      - EDGE_PORT=4566
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    volumes:
      - ./localstack/init-scripts:/etc/localstack/init/ready.d
      - localstack_data:/var/lib/localstack
      - "/var/run/docker.sock:/var/run/docker.sock"
    networks:
      - flexi_dev

networks:
  flexi_dev:
    driver: bridge

volumes:
  minio_data:
  metabase_data:
  localstack_data:
