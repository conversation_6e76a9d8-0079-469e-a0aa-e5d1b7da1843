# Return Insight

This is a Spring Boot application built with <PERSON><PERSON><PERSON>.

## Prerequisites

- Dock<PERSON> should be running before executing the application.
- Java JDK 21+ (Ensure that you have the appropriate version installed).

## Getting Started

1. **Clone the Repository**

   Pull the code from the repository:

   ```bash
   <NAME_EMAIL>:flexicom/return-insight.git

2. **Navigate to the Project Directory**

   ```bash
   cd return-insight

3. **Run the Application**

   Open your terminal and run this command to run this application.

   ```bash
   ./gradlew bootRun

4. **Reset DataBase**

   Open your terminal and run this command to reset the database.

   ```bash
   ./gradlew resetDB

5. **Once started, the application will be accessible at [http://localhost:8080](http://localhost:8080) by default.**


6. **Swagger -  [http://localhost:8080/swagger-ui/index.html#/](http://localhost:8080/swagger-ui/index.html#/)**


7. **Deployment - CommandLine**

   Step-1
   ```shell script
   docker login repos.flexicommerce.co.uk
   ```

   Step-2
   ```shell script
   //Test Environment 
   ./gradlew -Djib.to.image=repos.flexicommerce.co.uk/ri-api:dev -Djib.container.environment=ACTIVE_PROFILE=test  jib

   ```

   **FFmpeg Setup(Video Compression)**
   - Install ffmpeg on your server. For most Linux systems:
     ```bash
     sudo apt-get install ffmpeg
     ```
   - Set the ffmpeg path in your properties file (e.g., `application-test.properties`):
     ```properties
     ffmpeg.path=/usr/bin/ffmpeg
     ```
   - Ensure the path matches the installed location on your server.

### Order Tracking Code (for Demo Data)

   A compact format to specify demo orders, encoding ID type, partner, channel, and order lines.

   **Structure:**  `[A][B][C][Chunk...][XXX]`
   
   - **A** (ID type): `T`=Tracking, `O`=Order, `P`=Parent
   - **B** (Partner): `D`=Demo, `A`=Amazon, `Z`=Zalando
   - **C** (Channel): `W`=Website, `S`=Store, `C`=Call Center
   - **XXX** (3 digits): Additional numeric suffix (e.g., sequential or random) appended at the end
   
   **Chunks** (4 characters each):
   
   1. **Random (`R`)** — `R XX Q`
      - `XX`:
         - `00`= 1–3 random lines
         - `01`/`02`/`03`/... = fixed lines
      - `Q`: `0`=1–4 random qty; `1–9` = fixed qty
      - Example: `R011` → 1 line, random product, qty 1
   
   2. **Product (`P`)** — `P XX Q`
      - `XX`: product index `00–19` (1st–20th)
      - `Q`: `0` = 1–4 random qty; `1–9` = fixed qty
      - Multiple `P` chunks allowed.
      - Example: `P1502` → 16th product, qty 2
   
   **Examples**

| Code Example     | Description                                                                                | Validity |
|------------------|--------------------------------------------------------------------------------------------|----------|
| `TASR000123`     | Tracking Amazon Store; 1–3 random lines, qty 1–4; suffix `123`                             | Valid    |
| `OZWR011045`     | OrderID Zalando Website; 1 line, random product, qty 1; suffix `045`                       | Valid    |
| `PASP012067`     | Parent-OrderID Amazon Store; 1 line, 1st product, qty 2; suffix `067`                      | Valid    |
| `TADP050P011999` | Tracking Amazon Demo; 2 lines, 5th product × qty random, 1st product × qty 1; suffix `999` | Valid    |
| `OASR011P021065` | Mixing random (`R011`) and product (`P021`) chunks                                         | Invalid  |
| `TASP012R001064` | Random chunk cannot follow a selected chunk                                                | Invalid  |


### Merchant Setup

The Return Insights application supports multi-tenant architecture where each merchant operates as a separate tenant with isolated data and configurations. This section provides detailed instructions for setting up a new merchant in the system.

#### Overview

A complete merchant setup involves creating the following components:
- **Merchant Entity**: Core merchant information and configuration
- **Authentication Provider**: How users will authenticate (DB, SSO, etc.)
- **Channels**: Sales channels for order processing
- **User Groups & Users**: Administrative and operational users
- **OMS Integration**: Order Management System configuration
- **Roles & Permissions**: Access control and authorization
- **Templates**: UI configuration for data display

#### Reference Migration Scripts

The merchant setup process is based on the following migration scripts:
- `V3_0__FlexiMerchant.sql` - Example merchant setup (Flexi merchant)

#### Step-by-Step Setup Process

##### 1. Merchant Entity Creation

Create the core merchant record with essential information:

```sql
INSERT INTO merchant
(id, merchant_email, merchant_name, tenant, reporting_currency, hmac_key)
VALUES(
    [UNIQUE_ID],
    '[ADMIN_EMAIL]',
    '[MERCHANT_NAME]',
    [TENANT_ID],
    '[CURRENCY_CODE]',
    '[HMAC_KEY]'
);
```

**Key Fields:**
- `id`: Unique identifier for the merchant (increment from existing merchants)
- `merchant_email`: Primary contact email for the merchant
- `merchant_name`: Display name for the merchant (used in tenant ID calculation)
- `tenant`: Unique tenant identifier for data isolation
- `reporting_currency`: ISO 3-letter currency code (USD, EUR, GBP, etc.)
- `hmac_key`: Secure key for webhook signature verification

**Tenant ID Calculation:**
The system can auto-calculate tenant IDs based on merchant name using a weighted algorithm, or you can provide a custom unique identifier.

##### 2. Channel Configuration

Set up sales channels for order processing:

```sql
INSERT INTO channel
(id, channel_id, name, sla, tenant)
VALUES([CHANNEL_ID], UUID(), '[CHANNEL_NAME]', [SLA_DAYS], [TENANT_ID]);
```

**Channel Types:**
- **Default**: Primary channel for general orders
- **Website**: Online store orders
- **Store**: Physical store returns
- **Call Center**: Phone-based orders

**SLA (Service Level Agreement)**: Number of days for processing returns/inspections.

##### 3. Authentication Provider Setup

Configure how users will authenticate:

```sql
INSERT INTO auth_provider
(id, auth_url, provider, tenant, merchant_id)
VALUES([AUTH_ID], '[AUTH_URL]', '[PROVIDER_TYPE]', [TENANT_ID], [MERCHANT_ID]);
```

**Supported Providers:**
- `DB`: Database authentication (username/password)
- `GOOGLE`: Google OAuth integration
- `AZURE`: Microsoft Azure AD integration
- `OKTA`: Okta SSO integration
- `MANHATTAN`: Manhattan Associates integration

##### 4. User Groups and Permissions

Create user groups for role-based access control:

```sql
-- Admin Group
INSERT INTO user_group
(id, name, tenant, comment_visibility, user_group_id, partner_name, is_active)
VALUES ([GROUP_ID], 'Admin', [TENANT_ID], 'INTERNAL', UUID(), NULL, 1);

-- Role Group with Permissions
INSERT INTO role_group (id, role_group_id, name, tenant)
VALUES ([ROLE_GROUP_ID], UUID(), 'ADMIN_ROLES', [TENANT_ID]);
```

**Comment Visibility Options:**
- `INTERNAL`: Comments visible only to internal users
- `EXTERNAL`: Comments visible to external partners

##### 5. Admin User Creation

Create the primary administrative user:

```sql
INSERT INTO user
(id, is_active, password, user_name, auth_provider_id, tenant,
 first_name, last_name, user_id, merchant_id, language, role_group_id)
VALUES ([USER_ID], 1, '[HASHED_PASSWORD]', '[USERNAME]', [AUTH_PROVIDER_ID],
        [TENANT_ID], '[FIRST_NAME]', '[LAST_NAME]', UUID(), [MERCHANT_ID], 'en', [ROLE_GROUP_ID]);
```

**Password Hashing:**
Passwords must be BCrypt hashed. Default password 'password' hash:
`$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q`

##### 6. OMS Integration Configuration

Set up Order Management System integration:

```sql
-- OMS Provider
INSERT INTO oms_provider (id, provider_name, tenant)
VALUES ([OMS_PROVIDER_ID], '[PROVIDER_NAME]', [TENANT_ID]);

-- OMS Configuration
INSERT INTO oms_config (config_key, config_value, oms_provider_id)
VALUES
    ('user_name', '[OMS_USERNAME]', [OMS_PROVIDER_ID]),
    ('password', '[OMS_PASSWORD]', [OMS_PROVIDER_ID]),
    ('base_url', '[OMS_BASE_URL]', [OMS_PROVIDER_ID]);
```

**OMS Providers:**
- `MOCK`: Testing/demo environment
- `MAO`: Manhattan Associates integration
- Custom providers can be added as needed

##### 7. Search Strategies Configuration

Configure available search methods:

```sql
INSERT INTO search_strategies (search_strategy_id, search_strategy)
VALUES
    ([TENANT_ID], 'RETURN_BY_TRACKING_NUMBER'),
    ([TENANT_ID], 'ORDER_BY_ID'),
    ([TENANT_ID], 'ORDER_BY_RETURN_TRACKING'),
    ([TENANT_ID], 'ORDER_BY_PRODUCT_SERIAL_NUMBER');
```

##### 8. Role Assignment

Assign permissions to the admin role group:

```sql
INSERT INTO role_role_groups (role_group_id, role_id)
VALUES
    ([ROLE_GROUP_ID], 1021), -- Dashboard access
    ([ROLE_GROUP_ID], 1014), -- Audit access
    ([ROLE_GROUP_ID], 1016), -- User creation
    -- ... additional roles as needed
```


