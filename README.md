# Return Insight

This is a Spring Boot application built with <PERSON><PERSON><PERSON>.

## Prerequisites

- Dock<PERSON> should be running before executing the application.
- Java JDK 21+ (Ensure that you have the appropriate version installed).

## Getting Started

1. **Clone the Repository**

   Pull the code from the repository:

   ```bash
   <NAME_EMAIL>:flexicom/return-insight.git

2. **Navigate to the Project Directory**

   ```bash
   cd return-insight

3. **Run the Application**

   Open your terminal and run this command to run this application.

   ```bash
   ./gradlew bootRun

4. **Reset DataBase**

   Open your terminal and run this command to reset the database.

   ```bash
   ./gradlew resetDB

5. **Once started, the application will be accessible at [http://localhost:8080](http://localhost:8080) by default.**


6. **Swagger -  [http://localhost:8080/swagger-ui/index.html#/](http://localhost:8080/swagger-ui/index.html#/)**


7. **Deployment - CommandLine**

   Step-1
   ```shell script
   docker login repos.flexicommerce.co.uk
   ```

   Step-2
   ```shell script
   //Test Environment 
   ./gradlew -Djib.to.image=repos.flexicommerce.co.uk/ri-api:dev -Djib.container.environment=ACTIVE_PROFILE=test  jib

   ```

   **FFmpeg Setup(Video Compression)**
   - Install ffmpeg on your server. For most Linux systems:
     ```bash
     sudo apt-get install ffmpeg
     ```
   - Set the ffmpeg path in your properties file (e.g., `application-test.properties`):
     ```properties
     ffmpeg.path=/usr/bin/ffmpeg
     ```
   - Ensure the path matches the installed location on your server.

### Order Tracking Code (for Demo Data)

   A compact format to specify demo orders, encoding ID type, partner, channel, and order lines.

   **Structure:**  `[A][B][C][Chunk...][XXX]`
   
   - **A** (ID type): `T`=Tracking, `O`=Order, `P`=Parent
   - **B** (Partner): `D`=Demo, `A`=Amazon, `Z`=Zalando
   - **C** (Channel): `W`=Website, `S`=Store, `C`=Call Center
   - **XXX** (3 digits): Additional numeric suffix (e.g., sequential or random) appended at the end
   
   **Chunks** (4 characters each):
   
   1. **Random (`R`)** — `R XX Q`
      - `XX`:
         - `00`= 1–3 random lines
         - `01`/`02`/`03`/... = fixed lines
      - `Q`: `0`=1–4 random qty; `1–9` = fixed qty
      - Example: `R011` → 1 line, random product, qty 1
   
   2. **Product (`P`)** — `P XX Q`
      - `XX`: product index `00–19` (1st–20th)
      - `Q`: `0` = 1–4 random qty; `1–9` = fixed qty
      - Multiple `P` chunks allowed.
      - Example: `P1502` → 16th product, qty 2
   
   **Examples**

| Code Example     | Description                                                                                | Validity |
|------------------|--------------------------------------------------------------------------------------------|----------|
| `TASR000123`     | Tracking Amazon Store; 1–3 random lines, qty 1–4; suffix `123`                             | Valid    |
| `OZWR011045`     | OrderID Zalando Website; 1 line, random product, qty 1; suffix `045`                       | Valid    |
| `PASP012067`     | Parent-OrderID Amazon Store; 1 line, 1st product, qty 2; suffix `067`                      | Valid    |
| `TADP050P011999` | Tracking Amazon Demo; 2 lines, 5th product × qty random, 1st product × qty 1; suffix `999` | Valid    |
| `OASR011P021065` | Mixing random (`R011`) and product (`P021`) chunks                                         | Invalid  |
| `TASP012R001064` | Random chunk cannot follow a selected chunk                                                | Invalid  |


### Merchant Setup


