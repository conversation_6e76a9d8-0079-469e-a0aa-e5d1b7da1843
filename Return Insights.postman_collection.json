{"info": {"_postman_id": "7fbb402c-3ad1-4450-8698-b9cff4b9406b", "name": "Return Insights", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "36930777"}, "item": [{"name": "auth-controller", "item": [{"name": "Post Login", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"userName\": \"<EMAIL>\",\n  \"password\": \"password\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/api/v1/auth/login", "host": ["{{base-url}}"], "path": ["api", "v1", "auth", "login"]}}, "response": []}, {"name": "PostRefreshToken", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refresh-token}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/api/v1/auth/refresh-token", "host": ["{{base-url}}"], "path": ["api", "v1", "auth", "refresh-token"]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": ["let responseData = pm.response.json();", "", "let accessToken = responseData.accessToken;", "let refreshToken = responseData.refreshToken;", "", "pm.collectionVariables.set(\"access-token\", accessToken);", "pm.collectionVariables.set(\"refresh-token\",refreshToken);"]}}]}, {"name": "Inspection-item-controller", "item": [{"name": "Put InspectionItemUpdate", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "[\n{\n    \"id\": 2,\n    \"data\": {\n        \"receivedQuantity\": 1.0\n    }\n}\n]", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/api/v1/inspection-items", "host": ["{{base-url}}"], "path": ["api", "v1", "inspection-items"]}}, "response": []}, {"name": "Put Item-status", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"status\": \"APPROVED\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/api/v1/inspection-items/{{InspectionItem}}/update-status", "host": ["{{base-url}}"], "path": ["api", "v1", "inspection-items", "{{InspectionItem}}", "update-status"]}}, "response": []}, {"name": "Put item-add-review", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"itemCondition\": \"GOOD\",\n    \"review\":\"Test\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/api/v1/inspection-items/{{InspectionItem}}/add-review", "host": ["{{base-url}}"], "path": ["api", "v1", "inspection-items", "{{InspectionItem}}", "add-review"]}}, "response": []}]}, {"name": "Media-controller", "item": [{"name": "Post Media", "request": {"method": "POST", "header": [{"key": "", "value": "", "type": "text", "disabled": true}], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": "/Users/<USER>/Downloads/image.png"}, {"key": "inspectionItemId", "value": "1", "type": "text"}, {"key": "commentId", "value": "4", "type": "text"}]}, "url": {"raw": "{{base-url}}/api/v1/media/upload", "host": ["{{base-url}}"], "path": ["api", "v1", "media", "upload"], "query": [{"key": "inspectionItemId", "value": "1", "disabled": true}, {"key": "commentId", "value": "1", "disabled": true}]}}, "response": []}, {"name": "Get Media", "request": {"method": "GET", "header": [], "url": {"raw": "{{base-url}}/api/v1/media/download?fileName=/comment/image/image.png&mediaType=image/png", "host": ["{{base-url}}"], "path": ["api", "v1", "media", "download"], "query": [{"key": "fileName", "value": "/comment/image/image.png"}, {"key": "mediaType", "value": "image/png"}]}}, "response": []}]}, {"name": "Inspection-controller", "item": [{"name": "Post Inspections", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"status\": \"TODO\",\n  \"order\": {\n    \"orderId\": \"IT1016026\",\n    \"referenceId\": \"IT1016026\",\n    \"trackingId\": null\n  },\n  \"inspectionItems\": [\n    {\n      \"product\": {\n        \"sku\": \"810709591\",\n        \"productName\": \"Crocodile backpack with Double G\",\n        \"productImage\": \"https://imgdev.edge.regiongold.com/style/Transparent_Center_0_0_600x600/1659460562/710859_EAAC5_1000_001_100_0000_Light-Crocodile-backpack-with-Double-G.png,https://media.gucci.com/style/DarkGray_Center_0_0_800x800/1659460563/710859_EAAC5_1000_002_100_0000_Light-Crocodile-backpack-with-Double-G.jpg,https://media.gucci.com/style/DarkGray_Center_0_0_800x800/1659460564/710859_EAAC5_1000_009_100_0000_Light-Crocodile-backpack-with-Double-G.jpg,https://media.gucci.com/style/DarkGray_Center_0_0_800x800/1659460565/710859_EAAC5_1000_010_100_0000_Light-Crocodile-backpack-with-Double-G.jpg,https://media.gucci.com/style/DarkGray_Center_0_0_800x800/1659460568/710859_EAAC5_1000_013_100_0000_Light-Crocodile-backpack-with-Double-G.jpg\",\n        \"description\": \"MENS BAGS/Crocodile backpack with Double G/U/Black crocodile/Antique gold-toned hardware/Leather lining/Double G/Inside: 1 padded laptop compartment\\n/Outside: 2 side pockets with magnetic closure/Flap closure/9.4\\\"W x 15.7\\\"H x 6.2\\\"D/Made in Italy/Weight: 1.272kg approximately\",\n        \"colour\": null,\n        \"size\": null\n      },\n      \"expectedQuantity\": 1.0,\n      \"receivedQuantity\": 0\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/api/v1/inspections", "host": ["{{base-url}}"], "path": ["api", "v1", "inspections"]}}, "response": []}, {"name": "Get Inspections", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/api/v1/inspections/{{inspection-ID}}", "host": ["{{base-url}}"], "path": ["api", "v1", "inspections", "{{inspection-ID}}"]}}, "response": []}, {"name": "Del Inspection", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base-url}}/api/v1/inspections/{{reference-ID}}", "host": ["{{base-url}}"], "path": ["api", "v1", "inspections", "{{reference-ID}}"]}}, "response": []}]}, {"name": "Comment-controller", "item": [{"name": "Get Comments", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/api/v1/comments?inspectionItemId={{inspection-ID}}", "host": ["{{base-url}}"], "path": ["api", "v1", "comments"], "query": [{"key": "inspectionItemId", "value": "{{inspection-ID}}"}]}}, "response": []}, {"name": "Post Comments", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"content\": \"text comment\",\n  \"inspectionItemId\": 1,\n  \"visibility\": \"PUBLIC\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base-url}}/api/v1/comments", "host": ["{{base-url}}"], "path": ["api", "v1", "comments"]}}, "response": []}]}, {"name": "Order-controller", "item": [{"name": "Get OrderDetails", "request": {"method": "GET", "header": [], "url": {"raw": "{{base-url}}/api/v1/orders/\"{{order-ID}}\"", "host": ["{{base-url}}"], "path": ["api", "v1", "orders", "\"{{order-ID}}\""]}}, "response": []}]}, {"name": "Get Users", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "{{base-url}}/api/v1/users?userName=<EMAIL>", "host": ["{{base-url}}"], "path": ["api", "v1", "users"], "query": [{"key": "userName", "value": "<EMAIL>"}]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "access-token", "value": "", "type": "string"}, {"key": "refresh-token", "value": "", "type": "string"}, {"key": "base-url", "value": "http://localhost:8080", "type": "string"}, {"key": "order-ID", "value": "GPLTEST2112AAA5", "type": "string"}, {"key": "inspection-ID", "value": "", "type": "string"}, {"key": "QA-url", "value": "", "type": "string"}, {"key": "InspectionItem", "value": "", "type": "default"}, {"key": "reference-ID", "value": "", "type": "string"}]}