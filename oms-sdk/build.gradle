import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

plugins {
    id 'org.openapi.generator' version '6.6.0'
}

repositories {
    mavenCentral()
    gradlePluginPortal()
}


group = 'uk.co.flexi.oms.sdk'
version = '0.0.1-SNAPSHOT'

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

dependencies {
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    implementation 'org.apache.httpcomponents.client5:httpclient5:5.4'
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.10.0'
    implementation 'org.threeten:threetenbp:1.7.0'
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'io.gsonfire:gson-fire:1.8.5'
    implementation 'jakarta.annotation:jakarta.annotation-api:2.1.1'
    implementation 'jakarta.ws.rs:jakarta.ws.rs-api:3.1.0'
    implementation 'org.projectlombok:lombok:1.18.34'
    implementation 'org.apache.commons:commons-lang3:3.12.0'

    implementation 'org.slf4j:slf4j-api:2.0.9'
    implementation 'ch.qos.logback:logback-classic:1.4.11'

    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'

}

tasks.named('compileJava') {
}

tasks.named('test') {
    useJUnitPlatform()
}
sourceSets {
    main {
        java {
            srcDir 'src/main/java'
            srcDir "${buildDir}/generated/sources/mao/src/main/java"
        }
        resources {
            srcDirs = ['src/main/resources']
        }
    }
}

tasks.named("check") {
    dependsOn("test")
}