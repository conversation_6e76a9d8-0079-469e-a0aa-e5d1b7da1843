openapi: 3.0.0
info:
  title: API Authorization
  version: "1.0"
servers:
  - url: https://clients-auth.omni.manh.com
    description: mao - outh token
paths:
  /oauth/token:
    post:
      summary: API Authorization
      description: Use this endpoint to request the OAuth token (bearer token) to authorize your application to access MAO resources. You can pass this bearer token in your subsequent individual MAO API endpoint requests.
      operationId: API Authorization
      parameters:
        - name: Authorization
          in: header
          style: simple
          explode: false
          required: true
          schema:
              type: string
              example: Basic QWxhZGRpbjpvcGVuIHNlc2FtZQ==
        - name: Content-type
          in: header
          description: 'Indicates the media type of the request body'
          required: true
          style: simple
          explode: false
          schema:
            type: string
            example: application/x-www-form-urlencoded

      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/FullSchema'
            examples:
              example1:
                value:
                  grant_type: password
                  username: username
                  password: password
        required: true

      responses:
        "200":
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Response'
        "400":
          description: Bad Request - Invalid or missing parameters.
          content:
            application/json:
              schema:
                type: object
                properties:
                  errors:
                    type: string
                    description: Error type.
                    example: invalid_request
                  error_description:
                    type: string
                    description: Description of the error.
                    example: The username or password is missing or invalid.
        "500":
          description: Internal Server Error - An issue occurred on the server.
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    description: Error type.
                    example: server_error
                  error_description:
                    type: string
                    description: Description of the error.
                    example: An unexpected error occurred on the server. Please try again later.
components:
  schemas:
    FullSchema:
      required:
        - username
        - password
        - grant_type
      type: object
      properties:
        username:
          type: string
          description: 'User email address.'
        password:
          type: string
          description: 'User password.'
        grant_type:
          type: string
          description: 'Type of grant for token generation.'
      description: The request elements for OAuth API.
    Response:
      type: object
      properties:
        access_token:
          type: string
          description: 'The OAuth access token'
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpX……
        refresh_token:
          type: string
          description: 'The OAuth refresh token'
          example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpX……
        token_type:
          type: string
          description: 'The type of token issued.'
          example: bearer
        expires_in:
          type: integer
          description: 'Time (in seconds) until the access token expires.'
          example: 3600
        scope:
          type: string
          description: 'Indicates the scope of authorization'
          example: omni
        userOrgs:
          type: array
          items:
            type: string
          description: 'The organizations associated with the user.'
          example:
            - Gucci-US
            - GCOM
            - Gucci
        edge:
          type: integer
          description: 'Edge information related to the user.'
          example: 0
        organization:
          type: string
          description: 'The organization to which the user belongs.'
          example: Gucci-US
        userLocations:
          type: array
          items:
            type: object
            properties:
              locationId:
                type: string
                description: 'The ID of the location.'
                example: 23105
              locationType:
                type: string
                description: 'The type of the location.'
                example: dummy
          description: 'The locations associated with the user.'
          example:
            - locationId: 23105
              locationType: dummy
        accesstoAllBUs:
          type: boolean
          description: 'Indicates whether the user has access to all business units.'
          example: false
        tenantId:
          type: string
          description: 'The tenant ID associated with the user.'
          example: guccosf11o
        locale:
          type: string
          description: 'The locale of the user.'
          example: en
        excludedUserBusinessUnits:
          type: array
          items:
            type: object
          description: 'A list of excluded user business units.'
          example: [ ]
        userDefaults:
          type: array
          items:
            type: object
          description: 'The default settings for the user.'
          example: [ ]
        userBusinessUnits:
          type: array
          items:
            type: object
          description: 'The business units associated with the user.'
          example: [ ]
        userTimeZone:
          type: string
          description: 'The time zone of the user.'
          example: America/New_York
        jti:
          type: string
          description: 'The unique identifier for the token.'
          example: 5311a0d7-c2aa-4da5-b4ac-2408a9e35bbe
      description: This is the response of OAuth token and having access token details.
