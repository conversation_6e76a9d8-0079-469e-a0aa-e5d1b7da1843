/*
 * API Authorization
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.auth;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * ResponseUserLocationsInner
 */
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2024-12-05T21:09:24.954804Z[Europe/London]")
public class ResponseUserLocationsInner {
  public static final String SERIALIZED_NAME_LOCATION_ID = "locationId";
  @SerializedName(SERIALIZED_NAME_LOCATION_ID)
  private String locationId;

  public static final String SERIALIZED_NAME_LOCATION_TYPE = "locationType";
  @SerializedName(SERIALIZED_NAME_LOCATION_TYPE)
  private String locationType;

  public ResponseUserLocationsInner() {
  }

  public ResponseUserLocationsInner locationId(String locationId) {
    
    this.locationId = locationId;
    return this;
  }

   /**
   * The ID of the location.
   * @return locationId
  **/
  @jakarta.annotation.Nullable
  public String getLocationId() {
    return locationId;
  }


  public void setLocationId(String locationId) {
    this.locationId = locationId;
  }


  public ResponseUserLocationsInner locationType(String locationType) {
    
    this.locationType = locationType;
    return this;
  }

   /**
   * The type of the location.
   * @return locationType
  **/
  @jakarta.annotation.Nullable
  public String getLocationType() {
    return locationType;
  }


  public void setLocationType(String locationType) {
    this.locationType = locationType;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ResponseUserLocationsInner responseUserLocationsInner = (ResponseUserLocationsInner) o;
    return Objects.equals(this.locationId, responseUserLocationsInner.locationId) &&
        Objects.equals(this.locationType, responseUserLocationsInner.locationType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(locationId, locationType);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ResponseUserLocationsInner {\n");
    sb.append("    locationId: ").append(toIndentedString(locationId)).append("\n");
    sb.append("    locationType: ").append(toIndentedString(locationType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

