/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * RefundPaymentMethodId
 */
public class RefundPaymentMethodId {
  public static final String SERIALIZED_NAME_REFUND_PAYMENT_METHOD_ID = "RefundPaymentMethodId";
  @SerializedName(SERIALIZED_NAME_REFUND_PAYMENT_METHOD_ID)
  private String refundPaymentMethodId;

  public RefundPaymentMethodId refundPaymentMethodId(String refundPaymentMethodId) {
    this.refundPaymentMethodId = refundPaymentMethodId;
    return this;
  }

   /**
   * Indicates whether the customer wants to be refunded via the original payment method (e.g. credit card) or via a new payment method (e.g. gift card). Used only for POS returns.
   * @return refundPaymentMethodId
  **/
  
  public String getRefundPaymentMethodId() {
    return refundPaymentMethodId;
  }

  public void setRefundPaymentMethodId(String refundPaymentMethodId) {
    this.refundPaymentMethodId = refundPaymentMethodId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RefundPaymentMethodId refundPaymentMethodId = (RefundPaymentMethodId) o;
    return Objects.equals(this.refundPaymentMethodId, refundPaymentMethodId.refundPaymentMethodId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(refundPaymentMethodId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RefundPaymentMethodId {\n");
    
    sb.append("    refundPaymentMethodId: ").append(toIndentedString(refundPaymentMethodId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

