package uk.co.flexi.sdk.oms.mao.model.order;


import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class Extended {

    @SerializedName("DeliveryTime")
    private Object deliveryTime;

    @SerializedName("FirstDeliveryAttemptTime")
    private Object firstDeliveryAttemptTime;

    @SerializedName("DeliveryDate")
    private Object deliveryDate;

    @SerializedName("FirstDeliveryAttemptDate")
    private Object firstDeliveryAttemptDate;

    @SerializedName("PartnerName")
    private String partnerName;

    public Object getDeliveryTime() {
        return deliveryTime;
    }

    public Object getFirstDeliveryAttemptTime() {
        return firstDeliveryAttemptTime;
    }

    public Object getDeliveryDate() {
        return deliveryDate;
    }

    public Object getFirstDeliveryAttemptDate() {
        return firstDeliveryAttemptDate;
    }

    public String getPartnerName() {
        return partnerName;
    }
}