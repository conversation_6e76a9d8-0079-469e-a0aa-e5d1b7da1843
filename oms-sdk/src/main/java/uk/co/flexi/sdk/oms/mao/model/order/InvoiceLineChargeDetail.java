/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * InvoiceLineChargeDetail
 */
public class InvoiceLineChargeDetail {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ASSOCIATED_CHARGE_DETAIL_ID = "AssociatedChargeDetailId";
  @SerializedName(SERIALIZED_NAME_ASSOCIATED_CHARGE_DETAIL_ID)
  private String associatedChargeDetailId;

  public static final String SERIALIZED_NAME_CHARGE_DETAIL_ID = "ChargeDetailId";
  @SerializedName(SERIALIZED_NAME_CHARGE_DETAIL_ID)
  private String chargeDetailId;

  public static final String SERIALIZED_NAME_CHARGE_DISPLAY_NAME = "ChargeDisplayName";
  @SerializedName(SERIALIZED_NAME_CHARGE_DISPLAY_NAME)
  private String chargeDisplayName;

  public static final String SERIALIZED_NAME_CHARGE_PERCENT = "ChargePercent";
  @SerializedName(SERIALIZED_NAME_CHARGE_PERCENT)
  private Double chargePercent;

  public static final String SERIALIZED_NAME_CHARGE_REFERENCE_ID = "ChargeReferenceId";
  @SerializedName(SERIALIZED_NAME_CHARGE_REFERENCE_ID)
  private String chargeReferenceId;

  public static final String SERIALIZED_NAME_CHARGE_SEQUENCE = "ChargeSequence";
  @SerializedName(SERIALIZED_NAME_CHARGE_SEQUENCE)
  private Long chargeSequence;

  public static final String SERIALIZED_NAME_CHARGE_SUB_TYPE = "ChargeSubType";
  @SerializedName(SERIALIZED_NAME_CHARGE_SUB_TYPE)
  private ChargeSubTypeId chargeSubType = null;

  public static final String SERIALIZED_NAME_CHARGE_TOTAL = "ChargeTotal";
  @SerializedName(SERIALIZED_NAME_CHARGE_TOTAL)
  private BigDecimal chargeTotal;

  public static final String SERIALIZED_NAME_CHARGE_TYPE = "ChargeType";
  @SerializedName(SERIALIZED_NAME_CHARGE_TYPE)
  private ChargeTypeId chargeType = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DISCOUNT_ON = "DiscountOn";
  @SerializedName(SERIALIZED_NAME_DISCOUNT_ON)
  private DiscountOnId discountOn = null;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FULFILLMENT_GROUP_ID = "FulfillmentGroupId";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_GROUP_ID)
  private String fulfillmentGroupId;

  public static final String SERIALIZED_NAME_HEADER_CHARGE_DETAIL_ID = "HeaderChargeDetailId";
  @SerializedName(SERIALIZED_NAME_HEADER_CHARGE_DETAIL_ID)
  private String headerChargeDetailId;

  public static final String SERIALIZED_NAME_IS_INFORMATIONAL = "IsInformational";
  @SerializedName(SERIALIZED_NAME_IS_INFORMATIONAL)
  private Boolean isInformational;

  public static final String SERIALIZED_NAME_IS_LINE_DISCOUNT = "IsLineDiscount";
  @SerializedName(SERIALIZED_NAME_IS_LINE_DISCOUNT)
  private Boolean isLineDiscount;

  public static final String SERIALIZED_NAME_IS_OVERR_IDDEN = "IsOverrIdden";
  @SerializedName(SERIALIZED_NAME_IS_OVERR_IDDEN)
  private Boolean isOverrIdden;

  public static final String SERIALIZED_NAME_IS_OVERRIDDEN = "IsOverridden";
  @SerializedName(SERIALIZED_NAME_IS_OVERRIDDEN)
  private Boolean isOverridden;

  public static final String SERIALIZED_NAME_IS_PRORATED_AT_SAME_LEVEL = "IsProratedAtSameLevel";
  @SerializedName(SERIALIZED_NAME_IS_PRORATED_AT_SAME_LEVEL)
  private Boolean isProratedAtSameLevel;

  public static final String SERIALIZED_NAME_IS_RETURN_CHARGE = "IsReturnCharge";
  @SerializedName(SERIALIZED_NAME_IS_RETURN_CHARGE)
  private Boolean isReturnCharge;

  public static final String SERIALIZED_NAME_IS_TAX_INCLUDED = "IsTaxIncluded";
  @SerializedName(SERIALIZED_NAME_IS_TAX_INCLUDED)
  private Boolean isTaxIncluded;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_CHARGE_DETAIL_ID = "OrderLineChargeDetailId";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_CHARGE_DETAIL_ID)
  private String orderLineChargeDetailId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_ORIGINAL_CHARGE_AMOUNT = "OriginalChargeAmount";
  @SerializedName(SERIALIZED_NAME_ORIGINAL_CHARGE_AMOUNT)
  private BigDecimal originalChargeAmount;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_CHARGE_DETAIL_ID = "ParentChargeDetailId";
  @SerializedName(SERIALIZED_NAME_PARENT_CHARGE_DETAIL_ID)
  private String parentChargeDetailId;

  public static final String SERIALIZED_NAME_REASON = "Reason";
  @SerializedName(SERIALIZED_NAME_REASON)
  private ReasonId reason = null;

  public static final String SERIALIZED_NAME_RELATED_CHARGE_DETAIL_ID = "RelatedChargeDetailId";
  @SerializedName(SERIALIZED_NAME_RELATED_CHARGE_DETAIL_ID)
  private String relatedChargeDetailId;

  public static final String SERIALIZED_NAME_RELATED_CHARGE_TYPE = "RelatedChargeType";
  @SerializedName(SERIALIZED_NAME_RELATED_CHARGE_TYPE)
  private String relatedChargeType;

  public static final String SERIALIZED_NAME_RELATED_ORDER_LINE_ID = "RelatedOrderLineId";
  @SerializedName(SERIALIZED_NAME_RELATED_ORDER_LINE_ID)
  private String relatedOrderLineId;

  public static final String SERIALIZED_NAME_REQUESTED_AMOUNT = "RequestedAmount";
  @SerializedName(SERIALIZED_NAME_REQUESTED_AMOUNT)
  private BigDecimal requestedAmount;

  public static final String SERIALIZED_NAME_TAX_CODE = "TaxCode";
  @SerializedName(SERIALIZED_NAME_TAX_CODE)
  private String taxCode;

  public static final String SERIALIZED_NAME_TAXABLE_AMOUNT = "TaxableAmount";
  @SerializedName(SERIALIZED_NAME_TAXABLE_AMOUNT)
  private BigDecimal taxableAmount;

  public static final String SERIALIZED_NAME_UNIT_CHARGE = "UnitCharge";
  @SerializedName(SERIALIZED_NAME_UNIT_CHARGE)
  private Double unitCharge;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_CHARGE_TYPE_ID = "chargeTypeId";
  @SerializedName(SERIALIZED_NAME_CHARGE_TYPE_ID)
  private String chargeTypeId;

  public static final String SERIALIZED_NAME_COMMENTS = "comments";
  @SerializedName(SERIALIZED_NAME_COMMENTS)
  private String comments;

  public static final String SERIALIZED_NAME_DISCOUNT_ON_ID = "discountOnId";
  @SerializedName(SERIALIZED_NAME_DISCOUNT_ON_ID)
  private String discountOnId;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_IS_POST_RETURN = "isPostReturn";
  @SerializedName(SERIALIZED_NAME_IS_POST_RETURN)
  private Boolean isPostReturn;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public InvoiceLineChargeDetail actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public InvoiceLineChargeDetail putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public InvoiceLineChargeDetail associatedChargeDetailId(String associatedChargeDetailId) {
    this.associatedChargeDetailId = associatedChargeDetailId;
    return this;
  }

   /**
   * ID of the associated charge; for example, if a discount is applied against a charge
   * @return associatedChargeDetailId
  **/
  
  public String getAssociatedChargeDetailId() {
    return associatedChargeDetailId;
  }

  public void setAssociatedChargeDetailId(String associatedChargeDetailId) {
    this.associatedChargeDetailId = associatedChargeDetailId;
  }

  public InvoiceLineChargeDetail chargeDetailId(String chargeDetailId) {
    this.chargeDetailId = chargeDetailId;
    return this;
  }

   /**
   * Unique identifier of the charge
   * @return chargeDetailId
  **/
  
  public String getChargeDetailId() {
    return chargeDetailId;
  }

  public void setChargeDetailId(String chargeDetailId) {
    this.chargeDetailId = chargeDetailId;
  }

  public InvoiceLineChargeDetail chargeDisplayName(String chargeDisplayName) {
    this.chargeDisplayName = chargeDisplayName;
    return this;
  }

   /**
   * Text which should be displayed in the UI
   * @return chargeDisplayName
  **/
  
  public String getChargeDisplayName() {
    return chargeDisplayName;
  }

  public void setChargeDisplayName(String chargeDisplayName) {
    this.chargeDisplayName = chargeDisplayName;
  }

  public InvoiceLineChargeDetail chargePercent(Double chargePercent) {
    this.chargePercent = chargePercent;
    return this;
  }

   /**
   * Percentage charge, applied as a percentage of line subtotal. Applicable for charge types including Promotion, Coupon, Appeasement, Discount, and Return Fee. For example, if a discount is applied as 10% and the lineSubtotal &#x3D; $50, then the chargeTotal is calculated as -$5. Used for recalculating charges when a line subtotal is updated.
   * minimum: 0
   * maximum: 999999999999.9999
   * @return chargePercent
  **/
  
  public Double getChargePercent() {
    return chargePercent;
  }

  public void setChargePercent(Double chargePercent) {
    this.chargePercent = chargePercent;
  }

  public InvoiceLineChargeDetail chargeReferenceId(String chargeReferenceId) {
    this.chargeReferenceId = chargeReferenceId;
    return this;
  }

   /**
   * Unique Identifier of chargeType, if Charge Type Promotion it would PromotionId, ChargeType is Coupon, it would be CouponId
   * @return chargeReferenceId
  **/
  
  public String getChargeReferenceId() {
    return chargeReferenceId;
  }

  public void setChargeReferenceId(String chargeReferenceId) {
    this.chargeReferenceId = chargeReferenceId;
  }

  public InvoiceLineChargeDetail chargeSequence(Long chargeSequence) {
    this.chargeSequence = chargeSequence;
    return this;
  }

   /**
   * This attribute is intended to support stacked discounts. This attribute governs the sequence in which discounts are evaluated.
   * minimum: 0
   * maximum: -8446744073709551617
   * @return chargeSequence
  **/
  
  public Long getChargeSequence() {
    return chargeSequence;
  }

  public void setChargeSequence(Long chargeSequence) {
    this.chargeSequence = chargeSequence;
  }

  public InvoiceLineChargeDetail chargeSubType(ChargeSubTypeId chargeSubType) {
    this.chargeSubType = chargeSubType;
    return this;
  }

   /**
   * Get chargeSubType
   * @return chargeSubType
  **/
  
  public ChargeSubTypeId getChargeSubType() {
    return chargeSubType;
  }

  public void setChargeSubType(ChargeSubTypeId chargeSubType) {
    this.chargeSubType = chargeSubType;
  }

  public InvoiceLineChargeDetail chargeTotal(BigDecimal chargeTotal) {
    this.chargeTotal = chargeTotal;
    return this;
  }

   /**
   * Total charge; If charge C &#x3D; Ax+ B, then Charge Total &#x3D; C.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return chargeTotal
  **/
  
  public BigDecimal getChargeTotal() {
    return chargeTotal;
  }

  public void setChargeTotal(BigDecimal chargeTotal) {
    this.chargeTotal = chargeTotal;
  }

  public InvoiceLineChargeDetail chargeType(ChargeTypeId chargeType) {
    this.chargeType = chargeType;
    return this;
  }

   /**
   * Get chargeType
   * @return chargeType
  **/
  
  public ChargeTypeId getChargeType() {
    return chargeType;
  }

  public void setChargeType(ChargeTypeId chargeType) {
    this.chargeType = chargeType;
  }

  public InvoiceLineChargeDetail createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public InvoiceLineChargeDetail createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public InvoiceLineChargeDetail discountOn(DiscountOnId discountOn) {
    this.discountOn = discountOn;
    return this;
  }

   /**
   * Get discountOn
   * @return discountOn
  **/
  
  public DiscountOnId getDiscountOn() {
    return discountOn;
  }

  public void setDiscountOn(DiscountOnId discountOn) {
    this.discountOn = discountOn;
  }

  public InvoiceLineChargeDetail extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public InvoiceLineChargeDetail fulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
    return this;
  }

   /**
   * ID used to group items which need to be fulfilled together
   * @return fulfillmentGroupId
  **/
  
  public String getFulfillmentGroupId() {
    return fulfillmentGroupId;
  }

  public void setFulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
  }

  public InvoiceLineChargeDetail headerChargeDetailId(String headerChargeDetailId) {
    this.headerChargeDetailId = headerChargeDetailId;
    return this;
  }

   /**
   * Identifier of the header charge detail, if a detail is created as a result of prorating a header charge to line level
   * @return headerChargeDetailId
  **/
  
  public String getHeaderChargeDetailId() {
    return headerChargeDetailId;
  }

  public void setHeaderChargeDetailId(String headerChargeDetailId) {
    this.headerChargeDetailId = headerChargeDetailId;
  }

  public InvoiceLineChargeDetail isInformational(Boolean isInformational) {
    this.isInformational = isInformational;
    return this;
  }

   /**
   * Indicates a charge is informational and is not taken into account in the total calculations
   * @return isInformational
  **/
  
  public Boolean getIsInformational() {
    return isInformational;
  }

  public void setIsInformational(Boolean isInformational) {
    this.isInformational = isInformational;
  }

  public InvoiceLineChargeDetail isLineDiscount(Boolean isLineDiscount) {
    this.isLineDiscount = isLineDiscount;
    return this;
  }

   /**
   * DEPRECATED. PLEASE USE discountOn attribute.
   * @return isLineDiscount
  **/
  
  public Boolean getIsLineDiscount() {
    return isLineDiscount;
  }

  public void setIsLineDiscount(Boolean isLineDiscount) {
    this.isLineDiscount = isLineDiscount;
  }

  public InvoiceLineChargeDetail isOverrIdden(Boolean isOverrIdden) {
    this.isOverrIdden = isOverrIdden;
    return this;
  }

   /**
   * Deprecated, use isOverriden instead.
   * @return isOverrIdden
  **/
  
  public Boolean getIsOverrIdden() {
    return isOverrIdden;
  }

  public void setIsOverrIdden(Boolean isOverrIdden) {
    this.isOverrIdden = isOverrIdden;
  }

  public InvoiceLineChargeDetail isOverridden(Boolean isOverridden) {
    this.isOverridden = isOverridden;
    return this;
  }

   /**
   * Indicates if the charge is overridden
   * @return isOverridden
  **/
  
  public Boolean getIsOverridden() {
    return isOverridden;
  }

  public void setIsOverridden(Boolean isOverridden) {
    this.isOverridden = isOverridden;
  }

  public InvoiceLineChargeDetail isProratedAtSameLevel(Boolean isProratedAtSameLevel) {
    this.isProratedAtSameLevel = isProratedAtSameLevel;
    return this;
  }

   /**
   * Will be set to true when given discount is on group of charges via relatedChargeType or discount is on entire line.When set to true this discount will not be considered for tax, but the ones that are prorated 
   * @return isProratedAtSameLevel
  **/
  
  public Boolean getIsProratedAtSameLevel() {
    return isProratedAtSameLevel;
  }

  public void setIsProratedAtSameLevel(Boolean isProratedAtSameLevel) {
    this.isProratedAtSameLevel = isProratedAtSameLevel;
  }

  public InvoiceLineChargeDetail isReturnCharge(Boolean isReturnCharge) {
    this.isReturnCharge = isReturnCharge;
    return this;
  }

   /**
   * Indicates if a charge applies only to return order lines, such as a restocking or return shipping charge. If true, then header return charges are prorated across order lines where isReturn is true. Return charges are displayed separately in the call center UI and are included in separate order total calculations as part of the order response. Return charges are taxed using the return order creation date, while non-return charges on return orders are taxed using the original order fulfillment date.
   * @return isReturnCharge
  **/
  
  public Boolean getIsReturnCharge() {
    return isReturnCharge;
  }

  public void setIsReturnCharge(Boolean isReturnCharge) {
    this.isReturnCharge = isReturnCharge;
  }

  public InvoiceLineChargeDetail isTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
    return this;
  }

   /**
   * Indicates if tax is included in the charge. Included in the requests sent to the tax engine, so that tax can be back-calculated from the charge amount. Used for charges which are inclusive of tax such as return SnH. For example, if return SnH is $10 and taxIncluded is true, then if the tax rate is 10%, the tax engine returns a tax of $1 if isTaxIncluded &#x3D; false and the taxDetail is saved with isInformational &#x3D; false. If isTaxIncluded &#x3D; true, then the tax engine returns a tax of $.91 and the taxDetail is saved with isInformational &#x3D; true.
   * @return isTaxIncluded
  **/
  
  public Boolean getIsTaxIncluded() {
    return isTaxIncluded;
  }

  public void setIsTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
  }

  public InvoiceLineChargeDetail localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public InvoiceLineChargeDetail messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public InvoiceLineChargeDetail orderLineChargeDetailId(String orderLineChargeDetailId) {
    this.orderLineChargeDetailId = orderLineChargeDetailId;
    return this;
  }

   /**
   * Contains corresponding order line charge detail id
   * @return orderLineChargeDetailId
  **/
  
  public String getOrderLineChargeDetailId() {
    return orderLineChargeDetailId;
  }

  public void setOrderLineChargeDetailId(String orderLineChargeDetailId) {
    this.orderLineChargeDetailId = orderLineChargeDetailId;
  }

  public InvoiceLineChargeDetail orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public InvoiceLineChargeDetail originalChargeAmount(BigDecimal originalChargeAmount) {
    this.originalChargeAmount = originalChargeAmount;
    return this;
  }

   /**
   * If IsOverridden is true, this field includes the original charge amount
   * minimum: 0
   * maximum: 99999999999999.98
   * @return originalChargeAmount
  **/
  
  public BigDecimal getOriginalChargeAmount() {
    return originalChargeAmount;
  }

  public void setOriginalChargeAmount(BigDecimal originalChargeAmount) {
    this.originalChargeAmount = originalChargeAmount;
  }

  public InvoiceLineChargeDetail PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public InvoiceLineChargeDetail parentChargeDetailId(String parentChargeDetailId) {
    this.parentChargeDetailId = parentChargeDetailId;
    return this;
  }

   /**
   * Contains the ChargeDetailId of the discount that is prorated at line  level
   * @return parentChargeDetailId
  **/
  
  public String getParentChargeDetailId() {
    return parentChargeDetailId;
  }

  public void setParentChargeDetailId(String parentChargeDetailId) {
    this.parentChargeDetailId = parentChargeDetailId;
  }

  public InvoiceLineChargeDetail reason(ReasonId reason) {
    this.reason = reason;
    return this;
  }

   /**
   * Get reason
   * @return reason
  **/
  
  public ReasonId getReason() {
    return reason;
  }

  public void setReason(ReasonId reason) {
    this.reason = reason;
  }

  public InvoiceLineChargeDetail relatedChargeDetailId(String relatedChargeDetailId) {
    this.relatedChargeDetailId = relatedChargeDetailId;
    return this;
  }

   /**
   * Related Charge Detail Id if Promotion/Coupon/discount needs to apply for specific charge Detail id.
   * @return relatedChargeDetailId
  **/
  
  public String getRelatedChargeDetailId() {
    return relatedChargeDetailId;
  }

  public void setRelatedChargeDetailId(String relatedChargeDetailId) {
    this.relatedChargeDetailId = relatedChargeDetailId;
  }

  public InvoiceLineChargeDetail relatedChargeType(String relatedChargeType) {
    this.relatedChargeType = relatedChargeType;
    return this;
  }

   /**
   * Related Charge Type if Promotion/Coupon/Discount needs to apply for specific Charge Type e.g. all shipping Charges.
   * @return relatedChargeType
  **/
  
  public String getRelatedChargeType() {
    return relatedChargeType;
  }

  public void setRelatedChargeType(String relatedChargeType) {
    this.relatedChargeType = relatedChargeType;
  }

  public InvoiceLineChargeDetail relatedOrderLineId(String relatedOrderLineId) {
    this.relatedOrderLineId = relatedOrderLineId;
    return this;
  }

   /**
   * Related Line Item ID in the case of BOGO for which this Item is offered as a Promotion
   * @return relatedOrderLineId
  **/
  
  public String getRelatedOrderLineId() {
    return relatedOrderLineId;
  }

  public void setRelatedOrderLineId(String relatedOrderLineId) {
    this.relatedOrderLineId = relatedOrderLineId;
  }

  public InvoiceLineChargeDetail requestedAmount(BigDecimal requestedAmount) {
    this.requestedAmount = requestedAmount;
    return this;
  }

   /**
   * Requested amount, in case of $Off discount / Flat Appeasement
   * minimum: 0
   * maximum: 99999999999999.98
   * @return requestedAmount
  **/
  
  public BigDecimal getRequestedAmount() {
    return requestedAmount;
  }

  public void setRequestedAmount(BigDecimal requestedAmount) {
    this.requestedAmount = requestedAmount;
  }

  public InvoiceLineChargeDetail taxCode(String taxCode) {
    this.taxCode = taxCode;
    return this;
  }

   /**
   * Defines the charge category
   * @return taxCode
  **/
  
  public String getTaxCode() {
    return taxCode;
  }

  public void setTaxCode(String taxCode) {
    this.taxCode = taxCode;
  }

  public InvoiceLineChargeDetail taxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
    return this;
  }

   /**
   * Get taxableAmount
   * @return taxableAmount
  **/
  
  public BigDecimal getTaxableAmount() {
    return taxableAmount;
  }

  public void setTaxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
  }

  public InvoiceLineChargeDetail unitCharge(Double unitCharge) {
    this.unitCharge = unitCharge;
    return this;
  }

   /**
   * Per unit charge, if a charge is applied at the quantity level. For example, if a return fee is applied as $5/unit and a return line has 3 units, then a charge is applied for chargeTotal &#x3D; $15 with unitCharge &#x3D; $5. Used for recalculating charges when the line quantities are updated.
   * minimum: 0
   * maximum: 999999999999.9999
   * @return unitCharge
  **/
  
  public Double getUnitCharge() {
    return unitCharge;
  }

  public void setUnitCharge(Double unitCharge) {
    this.unitCharge = unitCharge;
  }

  public InvoiceLineChargeDetail updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public InvoiceLineChargeDetail updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public InvoiceLineChargeDetail chargeTypeId(String chargeTypeId) {
    this.chargeTypeId = chargeTypeId;
    return this;
  }

   /**
   * Get chargeTypeId
   * @return chargeTypeId
  **/
  
  public String getChargeTypeId() {
    return chargeTypeId;
  }

  public void setChargeTypeId(String chargeTypeId) {
    this.chargeTypeId = chargeTypeId;
  }

  public InvoiceLineChargeDetail comments(String comments) {
    this.comments = comments;
    return this;
  }

   /**
   * Get comments
   * @return comments
  **/
  
  public String getComments() {
    return comments;
  }

  public void setComments(String comments) {
    this.comments = comments;
  }

  public InvoiceLineChargeDetail discountOnId(String discountOnId) {
    this.discountOnId = discountOnId;
    return this;
  }

   /**
   * Get discountOnId
   * @return discountOnId
  **/
  
  public String getDiscountOnId() {
    return discountOnId;
  }

  public void setDiscountOnId(String discountOnId) {
    this.discountOnId = discountOnId;
  }

  public InvoiceLineChargeDetail entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public InvoiceLineChargeDetail isPostReturn(Boolean isPostReturn) {
    this.isPostReturn = isPostReturn;
    return this;
  }

   /**
   * Get isPostReturn
   * @return isPostReturn
  **/
  
  public Boolean getIsPostReturn() {
    return isPostReturn;
  }

  public void setIsPostReturn(Boolean isPostReturn) {
    this.isPostReturn = isPostReturn;
  }

  public InvoiceLineChargeDetail localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvoiceLineChargeDetail invoiceLineChargeDetail = (InvoiceLineChargeDetail) o;
    return Objects.equals(this.actions, invoiceLineChargeDetail.actions) &&
        Objects.equals(this.associatedChargeDetailId, invoiceLineChargeDetail.associatedChargeDetailId) &&
        Objects.equals(this.chargeDetailId, invoiceLineChargeDetail.chargeDetailId) &&
        Objects.equals(this.chargeDisplayName, invoiceLineChargeDetail.chargeDisplayName) &&
        Objects.equals(this.chargePercent, invoiceLineChargeDetail.chargePercent) &&
        Objects.equals(this.chargeReferenceId, invoiceLineChargeDetail.chargeReferenceId) &&
        Objects.equals(this.chargeSequence, invoiceLineChargeDetail.chargeSequence) &&
        Objects.equals(this.chargeSubType, invoiceLineChargeDetail.chargeSubType) &&
        Objects.equals(this.chargeTotal, invoiceLineChargeDetail.chargeTotal) &&
        Objects.equals(this.chargeType, invoiceLineChargeDetail.chargeType) &&
        Objects.equals(this.createdBy, invoiceLineChargeDetail.createdBy) &&
        Objects.equals(this.createdTimestamp, invoiceLineChargeDetail.createdTimestamp) &&
        Objects.equals(this.discountOn, invoiceLineChargeDetail.discountOn) &&
        Objects.equals(this.extended, invoiceLineChargeDetail.extended) &&
        Objects.equals(this.fulfillmentGroupId, invoiceLineChargeDetail.fulfillmentGroupId) &&
        Objects.equals(this.headerChargeDetailId, invoiceLineChargeDetail.headerChargeDetailId) &&
        Objects.equals(this.isInformational, invoiceLineChargeDetail.isInformational) &&
        Objects.equals(this.isLineDiscount, invoiceLineChargeDetail.isLineDiscount) &&
        Objects.equals(this.isOverrIdden, invoiceLineChargeDetail.isOverrIdden) &&
        Objects.equals(this.isOverridden, invoiceLineChargeDetail.isOverridden) &&
        Objects.equals(this.isProratedAtSameLevel, invoiceLineChargeDetail.isProratedAtSameLevel) &&
        Objects.equals(this.isReturnCharge, invoiceLineChargeDetail.isReturnCharge) &&
        Objects.equals(this.isTaxIncluded, invoiceLineChargeDetail.isTaxIncluded) &&
        Objects.equals(this.localizedTo, invoiceLineChargeDetail.localizedTo) &&
        Objects.equals(this.messages, invoiceLineChargeDetail.messages) &&
        Objects.equals(this.orderLineChargeDetailId, invoiceLineChargeDetail.orderLineChargeDetailId) &&
        Objects.equals(this.orgId, invoiceLineChargeDetail.orgId) &&
        Objects.equals(this.originalChargeAmount, invoiceLineChargeDetail.originalChargeAmount) &&
        Objects.equals(this.PK, invoiceLineChargeDetail.PK) &&
        Objects.equals(this.parentChargeDetailId, invoiceLineChargeDetail.parentChargeDetailId) &&
        Objects.equals(this.reason, invoiceLineChargeDetail.reason) &&
        Objects.equals(this.relatedChargeDetailId, invoiceLineChargeDetail.relatedChargeDetailId) &&
        Objects.equals(this.relatedChargeType, invoiceLineChargeDetail.relatedChargeType) &&
        Objects.equals(this.relatedOrderLineId, invoiceLineChargeDetail.relatedOrderLineId) &&
        Objects.equals(this.requestedAmount, invoiceLineChargeDetail.requestedAmount) &&
        Objects.equals(this.taxCode, invoiceLineChargeDetail.taxCode) &&
        Objects.equals(this.taxableAmount, invoiceLineChargeDetail.taxableAmount) &&
        Objects.equals(this.unitCharge, invoiceLineChargeDetail.unitCharge) &&
        Objects.equals(this.updatedBy, invoiceLineChargeDetail.updatedBy) &&
        Objects.equals(this.updatedTimestamp, invoiceLineChargeDetail.updatedTimestamp) &&
        Objects.equals(this.chargeTypeId, invoiceLineChargeDetail.chargeTypeId) &&
        Objects.equals(this.comments, invoiceLineChargeDetail.comments) &&
        Objects.equals(this.discountOnId, invoiceLineChargeDetail.discountOnId) &&
        Objects.equals(this.entityName, invoiceLineChargeDetail.entityName) &&
        Objects.equals(this.isPostReturn, invoiceLineChargeDetail.isPostReturn) &&
        Objects.equals(this.localize, invoiceLineChargeDetail.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, associatedChargeDetailId, chargeDetailId, chargeDisplayName, chargePercent, chargeReferenceId, chargeSequence, chargeSubType, chargeTotal, chargeType, createdBy, createdTimestamp, discountOn, extended, fulfillmentGroupId, headerChargeDetailId, isInformational, isLineDiscount, isOverrIdden, isOverridden, isProratedAtSameLevel, isReturnCharge, isTaxIncluded, localizedTo, messages, orderLineChargeDetailId, orgId, originalChargeAmount, PK, parentChargeDetailId, reason, relatedChargeDetailId, relatedChargeType, relatedOrderLineId, requestedAmount, taxCode, taxableAmount, unitCharge, updatedBy, updatedTimestamp, chargeTypeId, comments, discountOnId, entityName, isPostReturn, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvoiceLineChargeDetail {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    associatedChargeDetailId: ").append(toIndentedString(associatedChargeDetailId)).append("\n");
    sb.append("    chargeDetailId: ").append(toIndentedString(chargeDetailId)).append("\n");
    sb.append("    chargeDisplayName: ").append(toIndentedString(chargeDisplayName)).append("\n");
    sb.append("    chargePercent: ").append(toIndentedString(chargePercent)).append("\n");
    sb.append("    chargeReferenceId: ").append(toIndentedString(chargeReferenceId)).append("\n");
    sb.append("    chargeSequence: ").append(toIndentedString(chargeSequence)).append("\n");
    sb.append("    chargeSubType: ").append(toIndentedString(chargeSubType)).append("\n");
    sb.append("    chargeTotal: ").append(toIndentedString(chargeTotal)).append("\n");
    sb.append("    chargeType: ").append(toIndentedString(chargeType)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    discountOn: ").append(toIndentedString(discountOn)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    fulfillmentGroupId: ").append(toIndentedString(fulfillmentGroupId)).append("\n");
    sb.append("    headerChargeDetailId: ").append(toIndentedString(headerChargeDetailId)).append("\n");
    sb.append("    isInformational: ").append(toIndentedString(isInformational)).append("\n");
    sb.append("    isLineDiscount: ").append(toIndentedString(isLineDiscount)).append("\n");
    sb.append("    isOverrIdden: ").append(toIndentedString(isOverrIdden)).append("\n");
    sb.append("    isOverridden: ").append(toIndentedString(isOverridden)).append("\n");
    sb.append("    isProratedAtSameLevel: ").append(toIndentedString(isProratedAtSameLevel)).append("\n");
    sb.append("    isReturnCharge: ").append(toIndentedString(isReturnCharge)).append("\n");
    sb.append("    isTaxIncluded: ").append(toIndentedString(isTaxIncluded)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderLineChargeDetailId: ").append(toIndentedString(orderLineChargeDetailId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    originalChargeAmount: ").append(toIndentedString(originalChargeAmount)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentChargeDetailId: ").append(toIndentedString(parentChargeDetailId)).append("\n");
    sb.append("    reason: ").append(toIndentedString(reason)).append("\n");
    sb.append("    relatedChargeDetailId: ").append(toIndentedString(relatedChargeDetailId)).append("\n");
    sb.append("    relatedChargeType: ").append(toIndentedString(relatedChargeType)).append("\n");
    sb.append("    relatedOrderLineId: ").append(toIndentedString(relatedOrderLineId)).append("\n");
    sb.append("    requestedAmount: ").append(toIndentedString(requestedAmount)).append("\n");
    sb.append("    taxCode: ").append(toIndentedString(taxCode)).append("\n");
    sb.append("    taxableAmount: ").append(toIndentedString(taxableAmount)).append("\n");
    sb.append("    unitCharge: ").append(toIndentedString(unitCharge)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    chargeTypeId: ").append(toIndentedString(chargeTypeId)).append("\n");
    sb.append("    comments: ").append(toIndentedString(comments)).append("\n");
    sb.append("    discountOnId: ").append(toIndentedString(discountOnId)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    isPostReturn: ").append(toIndentedString(isPostReturn)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

