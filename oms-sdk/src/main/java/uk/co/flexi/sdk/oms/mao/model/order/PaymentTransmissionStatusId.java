/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * PaymentTransmissionStatusId
 */
public class PaymentTransmissionStatusId {
  public static final String SERIALIZED_NAME_PAYMENT_TRANSMISSION_STATUS_ID = "PaymentTransmissionStatusId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TRANSMISSION_STATUS_ID)
  private String paymentTransmissionStatusId;

  public PaymentTransmissionStatusId paymentTransmissionStatusId(String paymentTransmissionStatusId) {
    this.paymentTransmissionStatusId = paymentTransmissionStatusId;
    return this;
  }

   /**
   * Unique identifier of the transaction status
   * @return paymentTransmissionStatusId
  **/
  
  public String getPaymentTransmissionStatusId() {
    return paymentTransmissionStatusId;
  }

  public void setPaymentTransmissionStatusId(String paymentTransmissionStatusId) {
    this.paymentTransmissionStatusId = paymentTransmissionStatusId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentTransmissionStatusId paymentTransmissionStatusId = (PaymentTransmissionStatusId) o;
    return Objects.equals(this.paymentTransmissionStatusId, paymentTransmissionStatusId.paymentTransmissionStatusId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(paymentTransmissionStatusId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentTransmissionStatusId {\n");
    
    sb.append("    paymentTransmissionStatusId: ").append(toIndentedString(paymentTransmissionStatusId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

