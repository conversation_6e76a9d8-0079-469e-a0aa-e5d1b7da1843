/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * OrderTypeId
 */
public class OrderTypeId {
  public static final String SERIALIZED_NAME_ORDER_TYPE_ID = "OrderTypeId";
  @SerializedName(SERIALIZED_NAME_ORDER_TYPE_ID)
  private String orderTypeId;

  public OrderTypeId orderTypeId(String orderTypeId) {
    this.orderTypeId = orderTypeId;
    return this;
  }

   /**
   * Unique identifier of the order type
   * @return orderTypeId
  **/
  
  public String getOrderTypeId() {
    return orderTypeId;
  }

  public void setOrderTypeId(String orderTypeId) {
    this.orderTypeId = orderTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderTypeId orderTypeId = (OrderTypeId) o;
    return Objects.equals(this.orderTypeId, orderTypeId.orderTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(orderTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderTypeId {\n");
    
    sb.append("    orderTypeId: ").append(toIndentedString(orderTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

