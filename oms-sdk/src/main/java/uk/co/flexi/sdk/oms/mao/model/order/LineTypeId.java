/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * LineTypeId
 */
public class LineTypeId {
  public static final String SERIALIZED_NAME_LINE_TYPE_ID = "LineTypeId";
  @SerializedName(SERIALIZED_NAME_LINE_TYPE_ID)
  private String lineTypeId;

  public LineTypeId lineTypeId(String lineTypeId) {
    this.lineTypeId = lineTypeId;
    return this;
  }

   /**
   * Unique identifier of the Line Type
   * @return lineTypeId
  **/
  
  public String getLineTypeId() {
    return lineTypeId;
  }

  public void setLineTypeId(String lineTypeId) {
    this.lineTypeId = lineTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LineTypeId lineTypeId = (LineTypeId) o;
    return Objects.equals(this.lineTypeId, lineTypeId.lineTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(lineTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LineTypeId {\n");
    
    sb.append("    lineTypeId: ").append(toIndentedString(lineTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

