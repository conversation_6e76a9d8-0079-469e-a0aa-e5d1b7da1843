/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.*;

/**
 * OrderConfig
 */
public class OrderConfig {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CHARGE_TYPE_CONFIG = "ChargeTypeConfig";
  @SerializedName(SERIALIZED_NAME_CHARGE_TYPE_CONFIG)
  private List<ChargeTypeConfig> chargeTypeConfig = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DAYS_TO_ARCHIVE = "DaysToArchive";
  @SerializedName(SERIALIZED_NAME_DAYS_TO_ARCHIVE)
  private Long daysToArchive;

  public static final String SERIALIZED_NAME_DEMAND_TYPE_ID = "DemandTypeId";
  @SerializedName(SERIALIZED_NAME_DEMAND_TYPE_ID)
  private String demandTypeId;

  public static final String SERIALIZED_NAME_DESCRIPTION = "Description";
  @SerializedName(SERIALIZED_NAME_DESCRIPTION)
  private String description;

  public static final String SERIALIZED_NAME_EJ_TEMPLATE_ID = "EjTemplateId";
  @SerializedName(SERIALIZED_NAME_EJ_TEMPLATE_ID)
  private String ejTemplateId;

  public static final String SERIALIZED_NAME_EVENT_PUBLISHING_CONFIG = "EventPublishingConfig";
  @SerializedName(SERIALIZED_NAME_EVENT_PUBLISHING_CONFIG)
  private List<EventPublishingConfig> eventPublishingConfig = null;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_EXTENDED_DAYS_TO_ARCHIVE = "ExtendedDaysToArchive";
  @SerializedName(SERIALIZED_NAME_EXTENDED_DAYS_TO_ARCHIVE)
  private Long extendedDaysToArchive;

  public static final String SERIALIZED_NAME_IMMEDIATE_PUBLISH_SALES_POSTING = "ImmediatePublishSalesPosting";
  @SerializedName(SERIALIZED_NAME_IMMEDIATE_PUBLISH_SALES_POSTING)
  private Boolean immediatePublishSalesPosting;

  public static final String SERIALIZED_NAME_INVOICE_CONFIG = "InvoiceConfig";
  @SerializedName(SERIALIZED_NAME_INVOICE_CONFIG)
  private List<InvoiceConfig> invoiceConfig = null;

  public static final String SERIALIZED_NAME_IS_LINE_LEVEL_ALLOCATION = "IsLineLevelAllocation";
  @SerializedName(SERIALIZED_NAME_IS_LINE_LEVEL_ALLOCATION)
  private Boolean isLineLevelAllocation;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_MOD_TYPE_CONFIG = "ModTypeConfig";
  @SerializedName(SERIALIZED_NAME_MOD_TYPE_CONFIG)
  private List<ModTypeConfig> modTypeConfig = null;

  public static final String SERIALIZED_NAME_ORDER_CONFIG_ID = "OrderConfigId";
  @SerializedName(SERIALIZED_NAME_ORDER_CONFIG_ID)
  private String orderConfigId;

  public static final String SERIALIZED_NAME_ORDER_LINE_TEMPLATE_ID = "OrderLineTemplateId";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_TEMPLATE_ID)
  private String orderLineTemplateId;

  public static final String SERIALIZED_NAME_ORDER_TAX_CONFIG = "OrderTaxConfig";
  @SerializedName(SERIALIZED_NAME_ORDER_TAX_CONFIG)
  private OrderTaxConfig orderTaxConfig = null;

  public static final String SERIALIZED_NAME_ORDER_TEMPLATE_ID = "OrderTemplateId";
  @SerializedName(SERIALIZED_NAME_ORDER_TEMPLATE_ID)
  private String orderTemplateId;

  public static final String SERIALIZED_NAME_OVERAGE_ALLOWED = "OverageAllowed";
  @SerializedName(SERIALIZED_NAME_OVERAGE_ALLOWED)
  private Boolean overageAllowed;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROCESS_PAYMENT_IMMEDIATELY = "ProcessPaymentImmediately";
  @SerializedName(SERIALIZED_NAME_PROCESS_PAYMENT_IMMEDIATELY)
  private Boolean processPaymentImmediately;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_PUBLISH_ALL_INVOICE = "PublishAllInvoice";
  @SerializedName(SERIALIZED_NAME_PUBLISH_ALL_INVOICE)
  private Boolean publishAllInvoice;

  public static final String SERIALIZED_NAME_PUBLISH_E_J = "PublishEJ";
  @SerializedName(SERIALIZED_NAME_PUBLISH_E_J)
  private Boolean publishEJ;

  public static final String SERIALIZED_NAME_PUBLISHING_FORMAT = "PublishingFormat";
  @SerializedName(SERIALIZED_NAME_PUBLISHING_FORMAT)
  private PublishingFormat publishingFormat = null;

  public static final String SERIALIZED_NAME_RE_PRICE_EVEN_EXCHANGES = "RePriceEvenExchanges";
  @SerializedName(SERIALIZED_NAME_RE_PRICE_EVEN_EXCHANGES)
  private Boolean rePriceEvenExchanges;

  public static final String SERIALIZED_NAME_REALLOCATE_ON_SHORT = "ReallocateOnShort";
  @SerializedName(SERIALIZED_NAME_REALLOCATE_ON_SHORT)
  private Boolean reallocateOnShort;

  public static final String SERIALIZED_NAME_REQUIRE_PAYMENT = "RequirePayment";
  @SerializedName(SERIALIZED_NAME_REQUIRE_PAYMENT)
  private Boolean requirePayment;

  public static final String SERIALIZED_NAME_REQUIRE_PRICE = "RequirePrice";
  @SerializedName(SERIALIZED_NAME_REQUIRE_PRICE)
  private Boolean requirePrice;

  public static final String SERIALIZED_NAME_REQUIRE_SNH = "RequireSnh";
  @SerializedName(SERIALIZED_NAME_REQUIRE_SNH)
  private Boolean requireSnh;

  public static final String SERIALIZED_NAME_REQUIRE_TAX = "RequireTax";
  @SerializedName(SERIALIZED_NAME_REQUIRE_TAX)
  private Boolean requireTax;

  public static final String SERIALIZED_NAME_RETURN_CONFIG = "ReturnConfig";
  @SerializedName(SERIALIZED_NAME_RETURN_CONFIG)
  private ReturnConfig returnConfig = null;

  public static final String SERIALIZED_NAME_RETURN_FEE_CONFIG = "ReturnFeeConfig";
  @SerializedName(SERIALIZED_NAME_RETURN_FEE_CONFIG)
  private List<ReturnFeeConfig> returnFeeConfig = null;

  public static final String SERIALIZED_NAME_SALES_POSTING_TEMPLATE_ID = "SalesPostingTemplateId";
  @SerializedName(SERIALIZED_NAME_SALES_POSTING_TEMPLATE_ID)
  private String salesPostingTemplateId;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public OrderConfig actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderConfig putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderConfig chargeTypeConfig(List<ChargeTypeConfig> chargeTypeConfig) {
    this.chargeTypeConfig = chargeTypeConfig;
    return this;
  }

  public OrderConfig addChargeTypeConfigItem(ChargeTypeConfig chargeTypeConfigItem) {
    if (this.chargeTypeConfig == null) {
      this.chargeTypeConfig = new ArrayList<ChargeTypeConfig>();
    }
    this.chargeTypeConfig.add(chargeTypeConfigItem);
    return this;
  }

   /**
   * Get chargeTypeConfig
   * @return chargeTypeConfig
  **/
  
  public List<ChargeTypeConfig> getChargeTypeConfig() {
    return chargeTypeConfig;
  }

  public void setChargeTypeConfig(List<ChargeTypeConfig> chargeTypeConfig) {
    this.chargeTypeConfig = chargeTypeConfig;
  }

  public OrderConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderConfig createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderConfig daysToArchive(Long daysToArchive) {
    this.daysToArchive = daysToArchive;
    return this;
  }

   /**
   * This property value is used to calculate Order.archiveDate property.#{Order.archiveDate} get calculated once the Order is Fulfilled or Cancelled, and it is value is when Order got fulfilled or Cancelled + daysToArchive(this property).
   * minimum: 0
   * maximum: -8446744073709551617
   * @return daysToArchive
  **/
  
  public Long getDaysToArchive() {
    return daysToArchive;
  }

  public void setDaysToArchive(Long daysToArchive) {
    this.daysToArchive = daysToArchive;
  }

  public OrderConfig demandTypeId(String demandTypeId) {
    this.demandTypeId = demandTypeId;
    return this;
  }

   /**
   * Type of demand which is used to identify the supply types which are eligible for allocation. Promsing will use this information to allocate the order line only to supply types which map to this demand type
   * @return demandTypeId
  **/
  
  public String getDemandTypeId() {
    return demandTypeId;
  }

  public void setDemandTypeId(String demandTypeId) {
    this.demandTypeId = demandTypeId;
  }

  public OrderConfig description(String description) {
    this.description = description;
    return this;
  }

   /**
   * Description for Order Config
   * @return description
  **/
  
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public OrderConfig ejTemplateId(String ejTemplateId) {
    this.ejTemplateId = ejTemplateId;
    return this;
  }

   /**
   * Response template used for publishing EJ data.
   * @return ejTemplateId
  **/
  
  public String getEjTemplateId() {
    return ejTemplateId;
  }

  public void setEjTemplateId(String ejTemplateId) {
    this.ejTemplateId = ejTemplateId;
  }

  public OrderConfig eventPublishingConfig(List<EventPublishingConfig> eventPublishingConfig) {
    this.eventPublishingConfig = eventPublishingConfig;
    return this;
  }

  public OrderConfig addEventPublishingConfigItem(EventPublishingConfig eventPublishingConfigItem) {
    if (this.eventPublishingConfig == null) {
      this.eventPublishingConfig = new ArrayList<EventPublishingConfig>();
    }
    this.eventPublishingConfig.add(eventPublishingConfigItem);
    return this;
  }

   /**
   * Get eventPublishingConfig
   * @return eventPublishingConfig
  **/
  
  public List<EventPublishingConfig> getEventPublishingConfig() {
    return eventPublishingConfig;
  }

  public void setEventPublishingConfig(List<EventPublishingConfig> eventPublishingConfig) {
    this.eventPublishingConfig = eventPublishingConfig;
  }

  public OrderConfig extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderConfig extendedDaysToArchive(Long extendedDaysToArchive) {
    this.extendedDaysToArchive = extendedDaysToArchive;
    return this;
  }

   /**
   * This property value is used to extend the archival date if the order does not satisfy the archival condition at the time its picked by the scheduler for archiving.
   * minimum: 0
   * maximum: -8446744073709551617
   * @return extendedDaysToArchive
  **/
  
  public Long getExtendedDaysToArchive() {
    return extendedDaysToArchive;
  }

  public void setExtendedDaysToArchive(Long extendedDaysToArchive) {
    this.extendedDaysToArchive = extendedDaysToArchive;
  }

  public OrderConfig immediatePublishSalesPosting(Boolean immediatePublishSalesPosting) {
    this.immediatePublishSalesPosting = immediatePublishSalesPosting;
    return this;
  }

   /**
   * Flag to indicate whether to populate Sales Posting
   * @return immediatePublishSalesPosting
  **/
  
  public Boolean getImmediatePublishSalesPosting() {
    return immediatePublishSalesPosting;
  }

  public void setImmediatePublishSalesPosting(Boolean immediatePublishSalesPosting) {
    this.immediatePublishSalesPosting = immediatePublishSalesPosting;
  }

  public OrderConfig invoiceConfig(List<InvoiceConfig> invoiceConfig) {
    this.invoiceConfig = invoiceConfig;
    return this;
  }

  public OrderConfig addInvoiceConfigItem(InvoiceConfig invoiceConfigItem) {
    if (this.invoiceConfig == null) {
      this.invoiceConfig = new ArrayList<InvoiceConfig>();
    }
    this.invoiceConfig.add(invoiceConfigItem);
    return this;
  }

   /**
   * Get invoiceConfig
   * @return invoiceConfig
  **/
  
  public List<InvoiceConfig> getInvoiceConfig() {
    return invoiceConfig;
  }

  public void setInvoiceConfig(List<InvoiceConfig> invoiceConfig) {
    this.invoiceConfig = invoiceConfig;
  }

  public OrderConfig isLineLevelAllocation(Boolean isLineLevelAllocation) {
    this.isLineLevelAllocation = isLineLevelAllocation;
    return this;
  }

   /**
   * This to define whether orders belong to this config can do line level allocation
   * @return isLineLevelAllocation
  **/
  
  public Boolean getIsLineLevelAllocation() {
    return isLineLevelAllocation;
  }

  public void setIsLineLevelAllocation(Boolean isLineLevelAllocation) {
    this.isLineLevelAllocation = isLineLevelAllocation;
  }

  public OrderConfig localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderConfig messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderConfig modTypeConfig(List<ModTypeConfig> modTypeConfig) {
    this.modTypeConfig = modTypeConfig;
    return this;
  }

  public OrderConfig addModTypeConfigItem(ModTypeConfig modTypeConfigItem) {
    if (this.modTypeConfig == null) {
      this.modTypeConfig = new ArrayList<ModTypeConfig>();
    }
    this.modTypeConfig.add(modTypeConfigItem);
    return this;
  }

   /**
   * Get modTypeConfig
   * @return modTypeConfig
  **/
  
  public List<ModTypeConfig> getModTypeConfig() {
    return modTypeConfig;
  }

  public void setModTypeConfig(List<ModTypeConfig> modTypeConfig) {
    this.modTypeConfig = modTypeConfig;
  }

  public OrderConfig orderConfigId(String orderConfigId) {
    this.orderConfigId = orderConfigId;
    return this;
  }

   /**
   * Unique identifier for order config
   * @return orderConfigId
  **/
  
  public String getOrderConfigId() {
    return orderConfigId;
  }

  public void setOrderConfigId(String orderConfigId) {
    this.orderConfigId = orderConfigId;
  }

  public OrderConfig orderLineTemplateId(String orderLineTemplateId) {
    this.orderLineTemplateId = orderLineTemplateId;
    return this;
  }

   /**
   * Holds response Template Id, use this template to publish Data.
   * @return orderLineTemplateId
  **/
  
  public String getOrderLineTemplateId() {
    return orderLineTemplateId;
  }

  public void setOrderLineTemplateId(String orderLineTemplateId) {
    this.orderLineTemplateId = orderLineTemplateId;
  }

  public OrderConfig orderTaxConfig(OrderTaxConfig orderTaxConfig) {
    this.orderTaxConfig = orderTaxConfig;
    return this;
  }

   /**
   * Get orderTaxConfig
   * @return orderTaxConfig
  **/
  
  public OrderTaxConfig getOrderTaxConfig() {
    return orderTaxConfig;
  }

  public void setOrderTaxConfig(OrderTaxConfig orderTaxConfig) {
    this.orderTaxConfig = orderTaxConfig;
  }

  public OrderConfig orderTemplateId(String orderTemplateId) {
    this.orderTemplateId = orderTemplateId;
    return this;
  }

   /**
   * Holds response Template Id, use this template to publish Data.
   * @return orderTemplateId
  **/
  
  public String getOrderTemplateId() {
    return orderTemplateId;
  }

  public void setOrderTemplateId(String orderTemplateId) {
    this.orderTemplateId = orderTemplateId;
  }

  public OrderConfig overageAllowed(Boolean overageAllowed) {
    this.overageAllowed = overageAllowed;
    return this;
  }

   /**
   * when this flag is enabled, then over picking/packing/shipment is allowed for an order
   * @return overageAllowed
  **/
  
  public Boolean getOverageAllowed() {
    return overageAllowed;
  }

  public void setOverageAllowed(Boolean overageAllowed) {
    this.overageAllowed = overageAllowed;
  }

  public OrderConfig PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderConfig processPaymentImmediately(Boolean processPaymentImmediately) {
    this.processPaymentImmediately = processPaymentImmediately;
    return this;
  }

   /**
   * Indicates if payment is executed immediately upon invoice creation. If true, then a payment request with executionMode CalculateAndExecute is sent to the payment component when a complete shipment, complete return, or adjustment invoice is created. If false, then a payment request with executionMode SaveOnly is sent to the payment component, and payment is initiated via the next scheduled run. If orders are shipped or returned partially, then this attribute is not read and payments are executed based on the scheduler.
   * @return processPaymentImmediately
  **/
  
  public Boolean getProcessPaymentImmediately() {
    return processPaymentImmediately;
  }

  public void setProcessPaymentImmediately(Boolean processPaymentImmediately) {
    this.processPaymentImmediately = processPaymentImmediately;
  }

  public OrderConfig profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public OrderConfig publishAllInvoice(Boolean publishAllInvoice) {
    this.publishAllInvoice = publishAllInvoice;
    return this;
  }

   /**
   * Flag to indicate if all invoices required in sales posting
   * @return publishAllInvoice
  **/
  
  public Boolean getPublishAllInvoice() {
    return publishAllInvoice;
  }

  public void setPublishAllInvoice(Boolean publishAllInvoice) {
    this.publishAllInvoice = publishAllInvoice;
  }

  public OrderConfig publishEJ(Boolean publishEJ) {
    this.publishEJ = publishEJ;
    return this;
  }

   /**
   * This flag will Publish mod type data to a separate Electronic Journals queue for electronic journal functionality.
   * @return publishEJ
  **/
  
  public Boolean getPublishEJ() {
    return publishEJ;
  }

  public void setPublishEJ(Boolean publishEJ) {
    this.publishEJ = publishEJ;
  }

  public OrderConfig publishingFormat(PublishingFormat publishingFormat) {
    this.publishingFormat = publishingFormat;
    return this;
  }

   /**
   * Get publishingFormat
   * @return publishingFormat
  **/
  
  public PublishingFormat getPublishingFormat() {
    return publishingFormat;
  }

  public void setPublishingFormat(PublishingFormat publishingFormat) {
    this.publishingFormat = publishingFormat;
  }

  public OrderConfig rePriceEvenExchanges(Boolean rePriceEvenExchanges) {
    this.rePriceEvenExchanges = rePriceEvenExchanges;
    return this;
  }

   /**
   * Indicates if even exchanges are priced based on the original order or the current retail price and charge rates. If false, then when an even exchange line is created, the price, taxes, charges, and discounts are copied from the parent order line and prorated for the exchange qunatity. If true, then when an even exchange line is created, the line is sent through the pricing, promotion, SnH, and tax services like the typical sale flow. If even exchange lines are priced by an external system and created with isAlreadyPriced, isAlreadyTaxed, and isAlreadyCharged set to true, then this configuration is not read.
   * @return rePriceEvenExchanges
  **/
  
  public Boolean getRePriceEvenExchanges() {
    return rePriceEvenExchanges;
  }

  public void setRePriceEvenExchanges(Boolean rePriceEvenExchanges) {
    this.rePriceEvenExchanges = rePriceEvenExchanges;
  }

  public OrderConfig reallocateOnShort(Boolean reallocateOnShort) {
    this.reallocateOnShort = reallocateOnShort;
    return this;
  }

   /**
   * When an order quantity is shorted due to an order event update, this field determines whether that quantity should be re-submitted for allocation.  If this value is set to true, the shorted quantity is submitted for re-allocation.  If set to false, the quantity is cancelled. Provide with this configuration if short event is received with short reason id with reason that is configured on the system decision to cancel or re-allocate will made on basis of short reason id.
   * @return reallocateOnShort
  **/
  
  public Boolean getReallocateOnShort() {
    return reallocateOnShort;
  }

  public void setReallocateOnShort(Boolean reallocateOnShort) {
    this.reallocateOnShort = reallocateOnShort;
  }

  public OrderConfig requirePayment(Boolean requirePayment) {
    this.requirePayment = requirePayment;
    return this;
  }

   /**
   * Flag to indicate to use payment component
   * @return requirePayment
  **/
  
  public Boolean getRequirePayment() {
    return requirePayment;
  }

  public void setRequirePayment(Boolean requirePayment) {
    this.requirePayment = requirePayment;
  }

  public OrderConfig requirePrice(Boolean requirePrice) {
    this.requirePrice = requirePrice;
    return this;
  }

   /**
   * Flag to indicate to use pricing and promotion component
   * @return requirePrice
  **/
  
  public Boolean getRequirePrice() {
    return requirePrice;
  }

  public void setRequirePrice(Boolean requirePrice) {
    this.requirePrice = requirePrice;
  }

  public OrderConfig requireSnh(Boolean requireSnh) {
    this.requireSnh = requireSnh;
    return this;
  }

   /**
   * Flag to indicate to use shipping and handling component
   * @return requireSnh
  **/
  
  public Boolean getRequireSnh() {
    return requireSnh;
  }

  public void setRequireSnh(Boolean requireSnh) {
    this.requireSnh = requireSnh;
  }

  public OrderConfig requireTax(Boolean requireTax) {
    this.requireTax = requireTax;
    return this;
  }

   /**
   * Flag to indicate to use tax component
   * @return requireTax
  **/
  
  public Boolean getRequireTax() {
    return requireTax;
  }

  public void setRequireTax(Boolean requireTax) {
    this.requireTax = requireTax;
  }

  public OrderConfig returnConfig(ReturnConfig returnConfig) {
    this.returnConfig = returnConfig;
    return this;
  }

   /**
   * Get returnConfig
   * @return returnConfig
  **/
  
  public ReturnConfig getReturnConfig() {
    return returnConfig;
  }

  public void setReturnConfig(ReturnConfig returnConfig) {
    this.returnConfig = returnConfig;
  }

  public OrderConfig returnFeeConfig(List<ReturnFeeConfig> returnFeeConfig) {
    this.returnFeeConfig = returnFeeConfig;
    return this;
  }

  public OrderConfig addReturnFeeConfigItem(ReturnFeeConfig returnFeeConfigItem) {
    if (this.returnFeeConfig == null) {
      this.returnFeeConfig = new ArrayList<ReturnFeeConfig>();
    }
    this.returnFeeConfig.add(returnFeeConfigItem);
    return this;
  }

   /**
   * Get returnFeeConfig
   * @return returnFeeConfig
  **/
  
  public List<ReturnFeeConfig> getReturnFeeConfig() {
    return returnFeeConfig;
  }

  public void setReturnFeeConfig(List<ReturnFeeConfig> returnFeeConfig) {
    this.returnFeeConfig = returnFeeConfig;
  }

  public OrderConfig salesPostingTemplateId(String salesPostingTemplateId) {
    this.salesPostingTemplateId = salesPostingTemplateId;
    return this;
  }

   /**
   * Template used for publishing sales posting messages. When a sales posting message is published, this template is used to generate the outbound json. The configurations to include only net new invoices (PublishAllInvoices) and to include or exclude prorated charges, taxes, and discounts (PublishingFormat) are applied on top of the selected template.
   * @return salesPostingTemplateId
  **/
  
  public String getSalesPostingTemplateId() {
    return salesPostingTemplateId;
  }

  public void setSalesPostingTemplateId(String salesPostingTemplateId) {
    this.salesPostingTemplateId = salesPostingTemplateId;
  }

  public OrderConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderConfig updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderConfig rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderConfig orderConfig = (OrderConfig) o;
    return Objects.equals(this.actions, orderConfig.actions) &&
        Objects.equals(this.chargeTypeConfig, orderConfig.chargeTypeConfig) &&
        Objects.equals(this.createdBy, orderConfig.createdBy) &&
        Objects.equals(this.createdTimestamp, orderConfig.createdTimestamp) &&
        Objects.equals(this.daysToArchive, orderConfig.daysToArchive) &&
        Objects.equals(this.demandTypeId, orderConfig.demandTypeId) &&
        Objects.equals(this.description, orderConfig.description) &&
        Objects.equals(this.ejTemplateId, orderConfig.ejTemplateId) &&
        Objects.equals(this.eventPublishingConfig, orderConfig.eventPublishingConfig) &&
        Objects.equals(this.extended, orderConfig.extended) &&
        Objects.equals(this.extendedDaysToArchive, orderConfig.extendedDaysToArchive) &&
        Objects.equals(this.immediatePublishSalesPosting, orderConfig.immediatePublishSalesPosting) &&
        Objects.equals(this.invoiceConfig, orderConfig.invoiceConfig) &&
        Objects.equals(this.isLineLevelAllocation, orderConfig.isLineLevelAllocation) &&
        Objects.equals(this.localizedTo, orderConfig.localizedTo) &&
        Objects.equals(this.messages, orderConfig.messages) &&
        Objects.equals(this.modTypeConfig, orderConfig.modTypeConfig) &&
        Objects.equals(this.orderConfigId, orderConfig.orderConfigId) &&
        Objects.equals(this.orderLineTemplateId, orderConfig.orderLineTemplateId) &&
        Objects.equals(this.orderTaxConfig, orderConfig.orderTaxConfig) &&
        Objects.equals(this.orderTemplateId, orderConfig.orderTemplateId) &&
        Objects.equals(this.overageAllowed, orderConfig.overageAllowed) &&
        Objects.equals(this.PK, orderConfig.PK) &&
        Objects.equals(this.processPaymentImmediately, orderConfig.processPaymentImmediately) &&
        Objects.equals(this.profileId, orderConfig.profileId) &&
        Objects.equals(this.publishAllInvoice, orderConfig.publishAllInvoice) &&
        Objects.equals(this.publishEJ, orderConfig.publishEJ) &&
        Objects.equals(this.publishingFormat, orderConfig.publishingFormat) &&
        Objects.equals(this.rePriceEvenExchanges, orderConfig.rePriceEvenExchanges) &&
        Objects.equals(this.reallocateOnShort, orderConfig.reallocateOnShort) &&
        Objects.equals(this.requirePayment, orderConfig.requirePayment) &&
        Objects.equals(this.requirePrice, orderConfig.requirePrice) &&
        Objects.equals(this.requireSnh, orderConfig.requireSnh) &&
        Objects.equals(this.requireTax, orderConfig.requireTax) &&
        Objects.equals(this.returnConfig, orderConfig.returnConfig) &&
        Objects.equals(this.returnFeeConfig, orderConfig.returnFeeConfig) &&
        Objects.equals(this.salesPostingTemplateId, orderConfig.salesPostingTemplateId) &&
        Objects.equals(this.updatedBy, orderConfig.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderConfig.updatedTimestamp) &&
        Objects.equals(this.rootCause, orderConfig.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, chargeTypeConfig, createdBy, createdTimestamp, daysToArchive, demandTypeId, description, ejTemplateId, eventPublishingConfig, extended, extendedDaysToArchive, immediatePublishSalesPosting, invoiceConfig, isLineLevelAllocation, localizedTo, messages, modTypeConfig, orderConfigId, orderLineTemplateId, orderTaxConfig, orderTemplateId, overageAllowed, PK, processPaymentImmediately, profileId, publishAllInvoice, publishEJ, publishingFormat, rePriceEvenExchanges, reallocateOnShort, requirePayment, requirePrice, requireSnh, requireTax, returnConfig, returnFeeConfig, salesPostingTemplateId, updatedBy, updatedTimestamp, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderConfig {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    chargeTypeConfig: ").append(toIndentedString(chargeTypeConfig)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    daysToArchive: ").append(toIndentedString(daysToArchive)).append("\n");
    sb.append("    demandTypeId: ").append(toIndentedString(demandTypeId)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    ejTemplateId: ").append(toIndentedString(ejTemplateId)).append("\n");
    sb.append("    eventPublishingConfig: ").append(toIndentedString(eventPublishingConfig)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    extendedDaysToArchive: ").append(toIndentedString(extendedDaysToArchive)).append("\n");
    sb.append("    immediatePublishSalesPosting: ").append(toIndentedString(immediatePublishSalesPosting)).append("\n");
    sb.append("    invoiceConfig: ").append(toIndentedString(invoiceConfig)).append("\n");
    sb.append("    isLineLevelAllocation: ").append(toIndentedString(isLineLevelAllocation)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    modTypeConfig: ").append(toIndentedString(modTypeConfig)).append("\n");
    sb.append("    orderConfigId: ").append(toIndentedString(orderConfigId)).append("\n");
    sb.append("    orderLineTemplateId: ").append(toIndentedString(orderLineTemplateId)).append("\n");
    sb.append("    orderTaxConfig: ").append(toIndentedString(orderTaxConfig)).append("\n");
    sb.append("    orderTemplateId: ").append(toIndentedString(orderTemplateId)).append("\n");
    sb.append("    overageAllowed: ").append(toIndentedString(overageAllowed)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    processPaymentImmediately: ").append(toIndentedString(processPaymentImmediately)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    publishAllInvoice: ").append(toIndentedString(publishAllInvoice)).append("\n");
    sb.append("    publishEJ: ").append(toIndentedString(publishEJ)).append("\n");
    sb.append("    publishingFormat: ").append(toIndentedString(publishingFormat)).append("\n");
    sb.append("    rePriceEvenExchanges: ").append(toIndentedString(rePriceEvenExchanges)).append("\n");
    sb.append("    reallocateOnShort: ").append(toIndentedString(reallocateOnShort)).append("\n");
    sb.append("    requirePayment: ").append(toIndentedString(requirePayment)).append("\n");
    sb.append("    requirePrice: ").append(toIndentedString(requirePrice)).append("\n");
    sb.append("    requireSnh: ").append(toIndentedString(requireSnh)).append("\n");
    sb.append("    requireTax: ").append(toIndentedString(requireTax)).append("\n");
    sb.append("    returnConfig: ").append(toIndentedString(returnConfig)).append("\n");
    sb.append("    returnFeeConfig: ").append(toIndentedString(returnFeeConfig)).append("\n");
    sb.append("    salesPostingTemplateId: ").append(toIndentedString(salesPostingTemplateId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

