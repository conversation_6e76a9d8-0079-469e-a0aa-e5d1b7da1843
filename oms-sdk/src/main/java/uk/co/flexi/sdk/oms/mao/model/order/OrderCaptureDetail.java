/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderCaptureDetail
 */
public class OrderCaptureDetail {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CAPTURE_DATE = "CaptureDate";
  @SerializedName(SERIALIZED_NAME_CAPTURE_DATE)
  private OffsetDateTime captureDate;

  public static final String SERIALIZED_NAME_CAPTURE_DETAIL_ID = "CaptureDetailId";
  @SerializedName(SERIALIZED_NAME_CAPTURE_DETAIL_ID)
  private String captureDetailId;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DEVICE_ID = "DeviceId";
  @SerializedName(SERIALIZED_NAME_DEVICE_ID)
  private String deviceId;

  public static final String SERIALIZED_NAME_EMPLOYEE_ID = "EmployeeId";
  @SerializedName(SERIALIZED_NAME_EMPLOYEE_ID)
  private String employeeId;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IP_ADDRESS = "IpAddress";
  @SerializedName(SERIALIZED_NAME_IP_ADDRESS)
  private String ipAddress;

  public static final String SERIALIZED_NAME_JOB_CLASS = "JobClass";
  @SerializedName(SERIALIZED_NAME_JOB_CLASS)
  private String jobClass;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_OPERATOR_ID = "OperatorId";
  @SerializedName(SERIALIZED_NAME_OPERATOR_ID)
  private String operatorId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_ORDER = "ParentOrder";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER)
  private PrimaryKey parentOrder = null;

  public static final String SERIALIZED_NAME_REGISTER_ID = "RegisterId";
  @SerializedName(SERIALIZED_NAME_REGISTER_ID)
  private String registerId;

  public static final String SERIALIZED_NAME_STORE_DISPLAY_ID = "StoreDisplayId";
  @SerializedName(SERIALIZED_NAME_STORE_DISPLAY_ID)
  private String storeDisplayId;

  public static final String SERIALIZED_NAME_TILL_ID = "TillId";
  @SerializedName(SERIALIZED_NAME_TILL_ID)
  private String tillId;

  public static final String SERIALIZED_NAME_TRANSACTION_TYPE = "TransactionType";
  @SerializedName(SERIALIZED_NAME_TRANSACTION_TYPE)
  private TransactionTypeId transactionType = null;

  public static final String SERIALIZED_NAME_TRANSLATIONS = "Translations";
  @SerializedName(SERIALIZED_NAME_TRANSLATIONS)
  private Map<String, Map<String, String>> translations = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public OrderCaptureDetail actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderCaptureDetail putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderCaptureDetail captureDate(OffsetDateTime captureDate) {
    this.captureDate = captureDate;
    return this;
  }

   /**
   * Timestamp when the record is created
   * @return captureDate
  **/
  
  public OffsetDateTime getCaptureDate() {
    return captureDate;
  }

  public void setCaptureDate(OffsetDateTime captureDate) {
    this.captureDate = captureDate;
  }

  public OrderCaptureDetail captureDetailId(String captureDetailId) {
    this.captureDetailId = captureDetailId;
    return this;
  }

   /**
   * Unique identifier in the Order Capture
   * @return captureDetailId
  **/
  
  public String getCaptureDetailId() {
    return captureDetailId;
  }

  public void setCaptureDetailId(String captureDetailId) {
    this.captureDetailId = captureDetailId;
  }

  public OrderCaptureDetail createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderCaptureDetail createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderCaptureDetail deviceId(String deviceId) {
    this.deviceId = deviceId;
    return this;
  }

   /**
   * Unique ID of device
   * @return deviceId
  **/
  
  public String getDeviceId() {
    return deviceId;
  }

  public void setDeviceId(String deviceId) {
    this.deviceId = deviceId;
  }

  public OrderCaptureDetail employeeId(String employeeId) {
    this.employeeId = employeeId;
    return this;
  }

   /**
   * Retailer&#39;s employee for whom the order is being created.  Currently used by POS application.
   * @return employeeId
  **/
  
  public String getEmployeeId() {
    return employeeId;
  }

  public void setEmployeeId(String employeeId) {
    this.employeeId = employeeId;
  }

  public OrderCaptureDetail extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderCaptureDetail ipAddress(String ipAddress) {
    this.ipAddress = ipAddress;
    return this;
  }

   /**
   * IP address of the device
   * @return ipAddress
  **/
  
  public String getIpAddress() {
    return ipAddress;
  }

  public void setIpAddress(String ipAddress) {
    this.ipAddress = ipAddress;
  }

  public OrderCaptureDetail jobClass(String jobClass) {
    this.jobClass = jobClass;
    return this;
  }

   /**
   * Indicates the job class associated to the emplopyee for whom order is created.
   * @return jobClass
  **/
  
  public String getJobClass() {
    return jobClass;
  }

  public void setJobClass(String jobClass) {
    this.jobClass = jobClass;
  }

  public OrderCaptureDetail messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderCaptureDetail operatorId(String operatorId) {
    this.operatorId = operatorId;
    return this;
  }

   /**
   * Store associate or the clerk creating the order
   * @return operatorId
  **/
  
  public String getOperatorId() {
    return operatorId;
  }

  public void setOperatorId(String operatorId) {
    this.operatorId = operatorId;
  }

  public OrderCaptureDetail orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderCaptureDetail PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderCaptureDetail parentOrder(PrimaryKey parentOrder) {
    this.parentOrder = parentOrder;
    return this;
  }

   /**
   * Get parentOrder
   * @return parentOrder
  **/
  
  public PrimaryKey getParentOrder() {
    return parentOrder;
  }

  public void setParentOrder(PrimaryKey parentOrder) {
    this.parentOrder = parentOrder;
  }

  public OrderCaptureDetail registerId(String registerId) {
    this.registerId = registerId;
    return this;
  }

   /**
   * If in-store, register where transaction was completed
   * @return registerId
  **/
  
  public String getRegisterId() {
    return registerId;
  }

  public void setRegisterId(String registerId) {
    this.registerId = registerId;
  }

  public OrderCaptureDetail storeDisplayId(String storeDisplayId) {
    this.storeDisplayId = storeDisplayId;
    return this;
  }

   /**
   * This attribute indicates the display Id for the store.
   * @return storeDisplayId
  **/
  
  public String getStoreDisplayId() {
    return storeDisplayId;
  }

  public void setStoreDisplayId(String storeDisplayId) {
    this.storeDisplayId = storeDisplayId;
  }

  public OrderCaptureDetail tillId(String tillId) {
    this.tillId = tillId;
    return this;
  }

   /**
   * If in-store, till (cash drawer) where transaction was completed
   * @return tillId
  **/
  
  public String getTillId() {
    return tillId;
  }

  public void setTillId(String tillId) {
    this.tillId = tillId;
  }

  public OrderCaptureDetail transactionType(TransactionTypeId transactionType) {
    this.transactionType = transactionType;
    return this;
  }

   /**
   * Get transactionType
   * @return transactionType
  **/
  
  public TransactionTypeId getTransactionType() {
    return transactionType;
  }

  public void setTransactionType(TransactionTypeId transactionType) {
    this.transactionType = transactionType;
  }

  public OrderCaptureDetail translations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
    return this;
  }

  public OrderCaptureDetail putTranslationsItem(String key, Map<String, String> translationsItem) {
    if (this.translations == null) {
      this.translations = new HashMap<String, Map<String, String>>();
    }
    this.translations.put(key, translationsItem);
    return this;
  }

   /**
   * Get translations
   * @return translations
  **/
  
  public Map<String, Map<String, String>> getTranslations() {
    return translations;
  }

  public void setTranslations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
  }

  public OrderCaptureDetail updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderCaptureDetail updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderCaptureDetail entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderCaptureDetail orderCaptureDetail = (OrderCaptureDetail) o;
    return Objects.equals(this.actions, orderCaptureDetail.actions) &&
        Objects.equals(this.captureDate, orderCaptureDetail.captureDate) &&
        Objects.equals(this.captureDetailId, orderCaptureDetail.captureDetailId) &&
        Objects.equals(this.createdBy, orderCaptureDetail.createdBy) &&
        Objects.equals(this.createdTimestamp, orderCaptureDetail.createdTimestamp) &&
        Objects.equals(this.deviceId, orderCaptureDetail.deviceId) &&
        Objects.equals(this.employeeId, orderCaptureDetail.employeeId) &&
        Objects.equals(this.extended, orderCaptureDetail.extended) &&
        Objects.equals(this.ipAddress, orderCaptureDetail.ipAddress) &&
        Objects.equals(this.jobClass, orderCaptureDetail.jobClass) &&
        Objects.equals(this.messages, orderCaptureDetail.messages) &&
        Objects.equals(this.operatorId, orderCaptureDetail.operatorId) &&
        Objects.equals(this.orgId, orderCaptureDetail.orgId) &&
        Objects.equals(this.PK, orderCaptureDetail.PK) &&
        Objects.equals(this.parentOrder, orderCaptureDetail.parentOrder) &&
        Objects.equals(this.registerId, orderCaptureDetail.registerId) &&
        Objects.equals(this.storeDisplayId, orderCaptureDetail.storeDisplayId) &&
        Objects.equals(this.tillId, orderCaptureDetail.tillId) &&
        Objects.equals(this.transactionType, orderCaptureDetail.transactionType) &&
        Objects.equals(this.translations, orderCaptureDetail.translations) &&
        Objects.equals(this.updatedBy, orderCaptureDetail.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderCaptureDetail.updatedTimestamp) &&
        Objects.equals(this.entityName, orderCaptureDetail.entityName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, captureDate, captureDetailId, createdBy, createdTimestamp, deviceId, employeeId, extended, ipAddress, jobClass, messages, operatorId, orgId, PK, parentOrder, registerId, storeDisplayId, tillId, transactionType, translations, updatedBy, updatedTimestamp, entityName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderCaptureDetail {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    captureDate: ").append(toIndentedString(captureDate)).append("\n");
    sb.append("    captureDetailId: ").append(toIndentedString(captureDetailId)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    deviceId: ").append(toIndentedString(deviceId)).append("\n");
    sb.append("    employeeId: ").append(toIndentedString(employeeId)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    ipAddress: ").append(toIndentedString(ipAddress)).append("\n");
    sb.append("    jobClass: ").append(toIndentedString(jobClass)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    operatorId: ").append(toIndentedString(operatorId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentOrder: ").append(toIndentedString(parentOrder)).append("\n");
    sb.append("    registerId: ").append(toIndentedString(registerId)).append("\n");
    sb.append("    storeDisplayId: ").append(toIndentedString(storeDisplayId)).append("\n");
    sb.append("    tillId: ").append(toIndentedString(tillId)).append("\n");
    sb.append("    transactionType: ").append(toIndentedString(transactionType)).append("\n");
    sb.append("    translations: ").append(toIndentedString(translations)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

