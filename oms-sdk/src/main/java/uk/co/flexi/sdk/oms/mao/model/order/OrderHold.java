/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderHold
 */
public class OrderHold {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_APPLY_REASON_COMMENTS = "ApplyReasonComments";
  @SerializedName(SERIALIZED_NAME_APPLY_REASON_COMMENTS)
  private String applyReasonComments;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_EXTERNAL_CREATED_BY = "ExternalCreatedBy";
  @SerializedName(SERIALIZED_NAME_EXTERNAL_CREATED_BY)
  private String externalCreatedBy;

  public static final String SERIALIZED_NAME_EXTERNAL_CREATED_DATE = "ExternalCreatedDate";
  @SerializedName(SERIALIZED_NAME_EXTERNAL_CREATED_DATE)
  private OffsetDateTime externalCreatedDate;

  public static final String SERIALIZED_NAME_HOLD_TYPE_ID = "HoldTypeId";
  @SerializedName(SERIALIZED_NAME_HOLD_TYPE_ID)
  private String holdTypeId;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_RESOLVE_REASON_COMMENTS = "ResolveReasonComments";
  @SerializedName(SERIALIZED_NAME_RESOLVE_REASON_COMMENTS)
  private String resolveReasonComments;

  public static final String SERIALIZED_NAME_RESOLVE_REASON_ID = "ResolveReasonId";
  @SerializedName(SERIALIZED_NAME_RESOLVE_REASON_ID)
  private String resolveReasonId;

  public static final String SERIALIZED_NAME_STATUS_ID = "StatusId";
  @SerializedName(SERIALIZED_NAME_STATUS_ID)
  private String statusId;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderHold actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderHold putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderHold applyReasonComments(String applyReasonComments) {
    this.applyReasonComments = applyReasonComments;
    return this;
  }

   /**
   * Placeholder to capture comments when hold applied
   * @return applyReasonComments
  **/
  
  public String getApplyReasonComments() {
    return applyReasonComments;
  }

  public void setApplyReasonComments(String applyReasonComments) {
    this.applyReasonComments = applyReasonComments;
  }

  public OrderHold createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderHold createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderHold extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderHold externalCreatedBy(String externalCreatedBy) {
    this.externalCreatedBy = externalCreatedBy;
    return this;
  }

   /**
   * User who applied the hold, if updated in an external system
   * @return externalCreatedBy
  **/
  
  public String getExternalCreatedBy() {
    return externalCreatedBy;
  }

  public void setExternalCreatedBy(String externalCreatedBy) {
    this.externalCreatedBy = externalCreatedBy;
  }

  public OrderHold externalCreatedDate(OffsetDateTime externalCreatedDate) {
    this.externalCreatedDate = externalCreatedDate;
    return this;
  }

   /**
   * Timestamp when the hold is applied in external system
   * @return externalCreatedDate
  **/
  
  public OffsetDateTime getExternalCreatedDate() {
    return externalCreatedDate;
  }

  public void setExternalCreatedDate(OffsetDateTime externalCreatedDate) {
    this.externalCreatedDate = externalCreatedDate;
  }

  public OrderHold holdTypeId(String holdTypeId) {
    this.holdTypeId = holdTypeId;
    return this;
  }

   /**
   * Unique identifier of order  hold type table
   * @return holdTypeId
  **/
  
  public String getHoldTypeId() {
    return holdTypeId;
  }

  public void setHoldTypeId(String holdTypeId) {
    this.holdTypeId = holdTypeId;
  }

  public OrderHold localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderHold messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderHold orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderHold PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderHold resolveReasonComments(String resolveReasonComments) {
    this.resolveReasonComments = resolveReasonComments;
    return this;
  }

   /**
   * Placeholder to capture comments when hold is resolved
   * @return resolveReasonComments
  **/
  
  public String getResolveReasonComments() {
    return resolveReasonComments;
  }

  public void setResolveReasonComments(String resolveReasonComments) {
    this.resolveReasonComments = resolveReasonComments;
  }

  public OrderHold resolveReasonId(String resolveReasonId) {
    this.resolveReasonId = resolveReasonId;
    return this;
  }

   /**
   * Indicates the hold reason code
   * @return resolveReasonId
  **/
  
  public String getResolveReasonId() {
    return resolveReasonId;
  }

  public void setResolveReasonId(String resolveReasonId) {
    this.resolveReasonId = resolveReasonId;
  }

  public OrderHold statusId(String statusId) {
    this.statusId = statusId;
    return this;
  }

   /**
   * This field indicates the Hold status. Possible values: APPLY, RESOLVE, REJECT
   * @return statusId
  **/
  
  public String getStatusId() {
    return statusId;
  }

  public void setStatusId(String statusId) {
    this.statusId = statusId;
  }

  public OrderHold updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderHold updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderHold entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderHold localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderHold orderHold = (OrderHold) o;
    return Objects.equals(this.actions, orderHold.actions) &&
        Objects.equals(this.applyReasonComments, orderHold.applyReasonComments) &&
        Objects.equals(this.createdBy, orderHold.createdBy) &&
        Objects.equals(this.createdTimestamp, orderHold.createdTimestamp) &&
        Objects.equals(this.extended, orderHold.extended) &&
        Objects.equals(this.externalCreatedBy, orderHold.externalCreatedBy) &&
        Objects.equals(this.externalCreatedDate, orderHold.externalCreatedDate) &&
        Objects.equals(this.holdTypeId, orderHold.holdTypeId) &&
        Objects.equals(this.localizedTo, orderHold.localizedTo) &&
        Objects.equals(this.messages, orderHold.messages) &&
        Objects.equals(this.orgId, orderHold.orgId) &&
        Objects.equals(this.PK, orderHold.PK) &&
        Objects.equals(this.resolveReasonComments, orderHold.resolveReasonComments) &&
        Objects.equals(this.resolveReasonId, orderHold.resolveReasonId) &&
        Objects.equals(this.statusId, orderHold.statusId) &&
        Objects.equals(this.updatedBy, orderHold.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderHold.updatedTimestamp) &&
        Objects.equals(this.entityName, orderHold.entityName) &&
        Objects.equals(this.localize, orderHold.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, applyReasonComments, createdBy, createdTimestamp, extended, externalCreatedBy, externalCreatedDate, holdTypeId, localizedTo, messages, orgId, PK, resolveReasonComments, resolveReasonId, statusId, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderHold {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    applyReasonComments: ").append(toIndentedString(applyReasonComments)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    externalCreatedBy: ").append(toIndentedString(externalCreatedBy)).append("\n");
    sb.append("    externalCreatedDate: ").append(toIndentedString(externalCreatedDate)).append("\n");
    sb.append("    holdTypeId: ").append(toIndentedString(holdTypeId)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    resolveReasonComments: ").append(toIndentedString(resolveReasonComments)).append("\n");
    sb.append("    resolveReasonId: ").append(toIndentedString(resolveReasonId)).append("\n");
    sb.append("    statusId: ").append(toIndentedString(statusId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

