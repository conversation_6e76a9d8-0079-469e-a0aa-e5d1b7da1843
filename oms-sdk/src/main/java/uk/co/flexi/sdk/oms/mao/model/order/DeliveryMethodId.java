/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * DeliveryMethodId
 */
public class DeliveryMethodId {
  public static final String SERIALIZED_NAME_DELIVERY_METHOD_ID = "DeliveryMethodId";
  @SerializedName(SERIALIZED_NAME_DELIVERY_METHOD_ID)
  private String deliveryMethodId;

  public DeliveryMethodId deliveryMethodId(String deliveryMethodId) {
    this.deliveryMethodId = deliveryMethodId;
    return this;
  }

   /**
   * Unique identifier of the delivery method
   * @return deliveryMethodId
  **/
  
  public String getDeliveryMethodId() {
    return deliveryMethodId;
  }

  public void setDeliveryMethodId(String deliveryMethodId) {
    this.deliveryMethodId = deliveryMethodId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DeliveryMethodId deliveryMethodId = (DeliveryMethodId) o;
    return Objects.equals(this.deliveryMethodId, deliveryMethodId.deliveryMethodId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(deliveryMethodId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DeliveryMethodId {\n");
    
    sb.append("    deliveryMethodId: ").append(toIndentedString(deliveryMethodId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

