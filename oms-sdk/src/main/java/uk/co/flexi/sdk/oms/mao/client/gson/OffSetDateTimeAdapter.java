package uk.co.flexi.sdk.oms.mao.client.gson;

import com.google.gson.*;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.threeten.bp.DateTimeUtils;
import org.threeten.bp.OffsetDateTime;
import org.threeten.bp.ZoneOffset;
import org.threeten.bp.format.DateTimeFormatter;

import java.lang.reflect.Type;
import java.text.ParseException;
import java.util.Date;

public class OffSetDateTimeAdapter implements JsonSerializer<OffsetDateTime>, JsonDeserializer<OffsetDateTime> {

    private static final Logger log = LoggerFactory.getLogger(OffSetDateTimeAdapter.class);
    private final String pattern;

    public OffSetDateTimeAdapter(String pattern) {
        this.pattern = pattern;
    }

    public JsonElement serialize(OffsetDateTime date, Type typeOfSrc, JsonSerializationContext context) {
        return new JsonPrimitive(date.format(DateTimeFormatter.ofPattern(pattern))); // "yyyy-mm-dd"
    }

    @Override
    public OffsetDateTime deserialize(JsonElement json, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
        try {
            Date date = DateUtils.parseDate(json.getAsString().replaceAll("\"", ""), pattern, "yyyy'-'MM'-'dd'T'HH':'mm':'ss'.'SSS", "yyyy'-'MM'-'dd'T'HH':'mm':'ss'Z'");
            return DateTimeUtils.toInstant(date).atOffset(ZoneOffset.UTC);
        } catch (ParseException e) {
            log.error("Unable to parse date format received", e);
        }
        return null;
    }
}
