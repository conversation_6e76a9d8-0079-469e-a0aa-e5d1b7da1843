/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * PaymentTransactionStatusId
 */
public class PaymentTransactionStatusId {
  public static final String SERIALIZED_NAME_PAYMENT_TRANSACTION_STATUS_ID = "PaymentTransactionStatusId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TRANSACTION_STATUS_ID)
  private String paymentTransactionStatusId;

  public PaymentTransactionStatusId paymentTransactionStatusId(String paymentTransactionStatusId) {
    this.paymentTransactionStatusId = paymentTransactionStatusId;
    return this;
  }

   /**
   * Unique identifier of the transaction status
   * @return paymentTransactionStatusId
  **/
  
  public String getPaymentTransactionStatusId() {
    return paymentTransactionStatusId;
  }

  public void setPaymentTransactionStatusId(String paymentTransactionStatusId) {
    this.paymentTransactionStatusId = paymentTransactionStatusId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentTransactionStatusId paymentTransactionStatusId = (PaymentTransactionStatusId) o;
    return Objects.equals(this.paymentTransactionStatusId, paymentTransactionStatusId.paymentTransactionStatusId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(paymentTransactionStatusId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentTransactionStatusId {\n");
    
    sb.append("    paymentTransactionStatusId: ").append(toIndentedString(paymentTransactionStatusId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

