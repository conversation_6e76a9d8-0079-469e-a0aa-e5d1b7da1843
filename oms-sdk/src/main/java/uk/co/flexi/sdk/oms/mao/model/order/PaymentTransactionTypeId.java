/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * PaymentTransactionTypeId
 */
public class PaymentTransactionTypeId {
  public static final String SERIALIZED_NAME_PAYMENT_TRANSACTION_TYPE_ID = "PaymentTransactionTypeId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TRANSACTION_TYPE_ID)
  private String paymentTransactionTypeId;

  public PaymentTransactionTypeId paymentTransactionTypeId(String paymentTransactionTypeId) {
    this.paymentTransactionTypeId = paymentTransactionTypeId;
    return this;
  }

   /**
   * Unique identifier of the transaction type
   * @return paymentTransactionTypeId
  **/
  
  public String getPaymentTransactionTypeId() {
    return paymentTransactionTypeId;
  }

  public void setPaymentTransactionTypeId(String paymentTransactionTypeId) {
    this.paymentTransactionTypeId = paymentTransactionTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentTransactionTypeId paymentTransactionTypeId = (PaymentTransactionTypeId) o;
    return Objects.equals(this.paymentTransactionTypeId, paymentTransactionTypeId.paymentTransactionTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(paymentTransactionTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentTransactionTypeId {\n");
    
    sb.append("    paymentTransactionTypeId: ").append(toIndentedString(paymentTransactionTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

