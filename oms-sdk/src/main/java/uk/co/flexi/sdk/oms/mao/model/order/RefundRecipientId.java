/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * RefundRecipientId
 */
public class RefundRecipientId {
  public static final String SERIALIZED_NAME_REFUND_RECIPIENT_ID = "RefundRecipientId";
  @SerializedName(SERIALIZED_NAME_REFUND_RECIPIENT_ID)
  private String refundRecipientId;

  public RefundRecipientId refundRecipientId(String refundRecipientId) {
    this.refundRecipientId = refundRecipientId;
    return this;
  }

   /**
   * Unique identifier of the recipient type. Indicates whether the refund should be issued via the original payment method, or via a new gift card. Used during return invoice creation to determine whether to refund the customer via the original payment method, or to refund the gift recipient via a new gift card order line.
   * @return refundRecipientId
  **/
  
  public String getRefundRecipientId() {
    return refundRecipientId;
  }

  public void setRefundRecipientId(String refundRecipientId) {
    this.refundRecipientId = refundRecipientId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RefundRecipientId refundRecipientId = (RefundRecipientId) o;
    return Objects.equals(this.refundRecipientId, refundRecipientId.refundRecipientId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(refundRecipientId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RefundRecipientId {\n");
    
    sb.append("    refundRecipientId: ").append(toIndentedString(refundRecipientId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

