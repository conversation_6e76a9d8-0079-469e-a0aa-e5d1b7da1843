/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * IdentityDocTypeId
 */
public class IdentityDocTypeId {
  public static final String SERIALIZED_NAME_IDENTITY_DOC_TYPE_ID = "IdentityDocTypeId";
  @SerializedName(SERIALIZED_NAME_IDENTITY_DOC_TYPE_ID)
  private String identityDocTypeId;

  public IdentityDocTypeId identityDocTypeId(String identityDocTypeId) {
    this.identityDocTypeId = identityDocTypeId;
    return this;
  }

   /**
   * Unique identifier of the Doc type
   * @return identityDocTypeId
  **/
  
  public String getIdentityDocTypeId() {
    return identityDocTypeId;
  }

  public void setIdentityDocTypeId(String identityDocTypeId) {
    this.identityDocTypeId = identityDocTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IdentityDocTypeId identityDocTypeId = (IdentityDocTypeId) o;
    return Objects.equals(this.identityDocTypeId, identityDocTypeId.identityDocTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(identityDocTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IdentityDocTypeId {\n");
    
    sb.append("    identityDocTypeId: ").append(toIndentedString(identityDocTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

