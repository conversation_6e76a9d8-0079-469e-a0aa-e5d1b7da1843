/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderLineAdditional
 */
public class OrderLineAdditional {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_ELIGIBLE_FOR_RETURN_REASON = "EligibleForReturnReason";
  @SerializedName(SERIALIZED_NAME_ELIGIBLE_FOR_RETURN_REASON)
  private ReasonId eligibleForReturnReason = null;

  public static final String SERIALIZED_NAME_ELIGIBLE_FOR_RETURN_REASON_COMMENT = "EligibleForReturnReasonComment";
  @SerializedName(SERIALIZED_NAME_ELIGIBLE_FOR_RETURN_REASON_COMMENT)
  private String eligibleForReturnReasonComment;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IS_CONDITION_VARIANCE = "IsConditionVariance";
  @SerializedName(SERIALIZED_NAME_IS_CONDITION_VARIANCE)
  private Boolean isConditionVariance;

  public static final String SERIALIZED_NAME_IS_ITEM_VARIANCE = "IsItemVariance";
  @SerializedName(SERIALIZED_NAME_IS_ITEM_VARIANCE)
  private Boolean isItemVariance;

  public static final String SERIALIZED_NAME_ITEM_CONDITION = "ItemCondition";
  @SerializedName(SERIALIZED_NAME_ITEM_CONDITION)
  private ItemConditionId itemCondition = null;

  public static final String SERIALIZED_NAME_ITEM_DISPOSITION = "ItemDisposition";
  @SerializedName(SERIALIZED_NAME_ITEM_DISPOSITION)
  private String itemDisposition;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PRICE_OVERRIDE_COMMENTS = "PriceOverrideComments";
  @SerializedName(SERIALIZED_NAME_PRICE_OVERRIDE_COMMENTS)
  private String priceOverrideComments;

  public static final String SERIALIZED_NAME_PRICE_OVERRIDE_REASON = "PriceOverrideReason";
  @SerializedName(SERIALIZED_NAME_PRICE_OVERRIDE_REASON)
  private ReasonId priceOverrideReason = null;

  public static final String SERIALIZED_NAME_PRICE_OVERRIDE_REASON_ID = "PriceOverrideReasonId";
  @SerializedName(SERIALIZED_NAME_PRICE_OVERRIDE_REASON_ID)
  private String priceOverrideReasonId;

  public static final String SERIALIZED_NAME_RETURN_APPROVAL_COMMENTS = "ReturnApprovalComments";
  @SerializedName(SERIALIZED_NAME_RETURN_APPROVAL_COMMENTS)
  private String returnApprovalComments;

  public static final String SERIALIZED_NAME_RETURN_APPROVAL_REASON = "ReturnApprovalReason";
  @SerializedName(SERIALIZED_NAME_RETURN_APPROVAL_REASON)
  private ReasonId returnApprovalReason = null;

  public static final String SERIALIZED_NAME_RETURN_REASON = "ReturnReason";
  @SerializedName(SERIALIZED_NAME_RETURN_REASON)
  private String returnReason;

  public static final String SERIALIZED_NAME_ITEM_CONDTION_ID = "ItemConditionId";
  @SerializedName(SERIALIZED_NAME_ITEM_CONDTION_ID)
  private String itemConditionId;

  public static final String SERIALIZED_NAME_TAX_OVERRIDE_REASON = "TaxOverrideReason";
  @SerializedName(SERIALIZED_NAME_TAX_OVERRIDE_REASON)
  private ReasonId taxOverrideReason = null;

  public static final String SERIALIZED_NAME_TAX_OVERRIDE_REASON_ID = "TaxOverrideReasonId";
  @SerializedName(SERIALIZED_NAME_TAX_OVERRIDE_REASON_ID)
  private String taxOverrideReasonId;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_VARIANCE_QUANTITY = "VarianceQuantity";
  @SerializedName(SERIALIZED_NAME_VARIANCE_QUANTITY)
  private Double varianceQuantity;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderLineAdditional actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderLineAdditional putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderLineAdditional createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderLineAdditional createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderLineAdditional eligibleForReturnReason(ReasonId eligibleForReturnReason) {
    this.eligibleForReturnReason = eligibleForReturnReason;
    return this;
  }

   /**
   * Get eligibleForReturnReason
   * @return eligibleForReturnReason
  **/
  
  public ReasonId getEligibleForReturnReason() {
    return eligibleForReturnReason;
  }

  public void setEligibleForReturnReason(ReasonId eligibleForReturnReason) {
    this.eligibleForReturnReason = eligibleForReturnReason;
  }

  public OrderLineAdditional eligibleForReturnReasonComment(String eligibleForReturnReasonComment) {
    this.eligibleForReturnReasonComment = eligibleForReturnReasonComment;
    return this;
  }

   /**
   * Comments provided by user when a non returnable or non exchangeable item are marked as returnable or exchangeable.
   * @return eligibleForReturnReasonComment
  **/
  
  public String getEligibleForReturnReasonComment() {
    return eligibleForReturnReasonComment;
  }

  public void setEligibleForReturnReasonComment(String eligibleForReturnReasonComment) {
    this.eligibleForReturnReasonComment = eligibleForReturnReasonComment;
  }

  public OrderLineAdditional extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderLineAdditional isConditionVariance(Boolean isConditionVariance) {
    this.isConditionVariance = isConditionVariance;
    return this;
  }

   /**
   * It would be use to track Condition variance during return flow. For return line if received item condition doesn&#39;t match with Return line item condition then value of this attribute would be set as true. In case of multiple quantity on return line if condition of any unit of received item doesn&#39;t match with Return line item condition still a it would be set as true.
   * @return isConditionVariance
  **/
  
  public Boolean getIsConditionVariance() {
    return isConditionVariance;
  }

  public void setIsConditionVariance(Boolean isConditionVariance) {
    this.isConditionVariance = isConditionVariance;
  }

  public OrderLineAdditional isItemVariance(Boolean isItemVariance) {
    this.isItemVariance = isItemVariance;
    return this;
  }

   /**
   * It would be use to track Item variance during return flow. If return center receive an unexpected item, which is not present in original order or if item is present then quantity is more than returnable quantity, a new line would created for such item and this attribute would be set as true.
   * @return isItemVariance
  **/
  
  public Boolean getIsItemVariance() {
    return isItemVariance;
  }

  public void setIsItemVariance(Boolean isItemVariance) {
    this.isItemVariance = isItemVariance;
  }

  public OrderLineAdditional itemCondition(ItemConditionId itemCondition) {
    this.itemCondition = itemCondition;
    return this;
  }

   /**
   * Get itemCondition
   * @return itemCondition
  **/
  
  public ItemConditionId getItemCondition() {
    return itemCondition;
  }

  public void setItemCondition(ItemConditionId itemCondition) {
    this.itemCondition = itemCondition;
  }

  public OrderLineAdditional itemDisposition(String itemDisposition) {
    this.itemDisposition = itemDisposition;
    return this;
  }

   /**
   * This is the disposition for a particular quantity on an order line. This is used for supply events triggered by store transactions. For example, if there an item which is returned in store and the disposition is &#39;Damaged&#39;, this field is populated with &#39;Damaged&#39; when the return transaction updates the order.
   * @return itemDisposition
  **/
  
  public String getItemDisposition() {
    return itemDisposition;
  }

  public void setItemDisposition(String itemDisposition) {
    this.itemDisposition = itemDisposition;
  }

  public OrderLineAdditional localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderLineAdditional messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderLineAdditional orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderLineAdditional PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderLineAdditional priceOverrideComments(String priceOverrideComments) {
    this.priceOverrideComments = priceOverrideComments;
    return this;
  }

   /**
   * If IsPriceOverrIdden is true, the comments entered
   * @return priceOverrideComments
  **/
  
  public String getPriceOverrideComments() {
    return priceOverrideComments;
  }

  public void setPriceOverrideComments(String priceOverrideComments) {
    this.priceOverrideComments = priceOverrideComments;
  }

  public OrderLineAdditional priceOverrideReason(ReasonId priceOverrideReason) {
    this.priceOverrideReason = priceOverrideReason;
    return this;
  }

   /**
   * Get priceOverrideReason
   * @return priceOverrideReason
  **/
  
  public ReasonId getPriceOverrideReason() {
    return priceOverrideReason;
  }

  public void setPriceOverrideReason(ReasonId priceOverrideReason) {
    this.priceOverrideReason = priceOverrideReason;
  }

  public OrderLineAdditional priceOverrideReasonId(String priceOverrideReasonId) {
    this.priceOverrideReasonId = priceOverrideReasonId;
    return this;
  }

   /**
   * If isPriceOverridden is true, this field contains the price override reason code
   * @return priceOverrideReasonId
  **/
  
  public String getPriceOverrideReasonId() {
    return priceOverrideReasonId;
  }

  public void setPriceOverrideReasonId(String priceOverrideReasonId) {
    this.priceOverrideReasonId = priceOverrideReasonId;
  }

  public OrderLineAdditional returnApprovalComments(String returnApprovalComments) {
    this.returnApprovalComments = returnApprovalComments;
    return this;
  }

   /**
   * Comments provided by user when a return line is approved. Approval is required when receipt of a return item is not expected (e.g. field destroy), and the setting to auto-approve is disabled. When a return line is created with isReceiptExpected &#x3D; false, the line quantity moves to Pending Approval status. After a user approves the line, the quantity moves to Returned status and the comments are captured here.
   * @return returnApprovalComments
  **/
  
  public String getReturnApprovalComments() {
    return returnApprovalComments;
  }

  public void setReturnApprovalComments(String returnApprovalComments) {
    this.returnApprovalComments = returnApprovalComments;
  }

  public OrderLineAdditional returnApprovalReason(ReasonId returnApprovalReason) {
    this.returnApprovalReason = returnApprovalReason;
    return this;
  }

   /**
   * Get returnApprovalReason
   * @return returnApprovalReason
  **/
  
  public ReasonId getReturnApprovalReason() {
    return returnApprovalReason;
  }

  public void setReturnApprovalReason(ReasonId returnApprovalReason) {
    this.returnApprovalReason = returnApprovalReason;
  }

  public OrderLineAdditional returnReason(String returnReason) {
    this.returnReason = returnReason;
    return this;
  }

   /**
   * Customer-provided reason for returning an item. Captured during call center and POS returns and stored for informational purposes. Can optionally be used in return fee filters or other configurations. For example, return reason can be used to capture if a retailer or a customer is at fault for a return; if a retailer shipped a damaged item, then the retailer might provide a free re-shipment, while the customer might have to pay for shipping otherwise. Only used if isReturn &#x3D; true
   * @return returnReason
  **/
  
  public String getReturnReason() {
    return returnReason;
  }

  public void setReturnReason(String returnReason) {
    this.returnReason = returnReason;
  }

  public OrderLineAdditional itemConditionId(String itemConditionId) {
    this.itemConditionId = itemConditionId;
    return this;
  }

  public String getItemConditionId() {
    return itemConditionId;
  }

  public void setItemConditionId(String itemConditionId) {
    this.itemConditionId = itemConditionId;
  }

  public OrderLineAdditional taxOverrideReason(ReasonId taxOverrideReason) {
    this.taxOverrideReason = taxOverrideReason;
    return this;
  }

   /**
   * Get taxOverrideReason
   * @return taxOverrideReason
  **/
  
  public ReasonId getTaxOverrideReason() {
    return taxOverrideReason;
  }

  public void setTaxOverrideReason(ReasonId taxOverrideReason) {
    this.taxOverrideReason = taxOverrideReason;
  }

  public OrderLineAdditional taxOverrideReasonId(String taxOverrideReasonId) {
    this.taxOverrideReasonId = taxOverrideReasonId;
    return this;
  }

   /**
   * If isTaxOverridden is true, this field contains the price override reason code
   * @return taxOverrideReasonId
  **/
  
  public String getTaxOverrideReasonId() {
    return taxOverrideReasonId;
  }

  public void setTaxOverrideReasonId(String taxOverrideReasonId) {
    this.taxOverrideReasonId = taxOverrideReasonId;
  }

  public OrderLineAdditional updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderLineAdditional updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderLineAdditional varianceQuantity(Double varianceQuantity) {
    this.varianceQuantity = varianceQuantity;
    return this;
  }

   /**
   * It would be use to track Quantity variance during return flow.For return line if received Quantity is not equal to Orignial return line quantity then value for this attribute would be non zero. If received quantity is more than Original return line quantity then its value is positive. If received quantity is less than original return line quantity then its value would be negative.
   * minimum: 0
   * maximum: 999999999999.9999
   * @return varianceQuantity
  **/
  
  public Double getVarianceQuantity() {
    return varianceQuantity;
  }

  public void setVarianceQuantity(Double varianceQuantity) {
    this.varianceQuantity = varianceQuantity;
  }

  public OrderLineAdditional entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderLineAdditional localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderLineAdditional orderLineAdditional = (OrderLineAdditional) o;
    return Objects.equals(this.actions, orderLineAdditional.actions) &&
        Objects.equals(this.createdBy, orderLineAdditional.createdBy) &&
        Objects.equals(this.createdTimestamp, orderLineAdditional.createdTimestamp) &&
        Objects.equals(this.eligibleForReturnReason, orderLineAdditional.eligibleForReturnReason) &&
        Objects.equals(this.eligibleForReturnReasonComment, orderLineAdditional.eligibleForReturnReasonComment) &&
        Objects.equals(this.extended, orderLineAdditional.extended) &&
        Objects.equals(this.isConditionVariance, orderLineAdditional.isConditionVariance) &&
        Objects.equals(this.isItemVariance, orderLineAdditional.isItemVariance) &&
        Objects.equals(this.itemCondition, orderLineAdditional.itemCondition) &&
        Objects.equals(this.itemDisposition, orderLineAdditional.itemDisposition) &&
        Objects.equals(this.localizedTo, orderLineAdditional.localizedTo) &&
        Objects.equals(this.messages, orderLineAdditional.messages) &&
        Objects.equals(this.orgId, orderLineAdditional.orgId) &&
        Objects.equals(this.PK, orderLineAdditional.PK) &&
        Objects.equals(this.priceOverrideComments, orderLineAdditional.priceOverrideComments) &&
        Objects.equals(this.priceOverrideReason, orderLineAdditional.priceOverrideReason) &&
        Objects.equals(this.priceOverrideReasonId, orderLineAdditional.priceOverrideReasonId) &&
        Objects.equals(this.returnApprovalComments, orderLineAdditional.returnApprovalComments) &&
        Objects.equals(this.returnApprovalReason, orderLineAdditional.returnApprovalReason) &&
        Objects.equals(this.returnReason, orderLineAdditional.returnReason) &&
        Objects.equals(this.taxOverrideReason, orderLineAdditional.taxOverrideReason) &&
        Objects.equals(this.taxOverrideReasonId, orderLineAdditional.taxOverrideReasonId) &&
        Objects.equals(this.updatedBy, orderLineAdditional.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderLineAdditional.updatedTimestamp) &&
        Objects.equals(this.varianceQuantity, orderLineAdditional.varianceQuantity) &&
        Objects.equals(this.entityName, orderLineAdditional.entityName) &&
        Objects.equals(this.localize, orderLineAdditional.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, eligibleForReturnReason, eligibleForReturnReasonComment, extended, isConditionVariance, isItemVariance, itemCondition, itemDisposition, localizedTo, messages, orgId, PK, priceOverrideComments, priceOverrideReason, priceOverrideReasonId, returnApprovalComments, returnApprovalReason, returnReason, taxOverrideReason, taxOverrideReasonId, updatedBy, updatedTimestamp, varianceQuantity, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderLineAdditional {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    eligibleForReturnReason: ").append(toIndentedString(eligibleForReturnReason)).append("\n");
    sb.append("    eligibleForReturnReasonComment: ").append(toIndentedString(eligibleForReturnReasonComment)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    isConditionVariance: ").append(toIndentedString(isConditionVariance)).append("\n");
    sb.append("    isItemVariance: ").append(toIndentedString(isItemVariance)).append("\n");
    sb.append("    itemCondition: ").append(toIndentedString(itemCondition)).append("\n");
    sb.append("    itemDisposition: ").append(toIndentedString(itemDisposition)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    priceOverrideComments: ").append(toIndentedString(priceOverrideComments)).append("\n");
    sb.append("    priceOverrideReason: ").append(toIndentedString(priceOverrideReason)).append("\n");
    sb.append("    priceOverrideReasonId: ").append(toIndentedString(priceOverrideReasonId)).append("\n");
    sb.append("    returnApprovalComments: ").append(toIndentedString(returnApprovalComments)).append("\n");
    sb.append("    returnApprovalReason: ").append(toIndentedString(returnApprovalReason)).append("\n");
    sb.append("    returnReason: ").append(toIndentedString(returnReason)).append("\n");
    sb.append("    taxOverrideReason: ").append(toIndentedString(taxOverrideReason)).append("\n");
    sb.append("    taxOverrideReasonId: ").append(toIndentedString(taxOverrideReasonId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    varianceQuantity: ").append(toIndentedString(varianceQuantity)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

