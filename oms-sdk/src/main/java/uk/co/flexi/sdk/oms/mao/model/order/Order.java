/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.LocalDate;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.*;

/**
 * Order
 */
public class Order {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ALTERNATE_ORDER_ID = "AlternateOrderId";
  @SerializedName(SERIALIZED_NAME_ALTERNATE_ORDER_ID)
  private String alternateOrderId;

  public static final String SERIALIZED_NAME_ARCHIVE_DATE = "ArchiveDate";
  @SerializedName(SERIALIZED_NAME_ARCHIVE_DATE)
  private OffsetDateTime archiveDate;

  public static final String SERIALIZED_NAME_BALANCE_DUE = "BalanceDue";
  @SerializedName(SERIALIZED_NAME_BALANCE_DUE)
  private BigDecimal balanceDue;

  public static final String SERIALIZED_NAME_BUSINESS_DATE = "BusinessDate";
  @SerializedName(SERIALIZED_NAME_BUSINESS_DATE)
  private LocalDate businessDate;

  public static final String SERIALIZED_NAME_CALCULATED_VALUES = "CalculatedValues";
  @SerializedName(SERIALIZED_NAME_CALCULATED_VALUES)
  private Object calculatedValues = null;

  public static final String SERIALIZED_NAME_CANCEL_COMMENTS = "CancelComments";
  @SerializedName(SERIALIZED_NAME_CANCEL_COMMENTS)
  private String cancelComments;

  public static final String SERIALIZED_NAME_CANCEL_LINE_COUNT = "CancelLineCount";
  @SerializedName(SERIALIZED_NAME_CANCEL_LINE_COUNT)
  private Long cancelLineCount;

  public static final String SERIALIZED_NAME_CANCEL_REASON = "CancelReason";
  @SerializedName(SERIALIZED_NAME_CANCEL_REASON)
  private ReasonId cancelReason = null;

  public static final String SERIALIZED_NAME_CANCELLED_ORDER_SUB_TOTAL = "CancelledOrderSubTotal";
  @SerializedName(SERIALIZED_NAME_CANCELLED_ORDER_SUB_TOTAL)
  private BigDecimal cancelledOrderSubTotal;

  public static final String SERIALIZED_NAME_CANCELLED_ORDER_TOTAL = "CancelledOrderTotal";
  @SerializedName(SERIALIZED_NAME_CANCELLED_ORDER_TOTAL)
  private BigDecimal cancelledOrderTotal;

  public static final String SERIALIZED_NAME_CANCELLED_TOTAL_DISCOUNTS = "CancelledTotalDiscounts";
  @SerializedName(SERIALIZED_NAME_CANCELLED_TOTAL_DISCOUNTS)
  private BigDecimal cancelledTotalDiscounts;

  public static final String SERIALIZED_NAME_CAPTURED_DATE = "CapturedDate";
  @SerializedName(SERIALIZED_NAME_CAPTURED_DATE)
  private OffsetDateTime capturedDate;

  public static final String SERIALIZED_NAME_CHANGE_LOG = "ChangeLog";
  @SerializedName(SERIALIZED_NAME_CHANGE_LOG)
  private ChangeLog changeLog = null;

  public static final String SERIALIZED_NAME_COLLECTED_AMOUNT = "CollectedAmount";
  @SerializedName(SERIALIZED_NAME_COLLECTED_AMOUNT)
  private BigDecimal collectedAmount;

  public static final String SERIALIZED_NAME_CONFIRMED_DATE = "ConfirmedDate";
  @SerializedName(SERIALIZED_NAME_CONFIRMED_DATE)
  private OffsetDateTime confirmedDate;

  public static final String SERIALIZED_NAME_CONTACT_PREFERENCE = "ContactPreference";
  @SerializedName(SERIALIZED_NAME_CONTACT_PREFERENCE)
  private List<ContactPreference> contactPreference = null;

  public static final String SERIALIZED_NAME_COUNTED_DATE = "CountedDate";
  @SerializedName(SERIALIZED_NAME_COUNTED_DATE)
  private OffsetDateTime countedDate;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_CURRENCY_CODE = "CurrencyCode";
  @SerializedName(SERIALIZED_NAME_CURRENCY_CODE)
  private String currencyCode;

  public static final String SERIALIZED_NAME_CUSTOMER_ADDRESS = "CustomerAddress";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_ADDRESS)
  private Address customerAddress = null;

  public static final String SERIALIZED_NAME_CUSTOMER_EMAIL = "CustomerEmail";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_EMAIL)
  private String customerEmail;

  public static final String SERIALIZED_NAME_CUSTOMER_FIRST_NAME = "CustomerFirstName";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_FIRST_NAME)
  private String customerFirstName;

  public static final String SERIALIZED_NAME_CUSTOMER_ID = "CustomerId";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_ID)
  private String customerId;

  public static final String SERIALIZED_NAME_CUSTOMER_IDENTITY_DOC = "CustomerIdentityDoc";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_IDENTITY_DOC)
  private List<CustomerIdentityDoc> customerIdentityDoc = null;

  public static final String SERIALIZED_NAME_CUSTOMER_LAST_NAME = "CustomerLastName";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_LAST_NAME)
  private String customerLastName;

  public static final String SERIALIZED_NAME_CUSTOMER_PHONE = "CustomerPhone";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_PHONE)
  private String customerPhone;

  public static final String SERIALIZED_NAME_CUSTOMER_SIGNATURE = "CustomerSignature";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_SIGNATURE)
  private String customerSignature;

  public static final String SERIALIZED_NAME_CUSTOMER_TYPE_ID = "CustomerTypeId";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_TYPE_ID)
  private String customerTypeId;

  public static final String SERIALIZED_NAME_DO_NOT_RELEASE_BEFORE = "DoNotReleaseBefore";
  @SerializedName(SERIALIZED_NAME_DO_NOT_RELEASE_BEFORE)
  private OffsetDateTime doNotReleaseBefore;

  public static final String SERIALIZED_NAME_DOC_TYPE = "DocType";
  @SerializedName(SERIALIZED_NAME_DOC_TYPE)
  private DocTypeId docType = null;

  public static final String SERIALIZED_NAME_EVENT_SUBMIT_TIME = "EventSubmitTime";
  @SerializedName(SERIALIZED_NAME_EVENT_SUBMIT_TIME)
  private OffsetDateTime eventSubmitTime;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Map<String, Object>  extended = null;

  public static final String SERIALIZED_NAME_FULFILLMENT_STATUS = "FulfillmentStatus";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_STATUS)
  private String fulfillmentStatus;

  public static final String SERIALIZED_NAME_INVOICE = "Invoice";
  @SerializedName(SERIALIZED_NAME_INVOICE)
  private List<Invoice> invoice = null;

  public static final String SERIALIZED_NAME_IS_ARCHIVE_IN_PROGRESS = "IsArchiveInProgress";
  @SerializedName(SERIALIZED_NAME_IS_ARCHIVE_IN_PROGRESS)
  private Boolean isArchiveInProgress;

  public static final String SERIALIZED_NAME_IS_CANCELLED = "IsCancelled";
  @SerializedName(SERIALIZED_NAME_IS_CANCELLED)
  private Boolean isCancelled;

  public static final String SERIALIZED_NAME_IS_CAPTURED_OFFLINE = "IsCapturedOffline";
  @SerializedName(SERIALIZED_NAME_IS_CAPTURED_OFFLINE)
  private Boolean isCapturedOffline;

  public static final String SERIALIZED_NAME_IS_CONFIRMED = "IsConfirmed";
  @SerializedName(SERIALIZED_NAME_IS_CONFIRMED)
  private Boolean isConfirmed;

  public static final String SERIALIZED_NAME_IS_ON_HOLD = "IsOnHold";
  @SerializedName(SERIALIZED_NAME_IS_ON_HOLD)
  private Boolean isOnHold;

  public static final String SERIALIZED_NAME_IS_ORDER_COUNTABLE = "IsOrderCountable";
  @SerializedName(SERIALIZED_NAME_IS_ORDER_COUNTABLE)
  private Boolean isOrderCountable;

  public static final String SERIALIZED_NAME_IS_POST_VOIDED = "IsPostVoided";
  @SerializedName(SERIALIZED_NAME_IS_POST_VOIDED)
  private Boolean isPostVoided;

  public static final String SERIALIZED_NAME_IS_READY_FOR_TENDER = "IsReadyForTender";
  @SerializedName(SERIALIZED_NAME_IS_READY_FOR_TENDER)
  private Boolean isReadyForTender;

  public static final String SERIALIZED_NAME_IS_TAX_EXEMPT = "IsTaxExempt";
  @SerializedName(SERIALIZED_NAME_IS_TAX_EXEMPT)
  private Boolean isTaxExempt;

  public static final String SERIALIZED_NAME_IS_TAX_OVERRIDDEN = "IsTaxOverridden";
  @SerializedName(SERIALIZED_NAME_IS_TAX_OVERRIDDEN)
  private Boolean isTaxOverridden;

  public static final String SERIALIZED_NAME_IS_UN_ARCHIVE_IN_PROGRESS = "IsUnArchiveInProgress";
  @SerializedName(SERIALIZED_NAME_IS_UN_ARCHIVE_IN_PROGRESS)
  private Boolean isUnArchiveInProgress;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_LOYALTY_NUMBER = "LoyaltyNumber";
  @SerializedName(SERIALIZED_NAME_LOYALTY_NUMBER)
  private String loyaltyNumber;

  public static final String SERIALIZED_NAME_MANAGER_AUTH_DETAIL = "ManagerAuthDetail";
  @SerializedName(SERIALIZED_NAME_MANAGER_AUTH_DETAIL)
  private List<ManagerAuthDetail> managerAuthDetail = null;

  public static final String SERIALIZED_NAME_MAX_APPEASEMENT_AMOUNT = "MaxAppeasementAmount";
  @SerializedName(SERIALIZED_NAME_MAX_APPEASEMENT_AMOUNT)
  private BigDecimal maxAppeasementAmount;

  public static final String SERIALIZED_NAME_MAX_FULFILLMENT_STATUS = "MaxFulfillmentStatus";
  @SerializedName(SERIALIZED_NAME_MAX_FULFILLMENT_STATUS)
  private KeyDTO maxFulfillmentStatus = null;

  public static final String SERIALIZED_NAME_MAX_FULFILLMENT_STATUS_ID = "MaxFulfillmentStatusId";
  @SerializedName(SERIALIZED_NAME_MAX_FULFILLMENT_STATUS_ID)
  private String maxFulfillmentStatusId;

  public static final String SERIALIZED_NAME_MAX_RETURN_STATUS = "MaxReturnStatus";
  @SerializedName(SERIALIZED_NAME_MAX_RETURN_STATUS)
  private KeyDTO maxReturnStatus = null;

  public static final String SERIALIZED_NAME_MAX_RETURN_STATUS_ID = "MaxReturnStatusId";
  @SerializedName(SERIALIZED_NAME_MAX_RETURN_STATUS_ID)
  private String maxReturnStatusId;

  public static final String SERIALIZED_NAME_MERCH_RETURN_LINE_COUNT = "MerchReturnLineCount";
  @SerializedName(SERIALIZED_NAME_MERCH_RETURN_LINE_COUNT)
  private Long merchReturnLineCount;

  public static final String SERIALIZED_NAME_MERCH_SALE_LINE_COUNT = "MerchSaleLineCount";
  @SerializedName(SERIALIZED_NAME_MERCH_SALE_LINE_COUNT)
  private Long merchSaleLineCount;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_MIN_FULFILLMENT_STATUS = "MinFulfillmentStatus";
  @SerializedName(SERIALIZED_NAME_MIN_FULFILLMENT_STATUS)
  private KeyDTO minFulfillmentStatus = null;

  public static final String SERIALIZED_NAME_MIN_FULFILLMENT_STATUS_ID = "MinFulfillmentStatusId";
  @SerializedName(SERIALIZED_NAME_MIN_FULFILLMENT_STATUS_ID)
  private String minFulfillmentStatusId;

  public static final String SERIALIZED_NAME_MIN_RETURN_STATUS = "MinReturnStatus";
  @SerializedName(SERIALIZED_NAME_MIN_RETURN_STATUS)
  private KeyDTO minReturnStatus = null;

  public static final String SERIALIZED_NAME_MIN_RETURN_STATUS_ID = "MinReturnStatusId";
  @SerializedName(SERIALIZED_NAME_MIN_RETURN_STATUS_ID)
  private String minReturnStatusId;

  public static final String SERIALIZED_NAME_NEXT_EVENT_TIME = "NextEventTime";
  @SerializedName(SERIALIZED_NAME_NEXT_EVENT_TIME)
  private OffsetDateTime nextEventTime;

  public static final String SERIALIZED_NAME_ORDER_ACTIONS = "OrderActions";
  @SerializedName(SERIALIZED_NAME_ORDER_ACTIONS)
  private Map<String, Object> orderActions = null;

  public static final String SERIALIZED_NAME_ORDER_ATTRIBUTE = "OrderAttribute";
  @SerializedName(SERIALIZED_NAME_ORDER_ATTRIBUTE)
  private List<OrderAttribute> orderAttribute = null;

  public static final String SERIALIZED_NAME_ORDER_CAPTURE_DETAIL = "OrderCaptureDetail";
  @SerializedName(SERIALIZED_NAME_ORDER_CAPTURE_DETAIL)
  private List<OrderCaptureDetail> orderCaptureDetail = null;

  public static final String SERIALIZED_NAME_ORDER_CHARGE_DETAIL = "OrderChargeDetail";
  @SerializedName(SERIALIZED_NAME_ORDER_CHARGE_DETAIL)
  private List<OrderChargeDetail> orderChargeDetail = null;

  public static final String SERIALIZED_NAME_ORDER_EXTENSION1 = "OrderExtension1";
  @SerializedName(SERIALIZED_NAME_ORDER_EXTENSION1)
  private OrderExtension1 orderExtension1 = null;

  public static final String SERIALIZED_NAME_ORDER_EXTENSION2 = "OrderExtension2";
  @SerializedName(SERIALIZED_NAME_ORDER_EXTENSION2)
  private List<OrderExtension2> orderExtension2 = null;

  public static final String SERIALIZED_NAME_ORDER_EXTENSION3 = "OrderExtension3";
  @SerializedName(SERIALIZED_NAME_ORDER_EXTENSION3)
  private List<OrderExtension3> orderExtension3 = null;

  public static final String SERIALIZED_NAME_ORDER_EXTENSION4 = "OrderExtension4";
  @SerializedName(SERIALIZED_NAME_ORDER_EXTENSION4)
  private List<OrderExtension4> orderExtension4 = null;

  public static final String SERIALIZED_NAME_ORDER_EXTENSION5 = "OrderExtension5";
  @SerializedName(SERIALIZED_NAME_ORDER_EXTENSION5)
  private List<OrderExtension5> orderExtension5 = null;

  public static final String SERIALIZED_NAME_ORDER_FULFILLMENT_GROUPS = "OrderFulfillmentGroups";
  @SerializedName(SERIALIZED_NAME_ORDER_FULFILLMENT_GROUPS)
  private List<OrderFulfillmentGroupDTO> orderFulfillmentGroups = null;

  public static final String SERIALIZED_NAME_ORDER_HOLD = "OrderHold";
  @SerializedName(SERIALIZED_NAME_ORDER_HOLD)
  private List<OrderHold> orderHold = null;

  public static final String SERIALIZED_NAME_ORDER_ID = "OrderId";
  @SerializedName(SERIALIZED_NAME_ORDER_ID)
  private String orderId;

  public static final String SERIALIZED_NAME_ORDER_LINE = "OrderLine";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE)
  private List<OrderLine> orderLine = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_COUNT = "OrderLineCount";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_COUNT)
  private Long orderLineCount;

  public static final String SERIALIZED_NAME_ORDER_LOCALE = "OrderLocale";
  @SerializedName(SERIALIZED_NAME_ORDER_LOCALE)
  private String orderLocale;

  public static final String SERIALIZED_NAME_ORDER_MILESTONE = "OrderMilestone";
  @SerializedName(SERIALIZED_NAME_ORDER_MILESTONE)
  private List<OrderMilestone> orderMilestone = null;

  public static final String SERIALIZED_NAME_ORDER_MILESTONE_EVENT = "OrderMilestoneEvent";
  @SerializedName(SERIALIZED_NAME_ORDER_MILESTONE_EVENT)
  private List<OrderMilestoneEvent> orderMilestoneEvent = null;

  public static final String SERIALIZED_NAME_ORDER_NOTE = "OrderNote";
  @SerializedName(SERIALIZED_NAME_ORDER_NOTE)
  private List<OrderNote> orderNote = null;

  public static final String SERIALIZED_NAME_ORDER_PAYMENT_METHOD = "OrderPaymentMethod";
  @SerializedName(SERIALIZED_NAME_ORDER_PAYMENT_METHOD)
  private List<OrderPaymentMethod> orderPaymentMethod = null;

  public static final String SERIALIZED_NAME_ORDER_PROMISING_INFO = "OrderPromisingInfo";
  @SerializedName(SERIALIZED_NAME_ORDER_PROMISING_INFO)
  private OrderPromisingInfo orderPromisingInfo = null;

  public static final String SERIALIZED_NAME_ORDER_PROMOTION_REQUEST = "OrderPromotionRequest";
  @SerializedName(SERIALIZED_NAME_ORDER_PROMOTION_REQUEST)
  private List<OrderPromotionRequest> orderPromotionRequest = null;

  public static final String SERIALIZED_NAME_ORDER_SALES_ASSOCIATE = "OrderSalesAssociate";
  @SerializedName(SERIALIZED_NAME_ORDER_SALES_ASSOCIATE)
  private List<OrderSalesAssociate> orderSalesAssociate = null;

  public static final String SERIALIZED_NAME_ORDER_SUB_TOTAL = "OrderSubTotal";
  @SerializedName(SERIALIZED_NAME_ORDER_SUB_TOTAL)
  private BigDecimal orderSubTotal;

  public static final String SERIALIZED_NAME_ORDER_TAG_DETAIL = "OrderTagDetail";
  @SerializedName(SERIALIZED_NAME_ORDER_TAG_DETAIL)
  private List<OrderTagDetail> orderTagDetail = null;

  public static final String SERIALIZED_NAME_ORDER_TAX_DETAIL = "OrderTaxDetail";
  @SerializedName(SERIALIZED_NAME_ORDER_TAX_DETAIL)
  private List<OrderTaxDetail> orderTaxDetail = null;

  public static final String SERIALIZED_NAME_ORDER_TOTAL = "OrderTotal";
  @SerializedName(SERIALIZED_NAME_ORDER_TOTAL)
  private BigDecimal orderTotal;

  public static final String SERIALIZED_NAME_ORDER_TRACKING_INFO = "OrderTrackingInfo";
  @SerializedName(SERIALIZED_NAME_ORDER_TRACKING_INFO)
  private List<OrderTrackingInfo> orderTrackingInfo = null;

  public static final String SERIALIZED_NAME_ORDER_TYPE = "OrderType";
  @SerializedName(SERIALIZED_NAME_ORDER_TYPE)
  private OrderTypeId orderType = null;

  public static final String SERIALIZED_NAME_ORDER_UPDATED_BY = "OrderUpdatedBy";
  @SerializedName(SERIALIZED_NAME_ORDER_UPDATED_BY)
  private List<OrderUpdatedBy> orderUpdatedBy = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PACKAGE_COUNT = "PackageCount";
  @SerializedName(SERIALIZED_NAME_PACKAGE_COUNT)
  private Integer packageCount;

  public static final String SERIALIZED_NAME_PARENT_RESERVATION_REQUEST_ID = "ParentReservationRequestId";
  @SerializedName(SERIALIZED_NAME_PARENT_RESERVATION_REQUEST_ID)
  private String parentReservationRequestId;

  public static final String SERIALIZED_NAME_PAYMENT = "Payment";
  @SerializedName(SERIALIZED_NAME_PAYMENT)
  private List<PaymentHeaderDTO> payment = null;

  public static final String SERIALIZED_NAME_PAYMENT_STATUS = "PaymentStatus";
  @SerializedName(SERIALIZED_NAME_PAYMENT_STATUS)
  private PaymentStatusId paymentStatus = null;

  public static final String SERIALIZED_NAME_POST_VOID_REASON = "PostVoidReason";
  @SerializedName(SERIALIZED_NAME_POST_VOID_REASON)
  private ReasonId postVoidReason = null;

  public static final String SERIALIZED_NAME_PRIORITY = "Priority";
  @SerializedName(SERIALIZED_NAME_PRIORITY)
  private Long priority;

  public static final String SERIALIZED_NAME_PROCESS_INFO = "ProcessInfo";
  @SerializedName(SERIALIZED_NAME_PROCESS_INFO)
  private ProcessInfo processInfo = null;

  public static final String SERIALIZED_NAME_PROCESS_RETURN_COMMENTS = "ProcessReturnComments";
  @SerializedName(SERIALIZED_NAME_PROCESS_RETURN_COMMENTS)
  private String processReturnComments;

  public static final String SERIALIZED_NAME_PROCESS_RETURN_REASON = "ProcessReturnReason";
  @SerializedName(SERIALIZED_NAME_PROCESS_RETURN_REASON)
  private ReasonId processReturnReason = null;

  public static final String SERIALIZED_NAME_PUBLISH_STATUS = "PublishStatus";
  @SerializedName(SERIALIZED_NAME_PUBLISH_STATUS)
  private PublishStatusId publishStatus = null;

  public static final String SERIALIZED_NAME_REFUND_PAYMENT_METHOD = "RefundPaymentMethod";
  @SerializedName(SERIALIZED_NAME_REFUND_PAYMENT_METHOD)
  private RefundPaymentMethodId refundPaymentMethod = null;

  public static final String SERIALIZED_NAME_REFUND_RECIPIENT = "RefundRecipient";
  @SerializedName(SERIALIZED_NAME_REFUND_RECIPIENT)
  private RefundRecipientId refundRecipient = null;

  public static final String SERIALIZED_NAME_RELEASE = "Release";
  @SerializedName(SERIALIZED_NAME_RELEASE)
  private List<Release> release = null;

  public static final String SERIALIZED_NAME_RETURN_LABEL = "ReturnLabel";
  @SerializedName(SERIALIZED_NAME_RETURN_LABEL)
  private List<ReturnLabel> returnLabel = null;

  public static final String SERIALIZED_NAME_RETURN_LABEL_EMAIL = "ReturnLabelEmail";
  @SerializedName(SERIALIZED_NAME_RETURN_LABEL_EMAIL)
  private String returnLabelEmail;

  public static final String SERIALIZED_NAME_RETURN_LINE_COUNT = "ReturnLineCount";
  @SerializedName(SERIALIZED_NAME_RETURN_LINE_COUNT)
  private Long returnLineCount;

  public static final String SERIALIZED_NAME_RETURN_STATUS = "ReturnStatus";
  @SerializedName(SERIALIZED_NAME_RETURN_STATUS)
  private String returnStatus;

  public static final String SERIALIZED_NAME_RETURN_TOTAL_WITHOUT_FEES = "ReturnTotalWithoutFees";
  @SerializedName(SERIALIZED_NAME_RETURN_TOTAL_WITHOUT_FEES)
  private BigDecimal returnTotalWithoutFees;

  public static final String SERIALIZED_NAME_RETURN_TRACKING_DETAIL = "ReturnTrackingDetail";
  @SerializedName(SERIALIZED_NAME_RETURN_TRACKING_DETAIL)
  private List<ReturnTrackingDetail> returnTrackingDetail = null;

  public static final String SERIALIZED_NAME_RUN_ID = "RunId";
  @SerializedName(SERIALIZED_NAME_RUN_ID)
  private String runId;

  public static final String SERIALIZED_NAME_SELLING_CHANNEL = "SellingChannel";
  @SerializedName(SERIALIZED_NAME_SELLING_CHANNEL)
  private SellingChannelId sellingChannel = null;

  public static final String SERIALIZED_NAME_SELLING_LOCATION_ID = "SellingLocationId";
  @SerializedName(SERIALIZED_NAME_SELLING_LOCATION_ID)
  private String sellingLocationId;

  public static final String SERIALIZED_NAME_STORE_RETURN_COUNT = "StoreReturnCount";
  @SerializedName(SERIALIZED_NAME_STORE_RETURN_COUNT)
  private Long storeReturnCount;

  public static final String SERIALIZED_NAME_STORE_SALE_COUNT = "StoreSaleCount";
  @SerializedName(SERIALIZED_NAME_STORE_SALE_COUNT)
  private Long storeSaleCount;

  public static final String SERIALIZED_NAME_SUGGESTED_PROMO = "SuggestedPromo";
  @SerializedName(SERIALIZED_NAME_SUGGESTED_PROMO)
  private List<SuggestedPromo> suggestedPromo = null;

  public static final String SERIALIZED_NAME_SUSPENDED_ORDER_ID = "SuspendedOrderId";
  @SerializedName(SERIALIZED_NAME_SUSPENDED_ORDER_ID)
  private String suspendedOrderId;

  public static final String SERIALIZED_NAME_TAX_EXEMPT_COMMENTS = "TaxExemptComments";
  @SerializedName(SERIALIZED_NAME_TAX_EXEMPT_COMMENTS)
  private String taxExemptComments;

  public static final String SERIALIZED_NAME_TAX_EXEMPT_ID = "TaxExemptId";
  @SerializedName(SERIALIZED_NAME_TAX_EXEMPT_ID)
  private String taxExemptId;

  public static final String SERIALIZED_NAME_TAX_EXEMPT_REASON = "TaxExemptReason";
  @SerializedName(SERIALIZED_NAME_TAX_EXEMPT_REASON)
  private ReasonId taxExemptReason = null;

  public static final String SERIALIZED_NAME_TAX_OVERRIDE_PERC_VALUE = "TaxOverridePercValue";
  @SerializedName(SERIALIZED_NAME_TAX_OVERRIDE_PERC_VALUE)
  private BigDecimal taxOverridePercValue;

  public static final String SERIALIZED_NAME_TAX_OVERRIDE_REASON = "TaxOverrideReason";
  @SerializedName(SERIALIZED_NAME_TAX_OVERRIDE_REASON)
  private ReasonId taxOverrideReason = null;

  public static final String SERIALIZED_NAME_TAX_OVERRIDE_TYPE = "TaxOverrideType";
  @SerializedName(SERIALIZED_NAME_TAX_OVERRIDE_TYPE)
  private TaxOverrideTypeId taxOverrideType = null;

  public static final String SERIALIZED_NAME_TAX_OVERRIDE_VALUE = "TaxOverrideValue";
  @SerializedName(SERIALIZED_NAME_TAX_OVERRIDE_VALUE)
  private BigDecimal taxOverrideValue;

  public static final String SERIALIZED_NAME_TOTAL_CHARGES = "TotalCharges";
  @SerializedName(SERIALIZED_NAME_TOTAL_CHARGES)
  private BigDecimal totalCharges;

  public static final String SERIALIZED_NAME_TOTAL_DISCOUNTS = "TotalDiscounts";
  @SerializedName(SERIALIZED_NAME_TOTAL_DISCOUNTS)
  private BigDecimal totalDiscounts;

  public static final String SERIALIZED_NAME_TOTAL_INFORMATIONAL_TAXES = "TotalInformationalTaxes";
  @SerializedName(SERIALIZED_NAME_TOTAL_INFORMATIONAL_TAXES)
  private BigDecimal totalInformationalTaxes;

  public static final String SERIALIZED_NAME_TOTAL_RETURN_FEES = "TotalReturnFees";
  @SerializedName(SERIALIZED_NAME_TOTAL_RETURN_FEES)
  private BigDecimal totalReturnFees;

  public static final String SERIALIZED_NAME_TOTAL_TAXES = "TotalTaxes";
  @SerializedName(SERIALIZED_NAME_TOTAL_TAXES)
  private BigDecimal totalTaxes;

  public static final String SERIALIZED_NAME_TRANSACTION_REFERENCE = "TransactionReference";
  @SerializedName(SERIALIZED_NAME_TRANSACTION_REFERENCE)
  private List<TransactionReference> transactionReference = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ELIGIBLE_REFUND_CHARGES = "eligibleRefundCharges";
  @SerializedName(SERIALIZED_NAME_ELIGIBLE_REFUND_CHARGES)
  private OrderEligibleRefundChargesDTO eligibleRefundCharges = null;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public Order actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public Order putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public Order alternateOrderId(String alternateOrderId) {
    this.alternateOrderId = alternateOrderId;
    return this;
  }

   /**
   * Unique alternate identifier of order, as defined by external system
   * @return alternateOrderId
  **/
  
  public String getAlternateOrderId() {
    return alternateOrderId;
  }

  public void setAlternateOrderId(String alternateOrderId) {
    this.alternateOrderId = alternateOrderId;
  }

   /**
   * Date on which order would be archived post completion or cancellation
   * @return archiveDate
  **/
  
  public OffsetDateTime getArchiveDate() {
    return archiveDate;
  }

  public Order balanceDue(BigDecimal balanceDue) {
    this.balanceDue = balanceDue;
    return this;
  }

   /**
   * Get balanceDue
   * @return balanceDue
  **/
  
  public BigDecimal getBalanceDue() {
    return balanceDue;
  }

  public void setBalanceDue(BigDecimal balanceDue) {
    this.balanceDue = balanceDue;
  }

  public Order businessDate(LocalDate businessDate) {
    this.businessDate = businessDate;
    return this;
  }

   /**
   * Store&#39;s Business Date on which the order is created.  This can be different from the capture date if the store is opened through more than one calendar date.
   * @return businessDate
  **/
  
  public LocalDate getBusinessDate() {
    return businessDate;
  }

  public void setBusinessDate(LocalDate businessDate) {
    this.businessDate = businessDate;
  }

  public Order calculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
    return this;
  }

   /**
   * Get calculatedValues
   * @return calculatedValues
  **/
  
  public Object getCalculatedValues() {
    return calculatedValues;
  }

  public void setCalculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
  }

  public Order cancelComments(String cancelComments) {
    this.cancelComments = cancelComments;
    return this;
  }

   /**
   * If isCancelled is true, these are comments entered for the cancellation
   * @return cancelComments
  **/
  
  public String getCancelComments() {
    return cancelComments;
  }

  public void setCancelComments(String cancelComments) {
    this.cancelComments = cancelComments;
  }

   /**
   * Total number of cancelled lines in order
   * minimum: 0
   * maximum: -8446744073709551617
   * @return cancelLineCount
  **/
  
  public Long getCancelLineCount() {
    return cancelLineCount;
  }

  public Order cancelReason(ReasonId cancelReason) {
    this.cancelReason = cancelReason;
    return this;
  }

   /**
   * Get cancelReason
   * @return cancelReason
  **/
  
  public ReasonId getCancelReason() {
    return cancelReason;
  }

  public void setCancelReason(ReasonId cancelReason) {
    this.cancelReason = cancelReason;
  }

   /**
   * Sum of all cancelled lines&#39; SubTotal. If the entire order gets cancelled, OrderSubTotal value is moved to this field, Order. OrderSubTotal becomes 0.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return cancelledOrderSubTotal
  **/
  
  public BigDecimal getCancelledOrderSubTotal() {
    return cancelledOrderSubTotal;
  }

   /**
   * Sum of all cancelled order total. If the entire order gets cancelled, OrderTotal value is moved to this field, Order. OrderTotal becomes 0.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return cancelledOrderTotal
  **/
  
  public BigDecimal getCancelledOrderTotal() {
    return cancelledOrderTotal;
  }

   /**
   * Sum of the discounts applied on cancelled lines. If the entire order gets cancelled, Order.totalDiscounts is set to 0, value is moved to this field.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return cancelledTotalDiscounts
  **/
  
  public BigDecimal getCancelledTotalDiscounts() {
    return cancelledTotalDiscounts;
  }

  public Order capturedDate(OffsetDateTime capturedDate) {
    this.capturedDate = capturedDate;
    return this;
  }

   /**
   * Date of initial order capture from the order capture system
   * @return capturedDate
  **/
  
  public OffsetDateTime getCapturedDate() {
    return capturedDate;
  }

  public void setCapturedDate(OffsetDateTime capturedDate) {
    this.capturedDate = capturedDate;
  }

  public Order changeLog(ChangeLog changeLog) {
    this.changeLog = changeLog;
    return this;
  }

   /**
   * Get changeLog
   * @return changeLog
  **/
  
  public ChangeLog getChangeLog() {
    return changeLog;
  }

  public void setChangeLog(ChangeLog changeLog) {
    this.changeLog = changeLog;
  }

  public Order collectedAmount(BigDecimal collectedAmount) {
    this.collectedAmount = collectedAmount;
    return this;
  }

   /**
   * Get collectedAmount
   * @return collectedAmount
  **/
  
  public BigDecimal getCollectedAmount() {
    return collectedAmount;
  }

  public void setCollectedAmount(BigDecimal collectedAmount) {
    this.collectedAmount = collectedAmount;
  }

  public Order confirmedDate(OffsetDateTime confirmedDate) {
    this.confirmedDate = confirmedDate;
    return this;
  }

   /**
   * Date of the order confirmation
   * @return confirmedDate
  **/
  
  public OffsetDateTime getConfirmedDate() {
    return confirmedDate;
  }

  public void setConfirmedDate(OffsetDateTime confirmedDate) {
    this.confirmedDate = confirmedDate;
  }

  public Order contactPreference(List<ContactPreference> contactPreference) {
    this.contactPreference = contactPreference;
    return this;
  }

  public Order addContactPreferenceItem(ContactPreference contactPreferenceItem) {
    if (this.contactPreference == null) {
      this.contactPreference = new ArrayList<ContactPreference>();
    }
    this.contactPreference.add(contactPreferenceItem);
    return this;
  }

   /**
   * Get contactPreference
   * @return contactPreference
  **/
  
  public List<ContactPreference> getContactPreference() {
    return contactPreference;
  }

  public void setContactPreference(List<ContactPreference> contactPreference) {
    this.contactPreference = contactPreference;
  }

   /**
   * This column is used for storing the date when the order is confirmed
   * @return countedDate
  **/
  
  public OffsetDateTime getCountedDate() {
    return countedDate;
  }

  public Order createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public Order createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public Order currencyCode(String currencyCode) {
    this.currencyCode = currencyCode;
    return this;
  }

   /**
   * Currency code to be used for processing the order payment
   * @return currencyCode
  **/
  
  public String getCurrencyCode() {
    return currencyCode;
  }

  public void setCurrencyCode(String currencyCode) {
    this.currencyCode = currencyCode;
  }

  public Order customerAddress(Address customerAddress) {
    this.customerAddress = customerAddress;
    return this;
  }

   /**
   * Get customerAddress
   * @return customerAddress
  **/
  
  public Address getCustomerAddress() {
    return customerAddress;
  }

  public void setCustomerAddress(Address customerAddress) {
    this.customerAddress = customerAddress;
  }

  public Order customerEmail(String customerEmail) {
    this.customerEmail = customerEmail;
    return this;
  }

   /**
   * E-mail address for the customer on this order
   * @return customerEmail
  **/
  
  public String getCustomerEmail() {
    return customerEmail;
  }

  public void setCustomerEmail(String customerEmail) {
    this.customerEmail = customerEmail;
  }

  public Order customerFirstName(String customerFirstName) {
    this.customerFirstName = customerFirstName;
    return this;
  }

   /**
   * First name for the customer on this order
   * @return customerFirstName
  **/
  
  public String getCustomerFirstName() {
    return customerFirstName;
  }

  public void setCustomerFirstName(String customerFirstName) {
    this.customerFirstName = customerFirstName;
  }

  public Order customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

   /**
   * Unique ID used to identify the customer
   * @return customerId
  **/
  
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public Order customerIdentityDoc(List<CustomerIdentityDoc> customerIdentityDoc) {
    this.customerIdentityDoc = customerIdentityDoc;
    return this;
  }

  public Order addCustomerIdentityDocItem(CustomerIdentityDoc customerIdentityDocItem) {
    if (this.customerIdentityDoc == null) {
      this.customerIdentityDoc = new ArrayList<CustomerIdentityDoc>();
    }
    this.customerIdentityDoc.add(customerIdentityDocItem);
    return this;
  }

   /**
   * Get customerIdentityDoc
   * @return customerIdentityDoc
  **/
  
  public List<CustomerIdentityDoc> getCustomerIdentityDoc() {
    return customerIdentityDoc;
  }

  public void setCustomerIdentityDoc(List<CustomerIdentityDoc> customerIdentityDoc) {
    this.customerIdentityDoc = customerIdentityDoc;
  }

  public Order customerLastName(String customerLastName) {
    this.customerLastName = customerLastName;
    return this;
  }

   /**
   * Last name for the customer on this order
   * @return customerLastName
  **/
  
  public String getCustomerLastName() {
    return customerLastName;
  }

  public void setCustomerLastName(String customerLastName) {
    this.customerLastName = customerLastName;
  }

  public Order customerPhone(String customerPhone) {
    this.customerPhone = customerPhone;
    return this;
  }

   /**
   * Phone number for the customer on this order
   * @return customerPhone
  **/
  
  public String getCustomerPhone() {
    return customerPhone;
  }

  public void setCustomerPhone(String customerPhone) {
    this.customerPhone = customerPhone;
  }

  public Order customerSignature(String customerSignature) {
    this.customerSignature = customerSignature;
    return this;
  }

   /**
   * This attribute is used to persist customer signature captured at point of sale.
   * @return customerSignature
  **/
  
  public String getCustomerSignature() {
    return customerSignature;
  }

  public void setCustomerSignature(String customerSignature) {
    this.customerSignature = customerSignature;
  }

  public Order customerTypeId(String customerTypeId) {
    this.customerTypeId = customerTypeId;
    return this;
  }

   /**
   * The Customer Type for the customer on this Order. The Customer type is used to differentiate categories of customers
   * @return customerTypeId
  **/
  
  public String getCustomerTypeId() {
    return customerTypeId;
  }

  public void setCustomerTypeId(String customerTypeId) {
    this.customerTypeId = customerTypeId;
  }

  public Order doNotReleaseBefore(OffsetDateTime doNotReleaseBefore) {
    this.doNotReleaseBefore = doNotReleaseBefore;
    return this;
  }

   /**
   * Order quantities cannot be released before this date and time. This value can be imported on the order, or it can be calculated by the system as a specified amount of time after an order has been allocated. To specify the amount of time, this can be configured on the milestone expected time rule.
   * @return doNotReleaseBefore
  **/
  
  public OffsetDateTime getDoNotReleaseBefore() {
    return doNotReleaseBefore;
  }

  public void setDoNotReleaseBefore(OffsetDateTime doNotReleaseBefore) {
    this.doNotReleaseBefore = doNotReleaseBefore;
  }

  public Order docType(DocTypeId docType) {
    this.docType = docType;
    return this;
  }

   /**
   * Get docType
   * @return docType
  **/
  
  public DocTypeId getDocType() {
    return docType;
  }

  public void setDocType(DocTypeId docType) {
    this.docType = docType;
  }

   /**
   * This column is used by milestone scheduler to set the time at which it picked the order for processing
   * @return eventSubmitTime
  **/
  
  public OffsetDateTime getEventSubmitTime() {
    return eventSubmitTime;
  }

  public Order extended(Map<String, Object> extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Map<String, Object> getExtended() {
    return extended;
  }

  public void setExtended(Map<String, Object> extended) {
    this.extended = extended;
  }

  public Order fulfillmentStatus(String fulfillmentStatus) {
    this.fulfillmentStatus = fulfillmentStatus;
    return this;
  }

   /**
   * Get fulfillmentStatus
   * @return fulfillmentStatus
  **/
  
  public String getFulfillmentStatus() {
    return fulfillmentStatus;
  }

  public void setFulfillmentStatus(String fulfillmentStatus) {
    this.fulfillmentStatus = fulfillmentStatus;
  }

  public Order invoice(List<Invoice> invoice) {
    this.invoice = invoice;
    return this;
  }

  public Order addInvoiceItem(Invoice invoiceItem) {
    if (this.invoice == null) {
      this.invoice = new ArrayList<Invoice>();
    }
    this.invoice.add(invoiceItem);
    return this;
  }

   /**
   * Get invoice
   * @return invoice
  **/
  
  public List<Invoice> getInvoice() {
    return invoice;
  }

  public void setInvoice(List<Invoice> invoice) {
    this.invoice = invoice;
  }

   /**
   * Indicates if the order is under going archival process
   * @return isArchiveInProgress
  **/
  
  public Boolean getIsArchiveInProgress() {
    return isArchiveInProgress;
  }

  public Order isCancelled(Boolean isCancelled) {
    this.isCancelled = isCancelled;
    return this;
  }

   /**
   * Indicates if the order is cancelled
   * @return isCancelled
  **/
  
  public Boolean getIsCancelled() {
    return isCancelled;
  }

  public void setIsCancelled(Boolean isCancelled) {
    this.isCancelled = isCancelled;
  }

  public Order isCapturedOffline(Boolean isCapturedOffline) {
    this.isCapturedOffline = isCapturedOffline;
    return this;
  }

   /**
   * If set true ,it indicates that the order has been originally captured off-line i.e. Edge Mode. If set false then order has been captured on-line.
   * @return isCapturedOffline
  **/
  
  public Boolean getIsCapturedOffline() {
    return isCapturedOffline;
  }

  public void setIsCapturedOffline(Boolean isCapturedOffline) {
    this.isCapturedOffline = isCapturedOffline;
  }

  public Order isConfirmed(Boolean isConfirmed) {
    this.isConfirmed = isConfirmed;
    return this;
  }

   /**
   * Indicates if the order is confirmed
   * @return isConfirmed
  **/
  
  public Boolean getIsConfirmed() {
    return isConfirmed;
  }

  public void setIsConfirmed(Boolean isConfirmed) {
    this.isConfirmed = isConfirmed;
  }

  public Order isOnHold(Boolean isOnHold) {
    this.isOnHold = isOnHold;
    return this;
  }

   /**
   * Indicates if the order is on hold
   * @return isOnHold
  **/
  
  public Boolean getIsOnHold() {
    return isOnHold;
  }

  public void setIsOnHold(Boolean isOnHold) {
    this.isOnHold = isOnHold;
  }

   /**
   * This Column is used to check whether to count this Order
   * @return isOrderCountable
  **/
  
  public Boolean getIsOrderCountable() {
    return isOrderCountable;
  }

  public Order isPostVoided(Boolean isPostVoided) {
    this.isPostVoided = isPostVoided;
    return this;
  }

   /**
   * Indicates if the order is post-voided
   * @return isPostVoided
  **/
  
  public Boolean getIsPostVoided() {
    return isPostVoided;
  }

  public void setIsPostVoided(Boolean isPostVoided) {
    this.isPostVoided = isPostVoided;
  }

  public Order isReadyForTender(Boolean isReadyForTender) {
    this.isReadyForTender = isReadyForTender;
    return this;
  }

   /**
   * Indicates if order is ready to be tendered.  If true, order component will invoke payment component to process payment.
   * @return isReadyForTender
  **/
  
  public Boolean getIsReadyForTender() {
    return isReadyForTender;
  }

  public void setIsReadyForTender(Boolean isReadyForTender) {
    this.isReadyForTender = isReadyForTender;
  }

  public Order isTaxExempt(Boolean isTaxExempt) {
    this.isTaxExempt = isTaxExempt;
    return this;
  }

   /**
   * Indicates if order is tax exempt
   * @return isTaxExempt
  **/
  
  public Boolean getIsTaxExempt() {
    return isTaxExempt;
  }

  public void setIsTaxExempt(Boolean isTaxExempt) {
    this.isTaxExempt = isTaxExempt;
  }

  public Order isTaxOverridden(Boolean isTaxOverridden) {
    this.isTaxOverridden = isTaxOverridden;
    return this;
  }

   /**
   * Indicates if the tax has been overridden
   * @return isTaxOverridden
  **/
  
  public Boolean getIsTaxOverridden() {
    return isTaxOverridden;
  }

  public void setIsTaxOverridden(Boolean isTaxOverridden) {
    this.isTaxOverridden = isTaxOverridden;
  }

   /**
   * This property is introduced to handle unArchive failure scenarios.UnArchiving is a multi step process, if any step is failed after Order is loaded from search, this flag value set to true, so that on next unArchive process, it tries to resume the remaining process
   * @return isUnArchiveInProgress
  **/
  
  public Boolean getIsUnArchiveInProgress() {
    return isUnArchiveInProgress;
  }

  public Order localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public Order loyaltyNumber(String loyaltyNumber) {
    this.loyaltyNumber = loyaltyNumber;
    return this;
  }

   /**
   * This attribute is to capture the loyalty number of the customer on the order, this data can be used in user exits to validate with 3rd party application and come back with rewards to the customer.
   * @return loyaltyNumber
  **/
  
  public String getLoyaltyNumber() {
    return loyaltyNumber;
  }

  public void setLoyaltyNumber(String loyaltyNumber) {
    this.loyaltyNumber = loyaltyNumber;
  }

  public Order managerAuthDetail(List<ManagerAuthDetail> managerAuthDetail) {
    this.managerAuthDetail = managerAuthDetail;
    return this;
  }

  public Order addManagerAuthDetailItem(ManagerAuthDetail managerAuthDetailItem) {
    if (this.managerAuthDetail == null) {
      this.managerAuthDetail = new ArrayList<ManagerAuthDetail>();
    }
    this.managerAuthDetail.add(managerAuthDetailItem);
    return this;
  }

   /**
   * Get managerAuthDetail
   * @return managerAuthDetail
  **/
  
  public List<ManagerAuthDetail> getManagerAuthDetail() {
    return managerAuthDetail;
  }

  public void setManagerAuthDetail(List<ManagerAuthDetail> managerAuthDetail) {
    this.managerAuthDetail = managerAuthDetail;
  }

  public Order maxAppeasementAmount(BigDecimal maxAppeasementAmount) {
    this.maxAppeasementAmount = maxAppeasementAmount;
    return this;
  }

   /**
   * Get maxAppeasementAmount
   * @return maxAppeasementAmount
  **/
  
  public BigDecimal getMaxAppeasementAmount() {
    return maxAppeasementAmount;
  }

  public void setMaxAppeasementAmount(BigDecimal maxAppeasementAmount) {
    this.maxAppeasementAmount = maxAppeasementAmount;
  }

  public Order maxFulfillmentStatus(KeyDTO maxFulfillmentStatus) {
    this.maxFulfillmentStatus = maxFulfillmentStatus;
    return this;
  }

   /**
   * Get maxFulfillmentStatus
   * @return maxFulfillmentStatus
  **/
  
  public KeyDTO getMaxFulfillmentStatus() {
    return maxFulfillmentStatus;
  }

  public void setMaxFulfillmentStatus(KeyDTO maxFulfillmentStatus) {
    this.maxFulfillmentStatus = maxFulfillmentStatus;
  }

   /**
   * Indicates the maximum fulfillment lifecycle status of all units on the order
   * @return maxFulfillmentStatusId
  **/
  
  public String getMaxFulfillmentStatusId() {
    return maxFulfillmentStatusId;
  }

  public void setMaxFulfillmentStatusId(String maxFulfillmentStatusId) {
    this.maxFulfillmentStatusId = maxFulfillmentStatusId;
  }

  public Order maxReturnStatus(KeyDTO maxReturnStatus) {
    this.maxReturnStatus = maxReturnStatus;
    return this;
  }

   /**
   * Get maxReturnStatus
   * @return maxReturnStatus
  **/
  
  public KeyDTO getMaxReturnStatus() {
    return maxReturnStatus;
  }

  public void setMaxReturnStatus(KeyDTO maxReturnStatus) {
    this.maxReturnStatus = maxReturnStatus;
  }

   /**
   * Indicates the maximum status of all Return lines within Order. It consider all the lines for which IsReturn is true and among such lines this is the maximum status.
   * @return maxReturnStatusId
  **/
  
  public String getMaxReturnStatusId() {
    return maxReturnStatusId;
  }

  public void setMaxReturnStatusId(String maxReturnStatusId) {
    this.maxReturnStatusId = maxReturnStatusId;
  }

   /**
   * This field indicates the total merchandise items (Orderline.isNonMerchandise&#x3D;False) returned at store. This is not determined by the delivery option. Items bought across channels, returned at store are considered. The value should get incremented when an line is added where isReturn&#x3D;True. Decremented when the line is voided or cancelled.
   * minimum: 0
   * maximum: -8446744073709551617
   * @return merchReturnLineCount
  **/
  
  public Long getMerchReturnLineCount() {
    return merchReturnLineCount;
  }

   /**
   * This field indicates the total merchandise items (Orderline.isNonMerchandise&#x3D;False) sold in an order, with delivery option&#x3D;In store sale,Ship to address/Ship to store/Pick up . The value should get incremented when an In store line is added, decremented if canceled or removed or delivery option changes.
   * minimum: 0
   * maximum: -8446744073709551617
   * @return merchSaleLineCount
  **/
  
  public Long getMerchSaleLineCount() {
    return merchSaleLineCount;
  }

  public Order messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public Order minFulfillmentStatus(KeyDTO minFulfillmentStatus) {
    this.minFulfillmentStatus = minFulfillmentStatus;
    return this;
  }

   /**
   * Get minFulfillmentStatus
   * @return minFulfillmentStatus
  **/
  
  public KeyDTO getMinFulfillmentStatus() {
    return minFulfillmentStatus;
  }

  public void setMinFulfillmentStatus(KeyDTO minFulfillmentStatus) {
    this.minFulfillmentStatus = minFulfillmentStatus;
  }

   /**
   * Indicates the minimum fulfillment lifecycle status of all units on the order
   * @return minFulfillmentStatusId
  **/
  
  public String getMinFulfillmentStatusId() {
    return minFulfillmentStatusId;
  }

  public void setMinFulfillmentStatusId(String minFulfillmentStatusId) {
    this.minFulfillmentStatusId = minFulfillmentStatusId;
  }

  public Order minReturnStatus(KeyDTO minReturnStatus) {
    this.minReturnStatus = minReturnStatus;
    return this;
  }

   /**
   * Get minReturnStatus
   * @return minReturnStatus
  **/
  
  public KeyDTO getMinReturnStatus() {
    return minReturnStatus;
  }

  public void setMinReturnStatus(KeyDTO minReturnStatus) {
    this.minReturnStatus = minReturnStatus;
  }

   /**
   * Indicates the minimum status of all Return lines within Order. It consider all the lines for which IsReturn is true and among such lines this is the minimum status.
   * @return minReturnStatusId
  **/
  
  public String getMinReturnStatusId() {
    return minReturnStatusId;
  }

   /**
   * This column used by monitoring framework to set the time at which the immediate/next event has to occur
   * @return nextEventTime
  **/
  
  public OffsetDateTime getNextEventTime() {
    return nextEventTime;
  }

  public Order orderActions(Map<String, Object> orderActions) {
    this.orderActions = orderActions;
    return this;
  }

  public Order putOrderActionsItem(String key, Object orderActionsItem) {
    if (this.orderActions == null) {
      this.orderActions = new HashMap<String, Object>();
    }
    this.orderActions.put(key, orderActionsItem);
    return this;
  }

   /**
   *   All allowed values (IsAlreadyPriced,IsAlreadyCharged,IsReturnFeeApplied,IsAlreadyTaxed and IsImport) are Boolean transient fields which are valid for single transaction only.  For IsAlreadyPriced,IsAlreadyCharged,IsReturnFeeApplied,IsAlreadyTaxed attributes -   If the value is passed as true while creating or updating the order then call to respective component will be ignored for that particular transaction.  If its value is false or the attribute itself is not present, then respective component will be called based on order modification.  For example, it can be useful, while importing Ecom orders which is already priced to Order component.  For IsImport attribute - If value is passed as true while creating or updating the order then all the modification type on order will be suppressed.  If its value is passed as false or the attribute itself is not present, then all the modification type on order will be executed.  For example, it could be used to import fulfilled Order from Legacy system.
   * @return orderActions
  **/
  
  public Map<String, Object> getOrderActions() {
    return orderActions;
  }

  public void setOrderActions(Map<String, Object> orderActions) {
    this.orderActions = orderActions;
  }

  public Order orderAttribute(List<OrderAttribute> orderAttribute) {
    this.orderAttribute = orderAttribute;
    return this;
  }

  public Order addOrderAttributeItem(OrderAttribute orderAttributeItem) {
    if (this.orderAttribute == null) {
      this.orderAttribute = new ArrayList<OrderAttribute>();
    }
    this.orderAttribute.add(orderAttributeItem);
    return this;
  }

   /**
   * Get orderAttribute
   * @return orderAttribute
  **/
  
  public List<OrderAttribute> getOrderAttribute() {
    return orderAttribute;
  }

  public void setOrderAttribute(List<OrderAttribute> orderAttribute) {
    this.orderAttribute = orderAttribute;
  }

  public Order orderCaptureDetail(List<OrderCaptureDetail> orderCaptureDetail) {
    this.orderCaptureDetail = orderCaptureDetail;
    return this;
  }

  public Order addOrderCaptureDetailItem(OrderCaptureDetail orderCaptureDetailItem) {
    if (this.orderCaptureDetail == null) {
      this.orderCaptureDetail = new ArrayList<OrderCaptureDetail>();
    }
    this.orderCaptureDetail.add(orderCaptureDetailItem);
    return this;
  }

   /**
   * Get orderCaptureDetail
   * @return orderCaptureDetail
  **/
  
  public List<OrderCaptureDetail> getOrderCaptureDetail() {
    return orderCaptureDetail;
  }

  public void setOrderCaptureDetail(List<OrderCaptureDetail> orderCaptureDetail) {
    this.orderCaptureDetail = orderCaptureDetail;
  }

  public Order orderChargeDetail(List<OrderChargeDetail> orderChargeDetail) {
    this.orderChargeDetail = orderChargeDetail;
    return this;
  }

  public Order addOrderChargeDetailItem(OrderChargeDetail orderChargeDetailItem) {
    if (this.orderChargeDetail == null) {
      this.orderChargeDetail = new ArrayList<OrderChargeDetail>();
    }
    this.orderChargeDetail.add(orderChargeDetailItem);
    return this;
  }

   /**
   * Get orderChargeDetail
   * @return orderChargeDetail
  **/
  
  public List<OrderChargeDetail> getOrderChargeDetail() {
    return orderChargeDetail;
  }

  public void setOrderChargeDetail(List<OrderChargeDetail> orderChargeDetail) {
    this.orderChargeDetail = orderChargeDetail;
  }

  public Order orderExtension1(OrderExtension1 orderExtension1) {
    this.orderExtension1 = orderExtension1;
    return this;
  }

   /**
   * Get orderExtension1
   * @return orderExtension1
  **/
  
  public OrderExtension1 getOrderExtension1() {
    return orderExtension1;
  }

  public void setOrderExtension1(OrderExtension1 orderExtension1) {
    this.orderExtension1 = orderExtension1;
  }

  public Order orderExtension2(List<OrderExtension2> orderExtension2) {
    this.orderExtension2 = orderExtension2;
    return this;
  }

  public Order addOrderExtension2Item(OrderExtension2 orderExtension2Item) {
    if (this.orderExtension2 == null) {
      this.orderExtension2 = new ArrayList<OrderExtension2>();
    }
    this.orderExtension2.add(orderExtension2Item);
    return this;
  }

   /**
   * Get orderExtension2
   * @return orderExtension2
  **/
  
  public List<OrderExtension2> getOrderExtension2() {
    return orderExtension2;
  }

  public void setOrderExtension2(List<OrderExtension2> orderExtension2) {
    this.orderExtension2 = orderExtension2;
  }

  public Order orderExtension3(List<OrderExtension3> orderExtension3) {
    this.orderExtension3 = orderExtension3;
    return this;
  }

  public Order addOrderExtension3Item(OrderExtension3 orderExtension3Item) {
    if (this.orderExtension3 == null) {
      this.orderExtension3 = new ArrayList<OrderExtension3>();
    }
    this.orderExtension3.add(orderExtension3Item);
    return this;
  }

   /**
   * Get orderExtension3
   * @return orderExtension3
  **/
  
  public List<OrderExtension3> getOrderExtension3() {
    return orderExtension3;
  }

  public void setOrderExtension3(List<OrderExtension3> orderExtension3) {
    this.orderExtension3 = orderExtension3;
  }

  public Order orderExtension4(List<OrderExtension4> orderExtension4) {
    this.orderExtension4 = orderExtension4;
    return this;
  }

  public Order addOrderExtension4Item(OrderExtension4 orderExtension4Item) {
    if (this.orderExtension4 == null) {
      this.orderExtension4 = new ArrayList<OrderExtension4>();
    }
    this.orderExtension4.add(orderExtension4Item);
    return this;
  }

   /**
   * Get orderExtension4
   * @return orderExtension4
  **/
  
  public List<OrderExtension4> getOrderExtension4() {
    return orderExtension4;
  }

  public void setOrderExtension4(List<OrderExtension4> orderExtension4) {
    this.orderExtension4 = orderExtension4;
  }

  public Order orderExtension5(List<OrderExtension5> orderExtension5) {
    this.orderExtension5 = orderExtension5;
    return this;
  }

  public Order addOrderExtension5Item(OrderExtension5 orderExtension5Item) {
    if (this.orderExtension5 == null) {
      this.orderExtension5 = new ArrayList<OrderExtension5>();
    }
    this.orderExtension5.add(orderExtension5Item);
    return this;
  }

   /**
   * Get orderExtension5
   * @return orderExtension5
  **/
  
  public List<OrderExtension5> getOrderExtension5() {
    return orderExtension5;
  }

  public void setOrderExtension5(List<OrderExtension5> orderExtension5) {
    this.orderExtension5 = orderExtension5;
  }

  public Order orderFulfillmentGroups(List<OrderFulfillmentGroupDTO> orderFulfillmentGroups) {
    this.orderFulfillmentGroups = orderFulfillmentGroups;
    return this;
  }

  public Order addOrderFulfillmentGroupsItem(OrderFulfillmentGroupDTO orderFulfillmentGroupsItem) {
    if (this.orderFulfillmentGroups == null) {
      this.orderFulfillmentGroups = new ArrayList<OrderFulfillmentGroupDTO>();
    }
    this.orderFulfillmentGroups.add(orderFulfillmentGroupsItem);
    return this;
  }

   /**
   * Get orderFulfillmentGroups
   * @return orderFulfillmentGroups
  **/
  
  public List<OrderFulfillmentGroupDTO> getOrderFulfillmentGroups() {
    return orderFulfillmentGroups;
  }

  public void setOrderFulfillmentGroups(List<OrderFulfillmentGroupDTO> orderFulfillmentGroups) {
    this.orderFulfillmentGroups = orderFulfillmentGroups;
  }

  public Order orderHold(List<OrderHold> orderHold) {
    this.orderHold = orderHold;
    return this;
  }

  public Order addOrderHoldItem(OrderHold orderHoldItem) {
    if (this.orderHold == null) {
      this.orderHold = new ArrayList<OrderHold>();
    }
    this.orderHold.add(orderHoldItem);
    return this;
  }

   /**
   * Get orderHold
   * @return orderHold
  **/
  
  public List<OrderHold> getOrderHold() {
    return orderHold;
  }

  public void setOrderHold(List<OrderHold> orderHold) {
    this.orderHold = orderHold;
  }

  public Order orderId(String orderId) {
    this.orderId = orderId;
    return this;
  }

   /**
   * Unique identifier of order. If this is not already populated by an external system then the system populates this field with a next-up value
   * @return orderId
  **/
  
  public String getOrderId() {
    return orderId;
  }

  public void setOrderId(String orderId) {
    this.orderId = orderId;
  }

  public Order orderLine(List<OrderLine> orderLine) {
    this.orderLine = orderLine;
    return this;
  }

  public Order addOrderLineItem(OrderLine orderLineItem) {
    if (this.orderLine == null) {
      this.orderLine = new ArrayList<OrderLine>();
    }
    this.orderLine.add(orderLineItem);
    return this;
  }

   /**
   * Get orderLine
   * @return orderLine
  **/
  
  public List<OrderLine> getOrderLine() {
    return orderLine;
  }

  public void setOrderLine(List<OrderLine> orderLine) {
    this.orderLine = orderLine;
  }

   /**
   * Total number of lines including Cancelled line having devliveryMethod as ShipToAddress or ShipToStore or PickUpAtStore or Email
   * minimum: 0
   * maximum: -8446744073709551617
   * @return orderLineCount
  **/
  
  public Long getOrderLineCount() {
    return orderLineCount;
  }

  public Order orderLocale(String orderLocale) {
    this.orderLocale = orderLocale;
    return this;
  }

   /**
   * This specifies the locale in which the order is captured. If item attributes such as description, style, etc. are null on order creation, the attributes will be populated in this locale if translations are available in the item component. If no translations exist for the specified locale, then the default item attributes are saved. This field cannot be edited once an order is created.Valid values are locale codes supported in base like en, es, pt, etc.
   * @return orderLocale
  **/
  
  public String getOrderLocale() {
    return orderLocale;
  }

  public void setOrderLocale(String orderLocale) {
    this.orderLocale = orderLocale;
  }

  public Order orderMilestone(List<OrderMilestone> orderMilestone) {
    this.orderMilestone = orderMilestone;
    return this;
  }

  public Order addOrderMilestoneItem(OrderMilestone orderMilestoneItem) {
    if (this.orderMilestone == null) {
      this.orderMilestone = new ArrayList<OrderMilestone>();
    }
    this.orderMilestone.add(orderMilestoneItem);
    return this;
  }

   /**
   * Get orderMilestone
   * @return orderMilestone
  **/
  
  public List<OrderMilestone> getOrderMilestone() {
    return orderMilestone;
  }

  public void setOrderMilestone(List<OrderMilestone> orderMilestone) {
    this.orderMilestone = orderMilestone;
  }

  public Order orderMilestoneEvent(List<OrderMilestoneEvent> orderMilestoneEvent) {
    this.orderMilestoneEvent = orderMilestoneEvent;
    return this;
  }

  public Order addOrderMilestoneEventItem(OrderMilestoneEvent orderMilestoneEventItem) {
    if (this.orderMilestoneEvent == null) {
      this.orderMilestoneEvent = new ArrayList<OrderMilestoneEvent>();
    }
    this.orderMilestoneEvent.add(orderMilestoneEventItem);
    return this;
  }

   /**
   * Get orderMilestoneEvent
   * @return orderMilestoneEvent
  **/
  
  public List<OrderMilestoneEvent> getOrderMilestoneEvent() {
    return orderMilestoneEvent;
  }

  public void setOrderMilestoneEvent(List<OrderMilestoneEvent> orderMilestoneEvent) {
    this.orderMilestoneEvent = orderMilestoneEvent;
  }

  public Order orderNote(List<OrderNote> orderNote) {
    this.orderNote = orderNote;
    return this;
  }

  public Order addOrderNoteItem(OrderNote orderNoteItem) {
    if (this.orderNote == null) {
      this.orderNote = new ArrayList<OrderNote>();
    }
    this.orderNote.add(orderNoteItem);
    return this;
  }

   /**
   * Get orderNote
   * @return orderNote
  **/
  
  public List<OrderNote> getOrderNote() {
    return orderNote;
  }

  public void setOrderNote(List<OrderNote> orderNote) {
    this.orderNote = orderNote;
  }

  public Order orderPaymentMethod(List<OrderPaymentMethod> orderPaymentMethod) {
    this.orderPaymentMethod = orderPaymentMethod;
    return this;
  }

  public Order addOrderPaymentMethodItem(OrderPaymentMethod orderPaymentMethodItem) {
    if (this.orderPaymentMethod == null) {
      this.orderPaymentMethod = new ArrayList<OrderPaymentMethod>();
    }
    this.orderPaymentMethod.add(orderPaymentMethodItem);
    return this;
  }

   /**
   * Get orderPaymentMethod
   * @return orderPaymentMethod
  **/
  
  public List<OrderPaymentMethod> getOrderPaymentMethod() {
    return orderPaymentMethod;
  }

  public void setOrderPaymentMethod(List<OrderPaymentMethod> orderPaymentMethod) {
    this.orderPaymentMethod = orderPaymentMethod;
  }

  public Order orderPromisingInfo(OrderPromisingInfo orderPromisingInfo) {
    this.orderPromisingInfo = orderPromisingInfo;
    return this;
  }

   /**
   * Get orderPromisingInfo
   * @return orderPromisingInfo
  **/
  
  public OrderPromisingInfo getOrderPromisingInfo() {
    return orderPromisingInfo;
  }

  public void setOrderPromisingInfo(OrderPromisingInfo orderPromisingInfo) {
    this.orderPromisingInfo = orderPromisingInfo;
  }

  public Order orderPromotionRequest(List<OrderPromotionRequest> orderPromotionRequest) {
    this.orderPromotionRequest = orderPromotionRequest;
    return this;
  }

  public Order addOrderPromotionRequestItem(OrderPromotionRequest orderPromotionRequestItem) {
    if (this.orderPromotionRequest == null) {
      this.orderPromotionRequest = new ArrayList<OrderPromotionRequest>();
    }
    this.orderPromotionRequest.add(orderPromotionRequestItem);
    return this;
  }

   /**
   * Get orderPromotionRequest
   * @return orderPromotionRequest
  **/
  
  public List<OrderPromotionRequest> getOrderPromotionRequest() {
    return orderPromotionRequest;
  }

  public void setOrderPromotionRequest(List<OrderPromotionRequest> orderPromotionRequest) {
    this.orderPromotionRequest = orderPromotionRequest;
  }

  public Order orderSalesAssociate(List<OrderSalesAssociate> orderSalesAssociate) {
    this.orderSalesAssociate = orderSalesAssociate;
    return this;
  }

  public Order addOrderSalesAssociateItem(OrderSalesAssociate orderSalesAssociateItem) {
    if (this.orderSalesAssociate == null) {
      this.orderSalesAssociate = new ArrayList<OrderSalesAssociate>();
    }
    this.orderSalesAssociate.add(orderSalesAssociateItem);
    return this;
  }

   /**
   * Get orderSalesAssociate
   * @return orderSalesAssociate
  **/
  
  public List<OrderSalesAssociate> getOrderSalesAssociate() {
    return orderSalesAssociate;
  }

  public void setOrderSalesAssociate(List<OrderSalesAssociate> orderSalesAssociate) {
    this.orderSalesAssociate = orderSalesAssociate;
  }

   /**
   * Sum of all order line subtotals where each order line subtotal is the item price times the order line quantity.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return orderSubTotal
  **/
  
  public BigDecimal getOrderSubTotal() {
    return orderSubTotal;
  }

  public void setOrderSubTotal(BigDecimal orderSubTotal) {
     this.orderSubTotal = orderSubTotal;
  }

  public Order orderTagDetail(List<OrderTagDetail> orderTagDetail) {
    this.orderTagDetail = orderTagDetail;
    return this;
  }

  public Order addOrderTagDetailItem(OrderTagDetail orderTagDetailItem) {
    if (this.orderTagDetail == null) {
      this.orderTagDetail = new ArrayList<OrderTagDetail>();
    }
    this.orderTagDetail.add(orderTagDetailItem);
    return this;
  }

   /**
   * Get orderTagDetail
   * @return orderTagDetail
  **/
  
  public List<OrderTagDetail> getOrderTagDetail() {
    return orderTagDetail;
  }

  public void setOrderTagDetail(List<OrderTagDetail> orderTagDetail) {
    this.orderTagDetail = orderTagDetail;
  }

  public Order orderTaxDetail(List<OrderTaxDetail> orderTaxDetail) {
    this.orderTaxDetail = orderTaxDetail;
    return this;
  }

  public Order addOrderTaxDetailItem(OrderTaxDetail orderTaxDetailItem) {
    if (this.orderTaxDetail == null) {
      this.orderTaxDetail = new ArrayList<OrderTaxDetail>();
    }
    this.orderTaxDetail.add(orderTaxDetailItem);
    return this;
  }

   /**
   * Get orderTaxDetail
   * @return orderTaxDetail
  **/
  
  public List<OrderTaxDetail> getOrderTaxDetail() {
    return orderTaxDetail;
  }

  public void setOrderTaxDetail(List<OrderTaxDetail> orderTaxDetail) {
    this.orderTaxDetail = orderTaxDetail;
  }

   /**
   * Sum of items, charges, discounts, and taxes for the order and order lines
   * minimum: 0
   * maximum: 99999999999999.98
   * @return orderTotal
  **/
  
  public BigDecimal getOrderTotal() {
    return orderTotal;
  }

  public void setOrderTotal(BigDecimal orderTotal) {
     this.orderTotal = orderTotal;
  }

  public Order orderTrackingInfo(List<OrderTrackingInfo> orderTrackingInfo) {
    this.orderTrackingInfo = orderTrackingInfo;
    return this;
  }

  public Order addOrderTrackingInfoItem(OrderTrackingInfo orderTrackingInfoItem) {
    if (this.orderTrackingInfo == null) {
      this.orderTrackingInfo = new ArrayList<OrderTrackingInfo>();
    }
    this.orderTrackingInfo.add(orderTrackingInfoItem);
    return this;
  }

   /**
   * Get orderTrackingInfo
   * @return orderTrackingInfo
  **/
  
  public List<OrderTrackingInfo> getOrderTrackingInfo() {
    return orderTrackingInfo;
  }

  public void setOrderTrackingInfo(List<OrderTrackingInfo> orderTrackingInfo) {
    this.orderTrackingInfo = orderTrackingInfo;
  }

  public Order orderType(OrderTypeId orderType) {
    this.orderType = orderType;
    return this;
  }

   /**
   * Get orderType
   * @return orderType
  **/
  
  public OrderTypeId getOrderType() {
    return orderType;
  }

  public void setOrderType(OrderTypeId orderType) {
    this.orderType = orderType;
  }

  public Order orderUpdatedBy(List<OrderUpdatedBy> orderUpdatedBy) {
    this.orderUpdatedBy = orderUpdatedBy;
    return this;
  }

  public Order addOrderUpdatedByItem(OrderUpdatedBy orderUpdatedByItem) {
    if (this.orderUpdatedBy == null) {
      this.orderUpdatedBy = new ArrayList<OrderUpdatedBy>();
    }
    this.orderUpdatedBy.add(orderUpdatedByItem);
    return this;
  }

   /**
   * Get orderUpdatedBy
   * @return orderUpdatedBy
  **/
  
  public List<OrderUpdatedBy> getOrderUpdatedBy() {
    return orderUpdatedBy;
  }

  public void setOrderUpdatedBy(List<OrderUpdatedBy> orderUpdatedBy) {
    this.orderUpdatedBy = orderUpdatedBy;
  }

  public Order orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public Order PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public Order packageCount(Integer packageCount) {
    this.packageCount = packageCount;
    return this;
  }

   /**
   * Number of packages the customer will be shipping to the return center. Used to request labels from the carrier. For instance, if the customer indicates that two packages are required for shipping their return items, then the CSR inputs 2 in the call center UI, and 2 labels are retrieved from the carrier. The email marketing system emails these two labels to the customer to affix to the return shipments. Required only when delivery method is Ship to Return Center
   * minimum: 0
   * maximum: 99999
   * @return packageCount
  **/
  
  public Integer getPackageCount() {
    return packageCount;
  }

  public void setPackageCount(Integer packageCount) {
    this.packageCount = packageCount;
  }

  public Order parentReservationRequestId(String parentReservationRequestId) {
    this.parentReservationRequestId = parentReservationRequestId;
    return this;
  }

   /**
   * Identifier of existing reservation.  If inventory is already reserved for this order, the reservationRequestId for the reservation is populated here so that Promising can allocate this order against these reserved quantities
   * @return parentReservationRequestId
  **/
  
  public String getParentReservationRequestId() {
    return parentReservationRequestId;
  }

  public void setParentReservationRequestId(String parentReservationRequestId) {
    this.parentReservationRequestId = parentReservationRequestId;
  }

  public Order payment(List<PaymentHeaderDTO> payment) {
    this.payment = payment;
    return this;
  }

  public Order addPaymentItem(PaymentHeaderDTO paymentItem) {
    if (this.payment == null) {
      this.payment = new ArrayList<PaymentHeaderDTO>();
    }
    this.payment.add(paymentItem);
    return this;
  }

   /**
   * Get payment
   * @return payment
  **/
  
  public List<PaymentHeaderDTO> getPayment() {
    return payment;
  }

  public void setPayment(List<PaymentHeaderDTO> payment) {
    this.payment = payment;
  }

  public Order paymentStatus(PaymentStatusId paymentStatus) {
    this.paymentStatus = paymentStatus;
    return this;
  }

   /**
   * Get paymentStatus
   * @return paymentStatus
  **/
  
  public PaymentStatusId getPaymentStatus() {
    return paymentStatus;
  }

  public void setPaymentStatus(PaymentStatusId paymentStatus) {
    this.paymentStatus = paymentStatus;
  }

  public Order postVoidReason(ReasonId postVoidReason) {
    this.postVoidReason = postVoidReason;
    return this;
  }

   /**
   * Get postVoidReason
   * @return postVoidReason
  **/
  
  public ReasonId getPostVoidReason() {
    return postVoidReason;
  }

  public void setPostVoidReason(ReasonId postVoidReason) {
    this.postVoidReason = postVoidReason;
  }

  public Order priority(Long priority) {
    this.priority = priority;
    return this;
  }

   /**
   * Priority of order, used in case of constrained inventory
   * minimum: 0
   * maximum: -8446744073709551617
   * @return priority
  **/
  
  public Long getPriority() {
    return priority;
  }

  public void setPriority(Long priority) {
    this.priority = priority;
  }

  public Order processInfo(ProcessInfo processInfo) {
    this.processInfo = processInfo;
    return this;
  }

   /**
   * Get processInfo
   * @return processInfo
  **/
  
  public ProcessInfo getProcessInfo() {
    return processInfo;
  }

  public void setProcessInfo(ProcessInfo processInfo) {
    this.processInfo = processInfo;
  }

  public Order processReturnComments(String processReturnComments) {
    this.processReturnComments = processReturnComments;
    return this;
  }

   /**
   * Comments provided by user when a return order is processed by a CSR to expedite refund processing or an exchange shipment.
   * @return processReturnComments
  **/
  
  public String getProcessReturnComments() {
    return processReturnComments;
  }

  public void setProcessReturnComments(String processReturnComments) {
    this.processReturnComments = processReturnComments;
  }

  public Order processReturnReason(ReasonId processReturnReason) {
    this.processReturnReason = processReturnReason;
    return this;
  }

   /**
   * Get processReturnReason
   * @return processReturnReason
  **/
  
  public ReasonId getProcessReturnReason() {
    return processReturnReason;
  }

  public void setProcessReturnReason(ReasonId processReturnReason) {
    this.processReturnReason = processReturnReason;
  }

  public Order publishStatus(PublishStatusId publishStatus) {
    this.publishStatus = publishStatus;
    return this;
  }

   /**
   * Get publishStatus
   * @return publishStatus
  **/
  
  public PublishStatusId getPublishStatus() {
    return publishStatus;
  }

  public void setPublishStatus(PublishStatusId publishStatus) {
    this.publishStatus = publishStatus;
  }

  public Order refundPaymentMethod(RefundPaymentMethodId refundPaymentMethod) {
    this.refundPaymentMethod = refundPaymentMethod;
    return this;
  }

   /**
   * Get refundPaymentMethod
   * @return refundPaymentMethod
  **/
  
  public RefundPaymentMethodId getRefundPaymentMethod() {
    return refundPaymentMethod;
  }

  public void setRefundPaymentMethod(RefundPaymentMethodId refundPaymentMethod) {
    this.refundPaymentMethod = refundPaymentMethod;
  }

  public Order refundRecipient(RefundRecipientId refundRecipient) {
    this.refundRecipient = refundRecipient;
    return this;
  }

   /**
   * Get refundRecipient
   * @return refundRecipient
  **/
  
  public RefundRecipientId getRefundRecipient() {
    return refundRecipient;
  }

  public void setRefundRecipient(RefundRecipientId refundRecipient) {
    this.refundRecipient = refundRecipient;
  }

  public Order release(List<Release> release) {
    this.release = release;
    return this;
  }

  public Order addReleaseItem(Release releaseItem) {
    if (this.release == null) {
      this.release = new ArrayList<Release>();
    }
    this.release.add(releaseItem);
    return this;
  }

   /**
   * Get release
   * @return release
  **/
  
  public List<Release> getRelease() {
    return release;
  }

  public void setRelease(List<Release> release) {
    this.release = release;
  }

  public Order returnLabel(List<ReturnLabel> returnLabel) {
    this.returnLabel = returnLabel;
    return this;
  }

  public Order addReturnLabelItem(ReturnLabel returnLabelItem) {
    if (this.returnLabel == null) {
      this.returnLabel = new ArrayList<ReturnLabel>();
    }
    this.returnLabel.add(returnLabelItem);
    return this;
  }

   /**
   * Get returnLabel
   * @return returnLabel
  **/
  
  public List<ReturnLabel> getReturnLabel() {
    return returnLabel;
  }

  public void setReturnLabel(List<ReturnLabel> returnLabel) {
    this.returnLabel = returnLabel;
  }

  public Order returnLabelEmail(String returnLabelEmail) {
    this.returnLabelEmail = returnLabelEmail;
    return this;
  }

   /**
   * Email address to which the return shipping labels should be sent. During call center return creation, this is captured and input by the CSR. Required only when delivery method is Ship to Return Center
   * @return returnLabelEmail
  **/
  
  public String getReturnLabelEmail() {
    return returnLabelEmail;
  }

  public void setReturnLabelEmail(String returnLabelEmail) {
    this.returnLabelEmail = returnLabelEmail;
  }

   /**
   * Count of order lines where isReturn &#x3D; true. Used for displaying return order lines in the DS UI and to identify orders where any return items exist
   * minimum: 0
   * maximum: -8446744073709551617
   * @return returnLineCount
  **/
  
  public Long getReturnLineCount() {
    return returnLineCount;
  }

  public Order returnStatus(String returnStatus) {
    this.returnStatus = returnStatus;
    return this;
  }

   /**
   * Get returnStatus
   * @return returnStatus
  **/
  
  public String getReturnStatus() {
    return returnStatus;
  }

  public void setReturnStatus(String returnStatus) {
    this.returnStatus = returnStatus;
  }

  public Order returnTotalWithoutFees(BigDecimal returnTotalWithoutFees) {
    this.returnTotalWithoutFees = returnTotalWithoutFees;
    return this;
  }

   /**
   * Get returnTotalWithoutFees
   * @return returnTotalWithoutFees
  **/
  
  public BigDecimal getReturnTotalWithoutFees() {
    return returnTotalWithoutFees;
  }

  public void setReturnTotalWithoutFees(BigDecimal returnTotalWithoutFees) {
    this.returnTotalWithoutFees = returnTotalWithoutFees;
  }

  public Order returnTrackingDetail(List<ReturnTrackingDetail> returnTrackingDetail) {
    this.returnTrackingDetail = returnTrackingDetail;
    return this;
  }

  public Order addReturnTrackingDetailItem(ReturnTrackingDetail returnTrackingDetailItem) {
    if (this.returnTrackingDetail == null) {
      this.returnTrackingDetail = new ArrayList<ReturnTrackingDetail>();
    }
    this.returnTrackingDetail.add(returnTrackingDetailItem);
    return this;
  }

   /**
   * Get returnTrackingDetail
   * @return returnTrackingDetail
  **/
  
  public List<ReturnTrackingDetail> getReturnTrackingDetail() {
    return returnTrackingDetail;
  }

  public void setReturnTrackingDetail(List<ReturnTrackingDetail> returnTrackingDetail) {
    this.returnTrackingDetail = returnTrackingDetail;
  }

  public Order runId(String runId) {
    this.runId = runId;
    return this;
  }

   /**
   * Indicates a unique Id for every bulk order imports. System populates this when import bulk orders is triggered. This field also used on Order UI to render the list of matching orders per Run Id
   * @return runId
  **/
  
  public String getRunId() {
    return runId;
  }

  public void setRunId(String runId) {
    this.runId = runId;
  }

  public Order sellingChannel(SellingChannelId sellingChannel) {
    this.sellingChannel = sellingChannel;
    return this;
  }

   /**
   * Get sellingChannel
   * @return sellingChannel
  **/
  
  public SellingChannelId getSellingChannel() {
    return sellingChannel;
  }

  public void setSellingChannel(SellingChannelId sellingChannel) {
    this.sellingChannel = sellingChannel;
  }

  public Order sellingLocationId(String sellingLocationId) {
    this.sellingLocationId = sellingLocationId;
    return this;
  }

   /**
   * Specific store location ID or website where transaction was captured
   * @return sellingLocationId
  **/
  
  public String getSellingLocationId() {
    return sellingLocationId;
  }

  public void setSellingLocationId(String sellingLocationId) {
    this.sellingLocationId = sellingLocationId;
  }

   /**
   * Total number of non-cancelled lines having deliveryMethod as StoreReturn
   * minimum: 0
   * maximum: -8446744073709551617
   * @return storeReturnCount
  **/
  
  public Long getStoreReturnCount() {
    return storeReturnCount;
  }

   /**
   * Total number of non-cancelled lines having deliveryMethod as StoreSale
   * minimum: 0
   * maximum: -8446744073709551617
   * @return storeSaleCount
  **/
  
  public Long getStoreSaleCount() {
    return storeSaleCount;
  }

  public Order suggestedPromo(List<SuggestedPromo> suggestedPromo) {
    this.suggestedPromo = suggestedPromo;
    return this;
  }

  public Order addSuggestedPromoItem(SuggestedPromo suggestedPromoItem) {
    if (this.suggestedPromo == null) {
      this.suggestedPromo = new ArrayList<SuggestedPromo>();
    }
    this.suggestedPromo.add(suggestedPromoItem);
    return this;
  }

   /**
   * Get suggestedPromo
   * @return suggestedPromo
  **/
  
  public List<SuggestedPromo> getSuggestedPromo() {
    return suggestedPromo;
  }

  public void setSuggestedPromo(List<SuggestedPromo> suggestedPromo) {
    this.suggestedPromo = suggestedPromo;
  }

  public Order suspendedOrderId(String suspendedOrderId) {
    this.suspendedOrderId = suspendedOrderId;
    return this;
  }

   /**
   * Identifies the Suspended order ID  which is  resumed in the current order.  It should be populated only on the resumed order.
   * @return suspendedOrderId
  **/
  
  public String getSuspendedOrderId() {
    return suspendedOrderId;
  }

  public void setSuspendedOrderId(String suspendedOrderId) {
    this.suspendedOrderId = suspendedOrderId;
  }

  public Order taxExemptComments(String taxExemptComments) {
    this.taxExemptComments = taxExemptComments;
    return this;
  }

   /**
   * If isTaxExempt is true for this order, these are the comments entered
   * @return taxExemptComments
  **/
  
  public String getTaxExemptComments() {
    return taxExemptComments;
  }

  public void setTaxExemptComments(String taxExemptComments) {
    this.taxExemptComments = taxExemptComments;
  }

  public Order taxExemptId(String taxExemptId) {
    this.taxExemptId = taxExemptId;
    return this;
  }

   /**
   * If isTaxExempt is true for this order, then this is the taxexemptid which represents the tax excempt reason
   * @return taxExemptId
  **/
  
  public String getTaxExemptId() {
    return taxExemptId;
  }

  public void setTaxExemptId(String taxExemptId) {
    this.taxExemptId = taxExemptId;
  }

  public Order taxExemptReason(ReasonId taxExemptReason) {
    this.taxExemptReason = taxExemptReason;
    return this;
  }

   /**
   * Get taxExemptReason
   * @return taxExemptReason
  **/
  
  public ReasonId getTaxExemptReason() {
    return taxExemptReason;
  }

  public void setTaxExemptReason(ReasonId taxExemptReason) {
    this.taxExemptReason = taxExemptReason;
  }

  public Order taxOverridePercValue(BigDecimal taxOverridePercValue) {
    this.taxOverridePercValue = taxOverridePercValue;
    return this;
  }

   /**
   * It has the tax override value in percentage. This attribute can have values with a precision upto 4 places of decimal. Hence when the tax override percentage requires higher precision, then this attribute must be used.
   * minimum: 0
   * maximum: 999999999999.9999
   * @return taxOverridePercValue
  **/
  
  public BigDecimal getTaxOverridePercValue() {
    return taxOverridePercValue;
  }

  public void setTaxOverridePercValue(BigDecimal taxOverridePercValue) {
    this.taxOverridePercValue = taxOverridePercValue;
  }

  public Order taxOverrideReason(ReasonId taxOverrideReason) {
    this.taxOverrideReason = taxOverrideReason;
    return this;
  }

   /**
   * Get taxOverrideReason
   * @return taxOverrideReason
  **/
  
  public ReasonId getTaxOverrideReason() {
    return taxOverrideReason;
  }

  public void setTaxOverrideReason(ReasonId taxOverrideReason) {
    this.taxOverrideReason = taxOverrideReason;
  }

  public Order taxOverrideType(TaxOverrideTypeId taxOverrideType) {
    this.taxOverrideType = taxOverrideType;
    return this;
  }

   /**
   * Get taxOverrideType
   * @return taxOverrideType
  **/
  
  public TaxOverrideTypeId getTaxOverrideType() {
    return taxOverrideType;
  }

  public void setTaxOverrideType(TaxOverrideTypeId taxOverrideType) {
    this.taxOverrideType = taxOverrideType;
  }

  public Order taxOverrideValue(BigDecimal taxOverrideValue) {
    this.taxOverrideValue = taxOverrideValue;
    return this;
  }

   /**
   * It has the tax override value. The value can be in terms of percentage or flat number and the precision is upto 2 places of decimal. When there is a need to provide a percentage for taxOverrideValue which requires higher precision (upto 3 or 4 places of decimal, say 2.3456), then donot use this attribute instead use taxOverridePercValue
   * minimum: 0
   * maximum: 99999999999999.98
   * @return taxOverrideValue
  **/
  
  public BigDecimal getTaxOverrideValue() {
    return taxOverrideValue;
  }

  public void setTaxOverrideValue(BigDecimal taxOverrideValue) {
    this.taxOverrideValue = taxOverrideValue;
  }

   /**
   * Sum of all charges for the order and order lines
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalCharges
  **/
  
  public BigDecimal getTotalCharges() {
    return totalCharges;
  }

   /**
   * Sum of all discounts for the order and order lines
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalDiscounts
  **/
  
  public BigDecimal getTotalDiscounts() {
    return totalDiscounts;
  }

  public Order totalInformationalTaxes(BigDecimal totalInformationalTaxes) {
    this.totalInformationalTaxes = totalInformationalTaxes;
    return this;
  }

   /**
   * Get totalInformationalTaxes
   * @return totalInformationalTaxes
  **/
  
  public BigDecimal getTotalInformationalTaxes() {
    return totalInformationalTaxes;
  }

  public void setTotalInformationalTaxes(BigDecimal totalInformationalTaxes) {
    this.totalInformationalTaxes = totalInformationalTaxes;
  }

  public Order totalReturnFees(BigDecimal totalReturnFees) {
    this.totalReturnFees = totalReturnFees;
    return this;
  }

   /**
   * Get totalReturnFees
   * @return totalReturnFees
  **/
  
  public BigDecimal getTotalReturnFees() {
    return totalReturnFees;
  }

  public void setTotalReturnFees(BigDecimal totalReturnFees) {
    this.totalReturnFees = totalReturnFees;
  }

  public void setTotalTaxes(BigDecimal totalTaxes) {
    this.totalTaxes = totalTaxes;
  }
  public Order totalTaxes(BigDecimal totalTaxes) {
    this.totalTaxes = totalTaxes;
    return this;
  }

   /**
   * Sum of all taxes for the order and order lines
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalTaxes
  **/
  
  public BigDecimal getTotalTaxes() {
    return totalTaxes;
  }

  public Order transactionReference(List<TransactionReference> transactionReference) {
    this.transactionReference = transactionReference;
    return this;
  }

  public Order addTransactionReferenceItem(TransactionReference transactionReferenceItem) {
    if (this.transactionReference == null) {
      this.transactionReference = new ArrayList<TransactionReference>();
    }
    this.transactionReference.add(transactionReferenceItem);
    return this;
  }

   /**
   * Get transactionReference
   * @return transactionReference
  **/
  
  public List<TransactionReference> getTransactionReference() {
    return transactionReference;
  }

  public void setTransactionReference(List<TransactionReference> transactionReference) {
    this.transactionReference = transactionReference;
  }

  public Order updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public Order updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public Order eligibleRefundCharges(OrderEligibleRefundChargesDTO eligibleRefundCharges) {
    this.eligibleRefundCharges = eligibleRefundCharges;
    return this;
  }

   /**
   * Get eligibleRefundCharges
   * @return eligibleRefundCharges
  **/
  
  public OrderEligibleRefundChargesDTO getEligibleRefundCharges() {
    return eligibleRefundCharges;
  }

  public void setEligibleRefundCharges(OrderEligibleRefundChargesDTO eligibleRefundCharges) {
    this.eligibleRefundCharges = eligibleRefundCharges;
  }

  public Order entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public Order localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Order order = (Order) o;
    return Objects.equals(this.actions, order.actions) &&
        Objects.equals(this.alternateOrderId, order.alternateOrderId) &&
        Objects.equals(this.archiveDate, order.archiveDate) &&
        Objects.equals(this.balanceDue, order.balanceDue) &&
        Objects.equals(this.businessDate, order.businessDate) &&
        Objects.equals(this.calculatedValues, order.calculatedValues) &&
        Objects.equals(this.cancelComments, order.cancelComments) &&
        Objects.equals(this.cancelLineCount, order.cancelLineCount) &&
        Objects.equals(this.cancelReason, order.cancelReason) &&
        Objects.equals(this.cancelledOrderSubTotal, order.cancelledOrderSubTotal) &&
        Objects.equals(this.cancelledOrderTotal, order.cancelledOrderTotal) &&
        Objects.equals(this.cancelledTotalDiscounts, order.cancelledTotalDiscounts) &&
        Objects.equals(this.capturedDate, order.capturedDate) &&
        Objects.equals(this.changeLog, order.changeLog) &&
        Objects.equals(this.collectedAmount, order.collectedAmount) &&
        Objects.equals(this.confirmedDate, order.confirmedDate) &&
        Objects.equals(this.contactPreference, order.contactPreference) &&
        Objects.equals(this.countedDate, order.countedDate) &&
        Objects.equals(this.createdBy, order.createdBy) &&
        Objects.equals(this.createdTimestamp, order.createdTimestamp) &&
        Objects.equals(this.currencyCode, order.currencyCode) &&
        Objects.equals(this.customerAddress, order.customerAddress) &&
        Objects.equals(this.customerEmail, order.customerEmail) &&
        Objects.equals(this.customerFirstName, order.customerFirstName) &&
        Objects.equals(this.customerId, order.customerId) &&
        Objects.equals(this.customerIdentityDoc, order.customerIdentityDoc) &&
        Objects.equals(this.customerLastName, order.customerLastName) &&
        Objects.equals(this.customerPhone, order.customerPhone) &&
        Objects.equals(this.customerSignature, order.customerSignature) &&
        Objects.equals(this.customerTypeId, order.customerTypeId) &&
        Objects.equals(this.doNotReleaseBefore, order.doNotReleaseBefore) &&
        Objects.equals(this.docType, order.docType) &&
        Objects.equals(this.eventSubmitTime, order.eventSubmitTime) &&
        Objects.equals(this.extended, order.extended) &&
        Objects.equals(this.fulfillmentStatus, order.fulfillmentStatus) &&
        Objects.equals(this.invoice, order.invoice) &&
        Objects.equals(this.isArchiveInProgress, order.isArchiveInProgress) &&
        Objects.equals(this.isCancelled, order.isCancelled) &&
        Objects.equals(this.isCapturedOffline, order.isCapturedOffline) &&
        Objects.equals(this.isConfirmed, order.isConfirmed) &&
        Objects.equals(this.isOnHold, order.isOnHold) &&
        Objects.equals(this.isOrderCountable, order.isOrderCountable) &&
        Objects.equals(this.isPostVoided, order.isPostVoided) &&
        Objects.equals(this.isReadyForTender, order.isReadyForTender) &&
        Objects.equals(this.isTaxExempt, order.isTaxExempt) &&
        Objects.equals(this.isTaxOverridden, order.isTaxOverridden) &&
        Objects.equals(this.isUnArchiveInProgress, order.isUnArchiveInProgress) &&
        Objects.equals(this.localizedTo, order.localizedTo) &&
        Objects.equals(this.loyaltyNumber, order.loyaltyNumber) &&
        Objects.equals(this.managerAuthDetail, order.managerAuthDetail) &&
        Objects.equals(this.maxAppeasementAmount, order.maxAppeasementAmount) &&
        Objects.equals(this.maxFulfillmentStatus, order.maxFulfillmentStatus) &&
        Objects.equals(this.maxFulfillmentStatusId, order.maxFulfillmentStatusId) &&
        Objects.equals(this.maxReturnStatus, order.maxReturnStatus) &&
        Objects.equals(this.maxReturnStatusId, order.maxReturnStatusId) &&
        Objects.equals(this.merchReturnLineCount, order.merchReturnLineCount) &&
        Objects.equals(this.merchSaleLineCount, order.merchSaleLineCount) &&
        Objects.equals(this.messages, order.messages) &&
        Objects.equals(this.minFulfillmentStatus, order.minFulfillmentStatus) &&
        Objects.equals(this.minFulfillmentStatusId, order.minFulfillmentStatusId) &&
        Objects.equals(this.minReturnStatus, order.minReturnStatus) &&
        Objects.equals(this.minReturnStatusId, order.minReturnStatusId) &&
        Objects.equals(this.nextEventTime, order.nextEventTime) &&
        Objects.equals(this.orderActions, order.orderActions) &&
        Objects.equals(this.orderAttribute, order.orderAttribute) &&
        Objects.equals(this.orderCaptureDetail, order.orderCaptureDetail) &&
        Objects.equals(this.orderChargeDetail, order.orderChargeDetail) &&
        Objects.equals(this.orderExtension1, order.orderExtension1) &&
        Objects.equals(this.orderExtension2, order.orderExtension2) &&
        Objects.equals(this.orderExtension3, order.orderExtension3) &&
        Objects.equals(this.orderExtension4, order.orderExtension4) &&
        Objects.equals(this.orderExtension5, order.orderExtension5) &&
        Objects.equals(this.orderFulfillmentGroups, order.orderFulfillmentGroups) &&
        Objects.equals(this.orderHold, order.orderHold) &&
        Objects.equals(this.orderId, order.orderId) &&
        Objects.equals(this.orderLine, order.orderLine) &&
        Objects.equals(this.orderLineCount, order.orderLineCount) &&
        Objects.equals(this.orderLocale, order.orderLocale) &&
        Objects.equals(this.orderMilestone, order.orderMilestone) &&
        Objects.equals(this.orderMilestoneEvent, order.orderMilestoneEvent) &&
        Objects.equals(this.orderNote, order.orderNote) &&
        Objects.equals(this.orderPaymentMethod, order.orderPaymentMethod) &&
        Objects.equals(this.orderPromisingInfo, order.orderPromisingInfo) &&
        Objects.equals(this.orderPromotionRequest, order.orderPromotionRequest) &&
        Objects.equals(this.orderSalesAssociate, order.orderSalesAssociate) &&
        Objects.equals(this.orderSubTotal, order.orderSubTotal) &&
        Objects.equals(this.orderTagDetail, order.orderTagDetail) &&
        Objects.equals(this.orderTaxDetail, order.orderTaxDetail) &&
        Objects.equals(this.orderTotal, order.orderTotal) &&
        Objects.equals(this.orderTrackingInfo, order.orderTrackingInfo) &&
        Objects.equals(this.orderType, order.orderType) &&
        Objects.equals(this.orderUpdatedBy, order.orderUpdatedBy) &&
        Objects.equals(this.orgId, order.orgId) &&
        Objects.equals(this.PK, order.PK) &&
        Objects.equals(this.packageCount, order.packageCount) &&
        Objects.equals(this.parentReservationRequestId, order.parentReservationRequestId) &&
        Objects.equals(this.payment, order.payment) &&
        Objects.equals(this.paymentStatus, order.paymentStatus) &&
        Objects.equals(this.postVoidReason, order.postVoidReason) &&
        Objects.equals(this.priority, order.priority) &&
        Objects.equals(this.processInfo, order.processInfo) &&
        Objects.equals(this.processReturnComments, order.processReturnComments) &&
        Objects.equals(this.processReturnReason, order.processReturnReason) &&
        Objects.equals(this.publishStatus, order.publishStatus) &&
        Objects.equals(this.refundPaymentMethod, order.refundPaymentMethod) &&
        Objects.equals(this.refundRecipient, order.refundRecipient) &&
        Objects.equals(this.release, order.release) &&
        Objects.equals(this.returnLabel, order.returnLabel) &&
        Objects.equals(this.returnLabelEmail, order.returnLabelEmail) &&
        Objects.equals(this.returnLineCount, order.returnLineCount) &&
        Objects.equals(this.returnStatus, order.returnStatus) &&
        Objects.equals(this.returnTotalWithoutFees, order.returnTotalWithoutFees) &&
        Objects.equals(this.returnTrackingDetail, order.returnTrackingDetail) &&
        Objects.equals(this.runId, order.runId) &&
        Objects.equals(this.sellingChannel, order.sellingChannel) &&
        Objects.equals(this.sellingLocationId, order.sellingLocationId) &&
        Objects.equals(this.storeReturnCount, order.storeReturnCount) &&
        Objects.equals(this.storeSaleCount, order.storeSaleCount) &&
        Objects.equals(this.suggestedPromo, order.suggestedPromo) &&
        Objects.equals(this.suspendedOrderId, order.suspendedOrderId) &&
        Objects.equals(this.taxExemptComments, order.taxExemptComments) &&
        Objects.equals(this.taxExemptId, order.taxExemptId) &&
        Objects.equals(this.taxExemptReason, order.taxExemptReason) &&
        Objects.equals(this.taxOverridePercValue, order.taxOverridePercValue) &&
        Objects.equals(this.taxOverrideReason, order.taxOverrideReason) &&
        Objects.equals(this.taxOverrideType, order.taxOverrideType) &&
        Objects.equals(this.taxOverrideValue, order.taxOverrideValue) &&
        Objects.equals(this.totalCharges, order.totalCharges) &&
        Objects.equals(this.totalDiscounts, order.totalDiscounts) &&
        Objects.equals(this.totalInformationalTaxes, order.totalInformationalTaxes) &&
        Objects.equals(this.totalReturnFees, order.totalReturnFees) &&
        Objects.equals(this.totalTaxes, order.totalTaxes) &&
        Objects.equals(this.transactionReference, order.transactionReference) &&
        Objects.equals(this.updatedBy, order.updatedBy) &&
        Objects.equals(this.updatedTimestamp, order.updatedTimestamp) &&
        Objects.equals(this.eligibleRefundCharges, order.eligibleRefundCharges) &&
        Objects.equals(this.entityName, order.entityName) &&
        Objects.equals(this.localize, order.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, alternateOrderId, archiveDate, balanceDue, businessDate, calculatedValues, cancelComments, cancelLineCount, cancelReason, cancelledOrderSubTotal, cancelledOrderTotal, cancelledTotalDiscounts, capturedDate, changeLog, collectedAmount, confirmedDate, contactPreference, countedDate, createdBy, createdTimestamp, currencyCode, customerAddress, customerEmail, customerFirstName, customerId, customerIdentityDoc, customerLastName, customerPhone, customerSignature, customerTypeId, doNotReleaseBefore, docType, eventSubmitTime, extended, fulfillmentStatus, invoice, isArchiveInProgress, isCancelled, isCapturedOffline, isConfirmed, isOnHold, isOrderCountable, isPostVoided, isReadyForTender, isTaxExempt, isTaxOverridden, isUnArchiveInProgress, localizedTo, loyaltyNumber, managerAuthDetail, maxAppeasementAmount, maxFulfillmentStatus, maxFulfillmentStatusId, maxReturnStatus, maxReturnStatusId, merchReturnLineCount, merchSaleLineCount, messages, minFulfillmentStatus, minFulfillmentStatusId, minReturnStatus, minReturnStatusId, nextEventTime, orderActions, orderAttribute, orderCaptureDetail, orderChargeDetail, orderExtension1, orderExtension2, orderExtension3, orderExtension4, orderExtension5, orderFulfillmentGroups, orderHold, orderId, orderLine, orderLineCount, orderLocale, orderMilestone, orderMilestoneEvent, orderNote, orderPaymentMethod, orderPromisingInfo, orderPromotionRequest, orderSalesAssociate, orderSubTotal, orderTagDetail, orderTaxDetail, orderTotal, orderTrackingInfo, orderType, orderUpdatedBy, orgId, PK, packageCount, parentReservationRequestId, payment, paymentStatus, postVoidReason, priority, processInfo, processReturnComments, processReturnReason, publishStatus, refundPaymentMethod, refundRecipient, release, returnLabel, returnLabelEmail, returnLineCount, returnStatus, returnTotalWithoutFees, returnTrackingDetail, runId, sellingChannel, sellingLocationId, storeReturnCount, storeSaleCount, suggestedPromo, suspendedOrderId, taxExemptComments, taxExemptId, taxExemptReason, taxOverridePercValue, taxOverrideReason, taxOverrideType, taxOverrideValue, totalCharges, totalDiscounts, totalInformationalTaxes, totalReturnFees, totalTaxes, transactionReference, updatedBy, updatedTimestamp, eligibleRefundCharges, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Order {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    alternateOrderId: ").append(toIndentedString(alternateOrderId)).append("\n");
    sb.append("    archiveDate: ").append(toIndentedString(archiveDate)).append("\n");
    sb.append("    balanceDue: ").append(toIndentedString(balanceDue)).append("\n");
    sb.append("    businessDate: ").append(toIndentedString(businessDate)).append("\n");
    sb.append("    calculatedValues: ").append(toIndentedString(calculatedValues)).append("\n");
    sb.append("    cancelComments: ").append(toIndentedString(cancelComments)).append("\n");
    sb.append("    cancelLineCount: ").append(toIndentedString(cancelLineCount)).append("\n");
    sb.append("    cancelReason: ").append(toIndentedString(cancelReason)).append("\n");
    sb.append("    cancelledOrderSubTotal: ").append(toIndentedString(cancelledOrderSubTotal)).append("\n");
    sb.append("    cancelledOrderTotal: ").append(toIndentedString(cancelledOrderTotal)).append("\n");
    sb.append("    cancelledTotalDiscounts: ").append(toIndentedString(cancelledTotalDiscounts)).append("\n");
    sb.append("    capturedDate: ").append(toIndentedString(capturedDate)).append("\n");
    sb.append("    changeLog: ").append(toIndentedString(changeLog)).append("\n");
    sb.append("    collectedAmount: ").append(toIndentedString(collectedAmount)).append("\n");
    sb.append("    confirmedDate: ").append(toIndentedString(confirmedDate)).append("\n");
    sb.append("    contactPreference: ").append(toIndentedString(contactPreference)).append("\n");
    sb.append("    countedDate: ").append(toIndentedString(countedDate)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    currencyCode: ").append(toIndentedString(currencyCode)).append("\n");
    sb.append("    customerAddress: ").append(toIndentedString(customerAddress)).append("\n");
    sb.append("    customerEmail: ").append(toIndentedString(customerEmail)).append("\n");
    sb.append("    customerFirstName: ").append(toIndentedString(customerFirstName)).append("\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    customerIdentityDoc: ").append(toIndentedString(customerIdentityDoc)).append("\n");
    sb.append("    customerLastName: ").append(toIndentedString(customerLastName)).append("\n");
    sb.append("    customerPhone: ").append(toIndentedString(customerPhone)).append("\n");
    sb.append("    customerSignature: ").append(toIndentedString(customerSignature)).append("\n");
    sb.append("    customerTypeId: ").append(toIndentedString(customerTypeId)).append("\n");
    sb.append("    doNotReleaseBefore: ").append(toIndentedString(doNotReleaseBefore)).append("\n");
    sb.append("    docType: ").append(toIndentedString(docType)).append("\n");
    sb.append("    eventSubmitTime: ").append(toIndentedString(eventSubmitTime)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    fulfillmentStatus: ").append(toIndentedString(fulfillmentStatus)).append("\n");
    sb.append("    invoice: ").append(toIndentedString(invoice)).append("\n");
    sb.append("    isArchiveInProgress: ").append(toIndentedString(isArchiveInProgress)).append("\n");
    sb.append("    isCancelled: ").append(toIndentedString(isCancelled)).append("\n");
    sb.append("    isCapturedOffline: ").append(toIndentedString(isCapturedOffline)).append("\n");
    sb.append("    isConfirmed: ").append(toIndentedString(isConfirmed)).append("\n");
    sb.append("    isOnHold: ").append(toIndentedString(isOnHold)).append("\n");
    sb.append("    isOrderCountable: ").append(toIndentedString(isOrderCountable)).append("\n");
    sb.append("    isPostVoided: ").append(toIndentedString(isPostVoided)).append("\n");
    sb.append("    isReadyForTender: ").append(toIndentedString(isReadyForTender)).append("\n");
    sb.append("    isTaxExempt: ").append(toIndentedString(isTaxExempt)).append("\n");
    sb.append("    isTaxOverridden: ").append(toIndentedString(isTaxOverridden)).append("\n");
    sb.append("    isUnArchiveInProgress: ").append(toIndentedString(isUnArchiveInProgress)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    loyaltyNumber: ").append(toIndentedString(loyaltyNumber)).append("\n");
    sb.append("    managerAuthDetail: ").append(toIndentedString(managerAuthDetail)).append("\n");
    sb.append("    maxAppeasementAmount: ").append(toIndentedString(maxAppeasementAmount)).append("\n");
    sb.append("    maxFulfillmentStatus: ").append(toIndentedString(maxFulfillmentStatus)).append("\n");
    sb.append("    maxFulfillmentStatusId: ").append(toIndentedString(maxFulfillmentStatusId)).append("\n");
    sb.append("    maxReturnStatus: ").append(toIndentedString(maxReturnStatus)).append("\n");
    sb.append("    maxReturnStatusId: ").append(toIndentedString(maxReturnStatusId)).append("\n");
    sb.append("    merchReturnLineCount: ").append(toIndentedString(merchReturnLineCount)).append("\n");
    sb.append("    merchSaleLineCount: ").append(toIndentedString(merchSaleLineCount)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    minFulfillmentStatus: ").append(toIndentedString(minFulfillmentStatus)).append("\n");
    sb.append("    minFulfillmentStatusId: ").append(toIndentedString(minFulfillmentStatusId)).append("\n");
    sb.append("    minReturnStatus: ").append(toIndentedString(minReturnStatus)).append("\n");
    sb.append("    minReturnStatusId: ").append(toIndentedString(minReturnStatusId)).append("\n");
    sb.append("    nextEventTime: ").append(toIndentedString(nextEventTime)).append("\n");
    sb.append("    orderActions: ").append(toIndentedString(orderActions)).append("\n");
    sb.append("    orderAttribute: ").append(toIndentedString(orderAttribute)).append("\n");
    sb.append("    orderCaptureDetail: ").append(toIndentedString(orderCaptureDetail)).append("\n");
    sb.append("    orderChargeDetail: ").append(toIndentedString(orderChargeDetail)).append("\n");
    sb.append("    orderExtension1: ").append(toIndentedString(orderExtension1)).append("\n");
    sb.append("    orderExtension2: ").append(toIndentedString(orderExtension2)).append("\n");
    sb.append("    orderExtension3: ").append(toIndentedString(orderExtension3)).append("\n");
    sb.append("    orderExtension4: ").append(toIndentedString(orderExtension4)).append("\n");
    sb.append("    orderExtension5: ").append(toIndentedString(orderExtension5)).append("\n");
    sb.append("    orderFulfillmentGroups: ").append(toIndentedString(orderFulfillmentGroups)).append("\n");
    sb.append("    orderHold: ").append(toIndentedString(orderHold)).append("\n");
    sb.append("    orderId: ").append(toIndentedString(orderId)).append("\n");
    sb.append("    orderLine: ").append(toIndentedString(orderLine)).append("\n");
    sb.append("    orderLineCount: ").append(toIndentedString(orderLineCount)).append("\n");
    sb.append("    orderLocale: ").append(toIndentedString(orderLocale)).append("\n");
    sb.append("    orderMilestone: ").append(toIndentedString(orderMilestone)).append("\n");
    sb.append("    orderMilestoneEvent: ").append(toIndentedString(orderMilestoneEvent)).append("\n");
    sb.append("    orderNote: ").append(toIndentedString(orderNote)).append("\n");
    sb.append("    orderPaymentMethod: ").append(toIndentedString(orderPaymentMethod)).append("\n");
    sb.append("    orderPromisingInfo: ").append(toIndentedString(orderPromisingInfo)).append("\n");
    sb.append("    orderPromotionRequest: ").append(toIndentedString(orderPromotionRequest)).append("\n");
    sb.append("    orderSalesAssociate: ").append(toIndentedString(orderSalesAssociate)).append("\n");
    sb.append("    orderSubTotal: ").append(toIndentedString(orderSubTotal)).append("\n");
    sb.append("    orderTagDetail: ").append(toIndentedString(orderTagDetail)).append("\n");
    sb.append("    orderTaxDetail: ").append(toIndentedString(orderTaxDetail)).append("\n");
    sb.append("    orderTotal: ").append(toIndentedString(orderTotal)).append("\n");
    sb.append("    orderTrackingInfo: ").append(toIndentedString(orderTrackingInfo)).append("\n");
    sb.append("    orderType: ").append(toIndentedString(orderType)).append("\n");
    sb.append("    orderUpdatedBy: ").append(toIndentedString(orderUpdatedBy)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    packageCount: ").append(toIndentedString(packageCount)).append("\n");
    sb.append("    parentReservationRequestId: ").append(toIndentedString(parentReservationRequestId)).append("\n");
    sb.append("    payment: ").append(toIndentedString(payment)).append("\n");
    sb.append("    paymentStatus: ").append(toIndentedString(paymentStatus)).append("\n");
    sb.append("    postVoidReason: ").append(toIndentedString(postVoidReason)).append("\n");
    sb.append("    priority: ").append(toIndentedString(priority)).append("\n");
    sb.append("    processInfo: ").append(toIndentedString(processInfo)).append("\n");
    sb.append("    processReturnComments: ").append(toIndentedString(processReturnComments)).append("\n");
    sb.append("    processReturnReason: ").append(toIndentedString(processReturnReason)).append("\n");
    sb.append("    publishStatus: ").append(toIndentedString(publishStatus)).append("\n");
    sb.append("    refundPaymentMethod: ").append(toIndentedString(refundPaymentMethod)).append("\n");
    sb.append("    refundRecipient: ").append(toIndentedString(refundRecipient)).append("\n");
    sb.append("    release: ").append(toIndentedString(release)).append("\n");
    sb.append("    returnLabel: ").append(toIndentedString(returnLabel)).append("\n");
    sb.append("    returnLabelEmail: ").append(toIndentedString(returnLabelEmail)).append("\n");
    sb.append("    returnLineCount: ").append(toIndentedString(returnLineCount)).append("\n");
    sb.append("    returnStatus: ").append(toIndentedString(returnStatus)).append("\n");
    sb.append("    returnTotalWithoutFees: ").append(toIndentedString(returnTotalWithoutFees)).append("\n");
    sb.append("    returnTrackingDetail: ").append(toIndentedString(returnTrackingDetail)).append("\n");
    sb.append("    runId: ").append(toIndentedString(runId)).append("\n");
    sb.append("    sellingChannel: ").append(toIndentedString(sellingChannel)).append("\n");
    sb.append("    sellingLocationId: ").append(toIndentedString(sellingLocationId)).append("\n");
    sb.append("    storeReturnCount: ").append(toIndentedString(storeReturnCount)).append("\n");
    sb.append("    storeSaleCount: ").append(toIndentedString(storeSaleCount)).append("\n");
    sb.append("    suggestedPromo: ").append(toIndentedString(suggestedPromo)).append("\n");
    sb.append("    suspendedOrderId: ").append(toIndentedString(suspendedOrderId)).append("\n");
    sb.append("    taxExemptComments: ").append(toIndentedString(taxExemptComments)).append("\n");
    sb.append("    taxExemptId: ").append(toIndentedString(taxExemptId)).append("\n");
    sb.append("    taxExemptReason: ").append(toIndentedString(taxExemptReason)).append("\n");
    sb.append("    taxOverridePercValue: ").append(toIndentedString(taxOverridePercValue)).append("\n");
    sb.append("    taxOverrideReason: ").append(toIndentedString(taxOverrideReason)).append("\n");
    sb.append("    taxOverrideType: ").append(toIndentedString(taxOverrideType)).append("\n");
    sb.append("    taxOverrideValue: ").append(toIndentedString(taxOverrideValue)).append("\n");
    sb.append("    totalCharges: ").append(toIndentedString(totalCharges)).append("\n");
    sb.append("    totalDiscounts: ").append(toIndentedString(totalDiscounts)).append("\n");
    sb.append("    totalInformationalTaxes: ").append(toIndentedString(totalInformationalTaxes)).append("\n");
    sb.append("    totalReturnFees: ").append(toIndentedString(totalReturnFees)).append("\n");
    sb.append("    totalTaxes: ").append(toIndentedString(totalTaxes)).append("\n");
    sb.append("    transactionReference: ").append(toIndentedString(transactionReference)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    eligibleRefundCharges: ").append(toIndentedString(eligibleRefundCharges)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

