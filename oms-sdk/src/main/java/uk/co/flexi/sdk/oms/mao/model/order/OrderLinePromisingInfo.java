/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderLinePromisingInfo
 */
public class OrderLinePromisingInfo {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ASN_DETAIL_ID = "AsnDetailId";
  @SerializedName(SERIALIZED_NAME_ASN_DETAIL_ID)
  private String asnDetailId;

  public static final String SERIALIZED_NAME_ASN_ID = "AsnId";
  @SerializedName(SERIALIZED_NAME_ASN_ID)
  private String asnId;

  public static final String SERIALIZED_NAME_BATCH_NUMBER = "BatchNumber";
  @SerializedName(SERIALIZED_NAME_BATCH_NUMBER)
  private String batchNumber;

  public static final String SERIALIZED_NAME_CONSOLIDATATION_LOCATION_ID = "ConsolidatationLocationId";
  @SerializedName(SERIALIZED_NAME_CONSOLIDATATION_LOCATION_ID)
  private String consolidatationLocationId;

  public static final String SERIALIZED_NAME_COUNTRY_OF_ORIGIN = "CountryOfOrigin";
  @SerializedName(SERIALIZED_NAME_COUNTRY_OF_ORIGIN)
  private String countryOfOrigin;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_LAST_POSSIBLE_DELIVERY_DATE = "LastPossibleDeliveryDate";
  @SerializedName(SERIALIZED_LAST_POSSIBLE_DELIVERY_DATE)
  private OffsetDateTime lastPossibleDeliveryDate;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_EXTERNAL_ROUTE_ID = "ExternalRouteId";
  @SerializedName(SERIALIZED_NAME_EXTERNAL_ROUTE_ID)
  private String externalRouteId;

  public static final String SERIALIZED_NAME_INVENTORY_SEGMENT_ID = "InventorySegmentId";
  @SerializedName(SERIALIZED_NAME_INVENTORY_SEGMENT_ID)
  private String inventorySegmentId;

  public static final String SERIALIZED_NAME_INVENTORY_TYPE_ID = "InventoryTypeId";
  @SerializedName(SERIALIZED_NAME_INVENTORY_TYPE_ID)
  private String inventoryTypeId;

  public static final String SERIALIZED_NAME_IS_FORCE_ALLOCATE = "IsForceAllocate";
  @SerializedName(SERIALIZED_NAME_IS_FORCE_ALLOCATE)
  private Boolean isForceAllocate;

  public static final String SERIALIZED_NAME_ITEM_ATTRIBUTE1 = "ItemAttribute1";
  @SerializedName(SERIALIZED_NAME_ITEM_ATTRIBUTE1)
  private String itemAttribute1;

  public static final String SERIALIZED_NAME_ITEM_ATTRIBUTE2 = "ItemAttribute2";
  @SerializedName(SERIALIZED_NAME_ITEM_ATTRIBUTE2)
  private String itemAttribute2;

  public static final String SERIALIZED_NAME_ITEM_ATTRIBUTE3 = "ItemAttribute3";
  @SerializedName(SERIALIZED_NAME_ITEM_ATTRIBUTE3)
  private String itemAttribute3;

  public static final String SERIALIZED_NAME_ITEM_ATTRIBUTE4 = "ItemAttribute4";
  @SerializedName(SERIALIZED_NAME_ITEM_ATTRIBUTE4)
  private String itemAttribute4;

  public static final String SERIALIZED_NAME_ITEM_ATTRIBUTE5 = "ItemAttribute5";
  @SerializedName(SERIALIZED_NAME_ITEM_ATTRIBUTE5)
  private String itemAttribute5;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PO_DETAIL_ID = "PoDetailId";
  @SerializedName(SERIALIZED_NAME_PO_DETAIL_ID)
  private String poDetailId;

  public static final String SERIALIZED_NAME_PO_ID = "PoId";
  @SerializedName(SERIALIZED_NAME_PO_ID)
  private String poId;

  public static final String SERIALIZED_NAME_PRODUCT_STATUS_ID = "ProductStatusId";
  @SerializedName(SERIALIZED_NAME_PRODUCT_STATUS_ID)
  private String productStatusId;

  public static final String SERIALIZED_NAME_REQ_CAPACITY_PER_UNIT = "ReqCapacityPerUnit";
  @SerializedName(SERIALIZED_NAME_REQ_CAPACITY_PER_UNIT)
  private Long reqCapacityPerUnit;

  public static final String SERIALIZED_NAME_SHIP_FROM_LOCATION_ID = "ShipFromLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_FROM_LOCATION_ID)
  private String shipFromLocationId;

  public static final String SERIALIZED_NAME_SHIP_THROUGH_LOCATION_ID = "ShipThroughLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_THROUGH_LOCATION_ID)
  private String shipThroughLocationId;

  public static final String SERIALIZED_NAME_STRATEGY_TYPE = "StrategyType";
  @SerializedName(SERIALIZED_NAME_STRATEGY_TYPE)
  private StrategyTypeId strategyType = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderLinePromisingInfo actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderLinePromisingInfo putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderLinePromisingInfo asnDetailId(String asnDetailId) {
    this.asnDetailId = asnDetailId;
    return this;
  }

   /**
   * Preferred ASN  Line ID for Allocation
   * @return asnDetailId
  **/
  
  public String getAsnDetailId() {
    return asnDetailId;
  }

  public void setAsnDetailId(String asnDetailId) {
    this.asnDetailId = asnDetailId;
  }

  public OrderLinePromisingInfo asnId(String asnId) {
    this.asnId = asnId;
    return this;
  }

   /**
   * Preferred ASN ID for Allocation
   * @return asnId
  **/
  
  public String getAsnId() {
    return asnId;
  }

  public void setAsnId(String asnId) {
    this.asnId = asnId;
  }

  public OrderLinePromisingInfo batchNumber(String batchNumber) {
    this.batchNumber = batchNumber;
    return this;
  }

   /**
   * Preferred Batch Number for Allocation
   * @return batchNumber
  **/
  
  public String getBatchNumber() {
    return batchNumber;
  }

  public void setBatchNumber(String batchNumber) {
    this.batchNumber = batchNumber;
  }

  public OrderLinePromisingInfo consolidatationLocationId(String consolidatationLocationId) {
    this.consolidatationLocationId = consolidatationLocationId;
    return this;
  }

   /**
   * Indicates consolidation of multiple items in a single facility
   * @return consolidatationLocationId
  **/
  
  public String getConsolidatationLocationId() {
    return consolidatationLocationId;
  }

  public void setConsolidatationLocationId(String consolidatationLocationId) {
    this.consolidatationLocationId = consolidatationLocationId;
  }

  public OrderLinePromisingInfo countryOfOrigin(String countryOfOrigin) {
    this.countryOfOrigin = countryOfOrigin;
    return this;
  }

   /**
   * Country of Origin
   * @return countryOfOrigin
  **/
  
  public String getCountryOfOrigin() {
    return countryOfOrigin;
  }

  public void setCountryOfOrigin(String countryOfOrigin) {
    this.countryOfOrigin = countryOfOrigin;
  }

  public OrderLinePromisingInfo createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderLinePromisingInfo createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }


  /**
   * Last Possible Delivery Date
   * @return lastPossibleDeliveryDate
   **/

  public OrderLinePromisingInfo lastPossibleDeliveryDate(OffsetDateTime lastPossibleDeliveryDate) {
    this.lastPossibleDeliveryDate = lastPossibleDeliveryDate;
    return this;
  }

  
  public OffsetDateTime getLastPossibleDeliveryDate() {
    return lastPossibleDeliveryDate;
  }

  public void setLastPossibleDeliveryDate(OffsetDateTime lastPossibleDeliveryDate) {
    this.lastPossibleDeliveryDate = lastPossibleDeliveryDate;
  }

  public OrderLinePromisingInfo extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderLinePromisingInfo externalRouteId(String externalRouteId) {
    this.externalRouteId = externalRouteId;
    return this;
  }

   /**
   * Route identifier, if a merge route is specified for the order line. If no merge route is specified, then the system selects a route based on configuration. For example, if an external routing system determines that a merge route from DC1 to DC2 to customer should be used to deliver all units for the order line in a single package, then the route id should be passed in this field. This value acts as a matching criteria for merge route configured for an inventory location during promising.
   * @return externalRouteId
  **/
  
  public String getExternalRouteId() {
    return externalRouteId;
  }

  public void setExternalRouteId(String externalRouteId) {
    this.externalRouteId = externalRouteId;
  }

  public OrderLinePromisingInfo inventorySegmentId(String inventorySegmentId) {
    this.inventorySegmentId = inventorySegmentId;
    return this;
  }

   /**
   * Preferred Segment for Allocation
   * @return inventorySegmentId
  **/
  
  public String getInventorySegmentId() {
    return inventorySegmentId;
  }

  public void setInventorySegmentId(String inventorySegmentId) {
    this.inventorySegmentId = inventorySegmentId;
  }

  public OrderLinePromisingInfo inventoryTypeId(String inventoryTypeId) {
    this.inventoryTypeId = inventoryTypeId;
    return this;
  }

   /**
   * Item attribute; Valid values from sys code B 064; Provides additional level of tracking/allocation for Item inventory. For example, Finished Product, Raw Materials, etc
   * @return inventoryTypeId
  **/
  
  public String getInventoryTypeId() {
    return inventoryTypeId;
  }

  public void setInventoryTypeId(String inventoryTypeId) {
    this.inventoryTypeId = inventoryTypeId;
  }

  public OrderLinePromisingInfo isForceAllocate(Boolean isForceAllocate) {
    this.isForceAllocate = isForceAllocate;
    return this;
  }

   /**
   * Indicates if the line has to be force allocated
   * @return isForceAllocate
  **/
  
  public Boolean getIsForceAllocate() {
    return isForceAllocate;
  }

  public void setIsForceAllocate(Boolean isForceAllocate) {
    this.isForceAllocate = isForceAllocate;
  }

  public OrderLinePromisingInfo itemAttribute1(String itemAttribute1) {
    this.itemAttribute1 = itemAttribute1;
    return this;
  }

   /**
   * SKU attributes, WM only allocates inventory w/ matching SKU attributes
   * @return itemAttribute1
  **/
  
  public String getItemAttribute1() {
    return itemAttribute1;
  }

  public void setItemAttribute1(String itemAttribute1) {
    this.itemAttribute1 = itemAttribute1;
  }

  public OrderLinePromisingInfo itemAttribute2(String itemAttribute2) {
    this.itemAttribute2 = itemAttribute2;
    return this;
  }

   /**
   * SKU attributes, WM only allocates inventory w/ matching SKU attributes
   * @return itemAttribute2
  **/
  
  public String getItemAttribute2() {
    return itemAttribute2;
  }

  public void setItemAttribute2(String itemAttribute2) {
    this.itemAttribute2 = itemAttribute2;
  }

  public OrderLinePromisingInfo itemAttribute3(String itemAttribute3) {
    this.itemAttribute3 = itemAttribute3;
    return this;
  }

   /**
   * SKU attributes, WM only allocates inventory w/ matching SKU attributes
   * @return itemAttribute3
  **/
  
  public String getItemAttribute3() {
    return itemAttribute3;
  }

  public void setItemAttribute3(String itemAttribute3) {
    this.itemAttribute3 = itemAttribute3;
  }

  public OrderLinePromisingInfo itemAttribute4(String itemAttribute4) {
    this.itemAttribute4 = itemAttribute4;
    return this;
  }

   /**
   * SKU attributes, WM only allocates inventory w/ matching SKU attributes
   * @return itemAttribute4
  **/
  
  public String getItemAttribute4() {
    return itemAttribute4;
  }

  public void setItemAttribute4(String itemAttribute4) {
    this.itemAttribute4 = itemAttribute4;
  }

  public OrderLinePromisingInfo itemAttribute5(String itemAttribute5) {
    this.itemAttribute5 = itemAttribute5;
    return this;
  }

   /**
   * SKU attributes, WM only allocates inventory w/ matching SKU attributes
   * @return itemAttribute5
  **/
  
  public String getItemAttribute5() {
    return itemAttribute5;
  }

  public void setItemAttribute5(String itemAttribute5) {
    this.itemAttribute5 = itemAttribute5;
  }

  public OrderLinePromisingInfo localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderLinePromisingInfo messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderLinePromisingInfo orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderLinePromisingInfo PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderLinePromisingInfo poDetailId(String poDetailId) {
    this.poDetailId = poDetailId;
    return this;
  }

   /**
   * Preferred PO Line ID for Allocation
   * @return poDetailId
  **/
  
  public String getPoDetailId() {
    return poDetailId;
  }

  public void setPoDetailId(String poDetailId) {
    this.poDetailId = poDetailId;
  }

  public OrderLinePromisingInfo poId(String poId) {
    this.poId = poId;
    return this;
  }

   /**
   * Preferred PO ID for Allocation 
   * @return poId
  **/
  
  public String getPoId() {
    return poId;
  }

  public void setPoId(String poId) {
    this.poId = poId;
  }

  public OrderLinePromisingInfo productStatusId(String productStatusId) {
    this.productStatusId = productStatusId;
    return this;
  }

   /**
   * Indicates the current status of the item, For example, Out of Stock, In Stock, etc. Valid values from B 059 
   * @return productStatusId
  **/
  
  public String getProductStatusId() {
    return productStatusId;
  }

  public void setProductStatusId(String productStatusId) {
    this.productStatusId = productStatusId;
  }

  public OrderLinePromisingInfo reqCapacityPerUnit(Long reqCapacityPerUnit) {
    this.reqCapacityPerUnit = reqCapacityPerUnit;
    return this;
  }

   /**
   * Required Capacity to fulfill per unit of the Line Item
   * minimum: 0
   * maximum: -8446744073709551617
   * @return reqCapacityPerUnit
  **/
  
  public Long getReqCapacityPerUnit() {
    return reqCapacityPerUnit;
  }

  public void setReqCapacityPerUnit(Long reqCapacityPerUnit) {
    this.reqCapacityPerUnit = reqCapacityPerUnit;
  }

  public OrderLinePromisingInfo shipFromLocationId(String shipFromLocationId) {
    this.shipFromLocationId = shipFromLocationId;
    return this;
  }

   /**
   * Preferred Store Location for Allocation. Ship-to location ID, in case of ship to store, pick up in store, or any flow where the items are shipping to a destination which is configured as a location in the network
   * @return shipFromLocationId
  **/
  
  public String getShipFromLocationId() {
    return shipFromLocationId;
  }

  public void setShipFromLocationId(String shipFromLocationId) {
    this.shipFromLocationId = shipFromLocationId;
  }

  public OrderLinePromisingInfo shipThroughLocationId(String shipThroughLocationId) {
    this.shipThroughLocationId = shipThroughLocationId;
    return this;
  }

   /**
   * Shipping an item through a facility which does direct-to-consumer shipments, if the fulfilling facility (e.g. vendor) doesn&#39;t ship directly to customers
   * @return shipThroughLocationId
  **/
  
  public String getShipThroughLocationId() {
    return shipThroughLocationId;
  }

  public void setShipThroughLocationId(String shipThroughLocationId) {
    this.shipThroughLocationId = shipThroughLocationId;
  }

  public OrderLinePromisingInfo strategyType(StrategyTypeId strategyType) {
    this.strategyType = strategyType;
    return this;
  }

   /**
   * Get strategyType
   * @return strategyType
  **/
  
  public StrategyTypeId getStrategyType() {
    return strategyType;
  }

  public void setStrategyType(StrategyTypeId strategyType) {
    this.strategyType = strategyType;
  }

  public OrderLinePromisingInfo updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderLinePromisingInfo updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderLinePromisingInfo entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderLinePromisingInfo localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderLinePromisingInfo orderLinePromisingInfo = (OrderLinePromisingInfo) o;
    return Objects.equals(this.actions, orderLinePromisingInfo.actions) &&
        Objects.equals(this.asnDetailId, orderLinePromisingInfo.asnDetailId) &&
        Objects.equals(this.asnId, orderLinePromisingInfo.asnId) &&
        Objects.equals(this.batchNumber, orderLinePromisingInfo.batchNumber) &&
        Objects.equals(this.consolidatationLocationId, orderLinePromisingInfo.consolidatationLocationId) &&
        Objects.equals(this.countryOfOrigin, orderLinePromisingInfo.countryOfOrigin) &&
        Objects.equals(this.createdBy, orderLinePromisingInfo.createdBy) &&
        Objects.equals(this.createdTimestamp, orderLinePromisingInfo.createdTimestamp) &&
        Objects.equals(this.lastPossibleDeliveryDate, orderLinePromisingInfo.lastPossibleDeliveryDate) &&
        Objects.equals(this.extended, orderLinePromisingInfo.extended) &&
        Objects.equals(this.externalRouteId, orderLinePromisingInfo.externalRouteId) &&
        Objects.equals(this.inventorySegmentId, orderLinePromisingInfo.inventorySegmentId) &&
        Objects.equals(this.inventoryTypeId, orderLinePromisingInfo.inventoryTypeId) &&
        Objects.equals(this.isForceAllocate, orderLinePromisingInfo.isForceAllocate) &&
        Objects.equals(this.itemAttribute1, orderLinePromisingInfo.itemAttribute1) &&
        Objects.equals(this.itemAttribute2, orderLinePromisingInfo.itemAttribute2) &&
        Objects.equals(this.itemAttribute3, orderLinePromisingInfo.itemAttribute3) &&
        Objects.equals(this.itemAttribute4, orderLinePromisingInfo.itemAttribute4) &&
        Objects.equals(this.itemAttribute5, orderLinePromisingInfo.itemAttribute5) &&
        Objects.equals(this.localizedTo, orderLinePromisingInfo.localizedTo) &&
        Objects.equals(this.messages, orderLinePromisingInfo.messages) &&
        Objects.equals(this.orgId, orderLinePromisingInfo.orgId) &&
        Objects.equals(this.PK, orderLinePromisingInfo.PK) &&
        Objects.equals(this.poDetailId, orderLinePromisingInfo.poDetailId) &&
        Objects.equals(this.poId, orderLinePromisingInfo.poId) &&
        Objects.equals(this.productStatusId, orderLinePromisingInfo.productStatusId) &&
        Objects.equals(this.reqCapacityPerUnit, orderLinePromisingInfo.reqCapacityPerUnit) &&
        Objects.equals(this.shipFromLocationId, orderLinePromisingInfo.shipFromLocationId) &&
        Objects.equals(this.shipThroughLocationId, orderLinePromisingInfo.shipThroughLocationId) &&
        Objects.equals(this.strategyType, orderLinePromisingInfo.strategyType) &&
        Objects.equals(this.updatedBy, orderLinePromisingInfo.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderLinePromisingInfo.updatedTimestamp) &&
        Objects.equals(this.entityName, orderLinePromisingInfo.entityName) &&
        Objects.equals(this.localize, orderLinePromisingInfo.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, asnDetailId, asnId, batchNumber, consolidatationLocationId, countryOfOrigin, createdBy, createdTimestamp, lastPossibleDeliveryDate, extended, externalRouteId, inventorySegmentId, inventoryTypeId, isForceAllocate, itemAttribute1, itemAttribute2, itemAttribute3, itemAttribute4, itemAttribute5, localizedTo, messages, orgId, PK, poDetailId, poId, productStatusId, reqCapacityPerUnit, shipFromLocationId, shipThroughLocationId, strategyType, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderLinePromisingInfo {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    asnDetailId: ").append(toIndentedString(asnDetailId)).append("\n");
    sb.append("    asnId: ").append(toIndentedString(asnId)).append("\n");
    sb.append("    batchNumber: ").append(toIndentedString(batchNumber)).append("\n");
    sb.append("    consolidatationLocationId: ").append(toIndentedString(consolidatationLocationId)).append("\n");
    sb.append("    countryOfOrigin: ").append(toIndentedString(countryOfOrigin)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    lastPossibleDeliveryDate: ").append(toIndentedString(lastPossibleDeliveryDate)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    externalRouteId: ").append(toIndentedString(externalRouteId)).append("\n");
    sb.append("    inventorySegmentId: ").append(toIndentedString(inventorySegmentId)).append("\n");
    sb.append("    inventoryTypeId: ").append(toIndentedString(inventoryTypeId)).append("\n");
    sb.append("    isForceAllocate: ").append(toIndentedString(isForceAllocate)).append("\n");
    sb.append("    itemAttribute1: ").append(toIndentedString(itemAttribute1)).append("\n");
    sb.append("    itemAttribute2: ").append(toIndentedString(itemAttribute2)).append("\n");
    sb.append("    itemAttribute3: ").append(toIndentedString(itemAttribute3)).append("\n");
    sb.append("    itemAttribute4: ").append(toIndentedString(itemAttribute4)).append("\n");
    sb.append("    itemAttribute5: ").append(toIndentedString(itemAttribute5)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    poDetailId: ").append(toIndentedString(poDetailId)).append("\n");
    sb.append("    poId: ").append(toIndentedString(poId)).append("\n");
    sb.append("    productStatusId: ").append(toIndentedString(productStatusId)).append("\n");
    sb.append("    reqCapacityPerUnit: ").append(toIndentedString(reqCapacityPerUnit)).append("\n");
    sb.append("    shipFromLocationId: ").append(toIndentedString(shipFromLocationId)).append("\n");
    sb.append("    shipThroughLocationId: ").append(toIndentedString(shipThroughLocationId)).append("\n");
    sb.append("    strategyType: ").append(toIndentedString(strategyType)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

