/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderTaxConfig
 */
public class OrderTaxConfig {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_INVOICE_TAX_MODE_ID = "InvoiceTaxModeId";
  @SerializedName(SERIALIZED_NAME_INVOICE_TAX_MODE_ID)
  private String invoiceTaxModeId;

  public static final String SERIALIZED_NAME_LOCAL_TAX_CONFIG_ID = "LocalTaxConfigId";
  @SerializedName(SERIALIZED_NAME_LOCAL_TAX_CONFIG_ID)
  private String localTaxConfigId;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_OFFLINE_TAX_GATEWAY_ID = "OfflineTaxGatewayId";
  @SerializedName(SERIALIZED_NAME_OFFLINE_TAX_GATEWAY_ID)
  private String offlineTaxGatewayId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_RETURN_INVOICE_TAX_MODE = "ReturnInvoiceTaxMode";
  @SerializedName(SERIALIZED_NAME_RETURN_INVOICE_TAX_MODE)
  private ReturnInvoiceTaxMode returnInvoiceTaxMode = null;

  public static final String SERIALIZED_NAME_THIRD_PARTY_TAX_GATEWAY_ID = "ThirdPartyTaxGatewayId";
  @SerializedName(SERIALIZED_NAME_THIRD_PARTY_TAX_GATEWAY_ID)
  private String thirdPartyTaxGatewayId;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public OrderTaxConfig actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderTaxConfig putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderTaxConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderTaxConfig createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderTaxConfig extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderTaxConfig invoiceTaxModeId(String invoiceTaxModeId) {
    this.invoiceTaxModeId = invoiceTaxModeId;
    return this;
  }

   /**
   * Unique identifier for tax mode
   * @return invoiceTaxModeId
  **/
  
  public String getInvoiceTaxModeId() {
    return invoiceTaxModeId;
  }

  public void setInvoiceTaxModeId(String invoiceTaxModeId) {
    this.invoiceTaxModeId = invoiceTaxModeId;
  }

  public OrderTaxConfig localTaxConfigId(String localTaxConfigId) {
    this.localTaxConfigId = localTaxConfigId;
    return this;
  }

   /**
   * When LocalTax is selected as tax gateway, this attribute drives the tax process by informing LocalTax what config needs to be picked for taxing the given Order
   * @return localTaxConfigId
  **/
  
  public String getLocalTaxConfigId() {
    return localTaxConfigId;
  }

  public void setLocalTaxConfigId(String localTaxConfigId) {
    this.localTaxConfigId = localTaxConfigId;
  }

  public OrderTaxConfig localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderTaxConfig messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderTaxConfig offlineTaxGatewayId(String offlineTaxGatewayId) {
    this.offlineTaxGatewayId = offlineTaxGatewayId;
    return this;
  }

   /**
   * Indicates the local tax engine for in house tax calculations. This will be selected from the tax gateway entity where taxgateway is defined as offline mode.
   * @return offlineTaxGatewayId
  **/
  
  public String getOfflineTaxGatewayId() {
    return offlineTaxGatewayId;
  }

  public void setOfflineTaxGatewayId(String offlineTaxGatewayId) {
    this.offlineTaxGatewayId = offlineTaxGatewayId;
  }

  public OrderTaxConfig PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderTaxConfig profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public OrderTaxConfig returnInvoiceTaxMode(ReturnInvoiceTaxMode returnInvoiceTaxMode) {
    this.returnInvoiceTaxMode = returnInvoiceTaxMode;
    return this;
  }

   /**
   * Get returnInvoiceTaxMode
   * @return returnInvoiceTaxMode
  **/
  
  public ReturnInvoiceTaxMode getReturnInvoiceTaxMode() {
    return returnInvoiceTaxMode;
  }

  public void setReturnInvoiceTaxMode(ReturnInvoiceTaxMode returnInvoiceTaxMode) {
    this.returnInvoiceTaxMode = returnInvoiceTaxMode;
  }

  public OrderTaxConfig thirdPartyTaxGatewayId(String thirdPartyTaxGatewayId) {
    this.thirdPartyTaxGatewayId = thirdPartyTaxGatewayId;
    return this;
  }

   /**
   * Indicates the third party tax engine for tax calculations. This will be selected from the tax gateway entity.
   * @return thirdPartyTaxGatewayId
  **/
  
  public String getThirdPartyTaxGatewayId() {
    return thirdPartyTaxGatewayId;
  }

  public void setThirdPartyTaxGatewayId(String thirdPartyTaxGatewayId) {
    this.thirdPartyTaxGatewayId = thirdPartyTaxGatewayId;
  }

  public OrderTaxConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderTaxConfig updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderTaxConfig rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderTaxConfig orderTaxConfig = (OrderTaxConfig) o;
    return Objects.equals(this.actions, orderTaxConfig.actions) &&
        Objects.equals(this.createdBy, orderTaxConfig.createdBy) &&
        Objects.equals(this.createdTimestamp, orderTaxConfig.createdTimestamp) &&
        Objects.equals(this.extended, orderTaxConfig.extended) &&
        Objects.equals(this.invoiceTaxModeId, orderTaxConfig.invoiceTaxModeId) &&
        Objects.equals(this.localTaxConfigId, orderTaxConfig.localTaxConfigId) &&
        Objects.equals(this.localizedTo, orderTaxConfig.localizedTo) &&
        Objects.equals(this.messages, orderTaxConfig.messages) &&
        Objects.equals(this.offlineTaxGatewayId, orderTaxConfig.offlineTaxGatewayId) &&
        Objects.equals(this.PK, orderTaxConfig.PK) &&
        Objects.equals(this.profileId, orderTaxConfig.profileId) &&
        Objects.equals(this.returnInvoiceTaxMode, orderTaxConfig.returnInvoiceTaxMode) &&
        Objects.equals(this.thirdPartyTaxGatewayId, orderTaxConfig.thirdPartyTaxGatewayId) &&
        Objects.equals(this.updatedBy, orderTaxConfig.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderTaxConfig.updatedTimestamp) &&
        Objects.equals(this.rootCause, orderTaxConfig.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, extended, invoiceTaxModeId, localTaxConfigId, localizedTo, messages, offlineTaxGatewayId, PK, profileId, returnInvoiceTaxMode, thirdPartyTaxGatewayId, updatedBy, updatedTimestamp, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderTaxConfig {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    invoiceTaxModeId: ").append(toIndentedString(invoiceTaxModeId)).append("\n");
    sb.append("    localTaxConfigId: ").append(toIndentedString(localTaxConfigId)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    offlineTaxGatewayId: ").append(toIndentedString(offlineTaxGatewayId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    returnInvoiceTaxMode: ").append(toIndentedString(returnInvoiceTaxMode)).append("\n");
    sb.append("    thirdPartyTaxGatewayId: ").append(toIndentedString(thirdPartyTaxGatewayId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

