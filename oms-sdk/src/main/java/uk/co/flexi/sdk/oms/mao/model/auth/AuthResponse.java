/*
 * API Authorization
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.auth;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * This is the response of OAuth token and having access token details.
 */
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2024-12-05T14:42:51.402353Z[Europe/London]")
public class AuthResponse {
  public static final String SERIALIZED_NAME_ACCESS_TOKEN = "access_token";
  @SerializedName(SERIALIZED_NAME_ACCESS_TOKEN)
  private String accessToken;

  public static final String SERIALIZED_NAME_REFRESH_TOKEN = "refresh_token";
  @SerializedName(SERIALIZED_NAME_REFRESH_TOKEN)
  private String refreshToken;

  public static final String SERIALIZED_NAME_TOKEN_TYPE = "token_type";
  @SerializedName(SERIALIZED_NAME_TOKEN_TYPE)
  private String tokenType;

  public static final String SERIALIZED_NAME_EXPIRES_IN = "expires_in";
  @SerializedName(SERIALIZED_NAME_EXPIRES_IN)
  private Integer expiresIn;

  public static final String SERIALIZED_NAME_SCOPE = "scope";
  @SerializedName(SERIALIZED_NAME_SCOPE)
  private String scope;

  public static final String SERIALIZED_NAME_USER_ORGS = "userOrgs";
  @SerializedName(SERIALIZED_NAME_USER_ORGS)
  private List<String> userOrgs;

  public static final String SERIALIZED_NAME_EDGE = "edge";
  @SerializedName(SERIALIZED_NAME_EDGE)
  private Integer edge;

  public static final String SERIALIZED_NAME_ORGANIZATION = "organization";
  @SerializedName(SERIALIZED_NAME_ORGANIZATION)
  private String organization;

  public static final String SERIALIZED_NAME_USER_LOCATIONS = "userLocations";
  @SerializedName(SERIALIZED_NAME_USER_LOCATIONS)
  private List<ResponseUserLocationsInner> userLocations;

  public static final String SERIALIZED_NAME_ACCESSTO_ALL_B_US = "accesstoAllBUs";
  @SerializedName(SERIALIZED_NAME_ACCESSTO_ALL_B_US)
  private Boolean accesstoAllBUs;

  public static final String SERIALIZED_NAME_TENANT_ID = "tenantId";
  @SerializedName(SERIALIZED_NAME_TENANT_ID)
  private String tenantId;

  public static final String SERIALIZED_NAME_LOCALE = "locale";
  @SerializedName(SERIALIZED_NAME_LOCALE)
  private String locale;

  public static final String SERIALIZED_NAME_EXCLUDED_USER_BUSINESS_UNITS = "excludedUserBusinessUnits";
  @SerializedName(SERIALIZED_NAME_EXCLUDED_USER_BUSINESS_UNITS)
  private List<Object> excludedUserBusinessUnits;

  public static final String SERIALIZED_NAME_USER_DEFAULTS = "userDefaults";
  @SerializedName(SERIALIZED_NAME_USER_DEFAULTS)
  private List<Object> userDefaults;

  public static final String SERIALIZED_NAME_USER_BUSINESS_UNITS = "userBusinessUnits";
  @SerializedName(SERIALIZED_NAME_USER_BUSINESS_UNITS)
  private List<Object> userBusinessUnits;

  public static final String SERIALIZED_NAME_USER_TIME_ZONE = "userTimeZone";
  @SerializedName(SERIALIZED_NAME_USER_TIME_ZONE)
  private String userTimeZone;

  public static final String SERIALIZED_NAME_JTI = "jti";
  @SerializedName(SERIALIZED_NAME_JTI)
  private String jti;

  public AuthResponse() {
  }

  public AuthResponse accessToken(String accessToken) {
    this.accessToken = accessToken;
    return this;
  }

   /**
   * The OAuth access token
   * @return accessToken
  **/
  @jakarta.annotation.Nullable
  public String getAccessToken() {
    return accessToken;
  }


  public void setAccessToken(String accessToken) {
    this.accessToken = accessToken;
  }


  public AuthResponse refreshToken(String refreshToken) {
    
    this.refreshToken = refreshToken;
    return this;
  }

   /**
   * The OAuth refresh token
   * @return refreshToken
  **/
  @jakarta.annotation.Nullable
  public String getRefreshToken() {
    return refreshToken;
  }


  public void setRefreshToken(String refreshToken) {
    this.refreshToken = refreshToken;
  }


  public AuthResponse tokenType(String tokenType) {
    
    this.tokenType = tokenType;
    return this;
  }

   /**
   * The type of token issued.
   * @return tokenType
  **/
  @jakarta.annotation.Nullable
  public String getTokenType() {
    return tokenType;
  }


  public void setTokenType(String tokenType) {
    this.tokenType = tokenType;
  }


  public AuthResponse expiresIn(Integer expiresIn) {
    
    this.expiresIn = expiresIn;
    return this;
  }

   /**
   * Time (in seconds) until the access token expires.
   * @return expiresIn
  **/
  @jakarta.annotation.Nullable
  public Integer getExpiresIn() {
    return expiresIn;
  }


  public void setExpiresIn(Integer expiresIn) {
    this.expiresIn = expiresIn;
  }


  public AuthResponse scope(String scope) {
    
    this.scope = scope;
    return this;
  }

   /**
   * Indicates the scope of authorization
   * @return scope
  **/
  @jakarta.annotation.Nullable
  public String getScope() {
    return scope;
  }


  public void setScope(String scope) {
    this.scope = scope;
  }


  public AuthResponse userOrgs(List<String> userOrgs) {
    
    this.userOrgs = userOrgs;
    return this;
  }

  public AuthResponse addUserOrgsItem(String userOrgsItem) {
    if (this.userOrgs == null) {
      this.userOrgs = new ArrayList<>();
    }
    this.userOrgs.add(userOrgsItem);
    return this;
  }

   /**
   * The organizations associated with the user.
   * @return userOrgs
  **/
  @jakarta.annotation.Nullable
  public List<String> getUserOrgs() {
    return userOrgs;
  }


  public void setUserOrgs(List<String> userOrgs) {
    this.userOrgs = userOrgs;
  }


  public AuthResponse edge(Integer edge) {
    
    this.edge = edge;
    return this;
  }

   /**
   * Edge information related to the user.
   * @return edge
  **/
  @jakarta.annotation.Nullable
  public Integer getEdge() {
    return edge;
  }


  public void setEdge(Integer edge) {
    this.edge = edge;
  }


  public AuthResponse organization(String organization) {
    
    this.organization = organization;
    return this;
  }

   /**
   * The organization to which the user belongs.
   * @return organization
  **/
  @jakarta.annotation.Nullable
  public String getOrganization() {
    return organization;
  }


  public void setOrganization(String organization) {
    this.organization = organization;
  }


  public AuthResponse userLocations(List<ResponseUserLocationsInner> userLocations) {
    
    this.userLocations = userLocations;
    return this;
  }

  public AuthResponse addUserLocationsItem(ResponseUserLocationsInner userLocationsItem) {
    if (this.userLocations == null) {
      this.userLocations = new ArrayList<>();
    }
    this.userLocations.add(userLocationsItem);
    return this;
  }

   /**
   * The locations associated with the user.
   * @return userLocations
  **/
  @jakarta.annotation.Nullable
  public List<ResponseUserLocationsInner> getUserLocations() {
    return userLocations;
  }


  public void setUserLocations(List<ResponseUserLocationsInner> userLocations) {
    this.userLocations = userLocations;
  }


  public AuthResponse accesstoAllBUs(Boolean accesstoAllBUs) {
    
    this.accesstoAllBUs = accesstoAllBUs;
    return this;
  }

   /**
   * Indicates whether the user has access to all business units.
   * @return accesstoAllBUs
  **/
  @jakarta.annotation.Nullable
  public Boolean getAccesstoAllBUs() {
    return accesstoAllBUs;
  }


  public void setAccesstoAllBUs(Boolean accesstoAllBUs) {
    this.accesstoAllBUs = accesstoAllBUs;
  }


  public AuthResponse tenantId(String tenantId) {
    
    this.tenantId = tenantId;
    return this;
  }

   /**
   * The tenant ID associated with the user.
   * @return tenantId
  **/
  @jakarta.annotation.Nullable
  public String getTenantId() {
    return tenantId;
  }


  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }


  public AuthResponse locale(String locale) {
    
    this.locale = locale;
    return this;
  }

   /**
   * The locale of the user.
   * @return locale
  **/
  @jakarta.annotation.Nullable
  public String getLocale() {
    return locale;
  }


  public void setLocale(String locale) {
    this.locale = locale;
  }


  public AuthResponse excludedUserBusinessUnits(List<Object> excludedUserBusinessUnits) {
    
    this.excludedUserBusinessUnits = excludedUserBusinessUnits;
    return this;
  }

  public AuthResponse addExcludedUserBusinessUnitsItem(Object excludedUserBusinessUnitsItem) {
    if (this.excludedUserBusinessUnits == null) {
      this.excludedUserBusinessUnits = new ArrayList<>();
    }
    this.excludedUserBusinessUnits.add(excludedUserBusinessUnitsItem);
    return this;
  }

   /**
   * A list of excluded user business units.
   * @return excludedUserBusinessUnits
  **/
  @jakarta.annotation.Nullable
  public List<Object> getExcludedUserBusinessUnits() {
    return excludedUserBusinessUnits;
  }


  public void setExcludedUserBusinessUnits(List<Object> excludedUserBusinessUnits) {
    this.excludedUserBusinessUnits = excludedUserBusinessUnits;
  }


  public AuthResponse userDefaults(List<Object> userDefaults) {
    
    this.userDefaults = userDefaults;
    return this;
  }

  public AuthResponse addUserDefaultsItem(Object userDefaultsItem) {
    if (this.userDefaults == null) {
      this.userDefaults = new ArrayList<>();
    }
    this.userDefaults.add(userDefaultsItem);
    return this;
  }

   /**
   * The default settings for the user.
   * @return userDefaults
  **/
  @jakarta.annotation.Nullable
  public List<Object> getUserDefaults() {
    return userDefaults;
  }


  public void setUserDefaults(List<Object> userDefaults) {
    this.userDefaults = userDefaults;
  }


  public AuthResponse userBusinessUnits(List<Object> userBusinessUnits) {
    
    this.userBusinessUnits = userBusinessUnits;
    return this;
  }

  public AuthResponse addUserBusinessUnitsItem(Object userBusinessUnitsItem) {
    if (this.userBusinessUnits == null) {
      this.userBusinessUnits = new ArrayList<>();
    }
    this.userBusinessUnits.add(userBusinessUnitsItem);
    return this;
  }

   /**
   * The business units associated with the user.
   * @return userBusinessUnits
  **/
  @jakarta.annotation.Nullable
  public List<Object> getUserBusinessUnits() {
    return userBusinessUnits;
  }


  public void setUserBusinessUnits(List<Object> userBusinessUnits) {
    this.userBusinessUnits = userBusinessUnits;
  }


  public AuthResponse userTimeZone(String userTimeZone) {
    
    this.userTimeZone = userTimeZone;
    return this;
  }

   /**
   * The time zone of the user.
   * @return userTimeZone
  **/
  @jakarta.annotation.Nullable
  public String getUserTimeZone() {
    return userTimeZone;
  }


  public void setUserTimeZone(String userTimeZone) {
    this.userTimeZone = userTimeZone;
  }


  public AuthResponse jti(String jti) {
    
    this.jti = jti;
    return this;
  }

   /**
   * The unique identifier for the token.
   * @return jti
  **/
  @jakarta.annotation.Nullable
  public String getJti() {
    return jti;
  }


  public void setJti(String jti) {
    this.jti = jti;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AuthResponse response = (AuthResponse) o;
    return Objects.equals(this.accessToken, response.accessToken) &&
        Objects.equals(this.refreshToken, response.refreshToken) &&
        Objects.equals(this.tokenType, response.tokenType) &&
        Objects.equals(this.expiresIn, response.expiresIn) &&
        Objects.equals(this.scope, response.scope) &&
        Objects.equals(this.userOrgs, response.userOrgs) &&
        Objects.equals(this.edge, response.edge) &&
        Objects.equals(this.organization, response.organization) &&
        Objects.equals(this.userLocations, response.userLocations) &&
        Objects.equals(this.accesstoAllBUs, response.accesstoAllBUs) &&
        Objects.equals(this.tenantId, response.tenantId) &&
        Objects.equals(this.locale, response.locale) &&
        Objects.equals(this.excludedUserBusinessUnits, response.excludedUserBusinessUnits) &&
        Objects.equals(this.userDefaults, response.userDefaults) &&
        Objects.equals(this.userBusinessUnits, response.userBusinessUnits) &&
        Objects.equals(this.userTimeZone, response.userTimeZone) &&
        Objects.equals(this.jti, response.jti);
  }

  @Override
  public int hashCode() {
    return Objects.hash(accessToken, refreshToken, tokenType, expiresIn, scope, userOrgs, edge, organization, userLocations, accesstoAllBUs, tenantId, locale, excludedUserBusinessUnits, userDefaults, userBusinessUnits, userTimeZone, jti);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Response {\n");
    sb.append("    accessToken: ").append(toIndentedString(accessToken)).append("\n");
    sb.append("    refreshToken: ").append(toIndentedString(refreshToken)).append("\n");
    sb.append("    tokenType: ").append(toIndentedString(tokenType)).append("\n");
    sb.append("    expiresIn: ").append(toIndentedString(expiresIn)).append("\n");
    sb.append("    scope: ").append(toIndentedString(scope)).append("\n");
    sb.append("    userOrgs: ").append(toIndentedString(userOrgs)).append("\n");
    sb.append("    edge: ").append(toIndentedString(edge)).append("\n");
    sb.append("    organization: ").append(toIndentedString(organization)).append("\n");
    sb.append("    userLocations: ").append(toIndentedString(userLocations)).append("\n");
    sb.append("    accesstoAllBUs: ").append(toIndentedString(accesstoAllBUs)).append("\n");
    sb.append("    tenantId: ").append(toIndentedString(tenantId)).append("\n");
    sb.append("    locale: ").append(toIndentedString(locale)).append("\n");
    sb.append("    excludedUserBusinessUnits: ").append(toIndentedString(excludedUserBusinessUnits)).append("\n");
    sb.append("    userDefaults: ").append(toIndentedString(userDefaults)).append("\n");
    sb.append("    userBusinessUnits: ").append(toIndentedString(userBusinessUnits)).append("\n");
    sb.append("    userTimeZone: ").append(toIndentedString(userTimeZone)).append("\n");
    sb.append("    jti: ").append(toIndentedString(jti)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

