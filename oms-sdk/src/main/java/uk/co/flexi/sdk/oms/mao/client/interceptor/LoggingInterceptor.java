package uk.co.flexi.sdk.oms.mao.client.interceptor;

import okhttp3.*;
import okio.Buffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

public class LoggingInterceptor implements Interceptor {

	private static final Logger log = LoggerFactory.getLogger(LoggingInterceptor.class);
	@Override
	public Response intercept(Chain chain) throws IOException {
		Request request = chain.request();

		long t1 = System.nanoTime();
		log.info("Request: " + String.format("--> Sending request %s on %s%n%s", request.url(), chain.connection(), request.headers()));

		Buffer requestBuffer = new Buffer();
		RequestBody body = request.body();
		if (body != null) {
			body.writeTo(requestBuffer);
			log.info("Request Body: " + requestBuffer.readUtf8());
		}

		Response response = chain.proceed(request);

		long t2 = System.nanoTime();
		log.info("Response Time: " + String.format("<-- Received response for %s in %.1fms%n%s", response.request().url(), (t2 - t1) / 1e6d, response.headers()));

		MediaType contentType = response.body().contentType();
		String content = response.body().string();
		log.info("Response Body" + content);

		ResponseBody wrappedBody = ResponseBody.create(contentType, content);
		return response.newBuilder().body(wrappedBody).build();
	}
}