/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * InvoiceConfig
 */
public class InvoiceConfig {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DELIVERY_METHOD = "DeliveryMethod";
  @SerializedName(SERIALIZED_NAME_DELIVERY_METHOD)
  private DeliveryMethod deliveryMethod = null;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_INVOICE_ON_MERGE_LEG = "InvoiceOnMergeLeg";
  @SerializedName(SERIALIZED_NAME_INVOICE_ON_MERGE_LEG)
  private Boolean invoiceOnMergeLeg;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_MIN_FULFILLMENT_STATUS = "MinFulfillmentStatus";
  @SerializedName(SERIALIZED_NAME_MIN_FULFILLMENT_STATUS)
  private KeyDTO minFulfillmentStatus = null;

  public static final String SERIALIZED_NAME_MIN_FULFILLMENT_STATUS_ID = "MinFulfillmentStatusId";
  @SerializedName(SERIALIZED_NAME_MIN_FULFILLMENT_STATUS_ID)
  private String minFulfillmentStatusId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_READY_FOR_TENDER_REQUIRED = "ReadyForTenderRequired";
  @SerializedName(SERIALIZED_NAME_READY_FOR_TENDER_REQUIRED)
  private Boolean readyForTenderRequired;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public InvoiceConfig actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public InvoiceConfig putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public InvoiceConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public InvoiceConfig createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public InvoiceConfig deliveryMethod(DeliveryMethod deliveryMethod) {
    this.deliveryMethod = deliveryMethod;
    return this;
  }

   /**
   * Get deliveryMethod
   * @return deliveryMethod
  **/
  
  public DeliveryMethod getDeliveryMethod() {
    return deliveryMethod;
  }

  public void setDeliveryMethod(DeliveryMethod deliveryMethod) {
    this.deliveryMethod = deliveryMethod;
  }

  public InvoiceConfig extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public InvoiceConfig invoiceOnMergeLeg(Boolean invoiceOnMergeLeg) {
    this.invoiceOnMergeLeg = invoiceOnMergeLeg;
    return this;
  }

   /**
   * Indicates if a shipment invoice should be created for this delivery method, when a last merge leg is shipped. This setting can be used to initiate invoicing and settlement at an earlier stage in the fulfillment lifecycle. If set to false, then shipment invoices are created when the final leg ships. Otherwise, shipment invoices are created when the last merge leg ships. For example, if a ship to store order ships from DC to store for customer pickup, then if this attribute is set to true, a shipment invoice is created when the DC ships to the store. If this attribute is false, then a shipment invoice is created when the customer picks up the item from the store.
   * @return invoiceOnMergeLeg
  **/
  
  public Boolean getInvoiceOnMergeLeg() {
    return invoiceOnMergeLeg;
  }

  public void setInvoiceOnMergeLeg(Boolean invoiceOnMergeLeg) {
    this.invoiceOnMergeLeg = invoiceOnMergeLeg;
  }

  public InvoiceConfig localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public InvoiceConfig messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public InvoiceConfig minFulfillmentStatus(KeyDTO minFulfillmentStatus) {
    this.minFulfillmentStatus = minFulfillmentStatus;
    return this;
  }

   /**
   * Get minFulfillmentStatus
   * @return minFulfillmentStatus
  **/
  
  public KeyDTO getMinFulfillmentStatus() {
    return minFulfillmentStatus;
  }

  public void setMinFulfillmentStatus(KeyDTO minFulfillmentStatus) {
    this.minFulfillmentStatus = minFulfillmentStatus;
  }

  public InvoiceConfig minFulfillmentStatusId(String minFulfillmentStatusId) {
    this.minFulfillmentStatusId = minFulfillmentStatusId;
    return this;
  }

   /**
   * Minimum status in which quantities for this delivery method are invoiced. Use a status which is included in the pipeline for the given delivery method, to ensure all lines are invoiced. For example, if the minimum status is 7000 for delivery method Pick up in store, then when any quantity in status 7000 is created for a Pick up in store line, the quantity is invoiced and settlement is initiated
   * @return minFulfillmentStatusId
  **/
  
  public String getMinFulfillmentStatusId() {
    return minFulfillmentStatusId;
  }

  public void setMinFulfillmentStatusId(String minFulfillmentStatusId) {
    this.minFulfillmentStatusId = minFulfillmentStatusId;
  }

  public InvoiceConfig PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public InvoiceConfig profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public InvoiceConfig readyForTenderRequired(Boolean readyForTenderRequired) {
    this.readyForTenderRequired = readyForTenderRequired;
    return this;
  }

   /**
   * Indicates if the order-level readyForTender flag must be true for invoicing to occur for this delivery method. Used to trigger invoicing for point of sale orders, which need to be invoiced and settled while the customer is at the cash wrap. If false, then readyForTender is not read by the invoicing service. For example, if delivery method Ship to an address has minFulfillmentStatus of 7000 and readyForTenderRequired set to false, then any quantities created in status 7000 or greater are invoiced, regardless of readyForTenderRequired. If delivery method Store Sale has minFulfillmentStatus of 1000 and readyForTenderRequired set to true, then any quantities created in status 1000 or greater are invoiced, only if readyForTender is set to true.
   * @return readyForTenderRequired
  **/
  
  public Boolean getReadyForTenderRequired() {
    return readyForTenderRequired;
  }

  public void setReadyForTenderRequired(Boolean readyForTenderRequired) {
    this.readyForTenderRequired = readyForTenderRequired;
  }

  public InvoiceConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public InvoiceConfig updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public InvoiceConfig rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvoiceConfig invoiceConfig = (InvoiceConfig) o;
    return Objects.equals(this.actions, invoiceConfig.actions) &&
        Objects.equals(this.createdBy, invoiceConfig.createdBy) &&
        Objects.equals(this.createdTimestamp, invoiceConfig.createdTimestamp) &&
        Objects.equals(this.deliveryMethod, invoiceConfig.deliveryMethod) &&
        Objects.equals(this.extended, invoiceConfig.extended) &&
        Objects.equals(this.invoiceOnMergeLeg, invoiceConfig.invoiceOnMergeLeg) &&
        Objects.equals(this.localizedTo, invoiceConfig.localizedTo) &&
        Objects.equals(this.messages, invoiceConfig.messages) &&
        Objects.equals(this.minFulfillmentStatus, invoiceConfig.minFulfillmentStatus) &&
        Objects.equals(this.minFulfillmentStatusId, invoiceConfig.minFulfillmentStatusId) &&
        Objects.equals(this.PK, invoiceConfig.PK) &&
        Objects.equals(this.profileId, invoiceConfig.profileId) &&
        Objects.equals(this.readyForTenderRequired, invoiceConfig.readyForTenderRequired) &&
        Objects.equals(this.updatedBy, invoiceConfig.updatedBy) &&
        Objects.equals(this.updatedTimestamp, invoiceConfig.updatedTimestamp) &&
        Objects.equals(this.rootCause, invoiceConfig.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, deliveryMethod, extended, invoiceOnMergeLeg, localizedTo, messages, minFulfillmentStatus, minFulfillmentStatusId, PK, profileId, readyForTenderRequired, updatedBy, updatedTimestamp, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvoiceConfig {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    deliveryMethod: ").append(toIndentedString(deliveryMethod)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    invoiceOnMergeLeg: ").append(toIndentedString(invoiceOnMergeLeg)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    minFulfillmentStatus: ").append(toIndentedString(minFulfillmentStatus)).append("\n");
    sb.append("    minFulfillmentStatusId: ").append(toIndentedString(minFulfillmentStatusId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    readyForTenderRequired: ").append(toIndentedString(readyForTenderRequired)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

