/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * EventPublishingType
 */
public class EventPublishingType {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CHANGE_EVENT_ENTITY = "ChangeEventEntity";
  @SerializedName(SERIALIZED_NAME_CHANGE_EVENT_ENTITY)
  private String changeEventEntity;

  public static final String SERIALIZED_NAME_CHANGE_EVENT_TYPE = "ChangeEventType";
  @SerializedName(SERIALIZED_NAME_CHANGE_EVENT_TYPE)
  private String changeEventType;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DISPLAY_SEQUENCE = "DisplaySequence";
  @SerializedName(SERIALIZED_NAME_DISPLAY_SEQUENCE)
  private Long displaySequence;

  public static final String SERIALIZED_NAME_EVENT_PUBLISHING_TYPE_ID = "EventPublishingTypeId";
  @SerializedName(SERIALIZED_NAME_EVENT_PUBLISHING_TYPE_ID)
  private String eventPublishingTypeId;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public EventPublishingType actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public EventPublishingType putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public EventPublishingType changeEventEntity(String changeEventEntity) {
    this.changeEventEntity = changeEventEntity;
    return this;
  }

   /**
   * Entity for which this change event is generated (only Order is supported now)
   * @return changeEventEntity
  **/
  
  public String getChangeEventEntity() {
    return changeEventEntity;
  }

  public void setChangeEventEntity(String changeEventEntity) {
    this.changeEventEntity = changeEventEntity;
  }

  public EventPublishingType changeEventType(String changeEventType) {
    this.changeEventType = changeEventType;
    return this;
  }

   /**
   * Change event type that triggers outbound notification
   * @return changeEventType
  **/
  
  public String getChangeEventType() {
    return changeEventType;
  }

  public void setChangeEventType(String changeEventType) {
    this.changeEventType = changeEventType;
  }

  public EventPublishingType createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public EventPublishingType createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public EventPublishingType displaySequence(Long displaySequence) {
    this.displaySequence = displaySequence;
    return this;
  }

   /**
   * Display sequence of publishing types in UI
   * minimum: 0
   * maximum: -8446744073709551617
   * @return displaySequence
  **/
  
  public Long getDisplaySequence() {
    return displaySequence;
  }

  public void setDisplaySequence(Long displaySequence) {
    this.displaySequence = displaySequence;
  }

  public EventPublishingType eventPublishingTypeId(String eventPublishingTypeId) {
    this.eventPublishingTypeId = eventPublishingTypeId;
    return this;
  }

   /**
   * Unique identifier for event publishing type
   * @return eventPublishingTypeId
  **/
  
  public String getEventPublishingTypeId() {
    return eventPublishingTypeId;
  }

  public void setEventPublishingTypeId(String eventPublishingTypeId) {
    this.eventPublishingTypeId = eventPublishingTypeId;
  }

  public EventPublishingType extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public EventPublishingType localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public EventPublishingType messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public EventPublishingType PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public EventPublishingType profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public EventPublishingType updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public EventPublishingType updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public EventPublishingType rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EventPublishingType eventPublishingType = (EventPublishingType) o;
    return Objects.equals(this.actions, eventPublishingType.actions) &&
        Objects.equals(this.changeEventEntity, eventPublishingType.changeEventEntity) &&
        Objects.equals(this.changeEventType, eventPublishingType.changeEventType) &&
        Objects.equals(this.createdBy, eventPublishingType.createdBy) &&
        Objects.equals(this.createdTimestamp, eventPublishingType.createdTimestamp) &&
        Objects.equals(this.displaySequence, eventPublishingType.displaySequence) &&
        Objects.equals(this.eventPublishingTypeId, eventPublishingType.eventPublishingTypeId) &&
        Objects.equals(this.extended, eventPublishingType.extended) &&
        Objects.equals(this.localizedTo, eventPublishingType.localizedTo) &&
        Objects.equals(this.messages, eventPublishingType.messages) &&
        Objects.equals(this.PK, eventPublishingType.PK) &&
        Objects.equals(this.profileId, eventPublishingType.profileId) &&
        Objects.equals(this.updatedBy, eventPublishingType.updatedBy) &&
        Objects.equals(this.updatedTimestamp, eventPublishingType.updatedTimestamp) &&
        Objects.equals(this.rootCause, eventPublishingType.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, changeEventEntity, changeEventType, createdBy, createdTimestamp, displaySequence, eventPublishingTypeId, extended, localizedTo, messages, PK, profileId, updatedBy, updatedTimestamp, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EventPublishingType {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    changeEventEntity: ").append(toIndentedString(changeEventEntity)).append("\n");
    sb.append("    changeEventType: ").append(toIndentedString(changeEventType)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    displaySequence: ").append(toIndentedString(displaySequence)).append("\n");
    sb.append("    eventPublishingTypeId: ").append(toIndentedString(eventPublishingTypeId)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

