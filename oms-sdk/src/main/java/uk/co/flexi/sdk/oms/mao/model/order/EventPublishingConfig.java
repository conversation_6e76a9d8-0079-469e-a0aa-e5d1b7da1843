/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * EventPublishingConfig
 */
public class EventPublishingConfig {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_ENABLED = "Enabled";
  @SerializedName(SERIALIZED_NAME_ENABLED)
  private Boolean enabled;

  public static final String SERIALIZED_NAME_EVENT_PUBLISHING_TYPE = "EventPublishingType";
  @SerializedName(SERIALIZED_NAME_EVENT_PUBLISHING_TYPE)
  private EventPublishingType eventPublishingType = null;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_PUBLISH_EVENT_NAME = "PublishEventName";
  @SerializedName(SERIALIZED_NAME_PUBLISH_EVENT_NAME)
  private String publishEventName;

  public static final String SERIALIZED_NAME_TEMPLATE_ID = "TemplateId";
  @SerializedName(SERIALIZED_NAME_TEMPLATE_ID)
  private String templateId;

  public static final String SERIALIZED_NAME_TRANSLATIONS = "Translations";
  @SerializedName(SERIALIZED_NAME_TRANSLATIONS)
  private Map<String, Map<String, String>> translations = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public EventPublishingConfig actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public EventPublishingConfig putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public EventPublishingConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public EventPublishingConfig createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public EventPublishingConfig enabled(Boolean enabled) {
    this.enabled = enabled;
    return this;
  }

   /**
   * If set to false then the outbound notification is suppressed
   * @return enabled
  **/
  
  public Boolean getEnabled() {
    return enabled;
  }

  public void setEnabled(Boolean enabled) {
    this.enabled = enabled;
  }

  public EventPublishingConfig eventPublishingType(EventPublishingType eventPublishingType) {
    this.eventPublishingType = eventPublishingType;
    return this;
  }

   /**
   * Get eventPublishingType
   * @return eventPublishingType
  **/
  
  public EventPublishingType getEventPublishingType() {
    return eventPublishingType;
  }

  public void setEventPublishingType(EventPublishingType eventPublishingType) {
    this.eventPublishingType = eventPublishingType;
  }

  public EventPublishingConfig extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public EventPublishingConfig messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public EventPublishingConfig PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public EventPublishingConfig profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public EventPublishingConfig publishEventName(String publishEventName) {
    this.publishEventName = publishEventName;
    return this;
  }

   /**
   * Event name for outbound notification
   * @return publishEventName
  **/
  
  public String getPublishEventName() {
    return publishEventName;
  }

  public void setPublishEventName(String publishEventName) {
    this.publishEventName = publishEventName;
  }

  public EventPublishingConfig templateId(String templateId) {
    this.templateId = templateId;
    return this;
  }

   /**
   * Template Id, used to format data for outbound notification
   * @return templateId
  **/
  
  public String getTemplateId() {
    return templateId;
  }

  public void setTemplateId(String templateId) {
    this.templateId = templateId;
  }

  public EventPublishingConfig translations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
    return this;
  }

  public EventPublishingConfig putTranslationsItem(String key, Map<String, String> translationsItem) {
    if (this.translations == null) {
      this.translations = new HashMap<String, Map<String, String>>();
    }
    this.translations.put(key, translationsItem);
    return this;
  }

   /**
   * Get translations
   * @return translations
  **/
  
  public Map<String, Map<String, String>> getTranslations() {
    return translations;
  }

  public void setTranslations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
  }

  public EventPublishingConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public EventPublishingConfig updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public EventPublishingConfig rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EventPublishingConfig eventPublishingConfig = (EventPublishingConfig) o;
    return Objects.equals(this.actions, eventPublishingConfig.actions) &&
        Objects.equals(this.createdBy, eventPublishingConfig.createdBy) &&
        Objects.equals(this.createdTimestamp, eventPublishingConfig.createdTimestamp) &&
        Objects.equals(this.enabled, eventPublishingConfig.enabled) &&
        Objects.equals(this.eventPublishingType, eventPublishingConfig.eventPublishingType) &&
        Objects.equals(this.extended, eventPublishingConfig.extended) &&
        Objects.equals(this.messages, eventPublishingConfig.messages) &&
        Objects.equals(this.PK, eventPublishingConfig.PK) &&
        Objects.equals(this.profileId, eventPublishingConfig.profileId) &&
        Objects.equals(this.publishEventName, eventPublishingConfig.publishEventName) &&
        Objects.equals(this.templateId, eventPublishingConfig.templateId) &&
        Objects.equals(this.translations, eventPublishingConfig.translations) &&
        Objects.equals(this.updatedBy, eventPublishingConfig.updatedBy) &&
        Objects.equals(this.updatedTimestamp, eventPublishingConfig.updatedTimestamp) &&
        Objects.equals(this.rootCause, eventPublishingConfig.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, enabled, eventPublishingType, extended, messages, PK, profileId, publishEventName, templateId, translations, updatedBy, updatedTimestamp, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EventPublishingConfig {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    enabled: ").append(toIndentedString(enabled)).append("\n");
    sb.append("    eventPublishingType: ").append(toIndentedString(eventPublishingType)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    publishEventName: ").append(toIndentedString(publishEventName)).append("\n");
    sb.append("    templateId: ").append(toIndentedString(templateId)).append("\n");
    sb.append("    translations: ").append(toIndentedString(translations)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

