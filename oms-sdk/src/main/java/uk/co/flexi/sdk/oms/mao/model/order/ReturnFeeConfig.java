/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ReturnFeeConfig
 */
public class ReturnFeeConfig {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_AMOUNT = "Amount";
  @SerializedName(SERIALIZED_NAME_AMOUNT)
  private BigDecimal amount;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_CUSTOMER_TYPE_ID = "CustomerTypeId";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_TYPE_ID)
  private String customerTypeId;

  public static final String SERIALIZED_NAME_DESCRIPTION = "Description";
  @SerializedName(SERIALIZED_NAME_DESCRIPTION)
  private String description;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FEE_NAME = "FeeName";
  @SerializedName(SERIALIZED_NAME_FEE_NAME)
  private String feeName;

  public static final String SERIALIZED_NAME_FEE_TYPE = "FeeType";
  @SerializedName(SERIALIZED_NAME_FEE_TYPE)
  private ReturnFeeType feeType = null;

  public static final String SERIALIZED_NAME_IS_ORDER_LINE_FEE = "IsOrderLineFee";
  @SerializedName(SERIALIZED_NAME_IS_ORDER_LINE_FEE)
  private Boolean isOrderLineFee;

  public static final String SERIALIZED_NAME_ITEM_CONDITION = "ItemCondition";
  @SerializedName(SERIALIZED_NAME_ITEM_CONDITION)
  private ItemCondition itemCondition = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_TYPE = "OrderType";
  @SerializedName(SERIALIZED_NAME_ORDER_TYPE)
  private OrderType orderType = null;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_RETURN_FEE_CONFIG_ID = "ReturnFeeConfigId";
  @SerializedName(SERIALIZED_NAME_RETURN_FEE_CONFIG_ID)
  private String returnFeeConfigId;

  public static final String SERIALIZED_NAME_RETURN_REASON = "ReturnReason";
  @SerializedName(SERIALIZED_NAME_RETURN_REASON)
  private Reason returnReason = null;

  public static final String SERIALIZED_NAME_RETURN_TYPE = "ReturnType";
  @SerializedName(SERIALIZED_NAME_RETURN_TYPE)
  private ReturnType returnType = null;

  public static final String SERIALIZED_NAME_SELLING_CHANNEL = "SellingChannel";
  @SerializedName(SERIALIZED_NAME_SELLING_CHANNEL)
  private SellingChannel sellingChannel = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public ReturnFeeConfig actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ReturnFeeConfig putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ReturnFeeConfig amount(BigDecimal amount) {
    this.amount = amount;
    return this;
  }

   /**
   * Fee amount to be applied. If fee type is percent, then the amount given is a percentage (e.g. 10 for ten percent). If fee type is per quantity, then the amount given is the fee per quantity. If the fee type is flat, then the fee given is the fee applied.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return amount
  **/
  
  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public ReturnFeeConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ReturnFeeConfig createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ReturnFeeConfig customerTypeId(String customerTypeId) {
    this.customerTypeId = customerTypeId;
    return this;
  }

   /**
   * Indicate return fee can be calculated based on Customer type. This is applicable for only order level return fee i.e. isOrderLineFee should be false. Different return fee can be apply based on customer type e.g. A gold customer could be charged less return fee compare to regular customer.
   * @return customerTypeId
  **/
  
  public String getCustomerTypeId() {
    return customerTypeId;
  }

  public void setCustomerTypeId(String customerTypeId) {
    this.customerTypeId = customerTypeId;
  }

  public ReturnFeeConfig description(String description) {
    this.description = description;
    return this;
  }

   /**
   * Description of the template
   * @return description
  **/
  
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public ReturnFeeConfig extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ReturnFeeConfig feeName(String feeName) {
    this.feeName = feeName;
    return this;
  }

   /**
   * Name of the fee, which is mapped to the charge detail charge display name. Not displayed in the call center UI. Can be used by external systems for categorizing return SH, restocking, or other fees.
   * @return feeName
  **/
  
  public String getFeeName() {
    return feeName;
  }

  public void setFeeName(String feeName) {
    this.feeName = feeName;
  }

  public ReturnFeeConfig feeType(ReturnFeeType feeType) {
    this.feeType = feeType;
    return this;
  }

   /**
   * Get feeType
   * @return feeType
  **/
  
  public ReturnFeeType getFeeType() {
    return feeType;
  }

  public void setFeeType(ReturnFeeType feeType) {
    this.feeType = feeType;
  }

  public ReturnFeeConfig isOrderLineFee(Boolean isOrderLineFee) {
    this.isOrderLineFee = isOrderLineFee;
    return this;
  }

   /**
   * Indicates if the fee is applicable to order lines or to the order header.
   * @return isOrderLineFee
  **/
  
  public Boolean getIsOrderLineFee() {
    return isOrderLineFee;
  }

  public void setIsOrderLineFee(Boolean isOrderLineFee) {
    this.isOrderLineFee = isOrderLineFee;
  }

  public ReturnFeeConfig itemCondition(ItemCondition itemCondition) {
    this.itemCondition = itemCondition;
    return this;
  }

   /**
   * Get itemCondition
   * @return itemCondition
  **/
  
  public ItemCondition getItemCondition() {
    return itemCondition;
  }

  public void setItemCondition(ItemCondition itemCondition) {
    this.itemCondition = itemCondition;
  }

  public ReturnFeeConfig localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public ReturnFeeConfig messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ReturnFeeConfig orderType(OrderType orderType) {
    this.orderType = orderType;
    return this;
  }

   /**
   * Get orderType
   * @return orderType
  **/
  
  public OrderType getOrderType() {
    return orderType;
  }

  public void setOrderType(OrderType orderType) {
    this.orderType = orderType;
  }

  public ReturnFeeConfig PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ReturnFeeConfig profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public ReturnFeeConfig returnFeeConfigId(String returnFeeConfigId) {
    this.returnFeeConfigId = returnFeeConfigId;
    return this;
  }

   /**
   * Identifies the return order line fee template. This template is used to apply fees such as restocking to return order lines.
   * @return returnFeeConfigId
  **/
  
  public String getReturnFeeConfigId() {
    return returnFeeConfigId;
  }

  public void setReturnFeeConfigId(String returnFeeConfigId) {
    this.returnFeeConfigId = returnFeeConfigId;
  }

  public ReturnFeeConfig returnReason(Reason returnReason) {
    this.returnReason = returnReason;
    return this;
  }

   /**
   * Get returnReason
   * @return returnReason
  **/
  
  public Reason getReturnReason() {
    return returnReason;
  }

  public void setReturnReason(Reason returnReason) {
    this.returnReason = returnReason;
  }

  public ReturnFeeConfig returnType(ReturnType returnType) {
    this.returnType = returnType;
    return this;
  }

   /**
   * Get returnType
   * @return returnType
  **/
  
  public ReturnType getReturnType() {
    return returnType;
  }

  public void setReturnType(ReturnType returnType) {
    this.returnType = returnType;
  }

  public ReturnFeeConfig sellingChannel(SellingChannel sellingChannel) {
    this.sellingChannel = sellingChannel;
    return this;
  }

   /**
   * Get sellingChannel
   * @return sellingChannel
  **/
  
  public SellingChannel getSellingChannel() {
    return sellingChannel;
  }

  public void setSellingChannel(SellingChannel sellingChannel) {
    this.sellingChannel = sellingChannel;
  }

  public ReturnFeeConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ReturnFeeConfig updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ReturnFeeConfig rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReturnFeeConfig returnFeeConfig = (ReturnFeeConfig) o;
    return Objects.equals(this.actions, returnFeeConfig.actions) &&
        Objects.equals(this.amount, returnFeeConfig.amount) &&
        Objects.equals(this.createdBy, returnFeeConfig.createdBy) &&
        Objects.equals(this.createdTimestamp, returnFeeConfig.createdTimestamp) &&
        Objects.equals(this.customerTypeId, returnFeeConfig.customerTypeId) &&
        Objects.equals(this.description, returnFeeConfig.description) &&
        Objects.equals(this.extended, returnFeeConfig.extended) &&
        Objects.equals(this.feeName, returnFeeConfig.feeName) &&
        Objects.equals(this.feeType, returnFeeConfig.feeType) &&
        Objects.equals(this.isOrderLineFee, returnFeeConfig.isOrderLineFee) &&
        Objects.equals(this.itemCondition, returnFeeConfig.itemCondition) &&
        Objects.equals(this.localizedTo, returnFeeConfig.localizedTo) &&
        Objects.equals(this.messages, returnFeeConfig.messages) &&
        Objects.equals(this.orderType, returnFeeConfig.orderType) &&
        Objects.equals(this.PK, returnFeeConfig.PK) &&
        Objects.equals(this.profileId, returnFeeConfig.profileId) &&
        Objects.equals(this.returnFeeConfigId, returnFeeConfig.returnFeeConfigId) &&
        Objects.equals(this.returnReason, returnFeeConfig.returnReason) &&
        Objects.equals(this.returnType, returnFeeConfig.returnType) &&
        Objects.equals(this.sellingChannel, returnFeeConfig.sellingChannel) &&
        Objects.equals(this.updatedBy, returnFeeConfig.updatedBy) &&
        Objects.equals(this.updatedTimestamp, returnFeeConfig.updatedTimestamp) &&
        Objects.equals(this.rootCause, returnFeeConfig.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, amount, createdBy, createdTimestamp, customerTypeId, description, extended, feeName, feeType, isOrderLineFee, itemCondition, localizedTo, messages, orderType, PK, profileId, returnFeeConfigId, returnReason, returnType, sellingChannel, updatedBy, updatedTimestamp, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReturnFeeConfig {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    customerTypeId: ").append(toIndentedString(customerTypeId)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    feeName: ").append(toIndentedString(feeName)).append("\n");
    sb.append("    feeType: ").append(toIndentedString(feeType)).append("\n");
    sb.append("    isOrderLineFee: ").append(toIndentedString(isOrderLineFee)).append("\n");
    sb.append("    itemCondition: ").append(toIndentedString(itemCondition)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderType: ").append(toIndentedString(orderType)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    returnFeeConfigId: ").append(toIndentedString(returnFeeConfigId)).append("\n");
    sb.append("    returnReason: ").append(toIndentedString(returnReason)).append("\n");
    sb.append("    returnType: ").append(toIndentedString(returnType)).append("\n");
    sb.append("    sellingChannel: ").append(toIndentedString(sellingChannel)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

