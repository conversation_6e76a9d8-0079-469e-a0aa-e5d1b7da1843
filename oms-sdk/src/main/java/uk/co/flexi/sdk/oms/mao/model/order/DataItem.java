package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;


@Data
public class DataItem {

    @SerializedName("OrgId")
    private String orgId;

    @SerializedName("CurrencyCode")
    private String currencyCode;


    @SerializedName("Messages")
    private Object messages;

    @SerializedName("CreatedTimestamp")
    private String createdTimestamp;

    @SerializedName("OrderType")
    private OrderType orderType;

    @SerializedName("OrderLine")
    private List<OrderLineItem> orderLine;

    @SerializedName("RelatedOrders")
    private List<RelatedOrders> relatedOrders;

    @SerializedName("CustomerEmail")
    private String customerEmail;

    @SerializedName("ReturnTrackingDetail")
    private List<ReturnTrackingDetailItem> returnTrackingDetail;

    @SerializedName("OrderTrackingInfo")
    private List<OrderTrackingInfoItem> orderTrackingInfo;

    @SerializedName("OrderId")
    private String orderId;

    @SerializedName("TrackingNumber")
    private String trackingNumber;

    @SerializedName("SellingChannel")
    private SellingChannel sellingChannel;

    @SerializedName("Extended")
    private Extended extended;

    @SerializedName("OrderExtension1")
    private OrderExtension1 orderExtension1;

    @Data
    public static class OrderExtension1 {

        @SerializedName("Extended")
        private Extended extended = new Extended();

        @Data
        public static class Extended {

            @SerializedName("CarrierReturnType")
            private String carrierReturnType;

            @SerializedName("CarrierReturnPickupDate")
            private String carrierReturnPickupDate;

            @SerializedName("CarrierReturnPickupTimeSlot")
            private String carrierReturnPickupTimeSlot;

            @SerializedName("CarrierDropoffTrackingUpdated")
            private Boolean carrierDropoffTrackingUpdated;

            @SerializedName("KatakanaFirstName")
            private String katakanaFirstName;

            @SerializedName("KatakanaLastName")
            private String katakanaLastName;
        }
    }

    public String getOrgId() {
        return orgId;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public Object getMessages() {
        return messages;
    }

    public String getCreatedTimestamp() {
        return createdTimestamp;
    }

    public OrderType getOrderType() {
        return orderType;
    }

    public List<OrderLineItem> getOrderLine() {
        return orderLine;
    }

    public List<RelatedOrders> getRelatedOrders() {
        return relatedOrders;
    }

    public String getCustomerEmail() {
        return customerEmail;
    }

    public List<ReturnTrackingDetailItem> getReturnTrackingDetail() {
        return returnTrackingDetail;
    }

    public List<OrderTrackingInfoItem> getOrderTrackingInfo() {
        return orderTrackingInfo;
    }

    public String getOrderId() {
        return orderId;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public SellingChannel getSellingChannel() {
        return sellingChannel;
    }

    public Extended getExtended() {
        return extended;
    }

    public OrderExtension1 getOrderExtension1() {
        return orderExtension1;
    }
}
