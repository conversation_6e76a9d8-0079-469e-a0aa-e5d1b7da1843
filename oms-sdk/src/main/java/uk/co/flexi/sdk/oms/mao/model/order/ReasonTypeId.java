/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * ReasonTypeId
 */
public class ReasonTypeId {
  public static final String SERIALIZED_NAME_REASON_TYPE_ID = "ReasonTypeId";
  @SerializedName(SERIALIZED_NAME_REASON_TYPE_ID)
  private String reasonTypeId;

  public ReasonTypeId reasonTypeId(String reasonTypeId) {
    this.reasonTypeId = reasonTypeId;
    return this;
  }

   /**
   * Unique identifier of the Reason type
   * @return reasonTypeId
  **/
  
  public String getReasonTypeId() {
    return reasonTypeId;
  }

  public void setReasonTypeId(String reasonTypeId) {
    this.reasonTypeId = reasonTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReasonTypeId reasonTypeId = (ReasonTypeId) o;
    return Objects.equals(this.reasonTypeId, reasonTypeId.reasonTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(reasonTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReasonTypeId {\n");
    
    sb.append("    reasonTypeId: ").append(toIndentedString(reasonTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

