/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * ChargeTypeId
 */
public class ChargeTypeId {
  public static final String SERIALIZED_NAME_CHARGE_TYPE_ID = "ChargeTypeId";
  @SerializedName(SERIALIZED_NAME_CHARGE_TYPE_ID)
  private String chargeTypeId;

  public ChargeTypeId chargeTypeId(String chargeTypeId) {
    this.chargeTypeId = chargeTypeId;
    return this;
  }

   /**
   * Unique identifier of the charge type
   * @return chargeTypeId
  **/
  
  public String getChargeTypeId() {
    return chargeTypeId;
  }

  public void setChargeTypeId(String chargeTypeId) {
    this.chargeTypeId = chargeTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChargeTypeId chargeTypeId = (ChargeTypeId) o;
    return Objects.equals(this.chargeTypeId, chargeTypeId.chargeTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(chargeTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChargeTypeId {\n");
    
    sb.append("    chargeTypeId: ").append(toIndentedString(chargeTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

