/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * OrderEligibleRefundChargesDTO
 */
public class OrderEligibleRefundChargesDTO {
  public static final String SERIALIZED_NAME_ORDER_LINE_REFUND_CHARGES = "OrderLineRefundCharges";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_REFUND_CHARGES)
  private List<OrderLineEligibleRefundChargesDTO> orderLineRefundCharges = null;

  public OrderEligibleRefundChargesDTO orderLineRefundCharges(List<OrderLineEligibleRefundChargesDTO> orderLineRefundCharges) {
    this.orderLineRefundCharges = orderLineRefundCharges;
    return this;
  }

  public OrderEligibleRefundChargesDTO addOrderLineRefundChargesItem(OrderLineEligibleRefundChargesDTO orderLineRefundChargesItem) {
    if (this.orderLineRefundCharges == null) {
      this.orderLineRefundCharges = new ArrayList<OrderLineEligibleRefundChargesDTO>();
    }
    this.orderLineRefundCharges.add(orderLineRefundChargesItem);
    return this;
  }

   /**
   * Get orderLineRefundCharges
   * @return orderLineRefundCharges
  **/
  
  public List<OrderLineEligibleRefundChargesDTO> getOrderLineRefundCharges() {
    return orderLineRefundCharges;
  }

  public void setOrderLineRefundCharges(List<OrderLineEligibleRefundChargesDTO> orderLineRefundCharges) {
    this.orderLineRefundCharges = orderLineRefundCharges;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderEligibleRefundChargesDTO orderEligibleRefundChargesDTO = (OrderEligibleRefundChargesDTO) o;
    return Objects.equals(this.orderLineRefundCharges, orderEligibleRefundChargesDTO.orderLineRefundCharges);
  }

  @Override
  public int hashCode() {
    return Objects.hash(orderLineRefundCharges);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderEligibleRefundChargesDTO {\n");
    
    sb.append("    orderLineRefundCharges: ").append(toIndentedString(orderLineRefundCharges)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

