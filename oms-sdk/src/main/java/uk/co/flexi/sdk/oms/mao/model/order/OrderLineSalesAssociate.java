/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderLineSalesAssociate
 */
public class OrderLineSalesAssociate {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ASSOCIATE_ID = "AssociateId";
  @SerializedName(SERIALIZED_NAME_ASSOCIATE_ID)
  private String associateId;

  public static final String SERIALIZED_NAME_ASSOCIATE_TYPE = "AssociateType";
  @SerializedName(SERIALIZED_NAME_ASSOCIATE_TYPE)
  private String associateType;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_SALES_ASSOCIATE_ID = "OrderLineSalesAssociateId";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_SALES_ASSOCIATE_ID)
  private String orderLineSalesAssociateId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_ORDER_LINE = "ParentOrderLine";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER_LINE)
  private PrimaryKey parentOrderLine = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderLineSalesAssociate actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderLineSalesAssociate putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderLineSalesAssociate associateId(String associateId) {
    this.associateId = associateId;
    return this;
  }

   /**
   * Associate id
   * @return associateId
  **/
  
  public String getAssociateId() {
    return associateId;
  }

  public void setAssociateId(String associateId) {
    this.associateId = associateId;
  }

  public OrderLineSalesAssociate associateType(String associateType) {
    this.associateType = associateType;
    return this;
  }

   /**
   * Associate Type e.g. Cashier or Sales Rep
   * @return associateType
  **/
  
  public String getAssociateType() {
    return associateType;
  }

  public void setAssociateType(String associateType) {
    this.associateType = associateType;
  }

  public OrderLineSalesAssociate createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderLineSalesAssociate createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderLineSalesAssociate extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderLineSalesAssociate localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderLineSalesAssociate messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderLineSalesAssociate orderLineSalesAssociateId(String orderLineSalesAssociateId) {
    this.orderLineSalesAssociateId = orderLineSalesAssociateId;
    return this;
  }

   /**
   * Unique identifier of order Line Sales Associate table
   * @return orderLineSalesAssociateId
  **/
  
  public String getOrderLineSalesAssociateId() {
    return orderLineSalesAssociateId;
  }

  public void setOrderLineSalesAssociateId(String orderLineSalesAssociateId) {
    this.orderLineSalesAssociateId = orderLineSalesAssociateId;
  }

  public OrderLineSalesAssociate orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderLineSalesAssociate PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderLineSalesAssociate parentOrderLine(PrimaryKey parentOrderLine) {
    this.parentOrderLine = parentOrderLine;
    return this;
  }

   /**
   * Get parentOrderLine
   * @return parentOrderLine
  **/
  
  public PrimaryKey getParentOrderLine() {
    return parentOrderLine;
  }

  public void setParentOrderLine(PrimaryKey parentOrderLine) {
    this.parentOrderLine = parentOrderLine;
  }

  public OrderLineSalesAssociate updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderLineSalesAssociate updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderLineSalesAssociate entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderLineSalesAssociate localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderLineSalesAssociate orderLineSalesAssociate = (OrderLineSalesAssociate) o;
    return Objects.equals(this.actions, orderLineSalesAssociate.actions) &&
        Objects.equals(this.associateId, orderLineSalesAssociate.associateId) &&
        Objects.equals(this.associateType, orderLineSalesAssociate.associateType) &&
        Objects.equals(this.createdBy, orderLineSalesAssociate.createdBy) &&
        Objects.equals(this.createdTimestamp, orderLineSalesAssociate.createdTimestamp) &&
        Objects.equals(this.extended, orderLineSalesAssociate.extended) &&
        Objects.equals(this.localizedTo, orderLineSalesAssociate.localizedTo) &&
        Objects.equals(this.messages, orderLineSalesAssociate.messages) &&
        Objects.equals(this.orderLineSalesAssociateId, orderLineSalesAssociate.orderLineSalesAssociateId) &&
        Objects.equals(this.orgId, orderLineSalesAssociate.orgId) &&
        Objects.equals(this.PK, orderLineSalesAssociate.PK) &&
        Objects.equals(this.parentOrderLine, orderLineSalesAssociate.parentOrderLine) &&
        Objects.equals(this.updatedBy, orderLineSalesAssociate.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderLineSalesAssociate.updatedTimestamp) &&
        Objects.equals(this.entityName, orderLineSalesAssociate.entityName) &&
        Objects.equals(this.localize, orderLineSalesAssociate.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, associateId, associateType, createdBy, createdTimestamp, extended, localizedTo, messages, orderLineSalesAssociateId, orgId, PK, parentOrderLine, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderLineSalesAssociate {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    associateId: ").append(toIndentedString(associateId)).append("\n");
    sb.append("    associateType: ").append(toIndentedString(associateType)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderLineSalesAssociateId: ").append(toIndentedString(orderLineSalesAssociateId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentOrderLine: ").append(toIndentedString(parentOrderLine)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

