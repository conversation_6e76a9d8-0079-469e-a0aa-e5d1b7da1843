/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.*;

/**
 * Release
 */
public class Release {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CALCULATED_VALUES = "CalculatedValues";
  @SerializedName(SERIALIZED_NAME_CALCULATED_VALUES)
  private Object calculatedValues = null;

  public static final String SERIALIZED_NAME_CARRIER_CODE = "CarrierCode";
  @SerializedName(SERIALIZED_NAME_CARRIER_CODE)
  private String carrierCode;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DELIVERY_METHOD_ID = "DeliveryMethodId";
  @SerializedName(SERIALIZED_NAME_DELIVERY_METHOD_ID)
  private String deliveryMethodId;

  public static final String SERIALIZED_NAME_DESTINATION_ACTION = "DestinationAction";
  @SerializedName(SERIALIZED_NAME_DESTINATION_ACTION)
  private String destinationAction;

  public static final String SERIALIZED_NAME_EFFECTIVE_RANK = "EffectiveRank";
  @SerializedName(SERIALIZED_NAME_EFFECTIVE_RANK)
  private String effectiveRank;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_ID = "OrderId";
  @SerializedName(SERIALIZED_NAME_ORDER_ID)
  private String orderId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_RELEASE_EXTENSION1 = "ReleaseExtension1";
  @SerializedName(SERIALIZED_NAME_RELEASE_EXTENSION1)
  private List<ReleaseExtension1> releaseExtension1 = null;

  public static final String SERIALIZED_NAME_RELEASE_ID = "ReleaseId";
  @SerializedName(SERIALIZED_NAME_RELEASE_ID)
  private String releaseId;

  public static final String SERIALIZED_NAME_RELEASE_LINE = "ReleaseLine";
  @SerializedName(SERIALIZED_NAME_RELEASE_LINE)
  private List<ReleaseLine> releaseLine = null;

  public static final String SERIALIZED_NAME_RELEASE_TYPE = "ReleaseType";
  @SerializedName(SERIALIZED_NAME_RELEASE_TYPE)
  private String releaseType;

  public static final String SERIALIZED_NAME_SERVICE_LEVEL_CODE = "ServiceLevelCode";
  @SerializedName(SERIALIZED_NAME_SERVICE_LEVEL_CODE)
  private String serviceLevelCode;

  public static final String SERIALIZED_NAME_SHIP_FROM_LOCATION_ID = "ShipFromLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_FROM_LOCATION_ID)
  private String shipFromLocationId;

  public static final String SERIALIZED_NAME_SHIP_TO_LOCATION_ID = "ShipToLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_TO_LOCATION_ID)
  private String shipToLocationId;

  public static final String SERIALIZED_NAME_SHIP_VIA_ID = "ShipViaId";
  @SerializedName(SERIALIZED_NAME_SHIP_VIA_ID)
  private String shipViaId;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public Release actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public Release putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public Release calculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
    return this;
  }

   /**
   * Get calculatedValues
   * @return calculatedValues
  **/
  
  public Object getCalculatedValues() {
    return calculatedValues;
  }

  public void setCalculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
  }

  public Release carrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
    return this;
  }

   /**
   * Carrier which is requested for the order quantities
   * @return carrierCode
  **/
  
  public String getCarrierCode() {
    return carrierCode;
  }

  public void setCarrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
  }

  public Release createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public Release createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public Release deliveryMethodId(String deliveryMethodId) {
    this.deliveryMethodId = deliveryMethodId;
    return this;
  }

   /**
   * Method by which the ordered items should be delivered
   * @return deliveryMethodId
  **/
  
  public String getDeliveryMethodId() {
    return deliveryMethodId;
  }

  public void setDeliveryMethodId(String deliveryMethodId) {
    this.deliveryMethodId = deliveryMethodId;
  }

  public Release destinationAction(String destinationAction) {
    this.destinationAction = destinationAction;
    return this;
  }

   /**
   * This is used when the delivery mothod is ship to store to differentiate between merge and pickup orders
   * @return destinationAction
  **/
  
  public String getDestinationAction() {
    return destinationAction;
  }

  public void setDestinationAction(String destinationAction) {
    this.destinationAction = destinationAction;
  }

  public Release effectiveRank(String effectiveRank) {
    this.effectiveRank = effectiveRank;
    return this;
  }

   /**
   * Rank of the release, calculated as the highest rank of all release lines
   * @return effectiveRank
  **/
  
  public String getEffectiveRank() {
    return effectiveRank;
  }

  public void setEffectiveRank(String effectiveRank) {
    this.effectiveRank = effectiveRank;
  }

  public Release extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public Release localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public Release messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public Release orderId(String orderId) {
    this.orderId = orderId;
    return this;
  }

   /**
   * Unique identifier of the order with which the release is associated
   * @return orderId
  **/
  
  public String getOrderId() {
    return orderId;
  }

  public void setOrderId(String orderId) {
    this.orderId = orderId;
  }

  public Release orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public Release PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public Release releaseExtension1(List<ReleaseExtension1> releaseExtension1) {
    this.releaseExtension1 = releaseExtension1;
    return this;
  }

  public Release addReleaseExtension1Item(ReleaseExtension1 releaseExtension1Item) {
    if (this.releaseExtension1 == null) {
      this.releaseExtension1 = new ArrayList<ReleaseExtension1>();
    }
    this.releaseExtension1.add(releaseExtension1Item);
    return this;
  }

   /**
   * Get releaseExtension1
   * @return releaseExtension1
  **/
  
  public List<ReleaseExtension1> getReleaseExtension1() {
    return releaseExtension1;
  }

  public void setReleaseExtension1(List<ReleaseExtension1> releaseExtension1) {
    this.releaseExtension1 = releaseExtension1;
  }

  public Release releaseId(String releaseId) {
    this.releaseId = releaseId;
    return this;
  }

   /**
   * Unique identifier of the release (concatenate orderId + - + nextUpNumber as single digit within order 1, 2, 3, etc.)
   * @return releaseId
  **/
  
  public String getReleaseId() {
    return releaseId;
  }

  public void setReleaseId(String releaseId) {
    this.releaseId = releaseId;
  }

  public Release releaseLine(List<ReleaseLine> releaseLine) {
    this.releaseLine = releaseLine;
    return this;
  }

  public Release addReleaseLineItem(ReleaseLine releaseLineItem) {
    if (this.releaseLine == null) {
      this.releaseLine = new ArrayList<ReleaseLine>();
    }
    this.releaseLine.add(releaseLineItem);
    return this;
  }

   /**
   * Get releaseLine
   * @return releaseLine
  **/
  
  public List<ReleaseLine> getReleaseLine() {
    return releaseLine;
  }

  public void setReleaseLine(List<ReleaseLine> releaseLine) {
    this.releaseLine = releaseLine;
  }

  public Release releaseType(String releaseType) {
    this.releaseType = releaseType;
    return this;
  }

   /**
   * Type of release entity e.g. Customer Order, Transfer order etc
   * @return releaseType
  **/
  
  public String getReleaseType() {
    return releaseType;
  }

  public void setReleaseType(String releaseType) {
    this.releaseType = releaseType;
  }

  public Release serviceLevelCode(String serviceLevelCode) {
    this.serviceLevelCode = serviceLevelCode;
    return this;
  }

   /**
   * Service Level which is requested for the order quantities 
   * @return serviceLevelCode
  **/
  
  public String getServiceLevelCode() {
    return serviceLevelCode;
  }

  public void setServiceLevelCode(String serviceLevelCode) {
    this.serviceLevelCode = serviceLevelCode;
  }

  public Release shipFromLocationId(String shipFromLocationId) {
    this.shipFromLocationId = shipFromLocationId;
    return this;
  }

   /**
   * The location where the fulfillment will be executed
   * @return shipFromLocationId
  **/
  
  public String getShipFromLocationId() {
    return shipFromLocationId;
  }

  public void setShipFromLocationId(String shipFromLocationId) {
    this.shipFromLocationId = shipFromLocationId;
  }

  public Release shipToLocationId(String shipToLocationId) {
    this.shipToLocationId = shipToLocationId;
    return this;
  }

   /**
   * Ship-to location ID, in case of ship to store or any flow where the items are shipping to a destination which is configured as a location in the system
   * @return shipToLocationId
  **/
  
  public String getShipToLocationId() {
    return shipToLocationId;
  }

  public void setShipToLocationId(String shipToLocationId) {
    this.shipToLocationId = shipToLocationId;
  }

  public Release shipViaId(String shipViaId) {
    this.shipViaId = shipViaId;
    return this;
  }

   /**
   * Service Level which is requested for the order quantities
   * @return shipViaId
  **/
  
  public String getShipViaId() {
    return shipViaId;
  }

  public void setShipViaId(String shipViaId) {
    this.shipViaId = shipViaId;
  }

  public Release updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public Release updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public Release entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public Release localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Release release = (Release) o;
    return Objects.equals(this.actions, release.actions) &&
        Objects.equals(this.calculatedValues, release.calculatedValues) &&
        Objects.equals(this.carrierCode, release.carrierCode) &&
        Objects.equals(this.createdBy, release.createdBy) &&
        Objects.equals(this.createdTimestamp, release.createdTimestamp) &&
        Objects.equals(this.deliveryMethodId, release.deliveryMethodId) &&
        Objects.equals(this.destinationAction, release.destinationAction) &&
        Objects.equals(this.effectiveRank, release.effectiveRank) &&
        Objects.equals(this.extended, release.extended) &&
        Objects.equals(this.localizedTo, release.localizedTo) &&
        Objects.equals(this.messages, release.messages) &&
        Objects.equals(this.orderId, release.orderId) &&
        Objects.equals(this.orgId, release.orgId) &&
        Objects.equals(this.PK, release.PK) &&
        Objects.equals(this.releaseExtension1, release.releaseExtension1) &&
        Objects.equals(this.releaseId, release.releaseId) &&
        Objects.equals(this.releaseLine, release.releaseLine) &&
        Objects.equals(this.releaseType, release.releaseType) &&
        Objects.equals(this.serviceLevelCode, release.serviceLevelCode) &&
        Objects.equals(this.shipFromLocationId, release.shipFromLocationId) &&
        Objects.equals(this.shipToLocationId, release.shipToLocationId) &&
        Objects.equals(this.shipViaId, release.shipViaId) &&
        Objects.equals(this.updatedBy, release.updatedBy) &&
        Objects.equals(this.updatedTimestamp, release.updatedTimestamp) &&
        Objects.equals(this.entityName, release.entityName) &&
        Objects.equals(this.localize, release.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, calculatedValues, carrierCode, createdBy, createdTimestamp, deliveryMethodId, destinationAction, effectiveRank, extended, localizedTo, messages, orderId, orgId, PK, releaseExtension1, releaseId, releaseLine, releaseType, serviceLevelCode, shipFromLocationId, shipToLocationId, shipViaId, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Release {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    calculatedValues: ").append(toIndentedString(calculatedValues)).append("\n");
    sb.append("    carrierCode: ").append(toIndentedString(carrierCode)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    deliveryMethodId: ").append(toIndentedString(deliveryMethodId)).append("\n");
    sb.append("    destinationAction: ").append(toIndentedString(destinationAction)).append("\n");
    sb.append("    effectiveRank: ").append(toIndentedString(effectiveRank)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderId: ").append(toIndentedString(orderId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    releaseExtension1: ").append(toIndentedString(releaseExtension1)).append("\n");
    sb.append("    releaseId: ").append(toIndentedString(releaseId)).append("\n");
    sb.append("    releaseLine: ").append(toIndentedString(releaseLine)).append("\n");
    sb.append("    releaseType: ").append(toIndentedString(releaseType)).append("\n");
    sb.append("    serviceLevelCode: ").append(toIndentedString(serviceLevelCode)).append("\n");
    sb.append("    shipFromLocationId: ").append(toIndentedString(shipFromLocationId)).append("\n");
    sb.append("    shipToLocationId: ").append(toIndentedString(shipToLocationId)).append("\n");
    sb.append("    shipViaId: ").append(toIndentedString(shipViaId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

