/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * StrategyTypeId
 */
public class StrategyTypeId {
  public static final String SERIALIZED_NAME_STRATEGY_TYPE_ID = "StrategyTypeId";
  @SerializedName(SERIALIZED_NAME_STRATEGY_TYPE_ID)
  private String strategyTypeId;

  public StrategyTypeId strategyTypeId(String strategyTypeId) {
    this.strategyTypeId = strategyTypeId;
    return this;
  }

   /**
   * Unique identifier of the strategy type
   * @return strategyTypeId
  **/
  
  public String getStrategyTypeId() {
    return strategyTypeId;
  }

  public void setStrategyTypeId(String strategyTypeId) {
    this.strategyTypeId = strategyTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    StrategyTypeId strategyTypeId = (StrategyTypeId) o;
    return Objects.equals(this.strategyTypeId, strategyTypeId.strategyTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(strategyTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class StrategyTypeId {\n");
    
    sb.append("    strategyTypeId: ").append(toIndentedString(strategyTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

