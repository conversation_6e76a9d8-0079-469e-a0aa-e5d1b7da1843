/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.*;

/**
 * PaymentMethodDTO
 */
public class PaymentMethodDTO {
  public static final String SERIALIZED_NAME_ACCOUNT_DISPLAY_NUMBER = "AccountDisplayNumber";
  @SerializedName(SERIALIZED_NAME_ACCOUNT_DISPLAY_NUMBER)
  private String accountDisplayNumber;

  public static final String SERIALIZED_NAME_ACCOUNT_NUMBER = "AccountNumber";
  @SerializedName(SERIALIZED_NAME_ACCOUNT_NUMBER)
  private String accountNumber;

  public static final String SERIALIZED_NAME_ACCOUNT_TYPE = "AccountType";
  @SerializedName(SERIALIZED_NAME_ACCOUNT_TYPE)
  private AccountTypeId accountType = null;

  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ALTERNATE_CURRENCY_AMOUNT = "AlternateCurrencyAmount";
  @SerializedName(SERIALIZED_NAME_ALTERNATE_CURRENCY_AMOUNT)
  private BigDecimal alternateCurrencyAmount;

  public static final String SERIALIZED_NAME_ALTERNATE_CURRENCY_CODE = "AlternateCurrencyCode";
  @SerializedName(SERIALIZED_NAME_ALTERNATE_CURRENCY_CODE)
  private String alternateCurrencyCode;

  public static final String SERIALIZED_NAME_AMOUNT = "Amount";
  @SerializedName(SERIALIZED_NAME_AMOUNT)
  private BigDecimal amount;

  public static final String SERIALIZED_NAME_BILLING_ADDRESS = "BillingAddress";
  @SerializedName(SERIALIZED_NAME_BILLING_ADDRESS)
  private BillingAddress billingAddress = null;

  public static final String SERIALIZED_NAME_BUSINESS_NAME = "BusinessName";
  @SerializedName(SERIALIZED_NAME_BUSINESS_NAME)
  private String businessName;

  public static final String SERIALIZED_NAME_BUSINESS_TAX_ID = "BusinessTaxId";
  @SerializedName(SERIALIZED_NAME_BUSINESS_TAX_ID)
  private String businessTaxId;

  public static final String SERIALIZED_NAME_CAPTURED_IN_EDGE_MODE = "CapturedInEdgeMode";
  @SerializedName(SERIALIZED_NAME_CAPTURED_IN_EDGE_MODE)
  private Boolean capturedInEdgeMode;

  public static final String SERIALIZED_NAME_CARD_EXPIRY_MONTH = "CardExpiryMonth";
  @SerializedName(SERIALIZED_NAME_CARD_EXPIRY_MONTH)
  private String cardExpiryMonth;

  public static final String SERIALIZED_NAME_CARD_EXPIRY_YEAR = "CardExpiryYear";
  @SerializedName(SERIALIZED_NAME_CARD_EXPIRY_YEAR)
  private String cardExpiryYear;

  public static final String SERIALIZED_NAME_CARD_TYPE = "CardType";
  @SerializedName(SERIALIZED_NAME_CARD_TYPE)
  private CardTypeId cardType = null;

  public static final String SERIALIZED_NAME_CARD_TYPE_ID = "CardTypeId";
  @SerializedName(SERIALIZED_NAME_CARD_TYPE_ID)
  private String cardTypeId;

  public static final String SERIALIZED_NAME_CHANGE_AMOUNT = "ChangeAmount";
  @SerializedName(SERIALIZED_NAME_CHANGE_AMOUNT)
  private BigDecimal changeAmount;

  public static final String SERIALIZED_NAME_CHARGE_SEQUENCE = "ChargeSequence";
  @SerializedName(SERIALIZED_NAME_CHARGE_SEQUENCE)
  private Long chargeSequence;

  public static final String SERIALIZED_NAME_CHECK_NUMBER = "CheckNumber";
  @SerializedName(SERIALIZED_NAME_CHECK_NUMBER)
  private String checkNumber;

  public static final String SERIALIZED_NAME_CHECK_QUANTITY = "CheckQuantity";
  @SerializedName(SERIALIZED_NAME_CHECK_QUANTITY)
  private Long checkQuantity;

  public static final String SERIALIZED_NAME_CONVERSION_RATE = "ConversionRate";
  @SerializedName(SERIALIZED_NAME_CONVERSION_RATE)
  private Double conversionRate;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_CURRENCY_CODE = "CurrencyCode";
  @SerializedName(SERIALIZED_NAME_CURRENCY_CODE)
  private String currencyCode;

  public static final String SERIALIZED_NAME_CURRENT_AUTH_AMOUNT = "CurrentAuthAmount";
  @SerializedName(SERIALIZED_NAME_CURRENT_AUTH_AMOUNT)
  private BigDecimal currentAuthAmount;

  public static final String SERIALIZED_NAME_CURRENT_FAILED_AMOUNT = "CurrentFailedAmount";
  @SerializedName(SERIALIZED_NAME_CURRENT_FAILED_AMOUNT)
  private BigDecimal currentFailedAmount;

  public static final String SERIALIZED_NAME_CURRENT_REFUND_AMOUNT = "CurrentRefundAmount";
  @SerializedName(SERIALIZED_NAME_CURRENT_REFUND_AMOUNT)
  private BigDecimal currentRefundAmount;

  public static final String SERIALIZED_NAME_CURRENT_SETTLED_AMOUNT = "CurrentSettledAmount";
  @SerializedName(SERIALIZED_NAME_CURRENT_SETTLED_AMOUNT)
  private BigDecimal currentSettledAmount;

  public static final String SERIALIZED_NAME_CUSTOMER_PAY_SIGNATURE = "CustomerPaySignature";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_PAY_SIGNATURE)
  private String customerPaySignature;

  public static final String SERIALIZED_NAME_CUSTOMER_SIGNATURE = "CustomerSignature";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_SIGNATURE)
  private String customerSignature;

  public static final String SERIALIZED_NAME_DRIVERS_LICENSE_COUNTRY = "DriversLicenseCountry";
  @SerializedName(SERIALIZED_NAME_DRIVERS_LICENSE_COUNTRY)
  private String driversLicenseCountry;

  public static final String SERIALIZED_NAME_DRIVERS_LICENSE_NUMBER = "DriversLicenseNumber";
  @SerializedName(SERIALIZED_NAME_DRIVERS_LICENSE_NUMBER)
  private String driversLicenseNumber;

  public static final String SERIALIZED_NAME_DRIVERS_LICENSE_STATE = "DriversLicenseState";
  @SerializedName(SERIALIZED_NAME_DRIVERS_LICENSE_STATE)
  private String driversLicenseState;

  public static final String SERIALIZED_NAME_ENTRY_TYPE_ID = "EntryTypeId";
  @SerializedName(SERIALIZED_NAME_ENTRY_TYPE_ID)
  private String entryTypeId;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_GATEWAY_ACCOUNT_ID = "GatewayAccountId";
  @SerializedName(SERIALIZED_NAME_GATEWAY_ACCOUNT_ID)
  private String gatewayAccountId;

  public static final String SERIALIZED_NAME_GATEWAY_ID = "GatewayId";
  @SerializedName(SERIALIZED_NAME_GATEWAY_ID)
  private String gatewayId;

  public static final String SERIALIZED_NAME_GIFT_CARD_PIN = "GiftCardPin";
  @SerializedName(SERIALIZED_NAME_GIFT_CARD_PIN)
  private String giftCardPin;

  public static final String SERIALIZED_NAME_IS_COPIED = "IsCopied";
  @SerializedName(SERIALIZED_NAME_IS_COPIED)
  private Boolean isCopied;

  public static final String SERIALIZED_NAME_IS_MODIFIABLE = "IsModifiable";
  @SerializedName(SERIALIZED_NAME_IS_MODIFIABLE)
  private Boolean isModifiable;

  public static final String SERIALIZED_NAME_IS_SUSPENDED = "IsSuspended";
  @SerializedName(SERIALIZED_NAME_IS_SUSPENDED)
  private Boolean isSuspended;

  public static final String SERIALIZED_NAME_IS_VOIDED = "IsVoided";
  @SerializedName(SERIALIZED_NAME_IS_VOIDED)
  private Boolean isVoided;

  public static final String SERIALIZED_NAME_LOCATION_ID = "LocationId";
  @SerializedName(SERIALIZED_NAME_LOCATION_ID)
  private String locationId;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_NAME_ON_CARD = "NameOnCard";
  @SerializedName(SERIALIZED_NAME_NAME_ON_CARD)
  private String nameOnCard;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_ORIGINAL_AMOUNT = "OriginalAmount";
  @SerializedName(SERIALIZED_NAME_ORIGINAL_AMOUNT)
  private BigDecimal originalAmount;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_ORDER_ID = "ParentOrderId";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER_ID)
  private String parentOrderId;

  public static final String SERIALIZED_NAME_PARENT_ORDER_PAYMENT_METHOD = "ParentOrderPaymentMethod";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER_PAYMENT_METHOD)
  private List<ParentOrderPaymentMethod> parentOrderPaymentMethod = null;

  public static final String SERIALIZED_NAME_PARENT_PAYMENT_GROUP_ID = "ParentPaymentGroupId";
  @SerializedName(SERIALIZED_NAME_PARENT_PAYMENT_GROUP_ID)
  private String parentPaymentGroupId;

  public static final String SERIALIZED_NAME_PARENT_PAYMENT_METHOD_ID = "ParentPaymentMethodId";
  @SerializedName(SERIALIZED_NAME_PARENT_PAYMENT_METHOD_ID)
  private String parentPaymentMethodId;

  public static final String SERIALIZED_NAME_PAYMENT_METHOD_ATTRIBUTE = "PaymentMethodAttribute";
  @SerializedName(SERIALIZED_NAME_PAYMENT_METHOD_ATTRIBUTE)
  private List<PaymentMethodAttribute> paymentMethodAttribute = null;

  public static final String SERIALIZED_NAME_PAYMENT_METHOD_ENCR_ATTRIBUTE = "PaymentMethodEncrAttribute";
  @SerializedName(SERIALIZED_NAME_PAYMENT_METHOD_ENCR_ATTRIBUTE)
  private List<PaymentMethodEncrAttribute> paymentMethodEncrAttribute = null;

  public static final String SERIALIZED_NAME_PAYMENT_METHOD_ID = "PaymentMethodId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_METHOD_ID)
  private String paymentMethodId;

  public static final String SERIALIZED_NAME_PAYMENT_TRANSACTION = "PaymentTransaction";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TRANSACTION)
  private List<PaymentTransaction> paymentTransaction = null;

  public static final String SERIALIZED_NAME_PAYMENT_TYPE = "PaymentType";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TYPE)
  private PaymentTypeId paymentType = null;

  public static final String SERIALIZED_NAME_ROUTING_DISPLAY_NUMBER = "RoutingDisplayNumber";
  @SerializedName(SERIALIZED_NAME_ROUTING_DISPLAY_NUMBER)
  private String routingDisplayNumber;

  public static final String SERIALIZED_NAME_ROUTING_NUMBER = "RoutingNumber";
  @SerializedName(SERIALIZED_NAME_ROUTING_NUMBER)
  private String routingNumber;

  public static final String SERIALIZED_NAME_SECURITY_CODE = "SecurityCode";
  @SerializedName(SERIALIZED_NAME_SECURITY_CODE)
  private String securityCode;

  public static final String SERIALIZED_NAME_SWIPE_DATA = "SwipeData";
  @SerializedName(SERIALIZED_NAME_SWIPE_DATA)
  private String swipeData;

  public static final String SERIALIZED_NAME_TRANSACTION_REFERENCE_ID = "TransactionReferenceId";
  @SerializedName(SERIALIZED_NAME_TRANSACTION_REFERENCE_ID)
  private String transactionReferenceId;

  public static final String SERIALIZED_NAME_TRANSLATIONS = "Translations";
  @SerializedName(SERIALIZED_NAME_TRANSLATIONS)
  private Map<String, Map<String, String>> translations = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public PaymentMethodDTO accountDisplayNumber(String accountDisplayNumber) {
    this.accountDisplayNumber = accountDisplayNumber;
    return this;
  }

   /**
   * Account number text which is displayed in UIs; For example: &#39;Visa ending in 1111&#39;.
   * @return accountDisplayNumber
  **/
  
  public String getAccountDisplayNumber() {
    return accountDisplayNumber;
  }

  public void setAccountDisplayNumber(String accountDisplayNumber) {
    this.accountDisplayNumber = accountDisplayNumber;
  }

  public PaymentMethodDTO accountNumber(String accountNumber) {
    this.accountNumber = accountNumber;
    return this;
  }

   /**
   * Account number. For credit cards, e-check, etc., this is the payment token. Used for processing payments in the third party payment gateway.
   * @return accountNumber
  **/
  
  public String getAccountNumber() {
    return accountNumber;
  }

  public void setAccountNumber(String accountNumber) {
    this.accountNumber = accountNumber;
  }

  public PaymentMethodDTO accountType(AccountTypeId accountType) {
    this.accountType = accountType;
    return this;
  }

   /**
   * Get accountType
   * @return accountType
  **/
  
  public AccountTypeId getAccountType() {
    return accountType;
  }

  public void setAccountType(AccountTypeId accountType) {
    this.accountType = accountType;
  }

  public PaymentMethodDTO actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public PaymentMethodDTO putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public PaymentMethodDTO alternateCurrencyAmount(BigDecimal alternateCurrencyAmount) {
    this.alternateCurrencyAmount = alternateCurrencyAmount;
    return this;
  }

   /**
   * Amount provided in the alternate currency. For example, if a customer at a US-Canada  border store pays in25 CAD but the store&#39;s base currency is USD, then the payment method is saved with alternate currency amount &#x3D; 25, alternate currency code &#x3D; CAD, and currency code &#x3D; USD. Upon saving this payment method, the payment component converts the CAD to USD and updates the amount field with the value in USD and the conversion rate field with the conversion rate used for the calculation.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return alternateCurrencyAmount
  **/
  
  public BigDecimal getAlternateCurrencyAmount() {
    return alternateCurrencyAmount;
  }

  public void setAlternateCurrencyAmount(BigDecimal alternateCurrencyAmount) {
    this.alternateCurrencyAmount = alternateCurrencyAmount;
  }

  public PaymentMethodDTO alternateCurrencyCode(String alternateCurrencyCode) {
    this.alternateCurrencyCode = alternateCurrencyCode;
    return this;
  }

   /**
   * Currency in which the payment is given; used to invoke currency conversion. For example, if a customer at a US-Canada  border store pays in25 CAD but the store&#39;s base currency is USD, then the payment method is saved with alternate currency amount &#x3D; 25, alternate currency code &#x3D; CAD, and currency code &#x3D; USD. Upon saving this payment method, the payment component converts the CAD to USD and updates the amount field with the value in USD and the conversion rate field with the conversion rate used for the calculation.
   * @return alternateCurrencyCode
  **/
  
  public String getAlternateCurrencyCode() {
    return alternateCurrencyCode;
  }

  public void setAlternateCurrencyCode(String alternateCurrencyCode) {
    this.alternateCurrencyCode = alternateCurrencyCode;
  }

  public PaymentMethodDTO amount(BigDecimal amount) {
    this.amount = amount;
    return this;
  }

   /**
   * Amount customer authorized to be charged to payment method; if cash-based tender, then the amount paid.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return amount
  **/
  
  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public PaymentMethodDTO billingAddress(BillingAddress billingAddress) {
    this.billingAddress = billingAddress;
    return this;
  }

   /**
   * Get billingAddress
   * @return billingAddress
  **/
  
  public BillingAddress getBillingAddress() {
    return billingAddress;
  }

  public void setBillingAddress(BillingAddress billingAddress) {
    this.billingAddress = billingAddress;
  }

  public PaymentMethodDTO businessName(String businessName) {
    this.businessName = businessName;
    return this;
  }

   /**
   * Name of the business, if a business checking or savings account is being used for an e-check payment method. Included in the payment gateway request and used for validation.
   * @return businessName
  **/
  
  public String getBusinessName() {
    return businessName;
  }

  public void setBusinessName(String businessName) {
    this.businessName = businessName;
  }

  public PaymentMethodDTO businessTaxId(String businessTaxId) {
    this.businessTaxId = businessTaxId;
    return this;
  }

   /**
   * Tax identification number of the business, if a business checking or savings account is being used for an e-check payment method. Included in the payment gateway request and used for validation.
   * @return businessTaxId
  **/
  
  public String getBusinessTaxId() {
    return businessTaxId;
  }

  public void setBusinessTaxId(String businessTaxId) {
    this.businessTaxId = businessTaxId;
  }

  public PaymentMethodDTO capturedInEdgeMode(Boolean capturedInEdgeMode) {
    this.capturedInEdgeMode = capturedInEdgeMode;
    return this;
  }

   /**
   * Field indicates whether payment method is captured in edge mode. This is currently set by POS, when application is running in edge mode.  This value can be used to determine the follow on transaction processing mechanism. This will be used in followup
   * @return capturedInEdgeMode
  **/
  
  public Boolean getCapturedInEdgeMode() {
    return capturedInEdgeMode;
  }

  public void setCapturedInEdgeMode(Boolean capturedInEdgeMode) {
    this.capturedInEdgeMode = capturedInEdgeMode;
  }

  public PaymentMethodDTO cardExpiryMonth(String cardExpiryMonth) {
    this.cardExpiryMonth = cardExpiryMonth;
    return this;
  }

   /**
   * Month in which card expires (e.g. 1 for January, 11 for November). Used for processing payments in the third party payment gateway.
   * @return cardExpiryMonth
  **/
  
  public String getCardExpiryMonth() {
    return cardExpiryMonth;
  }

  public void setCardExpiryMonth(String cardExpiryMonth) {
    this.cardExpiryMonth = cardExpiryMonth;
  }

  public PaymentMethodDTO cardExpiryYear(String cardExpiryYear) {
    this.cardExpiryYear = cardExpiryYear;
    return this;
  }

   /**
   * Four-digit year in which card expires. Used for processing payments in the third party payment gateway.
   * @return cardExpiryYear
  **/
  
  public String getCardExpiryYear() {
    return cardExpiryYear;
  }

  public void setCardExpiryYear(String cardExpiryYear) {
    this.cardExpiryYear = cardExpiryYear;
  }

  public PaymentMethodDTO cardType(CardTypeId cardType) {
    this.cardType = cardType;
    return this;
  }

   /**
   * Get cardType
   * @return cardType
  **/
  
  public CardTypeId getCardType() {
    return cardType;
  }

  public void setCardType(CardTypeId cardType) {
    this.cardType = cardType;
  }

  public PaymentMethodDTO cardTypeId(String cardTypeId) {
    this.cardTypeId = cardTypeId;
    return this;
  }

   /**
   * Get cardTypeId
   * @return cardTypeId
  **/
  
  public String getCardTypeId() {
    return cardTypeId;
  }

  public void setCardTypeId(String cardTypeId) {
    this.cardTypeId = cardTypeId;
  }

  public PaymentMethodDTO changeAmount(BigDecimal changeAmount) {
    this.changeAmount = changeAmount;
    return this;
  }

   /**
   * Amount customer is provided in change, for cash payments. Used for audit purposes. For example, if the customer pays with $20 for  $12 order, then change amount should be saved as $8.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return changeAmount
  **/
  
  public BigDecimal getChangeAmount() {
    return changeAmount;
  }

  public void setChangeAmount(BigDecimal changeAmount) {
    this.changeAmount = changeAmount;
  }

  public PaymentMethodDTO chargeSequence(Long chargeSequence) {
    this.chargeSequence = chargeSequence;
    return this;
  }

   /**
   * Sequence in which the payment should be applied, if multiple payments exist on the order and a split shipment occurs. Payments are processed using charge sequence ascending. This attribute is only used if the payment configuration charge sequence is a tie. For example, if a customer pays with a Visa with charge sequence 1 and a Mastercard with charge sequence 2, then if one unit ships, the Visa is charged first.
   * minimum: 0
   * maximum: -8446744073709551617
   * @return chargeSequence
  **/
  
  public Long getChargeSequence() {
    return chargeSequence;
  }

  public void setChargeSequence(Long chargeSequence) {
    this.chargeSequence = chargeSequence;
  }

  public PaymentMethodDTO checkNumber(String checkNumber) {
    this.checkNumber = checkNumber;
    return this;
  }

   /**
   * Check number. Required if the payment method is paper check.
   * @return checkNumber
  **/
  
  public String getCheckNumber() {
    return checkNumber;
  }

  public void setCheckNumber(String checkNumber) {
    this.checkNumber = checkNumber;
  }

  public PaymentMethodDTO checkQuantity(Long checkQuantity) {
    this.checkQuantity = checkQuantity;
    return this;
  }

   /**
   * Quantity of checks given, in case of traveler&#39;s check. Used for audit purposes.
   * minimum: 0
   * maximum: -8446744073709551617
   * @return checkQuantity
  **/
  
  public Long getCheckQuantity() {
    return checkQuantity;
  }

  public void setCheckQuantity(Long checkQuantity) {
    this.checkQuantity = checkQuantity;
  }

  public PaymentMethodDTO conversionRate(Double conversionRate) {
    this.conversionRate = conversionRate;
    return this;
  }

   /**
   * Conversion rate from the alternate currency to the payment header currency. Used for currency conversion and audit of what the conversion rate was at the time of purchase.
   * minimum: 0
   * maximum: 9999.999999999998
   * @return conversionRate
  **/
  
  public Double getConversionRate() {
    return conversionRate;
  }

  public void setConversionRate(Double conversionRate) {
    this.conversionRate = conversionRate;
  }

  public PaymentMethodDTO createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public PaymentMethodDTO createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public PaymentMethodDTO currencyCode(String currencyCode) {
    this.currencyCode = currencyCode;
    return this;
  }

   /**
   * Currency in which the payment is processed
   * @return currencyCode
  **/
  
  public String getCurrencyCode() {
    return currencyCode;
  }

  public void setCurrencyCode(String currencyCode) {
    this.currencyCode = currencyCode;
  }

   /**
   * Amount currently authorized on the card. Increased when an authorization succeeds. Decreased when an authorization expires, when an authorization reversal is processed, or when a follow-on settlement is processed against an auth.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return currentAuthAmount
  **/
  
  public BigDecimal getCurrentAuthAmount() {
    return currentAuthAmount;
  }

  public void setCurrentAuthAmount(BigDecimal currentAuthAmount) {
    this.currentAuthAmount = currentAuthAmount;
  }

   /**
   * Amount currently failed
   * minimum: 0
   * maximum: 99999999999999.98
   * @return currentFailedAmount
  **/
  
  public BigDecimal getCurrentFailedAmount() {
    return currentFailedAmount;
  }

  public void setCurrentFailedAmount(BigDecimal currentFailedAmount) {
    this.currentFailedAmount = currentFailedAmount;
  }

   /**
   * Amount refunded to the card. Increased when a refund succeeds.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return currentRefundAmount
  **/
  
  public BigDecimal getCurrentRefundAmount() {
    return currentRefundAmount;
  }

  public void setCurrentRefundAmount(BigDecimal currentRefundAmount) {
    this.currentRefundAmount = currentRefundAmount;
  }

   /**
   * Amount settled against the card. Increased when a settlement succeeds. Decreased when a refund is processed.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return currentSettledAmount
  **/
  
  public BigDecimal getCurrentSettledAmount() {
    return currentSettledAmount;
  }

  public void setCurrentSettledAmount(BigDecimal currentSettledAmount) {
    this.currentSettledAmount = currentSettledAmount;
  }

  public PaymentMethodDTO customerPaySignature(String customerPaySignature) {
    this.customerPaySignature = customerPaySignature;
    return this;
  }

   /**
   * Signature captured at the point of sale in the device.
   * @return customerPaySignature
  **/
  
  public String getCustomerPaySignature() {
    return customerPaySignature;
  }

  public void setCustomerPaySignature(String customerPaySignature) {
    this.customerPaySignature = customerPaySignature;
  }

  public PaymentMethodDTO customerSignature(String customerSignature) {
    this.customerSignature = customerSignature;
    return this;
  }

   /**
   * Signature captured at the point of sale. Stored for informational purposes only.
   * @return customerSignature
  **/
  
  public String getCustomerSignature() {
    return customerSignature;
  }

  public void setCustomerSignature(String customerSignature) {
    this.customerSignature = customerSignature;
  }

  public PaymentMethodDTO driversLicenseCountry(String driversLicenseCountry) {
    this.driversLicenseCountry = driversLicenseCountry;
    return this;
  }

   /**
   * Country of the driver&#39;s license. Required if the payment method is e-Check. Included in the payment gateway request and used for validation.
   * @return driversLicenseCountry
  **/
  
  public String getDriversLicenseCountry() {
    return driversLicenseCountry;
  }

  public void setDriversLicenseCountry(String driversLicenseCountry) {
    this.driversLicenseCountry = driversLicenseCountry;
  }

  public PaymentMethodDTO driversLicenseNumber(String driversLicenseNumber) {
    this.driversLicenseNumber = driversLicenseNumber;
    return this;
  }

   /**
   * Driver&#39;s license number. Required if the payment method is e-Check. Included in the payment gateway request and used for validation.
   * @return driversLicenseNumber
  **/
  
  public String getDriversLicenseNumber() {
    return driversLicenseNumber;
  }

  public void setDriversLicenseNumber(String driversLicenseNumber) {
    this.driversLicenseNumber = driversLicenseNumber;
  }

  public PaymentMethodDTO driversLicenseState(String driversLicenseState) {
    this.driversLicenseState = driversLicenseState;
    return this;
  }

   /**
   * State/province of the driver&#39;s license. Required if the payment method is e-Check. Included in the payment gateway request and used for validation.
   * @return driversLicenseState
  **/
  
  public String getDriversLicenseState() {
    return driversLicenseState;
  }

  public void setDriversLicenseState(String driversLicenseState) {
    this.driversLicenseState = driversLicenseState;
  }

  public PaymentMethodDTO entryTypeId(String entryTypeId) {
    this.entryTypeId = entryTypeId;
    return this;
  }

   /**
   * Indicates the mode in which payment is captured, such as web or assisted selling. Included on the payment gateway request and used by the gateway to process the payment.
   * @return entryTypeId
  **/
  
  public String getEntryTypeId() {
    return entryTypeId;
  }

  public void setEntryTypeId(String entryTypeId) {
    this.entryTypeId = entryTypeId;
  }

  public PaymentMethodDTO extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public PaymentMethodDTO gatewayAccountId(String gatewayAccountId) {
    this.gatewayAccountId = gatewayAccountId;
    return this;
  }

   /**
   * Account id indicates gateway configuration that is picked while contacting gateway for this payment method.
   * @return gatewayAccountId
  **/
  
  public String getGatewayAccountId() {
    return gatewayAccountId;
  }

  public void setGatewayAccountId(String gatewayAccountId) {
    this.gatewayAccountId = gatewayAccountId;
  }

  public PaymentMethodDTO gatewayId(String gatewayId) {
    this.gatewayId = gatewayId;
    return this;
  }

   /**
   * ID of the payment gateway used to process the payment. If multiple payment gateways are supported across channels, then this field indicates which payment gateway is used for follow-on transactions.
   * @return gatewayId
  **/
  
  public String getGatewayId() {
    return gatewayId;
  }

  public void setGatewayId(String gatewayId) {
    this.gatewayId = gatewayId;
  }

  public PaymentMethodDTO giftCardPin(String giftCardPin) {
    this.giftCardPin = giftCardPin;
    return this;
  }

   /**
   * Gift card PIN number. Used for processing payments in the third party payment gateway. Not required for all gift card payments.
   * @return giftCardPin
  **/
  
  public String getGiftCardPin() {
    return giftCardPin;
  }

  public void setGiftCardPin(String giftCardPin) {
    this.giftCardPin = giftCardPin;
  }

  public PaymentMethodDTO isCopied(Boolean isCopied) {
    this.isCopied = isCopied;
    return this;
  }

   /**
   * Indicates if a payment method is copied from the parent order, in case of returns. When a return is created, return credit is carried from the original order to the return order by copying a payment method and settlement transactions to the return order. Only settlements which are closed, successful, and valid for return are copied for a total processed amount equal to the return invoice total. These copied settlements are used for performing-follow on refunds in case of a pure return or an exchange where the exchange item value is less than the return item value.
   * @return isCopied
  **/
  
  public Boolean getIsCopied() {
    return isCopied;
  }

  public void setIsCopied(Boolean isCopied) {
    this.isCopied = isCopied;
  }

   /**
   * Indicates if the payment method can be edited
   * @return isModifiable
  **/
  
  public Boolean getIsModifiable() {
    return isModifiable;
  }

  public void setIsModifiable(Boolean isModifiable) {
    this.isModifiable = isModifiable;
  }

  public PaymentMethodDTO isModifiable(Boolean isModifiable) {
    this.isModifiable = isModifiable;
    return this;
  }

  public PaymentMethodDTO isSuspended(Boolean isSuspended) {
    this.isSuspended = isSuspended;
    return this;
  }

   /**
   * Indicates if the payment method is suspended. Payment methods can be suspended when a customer requests that the card is not used for future payments. Suspending a card does not affect settlements or refunds which have already been processed, but it deletes any open authorization or settlement transactions. When a payment method is suspended, the customer should provide a new payment method to be used for any remaining balance due. When a payment method is suspended, it is not considered by the payment module when selecting payment methods to authorize or settle against. Suspended payment methods are eligible for refund.
   * @return isSuspended
  **/
  
  public Boolean getIsSuspended() {
    return isSuspended;
  }

  public void setIsSuspended(Boolean isSuspended) {
    this.isSuspended = isSuspended;
  }

  public PaymentMethodDTO isVoided(Boolean isVoided) {
    this.isVoided = isVoided;
    return this;
  }

   /**
   * Indicates if a payment method is voided. Used when a payment method is removed from an order during a POS transaction or in case of a cancellation or post void. When isVoided is updated to true, a void transaction is attempted against any successful settlement and refund transactions. If the void against a settlement fails, then a refund is immediately attempted. If the void against a refund fails, then manual intervention is required to capture a new payment method.
   * @return isVoided
  **/
  
  public Boolean getIsVoided() {
    return isVoided;
  }

  public void setIsVoided(Boolean isVoided) {
    this.isVoided = isVoided;
  }

  public PaymentMethodDTO locationId(String locationId) {
    this.locationId = locationId;
    return this;
  }

   /**
   * Location id indicates the location where operation is performed, also used to fetch gateway account information.
   * @return locationId
  **/
  
  public String getLocationId() {
    return locationId;
  }

  public void setLocationId(String locationId) {
    this.locationId = locationId;
  }

  public PaymentMethodDTO messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public PaymentMethodDTO nameOnCard(String nameOnCard) {
    this.nameOnCard = nameOnCard;
    return this;
  }

   /**
   * Name printed on the card. Used for processing payments in the third party payment gateway.
   * @return nameOnCard
  **/
  
  public String getNameOnCard() {
    return nameOnCard;
  }

  public void setNameOnCard(String nameOnCard) {
    this.nameOnCard = nameOnCard;
  }

  public PaymentMethodDTO orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

   /**
   * Amount originally authorized to be paid, if the amount has since been updated. Updated when an order total decreases. For example, if a customer provided a credit card for a $100 order but later called to add a coupon, reducing the order total to $80, then the original amount is $100 and the amount is $80.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return originalAmount
  **/
  
  public BigDecimal getOriginalAmount() {
    return originalAmount;
  }

  public PaymentMethodDTO PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public PaymentMethodDTO parentOrderId(String parentOrderId) {
    this.parentOrderId = parentOrderId;
    return this;
  }

   /**
   * Identifies the parent order from which the payment method is copied, in case of returns. For a pure return, the payment method is copied to the return order to issue a refund against the original settlement. For an exchange, the payment method is copied to be used as return credit for exchange item(s). For example, if a return line is created against an order line from order id 1001, then the return order payment method will reference parentOrderId 1001.
   * @return parentOrderId
  **/
  
  public String getParentOrderId() {
    return parentOrderId;
  }

  public void setParentOrderId(String parentOrderId) {
    this.parentOrderId = parentOrderId;
  }

  public PaymentMethodDTO parentOrderPaymentMethod(List<ParentOrderPaymentMethod> parentOrderPaymentMethod) {
    this.parentOrderPaymentMethod = parentOrderPaymentMethod;
    return this;
  }

  public PaymentMethodDTO addParentOrderPaymentMethodItem(ParentOrderPaymentMethod parentOrderPaymentMethodItem) {
    if (this.parentOrderPaymentMethod == null) {
      this.parentOrderPaymentMethod = new ArrayList<ParentOrderPaymentMethod>();
    }
    this.parentOrderPaymentMethod.add(parentOrderPaymentMethodItem);
    return this;
  }

   /**
   * Get parentOrderPaymentMethod
   * @return parentOrderPaymentMethod
  **/
  
  public List<ParentOrderPaymentMethod> getParentOrderPaymentMethod() {
    return parentOrderPaymentMethod;
  }

  public void setParentOrderPaymentMethod(List<ParentOrderPaymentMethod> parentOrderPaymentMethod) {
    this.parentOrderPaymentMethod = parentOrderPaymentMethod;
  }

  public PaymentMethodDTO parentPaymentGroupId(String parentPaymentGroupId) {
    this.parentPaymentGroupId = parentPaymentGroupId;
    return this;
  }

   /**
   * Identifies the payment group of the parent payment method which is being copied, in case of returns. For example, if a return line is created against an order which has payment method id PM01 which is part of payment group A, then the return order payment method will reference parentPaymentGroup A.
   * @return parentPaymentGroupId
  **/
  
  public String getParentPaymentGroupId() {
    return parentPaymentGroupId;
  }

  public void setParentPaymentGroupId(String parentPaymentGroupId) {
    this.parentPaymentGroupId = parentPaymentGroupId;
  }

  public PaymentMethodDTO parentPaymentMethodId(String parentPaymentMethodId) {
    this.parentPaymentMethodId = parentPaymentMethodId;
    return this;
  }

   /**
   * Identifies the parent payment method which is being copied, in case of returns. For a pure return, the payment method is copied to the return order to issue a refund against the original settlement. For an exchange, the payment method is copied to be used as return credit for exchange item(s). For example, if a return line is created against an order which has payment method id PM01, then the return order payment method will reference parentPaymentMethodId PM01.
   * @return parentPaymentMethodId
  **/
  
  public String getParentPaymentMethodId() {
    return parentPaymentMethodId;
  }

  public void setParentPaymentMethodId(String parentPaymentMethodId) {
    this.parentPaymentMethodId = parentPaymentMethodId;
  }

  public PaymentMethodDTO paymentMethodAttribute(List<PaymentMethodAttribute> paymentMethodAttribute) {
    this.paymentMethodAttribute = paymentMethodAttribute;
    return this;
  }

  public PaymentMethodDTO addPaymentMethodAttributeItem(PaymentMethodAttribute paymentMethodAttributeItem) {
    if (this.paymentMethodAttribute == null) {
      this.paymentMethodAttribute = new ArrayList<PaymentMethodAttribute>();
    }
    this.paymentMethodAttribute.add(paymentMethodAttributeItem);
    return this;
  }

   /**
   * Get paymentMethodAttribute
   * @return paymentMethodAttribute
  **/
  
  public List<PaymentMethodAttribute> getPaymentMethodAttribute() {
    return paymentMethodAttribute;
  }

  public void setPaymentMethodAttribute(List<PaymentMethodAttribute> paymentMethodAttribute) {
    this.paymentMethodAttribute = paymentMethodAttribute;
  }

  public PaymentMethodDTO paymentMethodEncrAttribute(List<PaymentMethodEncrAttribute> paymentMethodEncrAttribute) {
    this.paymentMethodEncrAttribute = paymentMethodEncrAttribute;
    return this;
  }

  public PaymentMethodDTO addPaymentMethodEncrAttributeItem(PaymentMethodEncrAttribute paymentMethodEncrAttributeItem) {
    if (this.paymentMethodEncrAttribute == null) {
      this.paymentMethodEncrAttribute = new ArrayList<PaymentMethodEncrAttribute>();
    }
    this.paymentMethodEncrAttribute.add(paymentMethodEncrAttributeItem);
    return this;
  }

   /**
   * Get paymentMethodEncrAttribute
   * @return paymentMethodEncrAttribute
  **/
  
  public List<PaymentMethodEncrAttribute> getPaymentMethodEncrAttribute() {
    return paymentMethodEncrAttribute;
  }

  public void setPaymentMethodEncrAttribute(List<PaymentMethodEncrAttribute> paymentMethodEncrAttribute) {
    this.paymentMethodEncrAttribute = paymentMethodEncrAttribute;
  }

  public PaymentMethodDTO paymentMethodId(String paymentMethodId) {
    this.paymentMethodId = paymentMethodId;
    return this;
  }

   /**
   * Unique identifier of the payment as defined by an external system. If not defined by external system, then defined by the payment component.
   * @return paymentMethodId
  **/
  
  public String getPaymentMethodId() {
    return paymentMethodId;
  }

  public void setPaymentMethodId(String paymentMethodId) {
    this.paymentMethodId = paymentMethodId;
  }

  public PaymentMethodDTO paymentTransaction(List<PaymentTransaction> paymentTransaction) {
    this.paymentTransaction = paymentTransaction;
    return this;
  }

  public PaymentMethodDTO addPaymentTransactionItem(PaymentTransaction paymentTransactionItem) {
    if (this.paymentTransaction == null) {
      this.paymentTransaction = new ArrayList<PaymentTransaction>();
    }
    this.paymentTransaction.add(paymentTransactionItem);
    return this;
  }

   /**
   * Get paymentTransaction
   * @return paymentTransaction
  **/
  
  public List<PaymentTransaction> getPaymentTransaction() {
    return paymentTransaction;
  }

  public void setPaymentTransaction(List<PaymentTransaction> paymentTransaction) {
    this.paymentTransaction = paymentTransaction;
  }

  public PaymentMethodDTO paymentType(PaymentTypeId paymentType) {
    this.paymentType = paymentType;
    return this;
  }

   /**
   * Get paymentType
   * @return paymentType
  **/
  
  public PaymentTypeId getPaymentType() {
    return paymentType;
  }

  public void setPaymentType(PaymentTypeId paymentType) {
    this.paymentType = paymentType;
  }

  public PaymentMethodDTO routingDisplayNumber(String routingDisplayNumber) {
    this.routingDisplayNumber = routingDisplayNumber;
    return this;
  }

   /**
   * Bank routing number text to be displayed in the call center UI, if the payment method is e-check or paper check. This attribute is not included in any payment gateway requests.
   * @return routingDisplayNumber
  **/
  
  public String getRoutingDisplayNumber() {
    return routingDisplayNumber;
  }

  public void setRoutingDisplayNumber(String routingDisplayNumber) {
    this.routingDisplayNumber = routingDisplayNumber;
  }

  public PaymentMethodDTO routingNumber(String routingNumber) {
    this.routingNumber = routingNumber;
    return this;
  }

   /**
   * Bank routing number. Required if the payment method is e-check or paper check, and used in the payment gateway request. For other payment methods, this attribute is not included in the payment gateway request.
   * @return routingNumber
  **/
  
  public String getRoutingNumber() {
    return routingNumber;
  }

  public void setRoutingNumber(String routingNumber) {
    this.routingNumber = routingNumber;
  }

  public PaymentMethodDTO securityCode(String securityCode) {
    this.securityCode = securityCode;
    return this;
  }

   /**
   * Get securityCode
   * @return securityCode
  **/
  
  public String getSecurityCode() {
    return securityCode;
  }

  public void setSecurityCode(String securityCode) {
    this.securityCode = securityCode;
  }

  public PaymentMethodDTO swipeData(String swipeData) {
    this.swipeData = swipeData;
    return this;
  }

   /**
   * Data captured from the card swipe
   * @return swipeData
  **/
  
  public String getSwipeData() {
    return swipeData;
  }

  public void setSwipeData(String swipeData) {
    this.swipeData = swipeData;
  }

  public PaymentMethodDTO transactionReferenceId(String transactionReferenceId) {
    this.transactionReferenceId = transactionReferenceId;
    return this;
  }

   /**
   * Indicates the transaction/order number of the additional purchases during pick up.  This is used to identify the payments done for those such items and UI to decide if certain actions be allowed or not. Example: Allow Void tender for the newly added payments, not the ones associated to original order.
   * @return transactionReferenceId
  **/
  
  public String getTransactionReferenceId() {
    return transactionReferenceId;
  }

  public void setTransactionReferenceId(String transactionReferenceId) {
    this.transactionReferenceId = transactionReferenceId;
  }

  public PaymentMethodDTO translations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
    return this;
  }

  public PaymentMethodDTO putTranslationsItem(String key, Map<String, String> translationsItem) {
    if (this.translations == null) {
      this.translations = new HashMap<String, Map<String, String>>();
    }
    this.translations.put(key, translationsItem);
    return this;
  }

   /**
   * Get translations
   * @return translations
  **/
  
  public Map<String, Map<String, String>> getTranslations() {
    return translations;
  }

  public void setTranslations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
  }

  public PaymentMethodDTO updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public PaymentMethodDTO updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public PaymentMethodDTO entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentMethodDTO paymentMethodDTO = (PaymentMethodDTO) o;
    return Objects.equals(this.accountDisplayNumber, paymentMethodDTO.accountDisplayNumber) &&
        Objects.equals(this.accountNumber, paymentMethodDTO.accountNumber) &&
        Objects.equals(this.accountType, paymentMethodDTO.accountType) &&
        Objects.equals(this.actions, paymentMethodDTO.actions) &&
        Objects.equals(this.alternateCurrencyAmount, paymentMethodDTO.alternateCurrencyAmount) &&
        Objects.equals(this.alternateCurrencyCode, paymentMethodDTO.alternateCurrencyCode) &&
        Objects.equals(this.amount, paymentMethodDTO.amount) &&
        Objects.equals(this.billingAddress, paymentMethodDTO.billingAddress) &&
        Objects.equals(this.businessName, paymentMethodDTO.businessName) &&
        Objects.equals(this.businessTaxId, paymentMethodDTO.businessTaxId) &&
        Objects.equals(this.capturedInEdgeMode, paymentMethodDTO.capturedInEdgeMode) &&
        Objects.equals(this.cardExpiryMonth, paymentMethodDTO.cardExpiryMonth) &&
        Objects.equals(this.cardExpiryYear, paymentMethodDTO.cardExpiryYear) &&
        Objects.equals(this.cardType, paymentMethodDTO.cardType) &&
        Objects.equals(this.cardTypeId, paymentMethodDTO.cardTypeId) &&
        Objects.equals(this.changeAmount, paymentMethodDTO.changeAmount) &&
        Objects.equals(this.chargeSequence, paymentMethodDTO.chargeSequence) &&
        Objects.equals(this.checkNumber, paymentMethodDTO.checkNumber) &&
        Objects.equals(this.checkQuantity, paymentMethodDTO.checkQuantity) &&
        Objects.equals(this.conversionRate, paymentMethodDTO.conversionRate) &&
        Objects.equals(this.createdBy, paymentMethodDTO.createdBy) &&
        Objects.equals(this.createdTimestamp, paymentMethodDTO.createdTimestamp) &&
        Objects.equals(this.currencyCode, paymentMethodDTO.currencyCode) &&
        Objects.equals(this.currentAuthAmount, paymentMethodDTO.currentAuthAmount) &&
        Objects.equals(this.currentFailedAmount, paymentMethodDTO.currentFailedAmount) &&
        Objects.equals(this.currentRefundAmount, paymentMethodDTO.currentRefundAmount) &&
        Objects.equals(this.currentSettledAmount, paymentMethodDTO.currentSettledAmount) &&
        Objects.equals(this.customerPaySignature, paymentMethodDTO.customerPaySignature) &&
        Objects.equals(this.customerSignature, paymentMethodDTO.customerSignature) &&
        Objects.equals(this.driversLicenseCountry, paymentMethodDTO.driversLicenseCountry) &&
        Objects.equals(this.driversLicenseNumber, paymentMethodDTO.driversLicenseNumber) &&
        Objects.equals(this.driversLicenseState, paymentMethodDTO.driversLicenseState) &&
        Objects.equals(this.entryTypeId, paymentMethodDTO.entryTypeId) &&
        Objects.equals(this.extended, paymentMethodDTO.extended) &&
        Objects.equals(this.gatewayAccountId, paymentMethodDTO.gatewayAccountId) &&
        Objects.equals(this.gatewayId, paymentMethodDTO.gatewayId) &&
        Objects.equals(this.giftCardPin, paymentMethodDTO.giftCardPin) &&
        Objects.equals(this.isCopied, paymentMethodDTO.isCopied) &&
        Objects.equals(this.isModifiable, paymentMethodDTO.isModifiable) &&
        Objects.equals(this.isSuspended, paymentMethodDTO.isSuspended) &&
        Objects.equals(this.isVoided, paymentMethodDTO.isVoided) &&
        Objects.equals(this.locationId, paymentMethodDTO.locationId) &&
        Objects.equals(this.messages, paymentMethodDTO.messages) &&
        Objects.equals(this.nameOnCard, paymentMethodDTO.nameOnCard) &&
        Objects.equals(this.orgId, paymentMethodDTO.orgId) &&
        Objects.equals(this.originalAmount, paymentMethodDTO.originalAmount) &&
        Objects.equals(this.PK, paymentMethodDTO.PK) &&
        Objects.equals(this.parentOrderId, paymentMethodDTO.parentOrderId) &&
        Objects.equals(this.parentOrderPaymentMethod, paymentMethodDTO.parentOrderPaymentMethod) &&
        Objects.equals(this.parentPaymentGroupId, paymentMethodDTO.parentPaymentGroupId) &&
        Objects.equals(this.parentPaymentMethodId, paymentMethodDTO.parentPaymentMethodId) &&
        Objects.equals(this.paymentMethodAttribute, paymentMethodDTO.paymentMethodAttribute) &&
        Objects.equals(this.paymentMethodEncrAttribute, paymentMethodDTO.paymentMethodEncrAttribute) &&
        Objects.equals(this.paymentMethodId, paymentMethodDTO.paymentMethodId) &&
        Objects.equals(this.paymentTransaction, paymentMethodDTO.paymentTransaction) &&
        Objects.equals(this.paymentType, paymentMethodDTO.paymentType) &&
        Objects.equals(this.routingDisplayNumber, paymentMethodDTO.routingDisplayNumber) &&
        Objects.equals(this.routingNumber, paymentMethodDTO.routingNumber) &&
        Objects.equals(this.securityCode, paymentMethodDTO.securityCode) &&
        Objects.equals(this.swipeData, paymentMethodDTO.swipeData) &&
        Objects.equals(this.transactionReferenceId, paymentMethodDTO.transactionReferenceId) &&
        Objects.equals(this.translations, paymentMethodDTO.translations) &&
        Objects.equals(this.updatedBy, paymentMethodDTO.updatedBy) &&
        Objects.equals(this.updatedTimestamp, paymentMethodDTO.updatedTimestamp) &&
        Objects.equals(this.entityName, paymentMethodDTO.entityName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(accountDisplayNumber, accountNumber, accountType, actions, alternateCurrencyAmount, alternateCurrencyCode, amount, billingAddress, businessName, businessTaxId, capturedInEdgeMode, cardExpiryMonth, cardExpiryYear, cardType, cardTypeId, changeAmount, chargeSequence, checkNumber, checkQuantity, conversionRate, createdBy, createdTimestamp, currencyCode, currentAuthAmount, currentFailedAmount, currentRefundAmount, currentSettledAmount, customerPaySignature, customerSignature, driversLicenseCountry, driversLicenseNumber, driversLicenseState, entryTypeId, extended, gatewayAccountId, gatewayId, giftCardPin, isCopied, isModifiable, isSuspended, isVoided, locationId, messages, nameOnCard, orgId, originalAmount, PK, parentOrderId, parentOrderPaymentMethod, parentPaymentGroupId, parentPaymentMethodId, paymentMethodAttribute, paymentMethodEncrAttribute, paymentMethodId, paymentTransaction, paymentType, routingDisplayNumber, routingNumber, securityCode, swipeData, transactionReferenceId, translations, updatedBy, updatedTimestamp, entityName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentMethodDTO {\n");
    
    sb.append("    accountDisplayNumber: ").append(toIndentedString(accountDisplayNumber)).append("\n");
    sb.append("    accountNumber: ").append(toIndentedString(accountNumber)).append("\n");
    sb.append("    accountType: ").append(toIndentedString(accountType)).append("\n");
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    alternateCurrencyAmount: ").append(toIndentedString(alternateCurrencyAmount)).append("\n");
    sb.append("    alternateCurrencyCode: ").append(toIndentedString(alternateCurrencyCode)).append("\n");
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    billingAddress: ").append(toIndentedString(billingAddress)).append("\n");
    sb.append("    businessName: ").append(toIndentedString(businessName)).append("\n");
    sb.append("    businessTaxId: ").append(toIndentedString(businessTaxId)).append("\n");
    sb.append("    capturedInEdgeMode: ").append(toIndentedString(capturedInEdgeMode)).append("\n");
    sb.append("    cardExpiryMonth: ").append(toIndentedString(cardExpiryMonth)).append("\n");
    sb.append("    cardExpiryYear: ").append(toIndentedString(cardExpiryYear)).append("\n");
    sb.append("    cardType: ").append(toIndentedString(cardType)).append("\n");
    sb.append("    cardTypeId: ").append(toIndentedString(cardTypeId)).append("\n");
    sb.append("    changeAmount: ").append(toIndentedString(changeAmount)).append("\n");
    sb.append("    chargeSequence: ").append(toIndentedString(chargeSequence)).append("\n");
    sb.append("    checkNumber: ").append(toIndentedString(checkNumber)).append("\n");
    sb.append("    checkQuantity: ").append(toIndentedString(checkQuantity)).append("\n");
    sb.append("    conversionRate: ").append(toIndentedString(conversionRate)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    currencyCode: ").append(toIndentedString(currencyCode)).append("\n");
    sb.append("    currentAuthAmount: ").append(toIndentedString(currentAuthAmount)).append("\n");
    sb.append("    currentFailedAmount: ").append(toIndentedString(currentFailedAmount)).append("\n");
    sb.append("    currentRefundAmount: ").append(toIndentedString(currentRefundAmount)).append("\n");
    sb.append("    currentSettledAmount: ").append(toIndentedString(currentSettledAmount)).append("\n");
    sb.append("    customerPaySignature: ").append(toIndentedString(customerPaySignature)).append("\n");
    sb.append("    customerSignature: ").append(toIndentedString(customerSignature)).append("\n");
    sb.append("    driversLicenseCountry: ").append(toIndentedString(driversLicenseCountry)).append("\n");
    sb.append("    driversLicenseNumber: ").append(toIndentedString(driversLicenseNumber)).append("\n");
    sb.append("    driversLicenseState: ").append(toIndentedString(driversLicenseState)).append("\n");
    sb.append("    entryTypeId: ").append(toIndentedString(entryTypeId)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    gatewayAccountId: ").append(toIndentedString(gatewayAccountId)).append("\n");
    sb.append("    gatewayId: ").append(toIndentedString(gatewayId)).append("\n");
    sb.append("    giftCardPin: ").append(toIndentedString(giftCardPin)).append("\n");
    sb.append("    isCopied: ").append(toIndentedString(isCopied)).append("\n");
    sb.append("    isModifiable: ").append(toIndentedString(isModifiable)).append("\n");
    sb.append("    isSuspended: ").append(toIndentedString(isSuspended)).append("\n");
    sb.append("    isVoided: ").append(toIndentedString(isVoided)).append("\n");
    sb.append("    locationId: ").append(toIndentedString(locationId)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    nameOnCard: ").append(toIndentedString(nameOnCard)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    originalAmount: ").append(toIndentedString(originalAmount)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentOrderId: ").append(toIndentedString(parentOrderId)).append("\n");
    sb.append("    parentOrderPaymentMethod: ").append(toIndentedString(parentOrderPaymentMethod)).append("\n");
    sb.append("    parentPaymentGroupId: ").append(toIndentedString(parentPaymentGroupId)).append("\n");
    sb.append("    parentPaymentMethodId: ").append(toIndentedString(parentPaymentMethodId)).append("\n");
    sb.append("    paymentMethodAttribute: ").append(toIndentedString(paymentMethodAttribute)).append("\n");
    sb.append("    paymentMethodEncrAttribute: ").append(toIndentedString(paymentMethodEncrAttribute)).append("\n");
    sb.append("    paymentMethodId: ").append(toIndentedString(paymentMethodId)).append("\n");
    sb.append("    paymentTransaction: ").append(toIndentedString(paymentTransaction)).append("\n");
    sb.append("    paymentType: ").append(toIndentedString(paymentType)).append("\n");
    sb.append("    routingDisplayNumber: ").append(toIndentedString(routingDisplayNumber)).append("\n");
    sb.append("    routingNumber: ").append(toIndentedString(routingNumber)).append("\n");
    sb.append("    securityCode: ").append(toIndentedString(securityCode)).append("\n");
    sb.append("    swipeData: ").append(toIndentedString(swipeData)).append("\n");
    sb.append("    transactionReferenceId: ").append(toIndentedString(transactionReferenceId)).append("\n");
    sb.append("    translations: ").append(toIndentedString(translations)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

