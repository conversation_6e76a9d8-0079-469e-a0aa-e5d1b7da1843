/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ShipToAddress
 */
public class ShipToAddress {
  public static final String SERIALIZED_NAME_AV_S_REASON_COMMENTS = "AVSReasonComments";
  @SerializedName(SERIALIZED_NAME_AV_S_REASON_COMMENTS)
  private String avSReasonComments;

  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ADDRESS = "Address";
  @SerializedName(SERIALIZED_NAME_ADDRESS)
  private Address address = null;

  public static final String SERIALIZED_NAME_ADDRESS_ID = "AddressId";
  @SerializedName(SERIALIZED_NAME_ADDRESS_ID)
  private String addressId;

  public static final String SERIALIZED_NAME_ADDRESS_NAME = "AddressName";
  @SerializedName(SERIALIZED_NAME_ADDRESS_NAME)
  private String addressName;

  public static final String SERIALIZED_NAME_AVS_REASON = "AvsReason";
  @SerializedName(SERIALIZED_NAME_AVS_REASON)
  private ReasonId avsReason = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IS_ADDRESS_VERIFIED = "IsAddressVerified";
  @SerializedName(SERIALIZED_NAME_IS_ADDRESS_VERIFIED)
  private Boolean isAddressVerified;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_ID = "OrderId";
  @SerializedName(SERIALIZED_NAME_ORDER_ID)
  private String orderId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public ShipToAddress avSReasonComments(String avSReasonComments) {
    this.avSReasonComments = avSReasonComments;
    return this;
  }

   /**
   * Placeholder to capture comments on avsReason
   * @return avSReasonComments
  **/
  
  public String getAvSReasonComments() {
    return avSReasonComments;
  }

  public void setAvSReasonComments(String avSReasonComments) {
    this.avSReasonComments = avSReasonComments;
  }

  public ShipToAddress actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ShipToAddress putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ShipToAddress address(Address address) {
    this.address = address;
    return this;
  }

   /**
   * Get address
   * @return address
  **/
  
  public Address getAddress() {
    return address;
  }

  public void setAddress(Address address) {
    this.address = address;
  }

  public ShipToAddress addressId(String addressId) {
    this.addressId = addressId;
    return this;
  }

   /**
   * Unique business key of the address. It is HashValue generated within System.
   * @return addressId
  **/
  
  public String getAddressId() {
    return addressId;
  }

  public void setAddressId(String addressId) {
    this.addressId = addressId;
  }

  public ShipToAddress addressName(String addressName) {
    this.addressName = addressName;
    return this;
  }

   /**
   * Mom Home&#39;, &#39;Work&#39;, etc.
   * @return addressName
  **/
  
  public String getAddressName() {
    return addressName;
  }

  public void setAddressName(String addressName) {
    this.addressName = addressName;
  }

  public ShipToAddress avsReason(ReasonId avsReason) {
    this.avsReason = avsReason;
    return this;
  }

   /**
   * Get avsReason
   * @return avsReason
  **/
  
  public ReasonId getAvsReason() {
    return avsReason;
  }

  public void setAvsReason(ReasonId avsReason) {
    this.avsReason = avsReason;
  }

  public ShipToAddress createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ShipToAddress createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ShipToAddress extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ShipToAddress isAddressVerified(Boolean isAddressVerified) {
    this.isAddressVerified = isAddressVerified;
    return this;
  }

   /**
   * Indicates if the address has been verified by an address verification system
   * @return isAddressVerified
  **/
  
  public Boolean getIsAddressVerified() {
    return isAddressVerified;
  }

  public void setIsAddressVerified(Boolean isAddressVerified) {
    this.isAddressVerified = isAddressVerified;
  }

  public ShipToAddress localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public ShipToAddress messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ShipToAddress orderId(String orderId) {
    this.orderId = orderId;
    return this;
  }

   /**
   * Unique identifier of the order with which the address is associated
   * @return orderId
  **/
  
  public String getOrderId() {
    return orderId;
  }

  public void setOrderId(String orderId) {
    this.orderId = orderId;
  }

  public ShipToAddress orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public ShipToAddress PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ShipToAddress updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ShipToAddress updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ShipToAddress entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public ShipToAddress localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ShipToAddress shipToAddress = (ShipToAddress) o;
    return Objects.equals(this.avSReasonComments, shipToAddress.avSReasonComments) &&
        Objects.equals(this.actions, shipToAddress.actions) &&
        Objects.equals(this.address, shipToAddress.address) &&
        Objects.equals(this.addressId, shipToAddress.addressId) &&
        Objects.equals(this.addressName, shipToAddress.addressName) &&
        Objects.equals(this.avsReason, shipToAddress.avsReason) &&
        Objects.equals(this.createdBy, shipToAddress.createdBy) &&
        Objects.equals(this.createdTimestamp, shipToAddress.createdTimestamp) &&
        Objects.equals(this.extended, shipToAddress.extended) &&
        Objects.equals(this.isAddressVerified, shipToAddress.isAddressVerified) &&
        Objects.equals(this.localizedTo, shipToAddress.localizedTo) &&
        Objects.equals(this.messages, shipToAddress.messages) &&
        Objects.equals(this.orderId, shipToAddress.orderId) &&
        Objects.equals(this.orgId, shipToAddress.orgId) &&
        Objects.equals(this.PK, shipToAddress.PK) &&
        Objects.equals(this.updatedBy, shipToAddress.updatedBy) &&
        Objects.equals(this.updatedTimestamp, shipToAddress.updatedTimestamp) &&
        Objects.equals(this.entityName, shipToAddress.entityName) &&
        Objects.equals(this.localize, shipToAddress.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(avSReasonComments, actions, address, addressId, addressName, avsReason, createdBy, createdTimestamp, extended, isAddressVerified, localizedTo, messages, orderId, orgId, PK, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ShipToAddress {\n");
    
    sb.append("    avSReasonComments: ").append(toIndentedString(avSReasonComments)).append("\n");
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    addressId: ").append(toIndentedString(addressId)).append("\n");
    sb.append("    addressName: ").append(toIndentedString(addressName)).append("\n");
    sb.append("    avsReason: ").append(toIndentedString(avsReason)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    isAddressVerified: ").append(toIndentedString(isAddressVerified)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderId: ").append(toIndentedString(orderId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

