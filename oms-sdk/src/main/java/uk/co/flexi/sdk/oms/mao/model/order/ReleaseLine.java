/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.LocalDate;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ReleaseLine
 */
public class ReleaseLine {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ALLOCATION_ID = "AllocationId";
  @SerializedName(SERIALIZED_NAME_ALLOCATION_ID)
  private String allocationId;

  public static final String SERIALIZED_NAME_CANCELLED_DATE = "CancelledDate";
  @SerializedName(SERIALIZED_NAME_CANCELLED_DATE)
  private LocalDate cancelledDate;

  public static final String SERIALIZED_NAME_CANCELLED_QUANTITY = "CancelledQuantity";
  @SerializedName(SERIALIZED_NAME_CANCELLED_QUANTITY)
  private Double cancelledQuantity;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EFFECTIVE_RANK = "EffectiveRank";
  @SerializedName(SERIALIZED_NAME_EFFECTIVE_RANK)
  private String effectiveRank;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FULFILLED_QUANTITY = "FulfilledQuantity";
  @SerializedName(SERIALIZED_NAME_FULFILLED_QUANTITY)
  private Double fulfilledQuantity;

  public static final String SERIALIZED_NAME_ITEM_ID = "ItemId";
  @SerializedName(SERIALIZED_NAME_ITEM_ID)
  private String itemId;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_ID = "OrderLineId";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_ID)
  private String orderLineId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_QUANTITY = "Quantity";
  @SerializedName(SERIALIZED_NAME_QUANTITY)
  private Double quantity;

  public static final String SERIALIZED_NAME_RELEASE_LINE_ID = "ReleaseLineId";
  @SerializedName(SERIALIZED_NAME_RELEASE_LINE_ID)
  private String releaseLineId;

  public static final String SERIALIZED_NAME_U_O_M = "UOM";
  @SerializedName(SERIALIZED_NAME_U_O_M)
  private String UOM;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public ReleaseLine actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ReleaseLine putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ReleaseLine allocationId(String allocationId) {
    this.allocationId = allocationId;
    return this;
  }

   /**
   * Unique identifier of the allocation
   * @return allocationId
  **/
  
  public String getAllocationId() {
    return allocationId;
  }

  public void setAllocationId(String allocationId) {
    this.allocationId = allocationId;
  }

  public ReleaseLine cancelledDate(LocalDate cancelledDate) {
    this.cancelledDate = cancelledDate;
    return this;
  }

   /**
   * Date when any unit of the release line is cancelled
   * @return cancelledDate
  **/
  
  public LocalDate getCancelledDate() {
    return cancelledDate;
  }

  public void setCancelledDate(LocalDate cancelledDate) {
    this.cancelledDate = cancelledDate;
  }

  public ReleaseLine cancelledQuantity(Double cancelledQuantity) {
    this.cancelledQuantity = cancelledQuantity;
    return this;
  }

   /**
   * Cancelled quantity
   * minimum: 0
   * maximum: 999999999999.9999
   * @return cancelledQuantity
  **/
  
  public Double getCancelledQuantity() {
    return cancelledQuantity;
  }

  public void setCancelledQuantity(Double cancelledQuantity) {
    this.cancelledQuantity = cancelledQuantity;
  }

  public ReleaseLine createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ReleaseLine createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ReleaseLine effectiveRank(String effectiveRank) {
    this.effectiveRank = effectiveRank;
    return this;
  }

   /**
   * Rank of the release line, as calculated by the system
   * @return effectiveRank
  **/
  
  public String getEffectiveRank() {
    return effectiveRank;
  }

  public void setEffectiveRank(String effectiveRank) {
    this.effectiveRank = effectiveRank;
  }

  public ReleaseLine extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ReleaseLine fulfilledQuantity(Double fulfilledQuantity) {
    this.fulfilledQuantity = fulfilledQuantity;
    return this;
  }

   /**
   * Total fulfilled quantity for the release line
   * minimum: 0
   * maximum: 999999999999.9999
   * @return fulfilledQuantity
  **/
  
  public Double getFulfilledQuantity() {
    return fulfilledQuantity;
  }

  public void setFulfilledQuantity(Double fulfilledQuantity) {
    this.fulfilledQuantity = fulfilledQuantity;
  }

  public ReleaseLine itemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

   /**
   * Identifier of the item
   * @return itemId
  **/
  
  public String getItemId() {
    return itemId;
  }

  public void setItemId(String itemId) {
    this.itemId = itemId;
  }

  public ReleaseLine localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public ReleaseLine messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ReleaseLine orderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
    return this;
  }

   /**
   * Identifier of the corresponding order line
   * @return orderLineId
  **/
  
  public String getOrderLineId() {
    return orderLineId;
  }

  public void setOrderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
  }

  public ReleaseLine orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public ReleaseLine PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ReleaseLine quantity(Double quantity) {
    this.quantity = quantity;
    return this;
  }

   /**
   * Quantity which is requested for fulfillment
   * minimum: 0
   * maximum: 999999999999.9999
   * @return quantity
  **/
  
  public Double getQuantity() {
    return quantity;
  }

  public void setQuantity(Double quantity) {
    this.quantity = quantity;
  }

  public ReleaseLine releaseLineId(String releaseLineId) {
    this.releaseLineId = releaseLineId;
    return this;
  }

   /**
   * Unique identifier of the release line (need to create as a next up number within the order: 1, 2, 3, 4)
   * @return releaseLineId
  **/
  
  public String getReleaseLineId() {
    return releaseLineId;
  }

  public void setReleaseLineId(String releaseLineId) {
    this.releaseLineId = releaseLineId;
  }

  public ReleaseLine UOM(String UOM) {
    this.UOM = UOM;
    return this;
  }

   /**
   * Unit of measure (UOM) of the quantity
   * @return UOM
  **/
  
  public String getUOM() {
    return UOM;
  }

  public void setUOM(String UOM) {
    this.UOM = UOM;
  }

  public ReleaseLine updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ReleaseLine updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ReleaseLine entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public ReleaseLine localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReleaseLine releaseLine = (ReleaseLine) o;
    return Objects.equals(this.actions, releaseLine.actions) &&
        Objects.equals(this.allocationId, releaseLine.allocationId) &&
        Objects.equals(this.cancelledDate, releaseLine.cancelledDate) &&
        Objects.equals(this.cancelledQuantity, releaseLine.cancelledQuantity) &&
        Objects.equals(this.createdBy, releaseLine.createdBy) &&
        Objects.equals(this.createdTimestamp, releaseLine.createdTimestamp) &&
        Objects.equals(this.effectiveRank, releaseLine.effectiveRank) &&
        Objects.equals(this.extended, releaseLine.extended) &&
        Objects.equals(this.fulfilledQuantity, releaseLine.fulfilledQuantity) &&
        Objects.equals(this.itemId, releaseLine.itemId) &&
        Objects.equals(this.localizedTo, releaseLine.localizedTo) &&
        Objects.equals(this.messages, releaseLine.messages) &&
        Objects.equals(this.orderLineId, releaseLine.orderLineId) &&
        Objects.equals(this.orgId, releaseLine.orgId) &&
        Objects.equals(this.PK, releaseLine.PK) &&
        Objects.equals(this.quantity, releaseLine.quantity) &&
        Objects.equals(this.releaseLineId, releaseLine.releaseLineId) &&
        Objects.equals(this.UOM, releaseLine.UOM) &&
        Objects.equals(this.updatedBy, releaseLine.updatedBy) &&
        Objects.equals(this.updatedTimestamp, releaseLine.updatedTimestamp) &&
        Objects.equals(this.entityName, releaseLine.entityName) &&
        Objects.equals(this.localize, releaseLine.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, allocationId, cancelledDate, cancelledQuantity, createdBy, createdTimestamp, effectiveRank, extended, fulfilledQuantity, itemId, localizedTo, messages, orderLineId, orgId, PK, quantity, releaseLineId, UOM, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReleaseLine {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    allocationId: ").append(toIndentedString(allocationId)).append("\n");
    sb.append("    cancelledDate: ").append(toIndentedString(cancelledDate)).append("\n");
    sb.append("    cancelledQuantity: ").append(toIndentedString(cancelledQuantity)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    effectiveRank: ").append(toIndentedString(effectiveRank)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    fulfilledQuantity: ").append(toIndentedString(fulfilledQuantity)).append("\n");
    sb.append("    itemId: ").append(toIndentedString(itemId)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderLineId: ").append(toIndentedString(orderLineId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    releaseLineId: ").append(toIndentedString(releaseLineId)).append("\n");
    sb.append("    UOM: ").append(toIndentedString(UOM)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

