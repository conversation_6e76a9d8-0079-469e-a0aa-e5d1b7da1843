package uk.co.flexi.sdk.oms.mao.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import uk.co.flexi.sdk.oms.mao.client.config.MaoConfig;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

import static uk.co.flexi.sdk.oms.mao.client.OMSClientFactory.OMSProvider.MAO;
import static uk.co.flexi.sdk.oms.mao.client.OMSClientFactory.OMSProvider.MOCK;

public class OMSClientFactory {

    public enum OMSProvider {
        MAO,
        MOCK,
        OTHER
    }
    private static final Logger log = LoggerFactory.getLogger(OMSClientFactory.class);
    private static final Map<OMSProvider, Function<?, ? extends OMSClient<?>>> providers = new HashMap<>();

    static {
        registerProvider(MAO, MaoOmsClient::new);
        registerProvider(MOCK, MockOMSClient::new);
    }
    // Register a provider
    static <T, S extends OMSClient<T>> void registerProvider(OMSProvider provider, Function<T, S> creator) {
        providers.put(provider, creator);
    }

    public static <T, S extends OMSClient<T>> S getClient(OMSProvider provider, T config) {
        @SuppressWarnings("unchecked")
        Function<T, S> creator = (Function<T, S>) providers.get(provider);
        if (creator == null) {
            throw new IllegalArgumentException("No provider registered for: " + provider);
        }
        return creator.apply(config);
    }


    public static void main(String[] args) {
        OMSClient<MaoConfig> client = getClient(MAO, new MaoConfig(
                "https://guccs-auth.omni.manh.com",
                "https://omni-guccs.omni.manh.com",
                "omnicomponent.1.0.0",
                "b4s8rgTyg55XYNun",
                "<EMAIL>",
                "Password3=",
                "Gucci-IT,Gucci-US",
                "GCOM"
        ));
        //log.info("" + client.getOrderByTrackingNumber("1ZF4286E8795669930"));
        //log.info("" + client.getOrderById("IT1317019")); //orderId
        log.info("" + client.getOrderById("US1300218")); //return order id
        //log.info("" + client.getOrderByProductSerialNumber("US1300218")); // product serial number
        //log.info("" + client.getOrderByReturnTrackingNumber("US1300218")); // get order based on return tracking number.
    }

}