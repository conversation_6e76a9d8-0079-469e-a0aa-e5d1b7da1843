package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.*;

/**
 * Media
 */
public class Media {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_DATA = "LocalizedData";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_DATA)
  private List<MediaTranslation> localizedData = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MEDIA_ID = "MediaId";
  @SerializedName(SERIALIZED_NAME_MEDIA_ID)
  private String mediaId;

  public static final String SERIALIZED_NAME_MEDIA_SIZE_ID = "MediaSizeId";
  @SerializedName(SERIALIZED_NAME_MEDIA_SIZE_ID)
  private String mediaSizeId;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_MIME_TYPE_ID = "MimeTypeId";
  @SerializedName(SERIALIZED_NAME_MIME_TYPE_ID)
  private String mimeTypeId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_ITEM = "ParentItem";
  @SerializedName(SERIALIZED_NAME_PARENT_ITEM)
  private PrimaryKey parentItem = null;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_SEQUENCE = "Sequence";
  @SerializedName(SERIALIZED_NAME_SEQUENCE)
  private Long sequence;

  public static final String SERIALIZED_NAME_U_R_I = "URI";
  @SerializedName(SERIALIZED_NAME_U_R_I)
  private String URI;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public Media actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public Media putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public Media createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public Media createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public Media extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public Media localizedData(List<MediaTranslation> localizedData) {
    this.localizedData = localizedData;
    return this;
  }

  public Media addLocalizedDataItem(MediaTranslation localizedDataItem) {
    if (this.localizedData == null) {
      this.localizedData = new ArrayList<MediaTranslation>();
    }
    this.localizedData.add(localizedDataItem);
    return this;
  }

   /**
   * Get localizedData
   * @return localizedData
  **/
  public List<MediaTranslation> getLocalizedData() {
    return localizedData;
  }

  public void setLocalizedData(List<MediaTranslation> localizedData) {
    this.localizedData = localizedData;
  }

  public Media localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public Media mediaId(String mediaId) {
    this.mediaId = mediaId;
    return this;
  }

   /**
   * Unique identifier of the media record
   * @return mediaId
  **/
  public String getMediaId() {
    return mediaId;
  }

  public void setMediaId(String mediaId) {
    this.mediaId = mediaId;
  }

  public Media mediaSizeId(String mediaSizeId) {
    this.mediaSizeId = mediaSizeId;
    return this;
  }

   /**
   * Size of the media (small image, thumbnail, high res image, video, etc.)
   * @return mediaSizeId
  **/
  public String getMediaSizeId() {
    return mediaSizeId;
  }

  public void setMediaSizeId(String mediaSizeId) {
    this.mediaSizeId = mediaSizeId;
  }

  public Media messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public Media mimeTypeId(String mimeTypeId) {
    this.mimeTypeId = mimeTypeId;
    return this;
  }

   /**
   * Type of mime (.png, .jpeg, .wav, etc.)
   * @return mimeTypeId
  **/
  public String getMimeTypeId() {
    return mimeTypeId;
  }

  public void setMimeTypeId(String mimeTypeId) {
    this.mimeTypeId = mimeTypeId;
  }

  public Media PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public Media parentItem(PrimaryKey parentItem) {
    this.parentItem = parentItem;
    return this;
  }

   /**
   * Get parentItem
   * @return parentItem
  **/
  public PrimaryKey getParentItem() {
    return parentItem;
  }

  public void setParentItem(PrimaryKey parentItem) {
    this.parentItem = parentItem;
  }

  public Media profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public Media sequence(Long sequence) {
    this.sequence = sequence;
    return this;
  }

   /**
   * Sequence in which media is displayed
   * minimum: 0
   * maximum: -8446744073709551617
   * @return sequence
  **/
  public Long getSequence() {
    return sequence;
  }

  public void setSequence(Long sequence) {
    this.sequence = sequence;
  }

  public Media URI(String URI) {
    this.URI = URI;
    return this;
  }

   /**
   * Localized. URI or file name of the media
   * @return URI
  **/
  public String getURI() {
    return URI;
  }

  public void setURI(String URI) {
    this.URI = URI;
  }

  public Media updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public Media updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public Media entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public Media localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Media media = (Media) o;
    return Objects.equals(this.actions, media.actions) &&
        Objects.equals(this.createdBy, media.createdBy) &&
        Objects.equals(this.createdTimestamp, media.createdTimestamp) &&
        Objects.equals(this.extended, media.extended) &&
        Objects.equals(this.localizedData, media.localizedData) &&
        Objects.equals(this.localizedTo, media.localizedTo) &&
        Objects.equals(this.mediaId, media.mediaId) &&
        Objects.equals(this.mediaSizeId, media.mediaSizeId) &&
        Objects.equals(this.messages, media.messages) &&
        Objects.equals(this.mimeTypeId, media.mimeTypeId) &&
        Objects.equals(this.PK, media.PK) &&
        Objects.equals(this.parentItem, media.parentItem) &&
        Objects.equals(this.profileId, media.profileId) &&
        Objects.equals(this.sequence, media.sequence) &&
        Objects.equals(this.URI, media.URI) &&
        Objects.equals(this.updatedBy, media.updatedBy) &&
        Objects.equals(this.updatedTimestamp, media.updatedTimestamp) &&
        Objects.equals(this.entityName, media.entityName) &&
        Objects.equals(this.localize, media.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, extended, localizedData, localizedTo, mediaId, mediaSizeId, messages, mimeTypeId, PK, parentItem, profileId, sequence, URI, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Media {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedData: ").append(toIndentedString(localizedData)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    mediaId: ").append(toIndentedString(mediaId)).append("\n");
    sb.append("    mediaSizeId: ").append(toIndentedString(mediaSizeId)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    mimeTypeId: ").append(toIndentedString(mimeTypeId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentItem: ").append(toIndentedString(parentItem)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    sequence: ").append(toIndentedString(sequence)).append("\n");
    sb.append("    URI: ").append(toIndentedString(URI)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

