/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.*;

/**
 * Invoice
 */
public class Invoice {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_AMOUNT_PROCESSED = "AmountProcessed";
  @SerializedName(SERIALIZED_NAME_AMOUNT_PROCESSED)
  private BigDecimal amountProcessed;

  public static final String SERIALIZED_NAME_CALCULATED_VALUES = "CalculatedValues";
  @SerializedName(SERIALIZED_NAME_CALCULATED_VALUES)
  private Object calculatedValues = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_CUSTOMER_ID = "CustomerId";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_ID)
  private String customerId;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FAILED_AMOUNT = "FailedAmount";
  @SerializedName(SERIALIZED_NAME_FAILED_AMOUNT)
  private BigDecimal failedAmount;

  public static final String SERIALIZED_NAME_FULFILLMENT_DATE = "FulfillmentDate";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_DATE)
  private OffsetDateTime fulfillmentDate;

  public static final String SERIALIZED_NAME_INVOICE_CHARGE_DETAIL = "InvoiceChargeDetail";
  @SerializedName(SERIALIZED_NAME_INVOICE_CHARGE_DETAIL)
  private List<InvoiceChargeDetail> invoiceChargeDetail = null;

  public static final String SERIALIZED_NAME_INVOICE_ID = "InvoiceId";
  @SerializedName(SERIALIZED_NAME_INVOICE_ID)
  private String invoiceId;

  public static final String SERIALIZED_NAME_INVOICE_LINE = "InvoiceLine";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE)
  private List<InvoiceLine> invoiceLine = null;

  public static final String SERIALIZED_NAME_INVOICE_SUB_TOTAL = "InvoiceSubTotal";
  @SerializedName(SERIALIZED_NAME_INVOICE_SUB_TOTAL)
  private BigDecimal invoiceSubTotal;

  public static final String SERIALIZED_NAME_INVOICE_TAX_DETAIL = "InvoiceTaxDetail";
  @SerializedName(SERIALIZED_NAME_INVOICE_TAX_DETAIL)
  private List<InvoiceTaxDetail> invoiceTaxDetail = null;

  public static final String SERIALIZED_NAME_INVOICE_TOTAL = "InvoiceTotal";
  @SerializedName(SERIALIZED_NAME_INVOICE_TOTAL)
  private BigDecimal invoiceTotal;

  public static final String SERIALIZED_NAME_INVOICE_TYPE = "InvoiceType";
  @SerializedName(SERIALIZED_NAME_INVOICE_TYPE)
  private InvoiceTypeId invoiceType = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PACKAGE_ID = "PackageId";
  @SerializedName(SERIALIZED_NAME_PACKAGE_ID)
  private String packageId;

  public static final String SERIALIZED_NAME_PARENT_ORDER_ID = "ParentOrderId";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER_ID)
  private String parentOrderId;

  public static final String SERIALIZED_NAME_PUBLISH_COUNT = "PublishCount";
  @SerializedName(SERIALIZED_NAME_PUBLISH_COUNT)
  private Long publishCount;

  public static final String SERIALIZED_NAME_PUBLISH_STATUS = "PublishStatus";
  @SerializedName(SERIALIZED_NAME_PUBLISH_STATUS)
  private PublishStatusId publishStatus = null;

  public static final String SERIALIZED_NAME_SELLING_LOCATION_ID = "SellingLocationId";
  @SerializedName(SERIALIZED_NAME_SELLING_LOCATION_ID)
  private String sellingLocationId;

  public static final String SERIALIZED_NAME_STATUS = "Status";
  @SerializedName(SERIALIZED_NAME_STATUS)
  private InvoiceStatusId status = null;

  public static final String SERIALIZED_NAME_TAX_EXEMPT_ID = "TaxExemptId";
  @SerializedName(SERIALIZED_NAME_TAX_EXEMPT_ID)
  private String taxExemptId;

  public static final String SERIALIZED_NAME_TOTAL_CHARGES = "TotalCharges";
  @SerializedName(SERIALIZED_NAME_TOTAL_CHARGES)
  private BigDecimal totalCharges;

  public static final String SERIALIZED_NAME_TOTAL_DISCOUNTS = "TotalDiscounts";
  @SerializedName(SERIALIZED_NAME_TOTAL_DISCOUNTS)
  private BigDecimal totalDiscounts;

  public static final String SERIALIZED_NAME_TOTAL_INFORMATIONAL_TAXES = "TotalInformationalTaxes";
  @SerializedName(SERIALIZED_NAME_TOTAL_INFORMATIONAL_TAXES)
  private BigDecimal totalInformationalTaxes;

  public static final String SERIALIZED_NAME_TOTAL_TAXES = "TotalTaxes";
  @SerializedName(SERIALIZED_NAME_TOTAL_TAXES)
  private BigDecimal totalTaxes;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_INVOICE_CHARGE_DETAI_REQUEST_LIST = "invoiceChargeDetaiRequestList";
  @SerializedName(SERIALIZED_NAME_INVOICE_CHARGE_DETAI_REQUEST_LIST)
  private List<InvoiceChargeDetailRequest> invoiceChargeDetaiRequestList = null;

  public static final String SERIALIZED_NAME_INVOICE_LINE_REQUEST_LIST = "invoiceLineRequestList";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE_REQUEST_LIST)
  private List<InvoiceLineRequest> invoiceLineRequestList = null;

  public static final String SERIALIZED_NAME_INVOICE_TAX_DETAI_REQUEST_LIST = "invoiceTaxDetaiRequestList";
  @SerializedName(SERIALIZED_NAME_INVOICE_TAX_DETAI_REQUEST_LIST)
  private List<InvoiceTaxDetailRequest> invoiceTaxDetaiRequestList = null;

  public static final String SERIALIZED_NAME_INVOICE_TYPE_ID = "invoiceTypeId";
  @SerializedName(SERIALIZED_NAME_INVOICE_TYPE_ID)
  private String invoiceTypeId;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public Invoice actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public Invoice putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

   /**
   * Amount processed for the invoice
   * minimum: 0
   * maximum: 99999999999999.98
   * @return amountProcessed
  **/
  
  public BigDecimal getAmountProcessed() {
    return amountProcessed;
  }

  public Invoice calculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
    return this;
  }

   /**
   * Get calculatedValues
   * @return calculatedValues
  **/
  
  public Object getCalculatedValues() {
    return calculatedValues;
  }

  public void setCalculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
  }

  public Invoice createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public Invoice createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public Invoice customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

   /**
   * Unique ID used to identify the customer
   * @return customerId
  **/
  
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public Invoice extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

   /**
   * Amount for failed payment transactions
   * minimum: 0
   * maximum: 99999999999999.98
   * @return failedAmount
  **/
  
  public BigDecimal getFailedAmount() {
    return failedAmount;
  }

  public Invoice fulfillmentDate(OffsetDateTime fulfillmentDate) {
    this.fulfillmentDate = fulfillmentDate;
    return this;
  }

   /**
   * Date when the items were fulfilled. Populated based on the fulfillment detail fulfillment date. If multiple fulfillment details are invoiced together, then the invoice fulfillment date is set to the latest of all fulfillment detail dates.Included in the invoice tax request and used by the third party tax engine to determine taxability.
   * @return fulfillmentDate
  **/
  
  public OffsetDateTime getFulfillmentDate() {
    return fulfillmentDate;
  }

  public void setFulfillmentDate(OffsetDateTime fulfillmentDate) {
    this.fulfillmentDate = fulfillmentDate;
  }

  public Invoice invoiceChargeDetail(List<InvoiceChargeDetail> invoiceChargeDetail) {
    this.invoiceChargeDetail = invoiceChargeDetail;
    return this;
  }

  public Invoice addInvoiceChargeDetailItem(InvoiceChargeDetail invoiceChargeDetailItem) {
    if (this.invoiceChargeDetail == null) {
      this.invoiceChargeDetail = new ArrayList<InvoiceChargeDetail>();
    }
    this.invoiceChargeDetail.add(invoiceChargeDetailItem);
    return this;
  }

   /**
   * Get invoiceChargeDetail
   * @return invoiceChargeDetail
  **/
  
  public List<InvoiceChargeDetail> getInvoiceChargeDetail() {
    return invoiceChargeDetail;
  }

  public void setInvoiceChargeDetail(List<InvoiceChargeDetail> invoiceChargeDetail) {
    this.invoiceChargeDetail = invoiceChargeDetail;
  }

  public Invoice invoiceId(String invoiceId) {
    this.invoiceId = invoiceId;
    return this;
  }

   /**
   * Unique identifier of the invoice, as defined by external system. If invoiceId is not provided on import, the system generates a value.
   * @return invoiceId
  **/
  
  public String getInvoiceId() {
    return invoiceId;
  }

  public void setInvoiceId(String invoiceId) {
    this.invoiceId = invoiceId;
  }

  public Invoice invoiceLine(List<InvoiceLine> invoiceLine) {
    this.invoiceLine = invoiceLine;
    return this;
  }

  public Invoice addInvoiceLineItem(InvoiceLine invoiceLineItem) {
    if (this.invoiceLine == null) {
      this.invoiceLine = new ArrayList<InvoiceLine>();
    }
    this.invoiceLine.add(invoiceLineItem);
    return this;
  }

   /**
   * Get invoiceLine
   * @return invoiceLine
  **/
  
  public List<InvoiceLine> getInvoiceLine() {
    return invoiceLine;
  }

  public void setInvoiceLine(List<InvoiceLine> invoiceLine) {
    this.invoiceLine = invoiceLine;
  }

  public Invoice invoiceSubTotal(BigDecimal invoiceSubTotal) {
    this.invoiceSubTotal = invoiceSubTotal;
    return this;
  }

   /**
   * Sum of item price times quantity for all invoice lines
   * minimum: 0
   * maximum: 99999999999999.98
   * @return invoiceSubTotal
  **/
  
  public BigDecimal getInvoiceSubTotal() {
    return invoiceSubTotal;
  }

  public void setInvoiceSubTotal(BigDecimal invoiceSubTotal) {
    this.invoiceSubTotal = invoiceSubTotal;
  }

  public Invoice invoiceTaxDetail(List<InvoiceTaxDetail> invoiceTaxDetail) {
    this.invoiceTaxDetail = invoiceTaxDetail;
    return this;
  }

  public Invoice addInvoiceTaxDetailItem(InvoiceTaxDetail invoiceTaxDetailItem) {
    if (this.invoiceTaxDetail == null) {
      this.invoiceTaxDetail = new ArrayList<InvoiceTaxDetail>();
    }
    this.invoiceTaxDetail.add(invoiceTaxDetailItem);
    return this;
  }

   /**
   * Get invoiceTaxDetail
   * @return invoiceTaxDetail
  **/
  
  public List<InvoiceTaxDetail> getInvoiceTaxDetail() {
    return invoiceTaxDetail;
  }

  public void setInvoiceTaxDetail(List<InvoiceTaxDetail> invoiceTaxDetail) {
    this.invoiceTaxDetail = invoiceTaxDetail;
  }

  public Invoice invoiceTotal(BigDecimal invoiceTotal) {
    this.invoiceTotal = invoiceTotal;
    return this;
  }

   /**
   * Sum of invoice lines and header-level discounts, charges, and taxes
   * minimum: 0
   * maximum: 99999999999999.98
   * @return invoiceTotal
  **/
  
  public BigDecimal getInvoiceTotal() {
    return invoiceTotal;
  }

  public void setInvoiceTotal(BigDecimal invoiceTotal) {
    this.invoiceTotal = invoiceTotal;
  }

  public Invoice invoiceType(InvoiceTypeId invoiceType) {
    this.invoiceType = invoiceType;
    return this;
  }

   /**
   * Get invoiceType
   * @return invoiceType
  **/
  
  public InvoiceTypeId getInvoiceType() {
    return invoiceType;
  }

  public void setInvoiceType(InvoiceTypeId invoiceType) {
    this.invoiceType = invoiceType;
  }

  public Invoice localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public Invoice messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public Invoice orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public Invoice PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public Invoice packageId(String packageId) {
    this.packageId = packageId;
    return this;
  }

   /**
   * Package associated with the invoice
   * @return packageId
  **/
  
  public String getPackageId() {
    return packageId;
  }

  public void setPackageId(String packageId) {
    this.packageId = packageId;
  }

  public Invoice parentOrderId(String parentOrderId) {
    this.parentOrderId = parentOrderId;
    return this;
  }

   /**
   * Populated only for return invoices, and used by the payment component to transfer credit from the parent order to the return order. For example, a customer purchases an item in order A and then returns the item in order B, then order B has a return invoice with parent order id &#39;A&#39;. When the payment component is transferring credit for the value of the return invoice, the credit is pulled from order A and applied to order B to be used as a refund or for exchange items.
   * @return parentOrderId
  **/
  
  public String getParentOrderId() {
    return parentOrderId;
  }

  public void setParentOrderId(String parentOrderId) {
    this.parentOrderId = parentOrderId;
  }

   /**
   * Indicates the number of times an Invoice has been published as Sales Posting Message.
   * minimum: 0
   * maximum: -8446744073709551617
   * @return publishCount
  **/
  
  public Long getPublishCount() {
    return publishCount;
  }

  public Invoice publishStatus(PublishStatusId publishStatus) {
    this.publishStatus = publishStatus;
    return this;
  }

   /**
   * Get publishStatus
   * @return publishStatus
  **/
  
  public PublishStatusId getPublishStatus() {
    return publishStatus;
  }

  public void setPublishStatus(PublishStatusId publishStatus) {
    this.publishStatus = publishStatus;
  }

  public Invoice sellingLocationId(String sellingLocationId) {
    this.sellingLocationId = sellingLocationId;
    return this;
  }

   /**
   * Specific store location ID or website where transaction was captured
   * @return sellingLocationId
  **/
  
  public String getSellingLocationId() {
    return sellingLocationId;
  }

  public void setSellingLocationId(String sellingLocationId) {
    this.sellingLocationId = sellingLocationId;
  }

  public Invoice status(InvoiceStatusId status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  
  public InvoiceStatusId getStatus() {
    return status;
  }

  public void setStatus(InvoiceStatusId status) {
    this.status = status;
  }

  public Invoice taxExemptId(String taxExemptId) {
    this.taxExemptId = taxExemptId;
    return this;
  }

   /**
   * Tax exemption ID
   * @return taxExemptId
  **/
  
  public String getTaxExemptId() {
    return taxExemptId;
  }

  public void setTaxExemptId(String taxExemptId) {
    this.taxExemptId = taxExemptId;
  }

  public Invoice totalCharges(BigDecimal totalCharges) {
    this.totalCharges = totalCharges;
    return this;
  }

   /**
   * Sum of charges for the invoice header
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalCharges
  **/
  
  public BigDecimal getTotalCharges() {
    return totalCharges;
  }

  public void setTotalCharges(BigDecimal totalCharges) {
    this.totalCharges = totalCharges;
  }

  public Invoice totalDiscounts(BigDecimal totalDiscounts) {
    this.totalDiscounts = totalDiscounts;
    return this;
  }

   /**
   * Sum of discounts for the invoice header
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalDiscounts
  **/
  
  public BigDecimal getTotalDiscounts() {
    return totalDiscounts;
  }

  public void setTotalDiscounts(BigDecimal totalDiscounts) {
    this.totalDiscounts = totalDiscounts;
  }

  public Invoice totalInformationalTaxes(BigDecimal totalInformationalTaxes) {
    this.totalInformationalTaxes = totalInformationalTaxes;
    return this;
  }

   /**
   * Get totalInformationalTaxes
   * @return totalInformationalTaxes
  **/
  
  public BigDecimal getTotalInformationalTaxes() {
    return totalInformationalTaxes;
  }

  public void setTotalInformationalTaxes(BigDecimal totalInformationalTaxes) {
    this.totalInformationalTaxes = totalInformationalTaxes;
  }

  public Invoice totalTaxes(BigDecimal totalTaxes) {
    this.totalTaxes = totalTaxes;
    return this;
  }

   /**
   * Sum of taxes for the invoice header
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalTaxes
  **/
  
  public BigDecimal getTotalTaxes() {
    return totalTaxes;
  }

  public void setTotalTaxes(BigDecimal totalTaxes) {
    this.totalTaxes = totalTaxes;
  }

  public Invoice updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public Invoice updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public Invoice entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public Invoice invoiceChargeDetaiRequestList(List<InvoiceChargeDetailRequest> invoiceChargeDetaiRequestList) {
    this.invoiceChargeDetaiRequestList = invoiceChargeDetaiRequestList;
    return this;
  }

  public Invoice addInvoiceChargeDetaiRequestListItem(InvoiceChargeDetailRequest invoiceChargeDetaiRequestListItem) {
    if (this.invoiceChargeDetaiRequestList == null) {
      this.invoiceChargeDetaiRequestList = new ArrayList<InvoiceChargeDetailRequest>();
    }
    this.invoiceChargeDetaiRequestList.add(invoiceChargeDetaiRequestListItem);
    return this;
  }

   /**
   * Get invoiceChargeDetaiRequestList
   * @return invoiceChargeDetaiRequestList
  **/
  
  public List<InvoiceChargeDetailRequest> getInvoiceChargeDetaiRequestList() {
    return invoiceChargeDetaiRequestList;
  }

  public void setInvoiceChargeDetaiRequestList(List<InvoiceChargeDetailRequest> invoiceChargeDetaiRequestList) {
    this.invoiceChargeDetaiRequestList = invoiceChargeDetaiRequestList;
  }

  public Invoice invoiceLineRequestList(List<InvoiceLineRequest> invoiceLineRequestList) {
    this.invoiceLineRequestList = invoiceLineRequestList;
    return this;
  }

  public Invoice addInvoiceLineRequestListItem(InvoiceLineRequest invoiceLineRequestListItem) {
    if (this.invoiceLineRequestList == null) {
      this.invoiceLineRequestList = new ArrayList<InvoiceLineRequest>();
    }
    this.invoiceLineRequestList.add(invoiceLineRequestListItem);
    return this;
  }

   /**
   * Get invoiceLineRequestList
   * @return invoiceLineRequestList
  **/
  
  public List<InvoiceLineRequest> getInvoiceLineRequestList() {
    return invoiceLineRequestList;
  }

  public void setInvoiceLineRequestList(List<InvoiceLineRequest> invoiceLineRequestList) {
    this.invoiceLineRequestList = invoiceLineRequestList;
  }

  public Invoice invoiceTaxDetaiRequestList(List<InvoiceTaxDetailRequest> invoiceTaxDetaiRequestList) {
    this.invoiceTaxDetaiRequestList = invoiceTaxDetaiRequestList;
    return this;
  }

  public Invoice addInvoiceTaxDetaiRequestListItem(InvoiceTaxDetailRequest invoiceTaxDetaiRequestListItem) {
    if (this.invoiceTaxDetaiRequestList == null) {
      this.invoiceTaxDetaiRequestList = new ArrayList<InvoiceTaxDetailRequest>();
    }
    this.invoiceTaxDetaiRequestList.add(invoiceTaxDetaiRequestListItem);
    return this;
  }

   /**
   * Get invoiceTaxDetaiRequestList
   * @return invoiceTaxDetaiRequestList
  **/
  
  public List<InvoiceTaxDetailRequest> getInvoiceTaxDetaiRequestList() {
    return invoiceTaxDetaiRequestList;
  }

  public void setInvoiceTaxDetaiRequestList(List<InvoiceTaxDetailRequest> invoiceTaxDetaiRequestList) {
    this.invoiceTaxDetaiRequestList = invoiceTaxDetaiRequestList;
  }

  public Invoice invoiceTypeId(String invoiceTypeId) {
    this.invoiceTypeId = invoiceTypeId;
    return this;
  }

   /**
   * Get invoiceTypeId
   * @return invoiceTypeId
  **/
  
  public String getInvoiceTypeId() {
    return invoiceTypeId;
  }

  public void setInvoiceTypeId(String invoiceTypeId) {
    this.invoiceTypeId = invoiceTypeId;
  }

  public Invoice localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Invoice invoice = (Invoice) o;
    return Objects.equals(this.actions, invoice.actions) &&
        Objects.equals(this.amountProcessed, invoice.amountProcessed) &&
        Objects.equals(this.calculatedValues, invoice.calculatedValues) &&
        Objects.equals(this.createdBy, invoice.createdBy) &&
        Objects.equals(this.createdTimestamp, invoice.createdTimestamp) &&
        Objects.equals(this.customerId, invoice.customerId) &&
        Objects.equals(this.extended, invoice.extended) &&
        Objects.equals(this.failedAmount, invoice.failedAmount) &&
        Objects.equals(this.fulfillmentDate, invoice.fulfillmentDate) &&
        Objects.equals(this.invoiceChargeDetail, invoice.invoiceChargeDetail) &&
        Objects.equals(this.invoiceId, invoice.invoiceId) &&
        Objects.equals(this.invoiceLine, invoice.invoiceLine) &&
        Objects.equals(this.invoiceSubTotal, invoice.invoiceSubTotal) &&
        Objects.equals(this.invoiceTaxDetail, invoice.invoiceTaxDetail) &&
        Objects.equals(this.invoiceTotal, invoice.invoiceTotal) &&
        Objects.equals(this.invoiceType, invoice.invoiceType) &&
        Objects.equals(this.localizedTo, invoice.localizedTo) &&
        Objects.equals(this.messages, invoice.messages) &&
        Objects.equals(this.orgId, invoice.orgId) &&
        Objects.equals(this.PK, invoice.PK) &&
        Objects.equals(this.packageId, invoice.packageId) &&
        Objects.equals(this.parentOrderId, invoice.parentOrderId) &&
        Objects.equals(this.publishCount, invoice.publishCount) &&
        Objects.equals(this.publishStatus, invoice.publishStatus) &&
        Objects.equals(this.sellingLocationId, invoice.sellingLocationId) &&
        Objects.equals(this.status, invoice.status) &&
        Objects.equals(this.taxExemptId, invoice.taxExemptId) &&
        Objects.equals(this.totalCharges, invoice.totalCharges) &&
        Objects.equals(this.totalDiscounts, invoice.totalDiscounts) &&
        Objects.equals(this.totalInformationalTaxes, invoice.totalInformationalTaxes) &&
        Objects.equals(this.totalTaxes, invoice.totalTaxes) &&
        Objects.equals(this.updatedBy, invoice.updatedBy) &&
        Objects.equals(this.updatedTimestamp, invoice.updatedTimestamp) &&
        Objects.equals(this.entityName, invoice.entityName) &&
        Objects.equals(this.invoiceChargeDetaiRequestList, invoice.invoiceChargeDetaiRequestList) &&
        Objects.equals(this.invoiceLineRequestList, invoice.invoiceLineRequestList) &&
        Objects.equals(this.invoiceTaxDetaiRequestList, invoice.invoiceTaxDetaiRequestList) &&
        Objects.equals(this.invoiceTypeId, invoice.invoiceTypeId) &&
        Objects.equals(this.localize, invoice.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, amountProcessed, calculatedValues, createdBy, createdTimestamp, customerId, extended, failedAmount, fulfillmentDate, invoiceChargeDetail, invoiceId, invoiceLine, invoiceSubTotal, invoiceTaxDetail, invoiceTotal, invoiceType, localizedTo, messages, orgId, PK, packageId, parentOrderId, publishCount, publishStatus, sellingLocationId, status, taxExemptId, totalCharges, totalDiscounts, totalInformationalTaxes, totalTaxes, updatedBy, updatedTimestamp, entityName, invoiceChargeDetaiRequestList, invoiceLineRequestList, invoiceTaxDetaiRequestList, invoiceTypeId, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Invoice {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    amountProcessed: ").append(toIndentedString(amountProcessed)).append("\n");
    sb.append("    calculatedValues: ").append(toIndentedString(calculatedValues)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    failedAmount: ").append(toIndentedString(failedAmount)).append("\n");
    sb.append("    fulfillmentDate: ").append(toIndentedString(fulfillmentDate)).append("\n");
    sb.append("    invoiceChargeDetail: ").append(toIndentedString(invoiceChargeDetail)).append("\n");
    sb.append("    invoiceId: ").append(toIndentedString(invoiceId)).append("\n");
    sb.append("    invoiceLine: ").append(toIndentedString(invoiceLine)).append("\n");
    sb.append("    invoiceSubTotal: ").append(toIndentedString(invoiceSubTotal)).append("\n");
    sb.append("    invoiceTaxDetail: ").append(toIndentedString(invoiceTaxDetail)).append("\n");
    sb.append("    invoiceTotal: ").append(toIndentedString(invoiceTotal)).append("\n");
    sb.append("    invoiceType: ").append(toIndentedString(invoiceType)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    packageId: ").append(toIndentedString(packageId)).append("\n");
    sb.append("    parentOrderId: ").append(toIndentedString(parentOrderId)).append("\n");
    sb.append("    publishCount: ").append(toIndentedString(publishCount)).append("\n");
    sb.append("    publishStatus: ").append(toIndentedString(publishStatus)).append("\n");
    sb.append("    sellingLocationId: ").append(toIndentedString(sellingLocationId)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    taxExemptId: ").append(toIndentedString(taxExemptId)).append("\n");
    sb.append("    totalCharges: ").append(toIndentedString(totalCharges)).append("\n");
    sb.append("    totalDiscounts: ").append(toIndentedString(totalDiscounts)).append("\n");
    sb.append("    totalInformationalTaxes: ").append(toIndentedString(totalInformationalTaxes)).append("\n");
    sb.append("    totalTaxes: ").append(toIndentedString(totalTaxes)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    invoiceChargeDetaiRequestList: ").append(toIndentedString(invoiceChargeDetaiRequestList)).append("\n");
    sb.append("    invoiceLineRequestList: ").append(toIndentedString(invoiceLineRequestList)).append("\n");
    sb.append("    invoiceTaxDetaiRequestList: ").append(toIndentedString(invoiceTaxDetaiRequestList)).append("\n");
    sb.append("    invoiceTypeId: ").append(toIndentedString(invoiceTypeId)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

