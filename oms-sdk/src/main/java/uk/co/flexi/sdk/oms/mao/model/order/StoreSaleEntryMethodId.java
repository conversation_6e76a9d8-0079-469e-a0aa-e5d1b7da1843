/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * StoreSaleEntryMethodId
 */
public class StoreSaleEntryMethodId {
  public static final String SERIALIZED_NAME_STORE_SALE_ENTRY_METHOD_ID = "StoreSaleEntryMethodId";
  @SerializedName(SERIALIZED_NAME_STORE_SALE_ENTRY_METHOD_ID)
  private String storeSaleEntryMethodId;

  public StoreSaleEntryMethodId storeSaleEntryMethodId(String storeSaleEntryMethodId) {
    this.storeSaleEntryMethodId = storeSaleEntryMethodId;
    return this;
  }

   /**
   * Indicates how item is added to the order in astore
   * @return storeSaleEntryMethodId
  **/
  
  public String getStoreSaleEntryMethodId() {
    return storeSaleEntryMethodId;
  }

  public void setStoreSaleEntryMethodId(String storeSaleEntryMethodId) {
    this.storeSaleEntryMethodId = storeSaleEntryMethodId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    StoreSaleEntryMethodId storeSaleEntryMethodId = (StoreSaleEntryMethodId) o;
    return Objects.equals(this.storeSaleEntryMethodId, storeSaleEntryMethodId.storeSaleEntryMethodId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(storeSaleEntryMethodId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class StoreSaleEntryMethodId {\n");
    
    sb.append("    storeSaleEntryMethodId: ").append(toIndentedString(storeSaleEntryMethodId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

