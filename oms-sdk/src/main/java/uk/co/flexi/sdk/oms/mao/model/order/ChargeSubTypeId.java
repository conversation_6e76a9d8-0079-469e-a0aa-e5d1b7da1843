/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * ChargeSubTypeId
 */
public class ChargeSubTypeId {
  public static final String SERIALIZED_NAME_CHARGE_SUB_TYPE_ID = "ChargeSubTypeId";
  @SerializedName(SERIALIZED_NAME_CHARGE_SUB_TYPE_ID)
  private String chargeSubTypeId;

  public ChargeSubTypeId chargeSubTypeId(String chargeSubTypeId) {
    this.chargeSubTypeId = chargeSubTypeId;
    return this;
  }

   /**
   * The unique identifier of the ChargeSubType e.g. Offer.
   * @return chargeSubTypeId
  **/
  
  public String getChargeSubTypeId() {
    return chargeSubTypeId;
  }

  public void setChargeSubTypeId(String chargeSubTypeId) {
    this.chargeSubTypeId = chargeSubTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChargeSubTypeId chargeSubTypeId = (ChargeSubTypeId) o;
    return Objects.equals(this.chargeSubTypeId, chargeSubTypeId.chargeSubTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(chargeSubTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChargeSubTypeId {\n");
    
    sb.append("    chargeSubTypeId: ").append(toIndentedString(chargeSubTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

