/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * Property
 */
public class Property {
  public static final String SERIALIZED_NAME_NEW = "New";
  @SerializedName(SERIALIZED_NAME_NEW)
  private String _new;

  public static final String SERIALIZED_NAME_OLD = "Old";
  @SerializedName(SERIALIZED_NAME_OLD)
  private String old;

  public static final String SERIALIZED_NAME_PROPERTY = "Property";
  @SerializedName(SERIALIZED_NAME_PROPERTY)
  private String property;

  public Property _new(String _new) {
    this._new = _new;
    return this;
  }

   /**
   * Get _new
   * @return _new
  **/
  
  public String getNew() {
    return _new;
  }

  public void setNew(String _new) {
    this._new = _new;
  }

  public Property old(String old) {
    this.old = old;
    return this;
  }

   /**
   * Get old
   * @return old
  **/
  
  public String getOld() {
    return old;
  }

  public void setOld(String old) {
    this.old = old;
  }

  public Property property(String property) {
    this.property = property;
    return this;
  }

   /**
   * Get property
   * @return property
  **/
  
  public String getProperty() {
    return property;
  }

  public void setProperty(String property) {
    this.property = property;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Property property = (Property) o;
    return Objects.equals(this._new, property._new) &&
        Objects.equals(this.old, property.old) &&
        Objects.equals(this.property, property.property);
  }

  @Override
  public int hashCode() {
    return Objects.hash(_new, old, property);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Property {\n");
    
    sb.append("    _new: ").append(toIndentedString(_new)).append("\n");
    sb.append("    old: ").append(toIndentedString(old)).append("\n");
    sb.append("    property: ").append(toIndentedString(property)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

