/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ReturnDetail
 */
public class ReturnDetail {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IS_GIFT_RETURN = "IsGiftReturn";
  @SerializedName(SERIALIZED_NAME_IS_GIFT_RETURN)
  private Boolean isGiftReturn;

  public static final String SERIALIZED_NAME_ITEM_ID = "ItemId";
  @SerializedName(SERIALIZED_NAME_ITEM_ID)
  private String itemId;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_LOCATION_ID = "LocationId";
  @SerializedName(SERIALIZED_NAME_LOCATION_ID)
  private String locationId;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_QUANTITY = "Quantity";
  @SerializedName(SERIALIZED_NAME_QUANTITY)
  private Double quantity;

  public static final String SERIALIZED_NAME_RECEIVED_ITEM_CONDITION = "ReceivedItemCondition";
  @SerializedName(SERIALIZED_NAME_RECEIVED_ITEM_CONDITION)
  private ItemConditionId receivedItemCondition = null;

  public static final String SERIALIZED_NAME_RETURN_DETAIL_ID = "ReturnDetailId";
  @SerializedName(SERIALIZED_NAME_RETURN_DETAIL_ID)
  private String returnDetailId;

  public static final String SERIALIZED_NAME_RETURN_ORDER_ID = "ReturnOrderId";
  @SerializedName(SERIALIZED_NAME_RETURN_ORDER_ID)
  private String returnOrderId;

  public static final String SERIALIZED_NAME_RETURN_ORDER_LINE_ID = "ReturnOrderLineId";
  @SerializedName(SERIALIZED_NAME_RETURN_ORDER_LINE_ID)
  private String returnOrderLineId;

  public static final String SERIALIZED_NAME_RETURN_TYPE = "ReturnType";
  @SerializedName(SERIALIZED_NAME_RETURN_TYPE)
  private ReturnTypeId returnType = null;

  public static final String SERIALIZED_NAME_U_O_M = "UOM";
  @SerializedName(SERIALIZED_NAME_U_O_M)
  private String UOM;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public ReturnDetail actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ReturnDetail putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ReturnDetail createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ReturnDetail createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ReturnDetail extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ReturnDetail isGiftReturn(Boolean isGiftReturn) {
    this.isGiftReturn = isGiftReturn;
    return this;
  }

   /**
   * Indicates that the item is being returned by a gift recipient. Used to determine refund payment methods. If a return is a gift, then a refund is issued via new gift card. Otherwise, a refund is issued against the original tender, per the default refund logic.
   * @return isGiftReturn
  **/
  
  public Boolean getIsGiftReturn() {
    return isGiftReturn;
  }

  public void setIsGiftReturn(Boolean isGiftReturn) {
    this.isGiftReturn = isGiftReturn;
  }

  public ReturnDetail itemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

   /**
   * Item received. Used in return variance analysis. Compared with the expected item (if captured), and used to flag a return line as having an item variance.
   * @return itemId
  **/
  
  public String getItemId() {
    return itemId;
  }

  public void setItemId(String itemId) {
    this.itemId = itemId;
  }

  public ReturnDetail localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public ReturnDetail locationId(String locationId) {
    this.locationId = locationId;
    return this;
  }

   /**
   * Location at which the return items are being received. Used for traceability and troubleshooting.
   * @return locationId
  **/
  
  public String getLocationId() {
    return locationId;
  }

  public void setLocationId(String locationId) {
    this.locationId = locationId;
  }

  public ReturnDetail messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ReturnDetail orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public ReturnDetail PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ReturnDetail quantity(Double quantity) {
    this.quantity = quantity;
    return this;
  }

   /**
   * Quantity received. Used in return variance analysis. Compared with the expected quantity (if captured), and used to flag a return line as having a quantity variance.
   * minimum: 0
   * maximum: 999999999999.9999
   * @return quantity
  **/
  
  public Double getQuantity() {
    return quantity;
  }

  public void setQuantity(Double quantity) {
    this.quantity = quantity;
  }

  public ReturnDetail receivedItemCondition(ItemConditionId receivedItemCondition) {
    this.receivedItemCondition = receivedItemCondition;
    return this;
  }

   /**
   * Get receivedItemCondition
   * @return receivedItemCondition
  **/
  
  public ItemConditionId getReceivedItemCondition() {
    return receivedItemCondition;
  }

  public void setReceivedItemCondition(ItemConditionId receivedItemCondition) {
    this.receivedItemCondition = receivedItemCondition;
  }

  public ReturnDetail returnDetailId(String returnDetailId) {
    this.returnDetailId = returnDetailId;
    return this;
  }

   /**
   * Unique identifier of the return detail entity. If not provided, then this is a generated number.
   * @return returnDetailId
  **/
  
  public String getReturnDetailId() {
    return returnDetailId;
  }

  public void setReturnDetailId(String returnDetailId) {
    this.returnDetailId = returnDetailId;
  }

  public ReturnDetail returnOrderId(String returnOrderId) {
    this.returnOrderId = returnOrderId;
    return this;
  }

   /**
   * For Receipt events for call center returns, this identifies the return order for which the receipt occurred. For Receipt events for automated or blind returns where no return order exists, this identifies the original order against which the items are being returned. When a receipt occurs and no return order exists, a return order is created against the original order.
   * @return returnOrderId
  **/
  
  public String getReturnOrderId() {
    return returnOrderId;
  }

  public void setReturnOrderId(String returnOrderId) {
    this.returnOrderId = returnOrderId;
  }

  public ReturnDetail returnOrderLineId(String returnOrderLineId) {
    this.returnOrderLineId = returnOrderLineId;
    return this;
  }

   /**
   * For Receipt events for call center returns, this identifies the return order line for which the receipt occurred. For Receipt events for automated or blind returns where no return order exists, this identifies the original order against which the items are being returned. When a receipt occurs and no return order exists, a return order is created against the original order.
   * @return returnOrderLineId
  **/
  
  public String getReturnOrderLineId() {
    return returnOrderLineId;
  }

  public void setReturnOrderLineId(String returnOrderLineId) {
    this.returnOrderLineId = returnOrderLineId;
  }

  public ReturnDetail returnType(ReturnTypeId returnType) {
    this.returnType = returnType;
    return this;
  }

   /**
   * Get returnType
   * @return returnType
  **/
  
  public ReturnTypeId getReturnType() {
    return returnType;
  }

  public void setReturnType(ReturnTypeId returnType) {
    this.returnType = returnType;
  }

  public ReturnDetail UOM(String UOM) {
    this.UOM = UOM;
    return this;
  }

   /**
   * Unit of measure of the quantity. Used for traceability.
   * @return UOM
  **/
  
  public String getUOM() {
    return UOM;
  }

  public void setUOM(String UOM) {
    this.UOM = UOM;
  }

  public ReturnDetail updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ReturnDetail updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ReturnDetail entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public ReturnDetail localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReturnDetail returnDetail = (ReturnDetail) o;
    return Objects.equals(this.actions, returnDetail.actions) &&
        Objects.equals(this.createdBy, returnDetail.createdBy) &&
        Objects.equals(this.createdTimestamp, returnDetail.createdTimestamp) &&
        Objects.equals(this.extended, returnDetail.extended) &&
        Objects.equals(this.isGiftReturn, returnDetail.isGiftReturn) &&
        Objects.equals(this.itemId, returnDetail.itemId) &&
        Objects.equals(this.localizedTo, returnDetail.localizedTo) &&
        Objects.equals(this.locationId, returnDetail.locationId) &&
        Objects.equals(this.messages, returnDetail.messages) &&
        Objects.equals(this.orgId, returnDetail.orgId) &&
        Objects.equals(this.PK, returnDetail.PK) &&
        Objects.equals(this.quantity, returnDetail.quantity) &&
        Objects.equals(this.receivedItemCondition, returnDetail.receivedItemCondition) &&
        Objects.equals(this.returnDetailId, returnDetail.returnDetailId) &&
        Objects.equals(this.returnOrderId, returnDetail.returnOrderId) &&
        Objects.equals(this.returnOrderLineId, returnDetail.returnOrderLineId) &&
        Objects.equals(this.returnType, returnDetail.returnType) &&
        Objects.equals(this.UOM, returnDetail.UOM) &&
        Objects.equals(this.updatedBy, returnDetail.updatedBy) &&
        Objects.equals(this.updatedTimestamp, returnDetail.updatedTimestamp) &&
        Objects.equals(this.entityName, returnDetail.entityName) &&
        Objects.equals(this.localize, returnDetail.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, extended, isGiftReturn, itemId, localizedTo, locationId, messages, orgId, PK, quantity, receivedItemCondition, returnDetailId, returnOrderId, returnOrderLineId, returnType, UOM, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReturnDetail {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    isGiftReturn: ").append(toIndentedString(isGiftReturn)).append("\n");
    sb.append("    itemId: ").append(toIndentedString(itemId)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    locationId: ").append(toIndentedString(locationId)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    receivedItemCondition: ").append(toIndentedString(receivedItemCondition)).append("\n");
    sb.append("    returnDetailId: ").append(toIndentedString(returnDetailId)).append("\n");
    sb.append("    returnOrderId: ").append(toIndentedString(returnOrderId)).append("\n");
    sb.append("    returnOrderLineId: ").append(toIndentedString(returnOrderLineId)).append("\n");
    sb.append("    returnType: ").append(toIndentedString(returnType)).append("\n");
    sb.append("    UOM: ").append(toIndentedString(UOM)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

