/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * ReturnTypeId
 */
public class ReturnTypeId {
  public static final String SERIALIZED_NAME_RETURN_TYPE_ID = "ReturnTypeId";
  @SerializedName(SERIALIZED_NAME_RETURN_TYPE_ID)
  private String returnTypeId;

  public ReturnTypeId returnTypeId(String returnTypeId) {
    this.returnTypeId = returnTypeId;
    return this;
  }

   /**
   * Indicates whether the item is part of an even exchange, uneven exchange, or pure refund. If a return line exists with no sale lines, then the return action is refund. If a return line and an even exchange line exist with the same parent line id, then the return action is even exchange. If a return line and sale lines exist, but no even exchanges exist with the same parent line id, then the return action is uneven exchange.
   * @return returnTypeId
  **/
  
  public String getReturnTypeId() {
    return returnTypeId;
  }

  public void setReturnTypeId(String returnTypeId) {
    this.returnTypeId = returnTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReturnTypeId returnTypeId = (ReturnTypeId) o;
    return Objects.equals(this.returnTypeId, returnTypeId.returnTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(returnTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReturnTypeId {\n");
    
    sb.append("    returnTypeId: ").append(toIndentedString(returnTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

