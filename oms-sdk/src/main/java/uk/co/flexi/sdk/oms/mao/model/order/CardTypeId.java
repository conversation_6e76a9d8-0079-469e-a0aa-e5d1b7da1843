/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * CardTypeId
 */
public class CardTypeId {
  public static final String SERIALIZED_NAME_CARD_TYPE_ID = "CardTypeId";
  @SerializedName(SERIALIZED_NAME_CARD_TYPE_ID)
  private String cardTypeId;

  public CardTypeId cardTypeId(String cardTypeId) {
    this.cardTypeId = cardTypeId;
    return this;
  }

   /**
   * Identifier of the card type, to be saved on the payment method of a credit card payment
   * @return cardTypeId
  **/
  
  public String getCardTypeId() {
    return cardTypeId;
  }

  public void setCardTypeId(String cardTypeId) {
    this.cardTypeId = cardTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CardTypeId cardTypeId = (CardTypeId) o;
    return Objects.equals(this.cardTypeId, cardTypeId.cardTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(cardTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CardTypeId {\n");
    
    sb.append("    cardTypeId: ").append(toIndentedString(cardTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

