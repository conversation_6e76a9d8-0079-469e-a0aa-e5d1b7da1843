package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * InventoryProtection
 */
public class InventoryProtection {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DC_PROTECTION1 = "DCProtection1";
  @SerializedName(SERIALIZED_NAME_DC_PROTECTION1)
  private Double dcProtection1;

  public static final String SERIALIZED_NAME_DC_PROTECTION2 = "DCProtection2";
  @SerializedName(SERIALIZED_NAME_DC_PROTECTION2)
  private Double dcProtection2;

  public static final String SERIALIZED_NAME_DC_PROTECTION3 = "DCProtection3";
  @SerializedName(SERIALIZED_NAME_DC_PROTECTION3)
  private Double dcProtection3;

  public static final String SERIALIZED_NAME_DC_PROTECTION4 = "DCProtection4";
  @SerializedName(SERIALIZED_NAME_DC_PROTECTION4)
  private Double dcProtection4;

  public static final String SERIALIZED_NAME_DC_PROTECTION5 = "DCProtection5";
  @SerializedName(SERIALIZED_NAME_DC_PROTECTION5)
  private Double dcProtection5;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_NETWORK_PROTECTION1 = "NetworkProtection1";
  @SerializedName(SERIALIZED_NAME_NETWORK_PROTECTION1)
  private Double networkProtection1;

  public static final String SERIALIZED_NAME_NETWORK_PROTECTION2 = "NetworkProtection2";
  @SerializedName(SERIALIZED_NAME_NETWORK_PROTECTION2)
  private Double networkProtection2;

  public static final String SERIALIZED_NAME_NETWORK_PROTECTION3 = "NetworkProtection3";
  @SerializedName(SERIALIZED_NAME_NETWORK_PROTECTION3)
  private Double networkProtection3;

  public static final String SERIALIZED_NAME_NETWORK_PROTECTION4 = "NetworkProtection4";
  @SerializedName(SERIALIZED_NAME_NETWORK_PROTECTION4)
  private Double networkProtection4;

  public static final String SERIALIZED_NAME_NETWORK_PROTECTION5 = "NetworkProtection5";
  @SerializedName(SERIALIZED_NAME_NETWORK_PROTECTION5)
  private Double networkProtection5;

  public static final String SERIALIZED_NAME_OTHER_PROTECTION1 = "OtherProtection1";
  @SerializedName(SERIALIZED_NAME_OTHER_PROTECTION1)
  private Double otherProtection1;

  public static final String SERIALIZED_NAME_OTHER_PROTECTION2 = "OtherProtection2";
  @SerializedName(SERIALIZED_NAME_OTHER_PROTECTION2)
  private Double otherProtection2;

  public static final String SERIALIZED_NAME_OTHER_PROTECTION3 = "OtherProtection3";
  @SerializedName(SERIALIZED_NAME_OTHER_PROTECTION3)
  private Double otherProtection3;

  public static final String SERIALIZED_NAME_OTHER_PROTECTION4 = "OtherProtection4";
  @SerializedName(SERIALIZED_NAME_OTHER_PROTECTION4)
  private Double otherProtection4;

  public static final String SERIALIZED_NAME_OTHER_PROTECTION5 = "OtherProtection5";
  @SerializedName(SERIALIZED_NAME_OTHER_PROTECTION5)
  private Double otherProtection5;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_STORE_PROTECTION1 = "StoreProtection1";
  @SerializedName(SERIALIZED_NAME_STORE_PROTECTION1)
  private Double storeProtection1;

  public static final String SERIALIZED_NAME_STORE_PROTECTION2 = "StoreProtection2";
  @SerializedName(SERIALIZED_NAME_STORE_PROTECTION2)
  private Double storeProtection2;

  public static final String SERIALIZED_NAME_STORE_PROTECTION3 = "StoreProtection3";
  @SerializedName(SERIALIZED_NAME_STORE_PROTECTION3)
  private Double storeProtection3;

  public static final String SERIALIZED_NAME_STORE_PROTECTION4 = "StoreProtection4";
  @SerializedName(SERIALIZED_NAME_STORE_PROTECTION4)
  private Double storeProtection4;

  public static final String SERIALIZED_NAME_STORE_PROTECTION5 = "StoreProtection5";
  @SerializedName(SERIALIZED_NAME_STORE_PROTECTION5)
  private Double storeProtection5;

  public static final String SERIALIZED_NAME_SUPPLIER_PROTECTION1 = "SupplierProtection1";
  @SerializedName(SERIALIZED_NAME_SUPPLIER_PROTECTION1)
  private Double supplierProtection1;

  public static final String SERIALIZED_NAME_SUPPLIER_PROTECTION2 = "SupplierProtection2";
  @SerializedName(SERIALIZED_NAME_SUPPLIER_PROTECTION2)
  private Double supplierProtection2;

  public static final String SERIALIZED_NAME_SUPPLIER_PROTECTION3 = "SupplierProtection3";
  @SerializedName(SERIALIZED_NAME_SUPPLIER_PROTECTION3)
  private Double supplierProtection3;

  public static final String SERIALIZED_NAME_SUPPLIER_PROTECTION4 = "SupplierProtection4";
  @SerializedName(SERIALIZED_NAME_SUPPLIER_PROTECTION4)
  private Double supplierProtection4;

  public static final String SERIALIZED_NAME_SUPPLIER_PROTECTION5 = "SupplierProtection5";
  @SerializedName(SERIALIZED_NAME_SUPPLIER_PROTECTION5)
  private Double supplierProtection5;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public InventoryProtection actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public InventoryProtection putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public InventoryProtection createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public InventoryProtection createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public InventoryProtection dcProtection1(Double dcProtection1) {
    this.dcProtection1 = dcProtection1;
    return this;
  }

   /**
   * Level 1 DC protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return dcProtection1
  **/
  public Double getDcProtection1() {
    return dcProtection1;
  }

  public void setDcProtection1(Double dcProtection1) {
    this.dcProtection1 = dcProtection1;
  }

  public InventoryProtection dcProtection2(Double dcProtection2) {
    this.dcProtection2 = dcProtection2;
    return this;
  }

   /**
   * Level 2 DC protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return dcProtection2
  **/
  public Double getDcProtection2() {
    return dcProtection2;
  }

  public void setDcProtection2(Double dcProtection2) {
    this.dcProtection2 = dcProtection2;
  }

  public InventoryProtection dcProtection3(Double dcProtection3) {
    this.dcProtection3 = dcProtection3;
    return this;
  }

   /**
   * Level 3 DC protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return dcProtection3
  **/
  public Double getDcProtection3() {
    return dcProtection3;
  }

  public void setDcProtection3(Double dcProtection3) {
    this.dcProtection3 = dcProtection3;
  }

  public InventoryProtection dcProtection4(Double dcProtection4) {
    this.dcProtection4 = dcProtection4;
    return this;
  }

   /**
   * Level 4 DC protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return dcProtection4
  **/
  public Double getDcProtection4() {
    return dcProtection4;
  }

  public void setDcProtection4(Double dcProtection4) {
    this.dcProtection4 = dcProtection4;
  }

  public InventoryProtection dcProtection5(Double dcProtection5) {
    this.dcProtection5 = dcProtection5;
    return this;
  }

   /**
   * Level 5 DC protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return dcProtection5
  **/
  public Double getDcProtection5() {
    return dcProtection5;
  }

  public void setDcProtection5(Double dcProtection5) {
    this.dcProtection5 = dcProtection5;
  }

  public InventoryProtection extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public InventoryProtection localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public InventoryProtection messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public InventoryProtection networkProtection1(Double networkProtection1) {
    this.networkProtection1 = networkProtection1;
    return this;
  }

   /**
   * Level 1 network protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return networkProtection1
  **/
  public Double getNetworkProtection1() {
    return networkProtection1;
  }

  public void setNetworkProtection1(Double networkProtection1) {
    this.networkProtection1 = networkProtection1;
  }

  public InventoryProtection networkProtection2(Double networkProtection2) {
    this.networkProtection2 = networkProtection2;
    return this;
  }

   /**
   * Level 2 network protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return networkProtection2
  **/
  public Double getNetworkProtection2() {
    return networkProtection2;
  }

  public void setNetworkProtection2(Double networkProtection2) {
    this.networkProtection2 = networkProtection2;
  }

  public InventoryProtection networkProtection3(Double networkProtection3) {
    this.networkProtection3 = networkProtection3;
    return this;
  }

   /**
   * Level 3 network protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return networkProtection3
  **/
  public Double getNetworkProtection3() {
    return networkProtection3;
  }

  public void setNetworkProtection3(Double networkProtection3) {
    this.networkProtection3 = networkProtection3;
  }

  public InventoryProtection networkProtection4(Double networkProtection4) {
    this.networkProtection4 = networkProtection4;
    return this;
  }

   /**
   * Level 4 network protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return networkProtection4
  **/
  public Double getNetworkProtection4() {
    return networkProtection4;
  }

  public void setNetworkProtection4(Double networkProtection4) {
    this.networkProtection4 = networkProtection4;
  }

  public InventoryProtection networkProtection5(Double networkProtection5) {
    this.networkProtection5 = networkProtection5;
    return this;
  }

   /**
   * Level 5 network protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return networkProtection5
  **/
  public Double getNetworkProtection5() {
    return networkProtection5;
  }

  public void setNetworkProtection5(Double networkProtection5) {
    this.networkProtection5 = networkProtection5;
  }

  public InventoryProtection otherProtection1(Double otherProtection1) {
    this.otherProtection1 = otherProtection1;
    return this;
  }

   /**
   * Level 1 other protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return otherProtection1
  **/
  public Double getOtherProtection1() {
    return otherProtection1;
  }

  public void setOtherProtection1(Double otherProtection1) {
    this.otherProtection1 = otherProtection1;
  }

  public InventoryProtection otherProtection2(Double otherProtection2) {
    this.otherProtection2 = otherProtection2;
    return this;
  }

   /**
   * Level 2 other protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return otherProtection2
  **/
  public Double getOtherProtection2() {
    return otherProtection2;
  }

  public void setOtherProtection2(Double otherProtection2) {
    this.otherProtection2 = otherProtection2;
  }

  public InventoryProtection otherProtection3(Double otherProtection3) {
    this.otherProtection3 = otherProtection3;
    return this;
  }

   /**
   * Level 3 other protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return otherProtection3
  **/
  public Double getOtherProtection3() {
    return otherProtection3;
  }

  public void setOtherProtection3(Double otherProtection3) {
    this.otherProtection3 = otherProtection3;
  }

  public InventoryProtection otherProtection4(Double otherProtection4) {
    this.otherProtection4 = otherProtection4;
    return this;
  }

   /**
   * Level 4 other protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return otherProtection4
  **/
  public Double getOtherProtection4() {
    return otherProtection4;
  }

  public void setOtherProtection4(Double otherProtection4) {
    this.otherProtection4 = otherProtection4;
  }

  public InventoryProtection otherProtection5(Double otherProtection5) {
    this.otherProtection5 = otherProtection5;
    return this;
  }

   /**
   * Level 5 other protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return otherProtection5
  **/
  public Double getOtherProtection5() {
    return otherProtection5;
  }

  public void setOtherProtection5(Double otherProtection5) {
    this.otherProtection5 = otherProtection5;
  }

  public InventoryProtection PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public InventoryProtection profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public InventoryProtection storeProtection1(Double storeProtection1) {
    this.storeProtection1 = storeProtection1;
    return this;
  }

   /**
   * Level 1 store protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return storeProtection1
  **/
  public Double getStoreProtection1() {
    return storeProtection1;
  }

  public void setStoreProtection1(Double storeProtection1) {
    this.storeProtection1 = storeProtection1;
  }

  public InventoryProtection storeProtection2(Double storeProtection2) {
    this.storeProtection2 = storeProtection2;
    return this;
  }

   /**
   * Level 2 store protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return storeProtection2
  **/
  public Double getStoreProtection2() {
    return storeProtection2;
  }

  public void setStoreProtection2(Double storeProtection2) {
    this.storeProtection2 = storeProtection2;
  }

  public InventoryProtection storeProtection3(Double storeProtection3) {
    this.storeProtection3 = storeProtection3;
    return this;
  }

   /**
   * Level 3 store protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return storeProtection3
  **/
  public Double getStoreProtection3() {
    return storeProtection3;
  }

  public void setStoreProtection3(Double storeProtection3) {
    this.storeProtection3 = storeProtection3;
  }

  public InventoryProtection storeProtection4(Double storeProtection4) {
    this.storeProtection4 = storeProtection4;
    return this;
  }

   /**
   * Level 4 store protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return storeProtection4
  **/
  public Double getStoreProtection4() {
    return storeProtection4;
  }

  public void setStoreProtection4(Double storeProtection4) {
    this.storeProtection4 = storeProtection4;
  }

  public InventoryProtection storeProtection5(Double storeProtection5) {
    this.storeProtection5 = storeProtection5;
    return this;
  }

   /**
   * Level 5 store protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return storeProtection5
  **/
  public Double getStoreProtection5() {
    return storeProtection5;
  }

  public void setStoreProtection5(Double storeProtection5) {
    this.storeProtection5 = storeProtection5;
  }

  public InventoryProtection supplierProtection1(Double supplierProtection1) {
    this.supplierProtection1 = supplierProtection1;
    return this;
  }

   /**
   * Level 1 supplier protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return supplierProtection1
  **/
  public Double getSupplierProtection1() {
    return supplierProtection1;
  }

  public void setSupplierProtection1(Double supplierProtection1) {
    this.supplierProtection1 = supplierProtection1;
  }

  public InventoryProtection supplierProtection2(Double supplierProtection2) {
    this.supplierProtection2 = supplierProtection2;
    return this;
  }

   /**
   * Level 2 supplier protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return supplierProtection2
  **/
  public Double getSupplierProtection2() {
    return supplierProtection2;
  }

  public void setSupplierProtection2(Double supplierProtection2) {
    this.supplierProtection2 = supplierProtection2;
  }

  public InventoryProtection supplierProtection3(Double supplierProtection3) {
    this.supplierProtection3 = supplierProtection3;
    return this;
  }

   /**
   * Level 3 supplier protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return supplierProtection3
  **/
  public Double getSupplierProtection3() {
    return supplierProtection3;
  }

  public void setSupplierProtection3(Double supplierProtection3) {
    this.supplierProtection3 = supplierProtection3;
  }

  public InventoryProtection supplierProtection4(Double supplierProtection4) {
    this.supplierProtection4 = supplierProtection4;
    return this;
  }

   /**
   * Level 4 supplier protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return supplierProtection4
  **/
  public Double getSupplierProtection4() {
    return supplierProtection4;
  }

  public void setSupplierProtection4(Double supplierProtection4) {
    this.supplierProtection4 = supplierProtection4;
  }

  public InventoryProtection supplierProtection5(Double supplierProtection5) {
    this.supplierProtection5 = supplierProtection5;
    return this;
  }

   /**
   * Level 5 supplier protection value
   * minimum: 0
   * maximum: 999999999999.9999
   * @return supplierProtection5
  **/
  public Double getSupplierProtection5() {
    return supplierProtection5;
  }

  public void setSupplierProtection5(Double supplierProtection5) {
    this.supplierProtection5 = supplierProtection5;
  }

  public InventoryProtection updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public InventoryProtection updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public InventoryProtection entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public InventoryProtection localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InventoryProtection inventoryProtection = (InventoryProtection) o;
    return Objects.equals(this.actions, inventoryProtection.actions) &&
        Objects.equals(this.createdBy, inventoryProtection.createdBy) &&
        Objects.equals(this.createdTimestamp, inventoryProtection.createdTimestamp) &&
        Objects.equals(this.dcProtection1, inventoryProtection.dcProtection1) &&
        Objects.equals(this.dcProtection2, inventoryProtection.dcProtection2) &&
        Objects.equals(this.dcProtection3, inventoryProtection.dcProtection3) &&
        Objects.equals(this.dcProtection4, inventoryProtection.dcProtection4) &&
        Objects.equals(this.dcProtection5, inventoryProtection.dcProtection5) &&
        Objects.equals(this.extended, inventoryProtection.extended) &&
        Objects.equals(this.localizedTo, inventoryProtection.localizedTo) &&
        Objects.equals(this.messages, inventoryProtection.messages) &&
        Objects.equals(this.networkProtection1, inventoryProtection.networkProtection1) &&
        Objects.equals(this.networkProtection2, inventoryProtection.networkProtection2) &&
        Objects.equals(this.networkProtection3, inventoryProtection.networkProtection3) &&
        Objects.equals(this.networkProtection4, inventoryProtection.networkProtection4) &&
        Objects.equals(this.networkProtection5, inventoryProtection.networkProtection5) &&
        Objects.equals(this.otherProtection1, inventoryProtection.otherProtection1) &&
        Objects.equals(this.otherProtection2, inventoryProtection.otherProtection2) &&
        Objects.equals(this.otherProtection3, inventoryProtection.otherProtection3) &&
        Objects.equals(this.otherProtection4, inventoryProtection.otherProtection4) &&
        Objects.equals(this.otherProtection5, inventoryProtection.otherProtection5) &&
        Objects.equals(this.PK, inventoryProtection.PK) &&
        Objects.equals(this.profileId, inventoryProtection.profileId) &&
        Objects.equals(this.storeProtection1, inventoryProtection.storeProtection1) &&
        Objects.equals(this.storeProtection2, inventoryProtection.storeProtection2) &&
        Objects.equals(this.storeProtection3, inventoryProtection.storeProtection3) &&
        Objects.equals(this.storeProtection4, inventoryProtection.storeProtection4) &&
        Objects.equals(this.storeProtection5, inventoryProtection.storeProtection5) &&
        Objects.equals(this.supplierProtection1, inventoryProtection.supplierProtection1) &&
        Objects.equals(this.supplierProtection2, inventoryProtection.supplierProtection2) &&
        Objects.equals(this.supplierProtection3, inventoryProtection.supplierProtection3) &&
        Objects.equals(this.supplierProtection4, inventoryProtection.supplierProtection4) &&
        Objects.equals(this.supplierProtection5, inventoryProtection.supplierProtection5) &&
        Objects.equals(this.updatedBy, inventoryProtection.updatedBy) &&
        Objects.equals(this.updatedTimestamp, inventoryProtection.updatedTimestamp) &&
        Objects.equals(this.entityName, inventoryProtection.entityName) &&
        Objects.equals(this.localize, inventoryProtection.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, dcProtection1, dcProtection2, dcProtection3, dcProtection4, dcProtection5, extended, localizedTo, messages, networkProtection1, networkProtection2, networkProtection3, networkProtection4, networkProtection5, otherProtection1, otherProtection2, otherProtection3, otherProtection4, otherProtection5, PK, profileId, storeProtection1, storeProtection2, storeProtection3, storeProtection4, storeProtection5, supplierProtection1, supplierProtection2, supplierProtection3, supplierProtection4, supplierProtection5, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InventoryProtection {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    dcProtection1: ").append(toIndentedString(dcProtection1)).append("\n");
    sb.append("    dcProtection2: ").append(toIndentedString(dcProtection2)).append("\n");
    sb.append("    dcProtection3: ").append(toIndentedString(dcProtection3)).append("\n");
    sb.append("    dcProtection4: ").append(toIndentedString(dcProtection4)).append("\n");
    sb.append("    dcProtection5: ").append(toIndentedString(dcProtection5)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    networkProtection1: ").append(toIndentedString(networkProtection1)).append("\n");
    sb.append("    networkProtection2: ").append(toIndentedString(networkProtection2)).append("\n");
    sb.append("    networkProtection3: ").append(toIndentedString(networkProtection3)).append("\n");
    sb.append("    networkProtection4: ").append(toIndentedString(networkProtection4)).append("\n");
    sb.append("    networkProtection5: ").append(toIndentedString(networkProtection5)).append("\n");
    sb.append("    otherProtection1: ").append(toIndentedString(otherProtection1)).append("\n");
    sb.append("    otherProtection2: ").append(toIndentedString(otherProtection2)).append("\n");
    sb.append("    otherProtection3: ").append(toIndentedString(otherProtection3)).append("\n");
    sb.append("    otherProtection4: ").append(toIndentedString(otherProtection4)).append("\n");
    sb.append("    otherProtection5: ").append(toIndentedString(otherProtection5)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    storeProtection1: ").append(toIndentedString(storeProtection1)).append("\n");
    sb.append("    storeProtection2: ").append(toIndentedString(storeProtection2)).append("\n");
    sb.append("    storeProtection3: ").append(toIndentedString(storeProtection3)).append("\n");
    sb.append("    storeProtection4: ").append(toIndentedString(storeProtection4)).append("\n");
    sb.append("    storeProtection5: ").append(toIndentedString(storeProtection5)).append("\n");
    sb.append("    supplierProtection1: ").append(toIndentedString(supplierProtection1)).append("\n");
    sb.append("    supplierProtection2: ").append(toIndentedString(supplierProtection2)).append("\n");
    sb.append("    supplierProtection3: ").append(toIndentedString(supplierProtection3)).append("\n");
    sb.append("    supplierProtection4: ").append(toIndentedString(supplierProtection4)).append("\n");
    sb.append("    supplierProtection5: ").append(toIndentedString(supplierProtection5)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

