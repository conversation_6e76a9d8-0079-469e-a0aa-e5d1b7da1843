/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderSalesAssociate
 */
public class OrderSalesAssociate {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ASSOCIATE_ID = "AssociateId";
  @SerializedName(SERIALIZED_NAME_ASSOCIATE_ID)
  private String associateId;

  public static final String SERIALIZED_NAME_ASSOCIATE_TYPE = "AssociateType";
  @SerializedName(SERIALIZED_NAME_ASSOCIATE_TYPE)
  private String associateType;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_SALES_ASSOCIATE_ID = "OrderSalesAssociateId";
  @SerializedName(SERIALIZED_NAME_ORDER_SALES_ASSOCIATE_ID)
  private String orderSalesAssociateId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_ORDER = "ParentOrder";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER)
  private PrimaryKey parentOrder = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderSalesAssociate actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderSalesAssociate putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderSalesAssociate associateId(String associateId) {
    this.associateId = associateId;
    return this;
  }

   /**
   * Associate id
   * @return associateId
  **/
  
  public String getAssociateId() {
    return associateId;
  }

  public void setAssociateId(String associateId) {
    this.associateId = associateId;
  }

  public OrderSalesAssociate associateType(String associateType) {
    this.associateType = associateType;
    return this;
  }

   /**
   * Associate Type e.g. Cashier or Sales Rep
   * @return associateType
  **/
  
  public String getAssociateType() {
    return associateType;
  }

  public void setAssociateType(String associateType) {
    this.associateType = associateType;
  }

  public OrderSalesAssociate createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderSalesAssociate createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderSalesAssociate extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderSalesAssociate localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderSalesAssociate messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderSalesAssociate orderSalesAssociateId(String orderSalesAssociateId) {
    this.orderSalesAssociateId = orderSalesAssociateId;
    return this;
  }

   /**
   * Unique identifier of order Sales Associate table
   * @return orderSalesAssociateId
  **/
  
  public String getOrderSalesAssociateId() {
    return orderSalesAssociateId;
  }

  public void setOrderSalesAssociateId(String orderSalesAssociateId) {
    this.orderSalesAssociateId = orderSalesAssociateId;
  }

  public OrderSalesAssociate orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderSalesAssociate PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderSalesAssociate parentOrder(PrimaryKey parentOrder) {
    this.parentOrder = parentOrder;
    return this;
  }

   /**
   * Get parentOrder
   * @return parentOrder
  **/
  
  public PrimaryKey getParentOrder() {
    return parentOrder;
  }

  public void setParentOrder(PrimaryKey parentOrder) {
    this.parentOrder = parentOrder;
  }

  public OrderSalesAssociate updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderSalesAssociate updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderSalesAssociate entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderSalesAssociate localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderSalesAssociate orderSalesAssociate = (OrderSalesAssociate) o;
    return Objects.equals(this.actions, orderSalesAssociate.actions) &&
        Objects.equals(this.associateId, orderSalesAssociate.associateId) &&
        Objects.equals(this.associateType, orderSalesAssociate.associateType) &&
        Objects.equals(this.createdBy, orderSalesAssociate.createdBy) &&
        Objects.equals(this.createdTimestamp, orderSalesAssociate.createdTimestamp) &&
        Objects.equals(this.extended, orderSalesAssociate.extended) &&
        Objects.equals(this.localizedTo, orderSalesAssociate.localizedTo) &&
        Objects.equals(this.messages, orderSalesAssociate.messages) &&
        Objects.equals(this.orderSalesAssociateId, orderSalesAssociate.orderSalesAssociateId) &&
        Objects.equals(this.orgId, orderSalesAssociate.orgId) &&
        Objects.equals(this.PK, orderSalesAssociate.PK) &&
        Objects.equals(this.parentOrder, orderSalesAssociate.parentOrder) &&
        Objects.equals(this.updatedBy, orderSalesAssociate.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderSalesAssociate.updatedTimestamp) &&
        Objects.equals(this.entityName, orderSalesAssociate.entityName) &&
        Objects.equals(this.localize, orderSalesAssociate.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, associateId, associateType, createdBy, createdTimestamp, extended, localizedTo, messages, orderSalesAssociateId, orgId, PK, parentOrder, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderSalesAssociate {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    associateId: ").append(toIndentedString(associateId)).append("\n");
    sb.append("    associateType: ").append(toIndentedString(associateType)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderSalesAssociateId: ").append(toIndentedString(orderSalesAssociateId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentOrder: ").append(toIndentedString(parentOrder)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

