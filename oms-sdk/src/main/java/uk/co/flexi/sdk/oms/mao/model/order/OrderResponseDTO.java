package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import lombok.Data;

@Data
public class OrderResponseDTO {

        @SerializedName("wmessageKey")
        private String messageKey;

        @SerializedName("cloudComponent")
        private String cloudComponent;

        @SerializedName("data")
        private DataItem data;

        @SerializedName("requestUri")
        private Object requestUri;

        @SerializedName("message")
        private Object message;

        @SerializedName("exceptions")
        private List<Object> exceptions;

        @SerializedName("cloudComponentHostName")
        private String cloudComponentHostName;

        @SerializedName("success")
        private Boolean success;

        @SerializedName("rootCause")
        private Object rootCause;

        @SerializedName("header")
        private Header header;

        @SerializedName("messages")
        private Messages messages;

        @SerializedName("errors")
        private List<Object> errors;

        @SerializedName("statusCode")
        private String statusCode;

        public Header getHeader() {
                return header;
        }

        public String getMessageKey() {
                return messageKey;
        }

        public String getCloudComponent() {
                return cloudComponent;
        }

        public DataItem getData() {
                return data;
        }

        public Object getRequestUri() {
                return requestUri;
        }

        public Object getMessage() {
                return message;
        }

        public String getCloudComponentHostName() {
                return cloudComponentHostName;
        }

        public Boolean getSuccess() {
                return success;
        }

        public List<Object> getExceptions() {
                return exceptions;
        }

        public Object getRootCause() {
                return rootCause;
        }

        public Messages getMessages() {
                return messages;
        }

        public String getStatusCode() {
                return statusCode;
        }

        public List<Object> getErrors() {
                return errors;
        }

}