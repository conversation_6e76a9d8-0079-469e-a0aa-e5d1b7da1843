/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * ProcessingModeId
 */
public class ProcessingModeId {
  public static final String SERIALIZED_NAME_PROCESSING_MODE_ID = "ProcessingModeId";
  @SerializedName(SERIALIZED_NAME_PROCESSING_MODE_ID)
  private String processingModeId;

  public ProcessingModeId processingModeId(String processingModeId) {
    this.processingModeId = processingModeId;
    return this;
  }

   /**
   * Unique identifier of the processing mode
   * @return processingModeId
  **/
  
  public String getProcessingModeId() {
    return processingModeId;
  }

  public void setProcessingModeId(String processingModeId) {
    this.processingModeId = processingModeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ProcessingModeId processingModeId = (ProcessingModeId) o;
    return Objects.equals(this.processingModeId, processingModeId.processingModeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(processingModeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProcessingModeId {\n");
    
    sb.append("    processingModeId: ").append(toIndentedString(processingModeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

