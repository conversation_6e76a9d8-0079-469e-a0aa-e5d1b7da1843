package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * PrimaryKey
 */
public class PrimaryKey {
  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public PrimaryKey PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Get PK
   * @return PK
  **/
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PrimaryKey primaryKey = (PrimaryKey) o;
    return Objects.equals(this.PK, primaryKey.PK);
  }

  @Override
  public int hashCode() {
    return Objects.hash(PK);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PrimaryKey {\n");
    
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

