/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * InvoiceLineTaxDetail
 */
public class InvoiceLineTaxDetail {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FULFILLMENT_GROUP_ID = "FulfillmentGroupId";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_GROUP_ID)
  private String fulfillmentGroupId;

  public static final String SERIALIZED_NAME_IS_INFORMATIONAL = "IsInformational";
  @SerializedName(SERIALIZED_NAME_IS_INFORMATIONAL)
  private Boolean isInformational;

  public static final String SERIALIZED_NAME_JURISDICTION = "Jurisdiction";
  @SerializedName(SERIALIZED_NAME_JURISDICTION)
  private String jurisdiction;

  public static final String SERIALIZED_NAME_JURISDICTION_TYPE_ID = "JurisdictionTypeId";
  @SerializedName(SERIALIZED_NAME_JURISDICTION_TYPE_ID)
  private String jurisdictionTypeId;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_RECORDED_TAX_AMOUNT = "RecordedTaxAmount";
  @SerializedName(SERIALIZED_NAME_RECORDED_TAX_AMOUNT)
  private BigDecimal recordedTaxAmount;

  public static final String SERIALIZED_NAME_TAX_AMOUNT = "TaxAmount";
  @SerializedName(SERIALIZED_NAME_TAX_AMOUNT)
  private BigDecimal taxAmount;

  public static final String SERIALIZED_NAME_TAX_CODE = "TaxCode";
  @SerializedName(SERIALIZED_NAME_TAX_CODE)
  private String taxCode;

  public static final String SERIALIZED_NAME_TAX_DATE = "TaxDate";
  @SerializedName(SERIALIZED_NAME_TAX_DATE)
  private OffsetDateTime taxDate;

  public static final String SERIALIZED_NAME_TAX_DETAIL_ID = "TaxDetailId";
  @SerializedName(SERIALIZED_NAME_TAX_DETAIL_ID)
  private String taxDetailId;

  public static final String SERIALIZED_NAME_TAX_ENGINE_ID = "TaxEngineId";
  @SerializedName(SERIALIZED_NAME_TAX_ENGINE_ID)
  private String taxEngineId;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER1 = "TaxIdentifier1";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER1)
  private String taxIdentifier1;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER2 = "TaxIdentifier2";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER2)
  private String taxIdentifier2;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER3 = "TaxIdentifier3";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER3)
  private String taxIdentifier3;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER4 = "TaxIdentifier4";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER4)
  private String taxIdentifier4;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER5 = "TaxIdentifier5";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER5)
  private String taxIdentifier5;

  public static final String SERIALIZED_NAME_TAX_RATE = "TaxRate";
  @SerializedName(SERIALIZED_NAME_TAX_RATE)
  private Double taxRate;

  public static final String SERIALIZED_NAME_TAX_TYPE_ID = "TaxTypeId";
  @SerializedName(SERIALIZED_NAME_TAX_TYPE_ID)
  private String taxTypeId;

  public static final String SERIALIZED_NAME_TAXABLE_AMOUNT = "TaxableAmount";
  @SerializedName(SERIALIZED_NAME_TAXABLE_AMOUNT)
  private BigDecimal taxableAmount;

  public static final String SERIALIZED_NAME_TRANSLATIONS = "Translations";
  @SerializedName(SERIALIZED_NAME_TRANSLATIONS)
  private Map<String, Map<String, String>> translations = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_IS_INVOICE_TAX = "isInvoiceTax";
  @SerializedName(SERIALIZED_NAME_IS_INVOICE_TAX)
  private Boolean isInvoiceTax;

  public static final String SERIALIZED_NAME_LINE_TAX = "lineTax";
  @SerializedName(SERIALIZED_NAME_LINE_TAX)
  private Boolean lineTax;

  public InvoiceLineTaxDetail actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public InvoiceLineTaxDetail putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public InvoiceLineTaxDetail createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public InvoiceLineTaxDetail createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public InvoiceLineTaxDetail extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public InvoiceLineTaxDetail fulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
    return this;
  }

   /**
   * ID used to group items which need to be fulfilled together
   * @return fulfillmentGroupId
  **/
  
  public String getFulfillmentGroupId() {
    return fulfillmentGroupId;
  }

  public void setFulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
  }

  public InvoiceLineTaxDetail isInformational(Boolean isInformational) {
    this.isInformational = isInformational;
    return this;
  }

   /**
   * Indicates a tax is informational and is not taken into account in the total calculations
   * @return isInformational
  **/
  
  public Boolean getIsInformational() {
    return isInformational;
  }

  public void setIsInformational(Boolean isInformational) {
    this.isInformational = isInformational;
  }

  public InvoiceLineTaxDetail jurisdiction(String jurisdiction) {
    this.jurisdiction = jurisdiction;
    return this;
  }

   /**
   * Jurisdiction in which the tax is applied
   * @return jurisdiction
  **/
  
  public String getJurisdiction() {
    return jurisdiction;
  }

  public void setJurisdiction(String jurisdiction) {
    this.jurisdiction = jurisdiction;
  }

  public InvoiceLineTaxDetail jurisdictionTypeId(String jurisdictionTypeId) {
    this.jurisdictionTypeId = jurisdictionTypeId;
    return this;
  }

   /**
   * Type of jurisdiction in which the tax is applied
   * @return jurisdictionTypeId
  **/
  
  public String getJurisdictionTypeId() {
    return jurisdictionTypeId;
  }

  public void setJurisdictionTypeId(String jurisdictionTypeId) {
    this.jurisdictionTypeId = jurisdictionTypeId;
  }

  public InvoiceLineTaxDetail messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public InvoiceLineTaxDetail orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public InvoiceLineTaxDetail PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public InvoiceLineTaxDetail recordedTaxAmount(BigDecimal recordedTaxAmount) {
    this.recordedTaxAmount = recordedTaxAmount;
    return this;
  }

   /**
   * Tax amount which was recorded in the tax system
   * minimum: 0
   * maximum: 99999999999999.98
   * @return recordedTaxAmount
  **/
  
  public BigDecimal getRecordedTaxAmount() {
    return recordedTaxAmount;
  }

  public void setRecordedTaxAmount(BigDecimal recordedTaxAmount) {
    this.recordedTaxAmount = recordedTaxAmount;
  }

  public InvoiceLineTaxDetail taxAmount(BigDecimal taxAmount) {
    this.taxAmount = taxAmount;
    return this;
  }

   /**
   * Tax amount (Tax Amount &#x3D; Tax Rate * Taxable Amount)
   * minimum: 0
   * maximum: 99999999999999.98
   * @return taxAmount
  **/
  
  public BigDecimal getTaxAmount() {
    return taxAmount;
  }

  public void setTaxAmount(BigDecimal taxAmount) {
    this.taxAmount = taxAmount;
  }

  public InvoiceLineTaxDetail taxCode(String taxCode) {
    this.taxCode = taxCode;
    return this;
  }

   /**
   * Defines the charge category
   * @return taxCode
  **/
  
  public String getTaxCode() {
    return taxCode;
  }

  public void setTaxCode(String taxCode) {
    this.taxCode = taxCode;
  }

  public InvoiceLineTaxDetail taxDate(OffsetDateTime taxDate) {
    this.taxDate = taxDate;
    return this;
  }

   /**
   * Timestamp when the tax record is created
   * @return taxDate
  **/
  
  public OffsetDateTime getTaxDate() {
    return taxDate;
  }

  public void setTaxDate(OffsetDateTime taxDate) {
    this.taxDate = taxDate;
  }

  public InvoiceLineTaxDetail taxDetailId(String taxDetailId) {
    this.taxDetailId = taxDetailId;
    return this;
  }

   /**
   * Unique identifier of the tax
   * @return taxDetailId
  **/
  
  public String getTaxDetailId() {
    return taxDetailId;
  }

  public void setTaxDetailId(String taxDetailId) {
    this.taxDetailId = taxDetailId;
  }

  public InvoiceLineTaxDetail taxEngineId(String taxEngineId) {
    this.taxEngineId = taxEngineId;
    return this;
  }

   /**
   * ID of the tax engine which is used to calculate the tax amount
   * @return taxEngineId
  **/
  
  public String getTaxEngineId() {
    return taxEngineId;
  }

  public void setTaxEngineId(String taxEngineId) {
    this.taxEngineId = taxEngineId;
  }

  public InvoiceLineTaxDetail taxIdentifier1(String taxIdentifier1) {
    this.taxIdentifier1 = taxIdentifier1;
    return this;
  }

   /**
   * Attribute to uniquely identify a tax record in addition to existing tax attributes like taxcode, jurisdiction and jurisdictionTypeId
   * @return taxIdentifier1
  **/
  
  public String getTaxIdentifier1() {
    return taxIdentifier1;
  }

  public void setTaxIdentifier1(String taxIdentifier1) {
    this.taxIdentifier1 = taxIdentifier1;
  }

  public InvoiceLineTaxDetail taxIdentifier2(String taxIdentifier2) {
    this.taxIdentifier2 = taxIdentifier2;
    return this;
  }

   /**
   * Attribute to uniquely identify a tax record in addition to existing tax attributes like taxcode, jurisdiction and jurisdictionTypeId
   * @return taxIdentifier2
  **/
  
  public String getTaxIdentifier2() {
    return taxIdentifier2;
  }

  public void setTaxIdentifier2(String taxIdentifier2) {
    this.taxIdentifier2 = taxIdentifier2;
  }

  public InvoiceLineTaxDetail taxIdentifier3(String taxIdentifier3) {
    this.taxIdentifier3 = taxIdentifier3;
    return this;
  }

   /**
   * Attribute to uniquely identify a tax record in addition to existing tax attributes like taxcode, jurisdiction and jurisdictionTypeId
   * @return taxIdentifier3
  **/
  
  public String getTaxIdentifier3() {
    return taxIdentifier3;
  }

  public void setTaxIdentifier3(String taxIdentifier3) {
    this.taxIdentifier3 = taxIdentifier3;
  }

  public InvoiceLineTaxDetail taxIdentifier4(String taxIdentifier4) {
    this.taxIdentifier4 = taxIdentifier4;
    return this;
  }

   /**
   * Attribute to uniquely identify a tax record in addition to existing tax attributes like taxcode, jurisdiction and jurisdictionTypeId
   * @return taxIdentifier4
  **/
  
  public String getTaxIdentifier4() {
    return taxIdentifier4;
  }

  public void setTaxIdentifier4(String taxIdentifier4) {
    this.taxIdentifier4 = taxIdentifier4;
  }

  public InvoiceLineTaxDetail taxIdentifier5(String taxIdentifier5) {
    this.taxIdentifier5 = taxIdentifier5;
    return this;
  }

   /**
   * Attribute to uniquely identify a tax record in addition to existing tax attributes like taxcode, jurisdiction and jurisdictionTypeId
   * @return taxIdentifier5
  **/
  
  public String getTaxIdentifier5() {
    return taxIdentifier5;
  }

  public void setTaxIdentifier5(String taxIdentifier5) {
    this.taxIdentifier5 = taxIdentifier5;
  }

  public InvoiceLineTaxDetail taxRate(Double taxRate) {
    this.taxRate = taxRate;
    return this;
  }

   /**
   * Tax rate
   * minimum: 0
   * maximum: 999999999999.9999
   * @return taxRate
  **/
  
  public Double getTaxRate() {
    return taxRate;
  }

  public void setTaxRate(Double taxRate) {
    this.taxRate = taxRate;
  }

  public InvoiceLineTaxDetail taxTypeId(String taxTypeId) {
    this.taxTypeId = taxTypeId;
    return this;
  }

   /**
   * Defines the type of tax
   * @return taxTypeId
  **/
  
  public String getTaxTypeId() {
    return taxTypeId;
  }

  public void setTaxTypeId(String taxTypeId) {
    this.taxTypeId = taxTypeId;
  }

  public InvoiceLineTaxDetail taxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
    return this;
  }

   /**
   * Amount which is taxable for the tax record
   * minimum: 0
   * maximum: 99999999999999.98
   * @return taxableAmount
  **/
  
  public BigDecimal getTaxableAmount() {
    return taxableAmount;
  }

  public void setTaxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
  }

  public InvoiceLineTaxDetail translations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
    return this;
  }

  public InvoiceLineTaxDetail putTranslationsItem(String key, Map<String, String> translationsItem) {
    if (this.translations == null) {
      this.translations = new HashMap<String, Map<String, String>>();
    }
    this.translations.put(key, translationsItem);
    return this;
  }

   /**
   * Get translations
   * @return translations
  **/
  
  public Map<String, Map<String, String>> getTranslations() {
    return translations;
  }

  public void setTranslations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
  }

  public InvoiceLineTaxDetail updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public InvoiceLineTaxDetail updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public InvoiceLineTaxDetail entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public InvoiceLineTaxDetail isInvoiceTax(Boolean isInvoiceTax) {
    this.isInvoiceTax = isInvoiceTax;
    return this;
  }

   /**
   * Get isInvoiceTax
   * @return isInvoiceTax
  **/
  
  public Boolean getIsInvoiceTax() {
    return isInvoiceTax;
  }

  public void setIsInvoiceTax(Boolean isInvoiceTax) {
    this.isInvoiceTax = isInvoiceTax;
  }

  public InvoiceLineTaxDetail lineTax(Boolean lineTax) {
    this.lineTax = lineTax;
    return this;
  }

   /**
   * Get lineTax
   * @return lineTax
  **/
  
  public Boolean getLineTax() {
    return lineTax;
  }

  public void setLineTax(Boolean lineTax) {
    this.lineTax = lineTax;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvoiceLineTaxDetail invoiceLineTaxDetail = (InvoiceLineTaxDetail) o;
    return Objects.equals(this.actions, invoiceLineTaxDetail.actions) &&
        Objects.equals(this.createdBy, invoiceLineTaxDetail.createdBy) &&
        Objects.equals(this.createdTimestamp, invoiceLineTaxDetail.createdTimestamp) &&
        Objects.equals(this.extended, invoiceLineTaxDetail.extended) &&
        Objects.equals(this.fulfillmentGroupId, invoiceLineTaxDetail.fulfillmentGroupId) &&
        Objects.equals(this.isInformational, invoiceLineTaxDetail.isInformational) &&
        Objects.equals(this.jurisdiction, invoiceLineTaxDetail.jurisdiction) &&
        Objects.equals(this.jurisdictionTypeId, invoiceLineTaxDetail.jurisdictionTypeId) &&
        Objects.equals(this.messages, invoiceLineTaxDetail.messages) &&
        Objects.equals(this.orgId, invoiceLineTaxDetail.orgId) &&
        Objects.equals(this.PK, invoiceLineTaxDetail.PK) &&
        Objects.equals(this.recordedTaxAmount, invoiceLineTaxDetail.recordedTaxAmount) &&
        Objects.equals(this.taxAmount, invoiceLineTaxDetail.taxAmount) &&
        Objects.equals(this.taxCode, invoiceLineTaxDetail.taxCode) &&
        Objects.equals(this.taxDate, invoiceLineTaxDetail.taxDate) &&
        Objects.equals(this.taxDetailId, invoiceLineTaxDetail.taxDetailId) &&
        Objects.equals(this.taxEngineId, invoiceLineTaxDetail.taxEngineId) &&
        Objects.equals(this.taxIdentifier1, invoiceLineTaxDetail.taxIdentifier1) &&
        Objects.equals(this.taxIdentifier2, invoiceLineTaxDetail.taxIdentifier2) &&
        Objects.equals(this.taxIdentifier3, invoiceLineTaxDetail.taxIdentifier3) &&
        Objects.equals(this.taxIdentifier4, invoiceLineTaxDetail.taxIdentifier4) &&
        Objects.equals(this.taxIdentifier5, invoiceLineTaxDetail.taxIdentifier5) &&
        Objects.equals(this.taxRate, invoiceLineTaxDetail.taxRate) &&
        Objects.equals(this.taxTypeId, invoiceLineTaxDetail.taxTypeId) &&
        Objects.equals(this.taxableAmount, invoiceLineTaxDetail.taxableAmount) &&
        Objects.equals(this.translations, invoiceLineTaxDetail.translations) &&
        Objects.equals(this.updatedBy, invoiceLineTaxDetail.updatedBy) &&
        Objects.equals(this.updatedTimestamp, invoiceLineTaxDetail.updatedTimestamp) &&
        Objects.equals(this.entityName, invoiceLineTaxDetail.entityName) &&
        Objects.equals(this.isInvoiceTax, invoiceLineTaxDetail.isInvoiceTax) &&
        Objects.equals(this.lineTax, invoiceLineTaxDetail.lineTax);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, extended, fulfillmentGroupId, isInformational, jurisdiction, jurisdictionTypeId, messages, orgId, PK, recordedTaxAmount, taxAmount, taxCode, taxDate, taxDetailId, taxEngineId, taxIdentifier1, taxIdentifier2, taxIdentifier3, taxIdentifier4, taxIdentifier5, taxRate, taxTypeId, taxableAmount, translations, updatedBy, updatedTimestamp, entityName, isInvoiceTax, lineTax);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvoiceLineTaxDetail {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    fulfillmentGroupId: ").append(toIndentedString(fulfillmentGroupId)).append("\n");
    sb.append("    isInformational: ").append(toIndentedString(isInformational)).append("\n");
    sb.append("    jurisdiction: ").append(toIndentedString(jurisdiction)).append("\n");
    sb.append("    jurisdictionTypeId: ").append(toIndentedString(jurisdictionTypeId)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    recordedTaxAmount: ").append(toIndentedString(recordedTaxAmount)).append("\n");
    sb.append("    taxAmount: ").append(toIndentedString(taxAmount)).append("\n");
    sb.append("    taxCode: ").append(toIndentedString(taxCode)).append("\n");
    sb.append("    taxDate: ").append(toIndentedString(taxDate)).append("\n");
    sb.append("    taxDetailId: ").append(toIndentedString(taxDetailId)).append("\n");
    sb.append("    taxEngineId: ").append(toIndentedString(taxEngineId)).append("\n");
    sb.append("    taxIdentifier1: ").append(toIndentedString(taxIdentifier1)).append("\n");
    sb.append("    taxIdentifier2: ").append(toIndentedString(taxIdentifier2)).append("\n");
    sb.append("    taxIdentifier3: ").append(toIndentedString(taxIdentifier3)).append("\n");
    sb.append("    taxIdentifier4: ").append(toIndentedString(taxIdentifier4)).append("\n");
    sb.append("    taxIdentifier5: ").append(toIndentedString(taxIdentifier5)).append("\n");
    sb.append("    taxRate: ").append(toIndentedString(taxRate)).append("\n");
    sb.append("    taxTypeId: ").append(toIndentedString(taxTypeId)).append("\n");
    sb.append("    taxableAmount: ").append(toIndentedString(taxableAmount)).append("\n");
    sb.append("    translations: ").append(toIndentedString(translations)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    isInvoiceTax: ").append(toIndentedString(isInvoiceTax)).append("\n");
    sb.append("    lineTax: ").append(toIndentedString(lineTax)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

