/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ManagerAuthDetail
 */
public class ManagerAuthDetail {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_AUTHORIZER_NAME = "AuthorizerName";
  @SerializedName(SERIALIZED_NAME_AUTHORIZER_NAME)
  private String authorizerName;

  public static final String SERIALIZED_NAME_COMMAND_DISPLAY_NAME = "CommandDisplayName";
  @SerializedName(SERIALIZED_NAME_COMMAND_DISPLAY_NAME)
  private String commandDisplayName;

  public static final String SERIALIZED_NAME_COMMAND_NAME = "CommandName";
  @SerializedName(SERIALIZED_NAME_COMMAND_NAME)
  private String commandName;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MANAGER_AUTH_DETAIL_ID = "ManagerAuthDetailId";
  @SerializedName(SERIALIZED_NAME_MANAGER_AUTH_DETAIL_ID)
  private String managerAuthDetailId;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_ID = "OrderLineId";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_ID)
  private String orderLineId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_OVERRIDE_SUCCESS = "OverrideSuccess";
  @SerializedName(SERIALIZED_NAME_OVERRIDE_SUCCESS)
  private Boolean overrideSuccess;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public ManagerAuthDetail actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ManagerAuthDetail putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ManagerAuthDetail authorizerName(String authorizerName) {
    this.authorizerName = authorizerName;
    return this;
  }

   /**
   * Name of the authorized user who authenticates to perform the action.
   * @return authorizerName
  **/
  
  public String getAuthorizerName() {
    return authorizerName;
  }

  public void setAuthorizerName(String authorizerName) {
    this.authorizerName = authorizerName;
  }

  public ManagerAuthDetail commandDisplayName(String commandDisplayName) {
    this.commandDisplayName = commandDisplayName;
    return this;
  }

   /**
   * This is the user friendly name for the command name.  Used to display on Electronic Journal (EJ) UI.
   * @return commandDisplayName
  **/
  
  public String getCommandDisplayName() {
    return commandDisplayName;
  }

  public void setCommandDisplayName(String commandDisplayName) {
    this.commandDisplayName = commandDisplayName;
  }

  public ManagerAuthDetail commandName(String commandName) {
    this.commandName = commandName;
    return this;
  }

   /**
   * Action perfromed via manager override.
   * @return commandName
  **/
  
  public String getCommandName() {
    return commandName;
  }

  public void setCommandName(String commandName) {
    this.commandName = commandName;
  }

  public ManagerAuthDetail createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ManagerAuthDetail createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ManagerAuthDetail extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ManagerAuthDetail localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public ManagerAuthDetail managerAuthDetailId(String managerAuthDetailId) {
    this.managerAuthDetailId = managerAuthDetailId;
    return this;
  }

   /**
   * Unique identifier in the Manager Auth Detail
   * @return managerAuthDetailId
  **/
  
  public String getManagerAuthDetailId() {
    return managerAuthDetailId;
  }

  public void setManagerAuthDetailId(String managerAuthDetailId) {
    this.managerAuthDetailId = managerAuthDetailId;
  }

  public ManagerAuthDetail messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ManagerAuthDetail orderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
    return this;
  }

   /**
   * captured orderLineId incase of line level manager overrides
   * @return orderLineId
  **/
  
  public String getOrderLineId() {
    return orderLineId;
  }

  public void setOrderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
  }

  public ManagerAuthDetail orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public ManagerAuthDetail overrideSuccess(Boolean overrideSuccess) {
    this.overrideSuccess = overrideSuccess;
    return this;
  }

   /**
   * Captures the status of manager override authentication.  If passes, it is True, false if it fails.
   * @return overrideSuccess
  **/
  
  public Boolean getOverrideSuccess() {
    return overrideSuccess;
  }

  public void setOverrideSuccess(Boolean overrideSuccess) {
    this.overrideSuccess = overrideSuccess;
  }

  public ManagerAuthDetail PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ManagerAuthDetail updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ManagerAuthDetail updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ManagerAuthDetail entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public ManagerAuthDetail localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ManagerAuthDetail managerAuthDetail = (ManagerAuthDetail) o;
    return Objects.equals(this.actions, managerAuthDetail.actions) &&
        Objects.equals(this.authorizerName, managerAuthDetail.authorizerName) &&
        Objects.equals(this.commandDisplayName, managerAuthDetail.commandDisplayName) &&
        Objects.equals(this.commandName, managerAuthDetail.commandName) &&
        Objects.equals(this.createdBy, managerAuthDetail.createdBy) &&
        Objects.equals(this.createdTimestamp, managerAuthDetail.createdTimestamp) &&
        Objects.equals(this.extended, managerAuthDetail.extended) &&
        Objects.equals(this.localizedTo, managerAuthDetail.localizedTo) &&
        Objects.equals(this.managerAuthDetailId, managerAuthDetail.managerAuthDetailId) &&
        Objects.equals(this.messages, managerAuthDetail.messages) &&
        Objects.equals(this.orderLineId, managerAuthDetail.orderLineId) &&
        Objects.equals(this.orgId, managerAuthDetail.orgId) &&
        Objects.equals(this.overrideSuccess, managerAuthDetail.overrideSuccess) &&
        Objects.equals(this.PK, managerAuthDetail.PK) &&
        Objects.equals(this.updatedBy, managerAuthDetail.updatedBy) &&
        Objects.equals(this.updatedTimestamp, managerAuthDetail.updatedTimestamp) &&
        Objects.equals(this.entityName, managerAuthDetail.entityName) &&
        Objects.equals(this.localize, managerAuthDetail.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, authorizerName, commandDisplayName, commandName, createdBy, createdTimestamp, extended, localizedTo, managerAuthDetailId, messages, orderLineId, orgId, overrideSuccess, PK, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ManagerAuthDetail {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    authorizerName: ").append(toIndentedString(authorizerName)).append("\n");
    sb.append("    commandDisplayName: ").append(toIndentedString(commandDisplayName)).append("\n");
    sb.append("    commandName: ").append(toIndentedString(commandName)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    managerAuthDetailId: ").append(toIndentedString(managerAuthDetailId)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderLineId: ").append(toIndentedString(orderLineId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    overrideSuccess: ").append(toIndentedString(overrideSuccess)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

