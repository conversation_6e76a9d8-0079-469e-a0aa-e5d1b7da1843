package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import lombok.Data;


@Data
public class Header {

    @SerializedName("size")
    private Integer size;

    @SerializedName("page")
    private Integer page;

    @SerializedName("totalCount")
    private String totalCount;


    public Integer getSize() {
        return size;
    }

    public Integer getPage() {
        return page;
    }

    public String getTotalCount() {
        return totalCount;
    }
}
