/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * InvoiceTaxDetailRequest
 */
public class InvoiceTaxDetailRequest {
  public static final String SERIALIZED_NAME_IS_INFORMATIONAL = "isInformational";
  @SerializedName(SERIALIZED_NAME_IS_INFORMATIONAL)
  private Boolean isInformational;

  public static final String SERIALIZED_NAME_TAX_AMOUNT = "taxAmount";
  @SerializedName(SERIALIZED_NAME_TAX_AMOUNT)
  private BigDecimal taxAmount;

  public static final String SERIALIZED_NAME_TAX_DETAIL_ID = "taxDetailId";
  @SerializedName(SERIALIZED_NAME_TAX_DETAIL_ID)
  private String taxDetailId;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER1 = "taxIdentifier1";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER1)
  private String taxIdentifier1;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER2 = "taxIdentifier2";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER2)
  private String taxIdentifier2;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER3 = "taxIdentifier3";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER3)
  private String taxIdentifier3;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER4 = "taxIdentifier4";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER4)
  private String taxIdentifier4;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER5 = "taxIdentifier5";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER5)
  private String taxIdentifier5;

  public static final String SERIALIZED_NAME_TAXABLE_AMOUNT = "taxableAmount";
  @SerializedName(SERIALIZED_NAME_TAXABLE_AMOUNT)
  private BigDecimal taxableAmount;

  public InvoiceTaxDetailRequest isInformational(Boolean isInformational) {
    this.isInformational = isInformational;
    return this;
  }

   /**
   * Get isInformational
   * @return isInformational
  **/
  
  public Boolean getIsInformational() {
    return isInformational;
  }

  public void setIsInformational(Boolean isInformational) {
    this.isInformational = isInformational;
  }

  public InvoiceTaxDetailRequest taxAmount(BigDecimal taxAmount) {
    this.taxAmount = taxAmount;
    return this;
  }

   /**
   * Get taxAmount
   * @return taxAmount
  **/
  
  public BigDecimal getTaxAmount() {
    return taxAmount;
  }

  public void setTaxAmount(BigDecimal taxAmount) {
    this.taxAmount = taxAmount;
  }

  public InvoiceTaxDetailRequest taxDetailId(String taxDetailId) {
    this.taxDetailId = taxDetailId;
    return this;
  }

   /**
   * Get taxDetailId
   * @return taxDetailId
  **/
  
  public String getTaxDetailId() {
    return taxDetailId;
  }

  public void setTaxDetailId(String taxDetailId) {
    this.taxDetailId = taxDetailId;
  }

  public InvoiceTaxDetailRequest taxIdentifier1(String taxIdentifier1) {
    this.taxIdentifier1 = taxIdentifier1;
    return this;
  }

   /**
   * Get taxIdentifier1
   * @return taxIdentifier1
  **/
  
  public String getTaxIdentifier1() {
    return taxIdentifier1;
  }

  public void setTaxIdentifier1(String taxIdentifier1) {
    this.taxIdentifier1 = taxIdentifier1;
  }

  public InvoiceTaxDetailRequest taxIdentifier2(String taxIdentifier2) {
    this.taxIdentifier2 = taxIdentifier2;
    return this;
  }

   /**
   * Get taxIdentifier2
   * @return taxIdentifier2
  **/
  
  public String getTaxIdentifier2() {
    return taxIdentifier2;
  }

  public void setTaxIdentifier2(String taxIdentifier2) {
    this.taxIdentifier2 = taxIdentifier2;
  }

  public InvoiceTaxDetailRequest taxIdentifier3(String taxIdentifier3) {
    this.taxIdentifier3 = taxIdentifier3;
    return this;
  }

   /**
   * Get taxIdentifier3
   * @return taxIdentifier3
  **/
  
  public String getTaxIdentifier3() {
    return taxIdentifier3;
  }

  public void setTaxIdentifier3(String taxIdentifier3) {
    this.taxIdentifier3 = taxIdentifier3;
  }

  public InvoiceTaxDetailRequest taxIdentifier4(String taxIdentifier4) {
    this.taxIdentifier4 = taxIdentifier4;
    return this;
  }

   /**
   * Get taxIdentifier4
   * @return taxIdentifier4
  **/
  
  public String getTaxIdentifier4() {
    return taxIdentifier4;
  }

  public void setTaxIdentifier4(String taxIdentifier4) {
    this.taxIdentifier4 = taxIdentifier4;
  }

  public InvoiceTaxDetailRequest taxIdentifier5(String taxIdentifier5) {
    this.taxIdentifier5 = taxIdentifier5;
    return this;
  }

   /**
   * Get taxIdentifier5
   * @return taxIdentifier5
  **/
  
  public String getTaxIdentifier5() {
    return taxIdentifier5;
  }

  public void setTaxIdentifier5(String taxIdentifier5) {
    this.taxIdentifier5 = taxIdentifier5;
  }

  public InvoiceTaxDetailRequest taxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
    return this;
  }

   /**
   * Get taxableAmount
   * @return taxableAmount
  **/
  
  public BigDecimal getTaxableAmount() {
    return taxableAmount;
  }

  public void setTaxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvoiceTaxDetailRequest invoiceTaxDetailRequest = (InvoiceTaxDetailRequest) o;
    return Objects.equals(this.isInformational, invoiceTaxDetailRequest.isInformational) &&
        Objects.equals(this.taxAmount, invoiceTaxDetailRequest.taxAmount) &&
        Objects.equals(this.taxDetailId, invoiceTaxDetailRequest.taxDetailId) &&
        Objects.equals(this.taxIdentifier1, invoiceTaxDetailRequest.taxIdentifier1) &&
        Objects.equals(this.taxIdentifier2, invoiceTaxDetailRequest.taxIdentifier2) &&
        Objects.equals(this.taxIdentifier3, invoiceTaxDetailRequest.taxIdentifier3) &&
        Objects.equals(this.taxIdentifier4, invoiceTaxDetailRequest.taxIdentifier4) &&
        Objects.equals(this.taxIdentifier5, invoiceTaxDetailRequest.taxIdentifier5) &&
        Objects.equals(this.taxableAmount, invoiceTaxDetailRequest.taxableAmount);
  }

  @Override
  public int hashCode() {
    return Objects.hash(isInformational, taxAmount, taxDetailId, taxIdentifier1, taxIdentifier2, taxIdentifier3, taxIdentifier4, taxIdentifier5, taxableAmount);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvoiceTaxDetailRequest {\n");
    
    sb.append("    isInformational: ").append(toIndentedString(isInformational)).append("\n");
    sb.append("    taxAmount: ").append(toIndentedString(taxAmount)).append("\n");
    sb.append("    taxDetailId: ").append(toIndentedString(taxDetailId)).append("\n");
    sb.append("    taxIdentifier1: ").append(toIndentedString(taxIdentifier1)).append("\n");
    sb.append("    taxIdentifier2: ").append(toIndentedString(taxIdentifier2)).append("\n");
    sb.append("    taxIdentifier3: ").append(toIndentedString(taxIdentifier3)).append("\n");
    sb.append("    taxIdentifier4: ").append(toIndentedString(taxIdentifier4)).append("\n");
    sb.append("    taxIdentifier5: ").append(toIndentedString(taxIdentifier5)).append("\n");
    sb.append("    taxableAmount: ").append(toIndentedString(taxableAmount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

