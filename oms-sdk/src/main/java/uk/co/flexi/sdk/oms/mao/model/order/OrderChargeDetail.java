/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderChargeDetail
 */
public class OrderChargeDetail {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CHARGE_DETAIL_ID = "ChargeDetailId";
  @SerializedName(SERIALIZED_NAME_CHARGE_DETAIL_ID)
  private String chargeDetailId;

  public static final String SERIALIZED_NAME_CHARGE_DISPLAY_NAME = "ChargeDisplayName";
  @SerializedName(SERIALIZED_NAME_CHARGE_DISPLAY_NAME)
  private String chargeDisplayName;

  public static final String SERIALIZED_NAME_CHARGE_PERCENT = "ChargePercent";
  @SerializedName(SERIALIZED_NAME_CHARGE_PERCENT)
  private Double chargePercent;

  public static final String SERIALIZED_NAME_CHARGE_REFERENCE_DETAIL_ID = "ChargeReferenceDetailId";
  @SerializedName(SERIALIZED_NAME_CHARGE_REFERENCE_DETAIL_ID)
  private String chargeReferenceDetailId;

  public static final String SERIALIZED_NAME_CHARGE_REFERENCE_ID = "ChargeReferenceId";
  @SerializedName(SERIALIZED_NAME_CHARGE_REFERENCE_ID)
  private String chargeReferenceId;

  public static final String SERIALIZED_NAME_CHARGE_SEQUENCE = "ChargeSequence";
  @SerializedName(SERIALIZED_NAME_CHARGE_SEQUENCE)
  private Long chargeSequence;

  public static final String SERIALIZED_NAME_CHARGE_SUB_TYPE = "ChargeSubType";
  @SerializedName(SERIALIZED_NAME_CHARGE_SUB_TYPE)
  private ChargeSubTypeId chargeSubType = null;

  public static final String SERIALIZED_NAME_CHARGE_TOTAL = "ChargeTotal";
  @SerializedName(SERIALIZED_NAME_CHARGE_TOTAL)
  private BigDecimal chargeTotal;

  public static final String SERIALIZED_NAME_CHARGE_TYPE = "ChargeType";
  @SerializedName(SERIALIZED_NAME_CHARGE_TYPE)
  private ChargeTypeId chargeType = null;

  public static final String SERIALIZED_NAME_COMMENTS = "Comments";
  @SerializedName(SERIALIZED_NAME_COMMENTS)
  private String comments;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DISCOUNT_ON = "DiscountOn";
  @SerializedName(SERIALIZED_NAME_DISCOUNT_ON)
  private DiscountOnId discountOn = null;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FULFILLMENT_GROUP_ID = "FulfillmentGroupId";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_GROUP_ID)
  private String fulfillmentGroupId;

  public static final String SERIALIZED_NAME_IS_INFORMATIONAL = "IsInformational";
  @SerializedName(SERIALIZED_NAME_IS_INFORMATIONAL)
  private Boolean isInformational;

  public static final String SERIALIZED_NAME_IS_ORDER_DISCOUNT = "IsOrderDiscount";
  @SerializedName(SERIALIZED_NAME_IS_ORDER_DISCOUNT)
  private Boolean isOrderDiscount;

  public static final String SERIALIZED_NAME_IS_OVERRIDDEN = "IsOverridden";
  @SerializedName(SERIALIZED_NAME_IS_OVERRIDDEN)
  private Boolean isOverridden;

  public static final String SERIALIZED_NAME_IS_POST_RETURN = "IsPostReturn";
  @SerializedName(SERIALIZED_NAME_IS_POST_RETURN)
  private Boolean isPostReturn;

  public static final String SERIALIZED_NAME_IS_PRORATED = "IsProrated";
  @SerializedName(SERIALIZED_NAME_IS_PRORATED)
  private Boolean isProrated;

  public static final String SERIALIZED_NAME_IS_PRORATED_AT_SAME_LEVEL = "IsProratedAtSameLevel";
  @SerializedName(SERIALIZED_NAME_IS_PRORATED_AT_SAME_LEVEL)
  private Boolean isProratedAtSameLevel;

  public static final String SERIALIZED_NAME_IS_RETURN_CHARGE = "IsReturnCharge";
  @SerializedName(SERIALIZED_NAME_IS_RETURN_CHARGE)
  private Boolean isReturnCharge;

  public static final String SERIALIZED_NAME_IS_TAX_INCLUDED = "IsTaxIncluded";
  @SerializedName(SERIALIZED_NAME_IS_TAX_INCLUDED)
  private Boolean isTaxIncluded;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_ORIGINAL_CHARGE_AMOUNT = "OriginalChargeAmount";
  @SerializedName(SERIALIZED_NAME_ORIGINAL_CHARGE_AMOUNT)
  private BigDecimal originalChargeAmount;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_CHARGE_DETAIL_ID = "ParentChargeDetailId";
  @SerializedName(SERIALIZED_NAME_PARENT_CHARGE_DETAIL_ID)
  private String parentChargeDetailId;

  public static final String SERIALIZED_NAME_REASON = "Reason";
  @SerializedName(SERIALIZED_NAME_REASON)
  private ReasonId reason = null;

  public static final String SERIALIZED_NAME_RELATED_CHARGE_DETAIL_ID = "RelatedChargeDetailId";
  @SerializedName(SERIALIZED_NAME_RELATED_CHARGE_DETAIL_ID)
  private String relatedChargeDetailId;

  public static final String SERIALIZED_NAME_RELATED_CHARGE_TYPE = "RelatedChargeType";
  @SerializedName(SERIALIZED_NAME_RELATED_CHARGE_TYPE)
  private String relatedChargeType;

  public static final String SERIALIZED_NAME_REQUESTED_AMOUNT = "RequestedAmount";
  @SerializedName(SERIALIZED_NAME_REQUESTED_AMOUNT)
  private BigDecimal requestedAmount;

  public static final String SERIALIZED_NAME_TAX_CODE = "TaxCode";
  @SerializedName(SERIALIZED_NAME_TAX_CODE)
  private String taxCode;

  public static final String SERIALIZED_NAME_TAXABLE_AMOUNT = "TaxableAmount";
  @SerializedName(SERIALIZED_NAME_TAXABLE_AMOUNT)
  private BigDecimal taxableAmount;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderChargeDetail actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderChargeDetail putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderChargeDetail chargeDetailId(String chargeDetailId) {
    this.chargeDetailId = chargeDetailId;
    return this;
  }

   /**
   * Unique identifier of the charge
   * @return chargeDetailId
  **/
  
  public String getChargeDetailId() {
    return chargeDetailId;
  }

  public void setChargeDetailId(String chargeDetailId) {
    this.chargeDetailId = chargeDetailId;
  }

  public OrderChargeDetail chargeDisplayName(String chargeDisplayName) {
    this.chargeDisplayName = chargeDisplayName;
    return this;
  }

   /**
   * Text which should be displayed in the UI
   * @return chargeDisplayName
  **/
  
  public String getChargeDisplayName() {
    return chargeDisplayName;
  }

  public void setChargeDisplayName(String chargeDisplayName) {
    this.chargeDisplayName = chargeDisplayName;
  }

  public OrderChargeDetail chargePercent(Double chargePercent) {
    this.chargePercent = chargePercent;
    return this;
  }

   /**
   * Charge Percent; If ChargeType is Promotion, Coupon, Appeasement or Discount, in case of %Off discounts w.r.t. Order Sub Total.
   * minimum: 0
   * maximum: 999999999999.9999
   * @return chargePercent
  **/
  
  public Double getChargePercent() {
    return chargePercent;
  }

  public void setChargePercent(Double chargePercent) {
    this.chargePercent = chargePercent;
  }

  public OrderChargeDetail chargeReferenceDetailId(String chargeReferenceDetailId) {
    this.chargeReferenceDetailId = chargeReferenceDetailId;
    return this;
  }

   /**
   * Unique sub-Identifier of chargeType, if ChargeType is Coupon, it would be CouponCode
   * @return chargeReferenceDetailId
  **/
  
  public String getChargeReferenceDetailId() {
    return chargeReferenceDetailId;
  }

  public void setChargeReferenceDetailId(String chargeReferenceDetailId) {
    this.chargeReferenceDetailId = chargeReferenceDetailId;
  }

  public OrderChargeDetail chargeReferenceId(String chargeReferenceId) {
    this.chargeReferenceId = chargeReferenceId;
    return this;
  }

   /**
   * Unique Identifier of chargeType, if Charge Type Promotion it would PromotionId, ChargeType is Coupon, it would be CouponId
   * @return chargeReferenceId
  **/
  
  public String getChargeReferenceId() {
    return chargeReferenceId;
  }

  public void setChargeReferenceId(String chargeReferenceId) {
    this.chargeReferenceId = chargeReferenceId;
  }

  public OrderChargeDetail chargeSequence(Long chargeSequence) {
    this.chargeSequence = chargeSequence;
    return this;
  }

   /**
   * This attribute is intended to support stacked discounts. This attribute governs the sequence in which discounts are evaluated.
   * minimum: 0
   * maximum: -8446744073709551617
   * @return chargeSequence
  **/
  
  public Long getChargeSequence() {
    return chargeSequence;
  }

  public void setChargeSequence(Long chargeSequence) {
    this.chargeSequence = chargeSequence;
  }

  public OrderChargeDetail chargeSubType(ChargeSubTypeId chargeSubType) {
    this.chargeSubType = chargeSubType;
    return this;
  }

   /**
   * Get chargeSubType
   * @return chargeSubType
  **/
  
  public ChargeSubTypeId getChargeSubType() {
    return chargeSubType;
  }

  public void setChargeSubType(ChargeSubTypeId chargeSubType) {
    this.chargeSubType = chargeSubType;
  }

  public OrderChargeDetail chargeTotal(BigDecimal chargeTotal) {
    this.chargeTotal = chargeTotal;
    return this;
  }

   /**
   * Total charge; If charge C &#x3D; Ax+ B, then Charge Total &#x3D; C.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return chargeTotal
  **/
  
  public BigDecimal getChargeTotal() {
    return chargeTotal;
  }

  public void setChargeTotal(BigDecimal chargeTotal) {
    this.chargeTotal = chargeTotal;
  }

  public OrderChargeDetail chargeType(ChargeTypeId chargeType) {
    this.chargeType = chargeType;
    return this;
  }

   /**
   * Get chargeType
   * @return chargeType
  **/
  
  public ChargeTypeId getChargeType() {
    return chargeType;
  }

  public void setChargeType(ChargeTypeId chargeType) {
    this.chargeType = chargeType;
  }

  public OrderChargeDetail comments(String comments) {
    this.comments = comments;
    return this;
  }

   /**
   * Placeholder to capture comments for given charge
   * @return comments
  **/
  
  public String getComments() {
    return comments;
  }

  public void setComments(String comments) {
    this.comments = comments;
  }

  public OrderChargeDetail createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderChargeDetail createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderChargeDetail discountOn(DiscountOnId discountOn) {
    this.discountOn = discountOn;
    return this;
  }

   /**
   * Get discountOn
   * @return discountOn
  **/
  
  public DiscountOnId getDiscountOn() {
    return discountOn;
  }

  public void setDiscountOn(DiscountOnId discountOn) {
    this.discountOn = discountOn;
  }

  public OrderChargeDetail extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderChargeDetail fulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
    return this;
  }

   /**
   * ID used to group items which need to be fulfilled together (e.g. warranties, kits, gift with purchase, etc.)
   * @return fulfillmentGroupId
  **/
  
  public String getFulfillmentGroupId() {
    return fulfillmentGroupId;
  }

  public void setFulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
  }

  public OrderChargeDetail isInformational(Boolean isInformational) {
    this.isInformational = isInformational;
    return this;
  }

   /**
   * Indicates a charge is informational and is not taken into account in the order total calculations
   * @return isInformational
  **/
  
  public Boolean getIsInformational() {
    return isInformational;
  }

  public void setIsInformational(Boolean isInformational) {
    this.isInformational = isInformational;
  }

  public OrderChargeDetail isOrderDiscount(Boolean isOrderDiscount) {
    this.isOrderDiscount = isOrderDiscount;
    return this;
  }

   /**
   * DEPRECATED. PLEASE USE discountOn attribute.
   * @return isOrderDiscount
  **/
  
  public Boolean getIsOrderDiscount() {
    return isOrderDiscount;
  }

  public void setIsOrderDiscount(Boolean isOrderDiscount) {
    this.isOrderDiscount = isOrderDiscount;
  }

  public OrderChargeDetail isOverridden(Boolean isOverridden) {
    this.isOverridden = isOverridden;
    return this;
  }

   /**
   * Indicates if the charge is overridden
   * @return isOverridden
  **/
  
  public Boolean getIsOverridden() {
    return isOverridden;
  }

  public void setIsOverridden(Boolean isOverridden) {
    this.isOverridden = isOverridden;
  }

   /**
   * Indicates if a charge applied after one or more quantity is returned.
   * @return isPostReturn
  **/
  
  public Boolean getIsPostReturn() {
    return isPostReturn;
  }

  public OrderChargeDetail isProrated(Boolean isProrated) {
    this.isProrated = isProrated;
    return this;
  }

   /**
   * Indicates a charge has been prorated across order lines
   * @return isProrated
  **/
  
  public Boolean getIsProrated() {
    return isProrated;
  }

  public void setIsProrated(Boolean isProrated) {
    this.isProrated = isProrated;
  }

  public OrderChargeDetail isProratedAtSameLevel(Boolean isProratedAtSameLevel) {
    this.isProratedAtSameLevel = isProratedAtSameLevel;
    return this;
  }

   /**
   * Will be set to true when given discount is on group of charges via relatedChargeType or discount is on entire line.When set to true this discount will not be considered for tax, but the ones that are prorated 
   * @return isProratedAtSameLevel
  **/
  
  public Boolean getIsProratedAtSameLevel() {
    return isProratedAtSameLevel;
  }

  public void setIsProratedAtSameLevel(Boolean isProratedAtSameLevel) {
    this.isProratedAtSameLevel = isProratedAtSameLevel;
  }

  public OrderChargeDetail isReturnCharge(Boolean isReturnCharge) {
    this.isReturnCharge = isReturnCharge;
    return this;
  }

   /**
   * Indicates if a charge applies only to return order lines, such as a restocking or return shipping charge. If true, then header return charges are prorated across order lines where isReturn is true. Return charges are displayed separately in the call center UI and are included in separate order total calculations as part of the order response. Return charges are taxed using the return order creation date, while non-return charges on return orders are taxed using the original order fulfillment date.
   * @return isReturnCharge
  **/
  
  public Boolean getIsReturnCharge() {
    return isReturnCharge;
  }

  public void setIsReturnCharge(Boolean isReturnCharge) {
    this.isReturnCharge = isReturnCharge;
  }

  public OrderChargeDetail isTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
    return this;
  }

   /**
   * Indicates if tax is included in the charge. Included in the requests sent to the tax engine, so that tax can be back-calculated from the charge amount. Used for charges which are inclusive of tax such as return SnH. For example, if return SnH is $10 and taxIncluded is true, then if the tax rate is 10%, the tax engine returns a tax of $1 if isTaxIncluded &#x3D; false and the taxDetail is saved with isInformational &#x3D; false. If isTaxIncluded &#x3D; true, then the tax engine returns a tax of $.91 and the taxDetail is saved with isInformational &#x3D; true.
   * @return isTaxIncluded
  **/
  
  public Boolean getIsTaxIncluded() {
    return isTaxIncluded;
  }

  public void setIsTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
  }

  public OrderChargeDetail localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderChargeDetail messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderChargeDetail orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderChargeDetail originalChargeAmount(BigDecimal originalChargeAmount) {
    this.originalChargeAmount = originalChargeAmount;
    return this;
  }

   /**
   * If IsOverridden is true, this field includes the original charge amount
   * minimum: 0
   * maximum: 99999999999999.98
   * @return originalChargeAmount
  **/
  
  public BigDecimal getOriginalChargeAmount() {
    return originalChargeAmount;
  }

  public void setOriginalChargeAmount(BigDecimal originalChargeAmount) {
    this.originalChargeAmount = originalChargeAmount;
  }

  public OrderChargeDetail PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderChargeDetail parentChargeDetailId(String parentChargeDetailId) {
    this.parentChargeDetailId = parentChargeDetailId;
    return this;
  }

   /**
   * Contains the ChargeDetailId of the discount that is prorated at line  level
   * @return parentChargeDetailId
  **/
  
  public String getParentChargeDetailId() {
    return parentChargeDetailId;
  }

  public void setParentChargeDetailId(String parentChargeDetailId) {
    this.parentChargeDetailId = parentChargeDetailId;
  }

  public OrderChargeDetail reason(ReasonId reason) {
    this.reason = reason;
    return this;
  }

   /**
   * Get reason
   * @return reason
  **/
  
  public ReasonId getReason() {
    return reason;
  }

  public void setReason(ReasonId reason) {
    this.reason = reason;
  }

  public OrderChargeDetail relatedChargeDetailId(String relatedChargeDetailId) {
    this.relatedChargeDetailId = relatedChargeDetailId;
    return this;
  }

   /**
   * Related Charge Detail Id if Promotion/Coupon/discount needs to apply for specific charge Detail id.
   * @return relatedChargeDetailId
  **/
  
  public String getRelatedChargeDetailId() {
    return relatedChargeDetailId;
  }

  public void setRelatedChargeDetailId(String relatedChargeDetailId) {
    this.relatedChargeDetailId = relatedChargeDetailId;
  }

  public OrderChargeDetail relatedChargeType(String relatedChargeType) {
    this.relatedChargeType = relatedChargeType;
    return this;
  }

   /**
   * Related Charge Type if Promotion/Coupon/Discount needs to apply for specific Charge Type e.g. all shipping Charges.
   * @return relatedChargeType
  **/
  
  public String getRelatedChargeType() {
    return relatedChargeType;
  }

  public void setRelatedChargeType(String relatedChargeType) {
    this.relatedChargeType = relatedChargeType;
  }

  public OrderChargeDetail requestedAmount(BigDecimal requestedAmount) {
    this.requestedAmount = requestedAmount;
    return this;
  }

   /**
   * Requested amount, in case of $Off discount / Flat Appeasement
   * minimum: 0
   * maximum: 99999999999999.98
   * @return requestedAmount
  **/
  
  public BigDecimal getRequestedAmount() {
    return requestedAmount;
  }

  public void setRequestedAmount(BigDecimal requestedAmount) {
    this.requestedAmount = requestedAmount;
  }

  public OrderChargeDetail taxCode(String taxCode) {
    this.taxCode = taxCode;
    return this;
  }

   /**
   * Defines the charge category
   * @return taxCode
  **/
  
  public String getTaxCode() {
    return taxCode;
  }

  public void setTaxCode(String taxCode) {
    this.taxCode = taxCode;
  }

  public OrderChargeDetail taxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
    return this;
  }

   /**
   * Get taxableAmount
   * @return taxableAmount
  **/
  
  public BigDecimal getTaxableAmount() {
    return taxableAmount;
  }

  public void setTaxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
  }

  public OrderChargeDetail updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderChargeDetail updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderChargeDetail entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderChargeDetail localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderChargeDetail orderChargeDetail = (OrderChargeDetail) o;
    return Objects.equals(this.actions, orderChargeDetail.actions) &&
        Objects.equals(this.chargeDetailId, orderChargeDetail.chargeDetailId) &&
        Objects.equals(this.chargeDisplayName, orderChargeDetail.chargeDisplayName) &&
        Objects.equals(this.chargePercent, orderChargeDetail.chargePercent) &&
        Objects.equals(this.chargeReferenceDetailId, orderChargeDetail.chargeReferenceDetailId) &&
        Objects.equals(this.chargeReferenceId, orderChargeDetail.chargeReferenceId) &&
        Objects.equals(this.chargeSequence, orderChargeDetail.chargeSequence) &&
        Objects.equals(this.chargeSubType, orderChargeDetail.chargeSubType) &&
        Objects.equals(this.chargeTotal, orderChargeDetail.chargeTotal) &&
        Objects.equals(this.chargeType, orderChargeDetail.chargeType) &&
        Objects.equals(this.comments, orderChargeDetail.comments) &&
        Objects.equals(this.createdBy, orderChargeDetail.createdBy) &&
        Objects.equals(this.createdTimestamp, orderChargeDetail.createdTimestamp) &&
        Objects.equals(this.discountOn, orderChargeDetail.discountOn) &&
        Objects.equals(this.extended, orderChargeDetail.extended) &&
        Objects.equals(this.fulfillmentGroupId, orderChargeDetail.fulfillmentGroupId) &&
        Objects.equals(this.isInformational, orderChargeDetail.isInformational) &&
        Objects.equals(this.isOrderDiscount, orderChargeDetail.isOrderDiscount) &&
        Objects.equals(this.isOverridden, orderChargeDetail.isOverridden) &&
        Objects.equals(this.isPostReturn, orderChargeDetail.isPostReturn) &&
        Objects.equals(this.isProrated, orderChargeDetail.isProrated) &&
        Objects.equals(this.isProratedAtSameLevel, orderChargeDetail.isProratedAtSameLevel) &&
        Objects.equals(this.isReturnCharge, orderChargeDetail.isReturnCharge) &&
        Objects.equals(this.isTaxIncluded, orderChargeDetail.isTaxIncluded) &&
        Objects.equals(this.localizedTo, orderChargeDetail.localizedTo) &&
        Objects.equals(this.messages, orderChargeDetail.messages) &&
        Objects.equals(this.orgId, orderChargeDetail.orgId) &&
        Objects.equals(this.originalChargeAmount, orderChargeDetail.originalChargeAmount) &&
        Objects.equals(this.PK, orderChargeDetail.PK) &&
        Objects.equals(this.parentChargeDetailId, orderChargeDetail.parentChargeDetailId) &&
        Objects.equals(this.reason, orderChargeDetail.reason) &&
        Objects.equals(this.relatedChargeDetailId, orderChargeDetail.relatedChargeDetailId) &&
        Objects.equals(this.relatedChargeType, orderChargeDetail.relatedChargeType) &&
        Objects.equals(this.requestedAmount, orderChargeDetail.requestedAmount) &&
        Objects.equals(this.taxCode, orderChargeDetail.taxCode) &&
        Objects.equals(this.taxableAmount, orderChargeDetail.taxableAmount) &&
        Objects.equals(this.updatedBy, orderChargeDetail.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderChargeDetail.updatedTimestamp) &&
        Objects.equals(this.entityName, orderChargeDetail.entityName) &&
        Objects.equals(this.localize, orderChargeDetail.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, chargeDetailId, chargeDisplayName, chargePercent, chargeReferenceDetailId, chargeReferenceId, chargeSequence, chargeSubType, chargeTotal, chargeType, comments, createdBy, createdTimestamp, discountOn, extended, fulfillmentGroupId, isInformational, isOrderDiscount, isOverridden, isPostReturn, isProrated, isProratedAtSameLevel, isReturnCharge, isTaxIncluded, localizedTo, messages, orgId, originalChargeAmount, PK, parentChargeDetailId, reason, relatedChargeDetailId, relatedChargeType, requestedAmount, taxCode, taxableAmount, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderChargeDetail {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    chargeDetailId: ").append(toIndentedString(chargeDetailId)).append("\n");
    sb.append("    chargeDisplayName: ").append(toIndentedString(chargeDisplayName)).append("\n");
    sb.append("    chargePercent: ").append(toIndentedString(chargePercent)).append("\n");
    sb.append("    chargeReferenceDetailId: ").append(toIndentedString(chargeReferenceDetailId)).append("\n");
    sb.append("    chargeReferenceId: ").append(toIndentedString(chargeReferenceId)).append("\n");
    sb.append("    chargeSequence: ").append(toIndentedString(chargeSequence)).append("\n");
    sb.append("    chargeSubType: ").append(toIndentedString(chargeSubType)).append("\n");
    sb.append("    chargeTotal: ").append(toIndentedString(chargeTotal)).append("\n");
    sb.append("    chargeType: ").append(toIndentedString(chargeType)).append("\n");
    sb.append("    comments: ").append(toIndentedString(comments)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    discountOn: ").append(toIndentedString(discountOn)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    fulfillmentGroupId: ").append(toIndentedString(fulfillmentGroupId)).append("\n");
    sb.append("    isInformational: ").append(toIndentedString(isInformational)).append("\n");
    sb.append("    isOrderDiscount: ").append(toIndentedString(isOrderDiscount)).append("\n");
    sb.append("    isOverridden: ").append(toIndentedString(isOverridden)).append("\n");
    sb.append("    isPostReturn: ").append(toIndentedString(isPostReturn)).append("\n");
    sb.append("    isProrated: ").append(toIndentedString(isProrated)).append("\n");
    sb.append("    isProratedAtSameLevel: ").append(toIndentedString(isProratedAtSameLevel)).append("\n");
    sb.append("    isReturnCharge: ").append(toIndentedString(isReturnCharge)).append("\n");
    sb.append("    isTaxIncluded: ").append(toIndentedString(isTaxIncluded)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    originalChargeAmount: ").append(toIndentedString(originalChargeAmount)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentChargeDetailId: ").append(toIndentedString(parentChargeDetailId)).append("\n");
    sb.append("    reason: ").append(toIndentedString(reason)).append("\n");
    sb.append("    relatedChargeDetailId: ").append(toIndentedString(relatedChargeDetailId)).append("\n");
    sb.append("    relatedChargeType: ").append(toIndentedString(relatedChargeType)).append("\n");
    sb.append("    requestedAmount: ").append(toIndentedString(requestedAmount)).append("\n");
    sb.append("    taxCode: ").append(toIndentedString(taxCode)).append("\n");
    sb.append("    taxableAmount: ").append(toIndentedString(taxableAmount)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

