/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.*;

/**
 * PaymentTransaction
 */
public class PaymentTransaction {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_EXTERNAL_RESPONSE_ID = "ExternalResponseId";
  @SerializedName(SERIALIZED_NAME_EXTERNAL_RESPONSE_ID)
  private String externalResponseId;

  public static final String SERIALIZED_NAME_FOLLOW_ON_COUNT = "FollowOnCount";
  @SerializedName(SERIALIZED_NAME_FOLLOW_ON_COUNT)
  private Integer followOnCount;

  public static final String SERIALIZED_NAME_FOLLOW_ON_ID = "FollowOnId";
  @SerializedName(SERIALIZED_NAME_FOLLOW_ON_ID)
  private String followOnId;

  public static final String SERIALIZED_NAME_FOLLOW_ON_PROCESSED_AMOUNT = "FollowOnProcessedAmount";
  @SerializedName(SERIALIZED_NAME_FOLLOW_ON_PROCESSED_AMOUNT)
  private BigDecimal followOnProcessedAmount;

  public static final String SERIALIZED_NAME_FOLLOW_ON_TOKEN = "FollowOnToken";
  @SerializedName(SERIALIZED_NAME_FOLLOW_ON_TOKEN)
  private String followOnToken;

  public static final String SERIALIZED_NAME_INTERACTION_MODE = "InteractionMode";
  @SerializedName(SERIALIZED_NAME_INTERACTION_MODE)
  private InteractionModeId interactionMode = null;

  public static final String SERIALIZED_NAME_IS_ACTIVATION = "IsActivation";
  @SerializedName(SERIALIZED_NAME_IS_ACTIVATION)
  private Boolean isActivation;

  public static final String SERIALIZED_NAME_IS_ACTIVE = "IsActive";
  @SerializedName(SERIALIZED_NAME_IS_ACTIVE)
  private Boolean isActive;

  public static final String SERIALIZED_NAME_IS_COPIED = "IsCopied";
  @SerializedName(SERIALIZED_NAME_IS_COPIED)
  private Boolean isCopied;

  public static final String SERIALIZED_NAME_IS_VALID_FOR_REFUND = "IsValidForRefund";
  @SerializedName(SERIALIZED_NAME_IS_VALID_FOR_REFUND)
  private Boolean isValidForRefund;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_ID = "OrderId";
  @SerializedName(SERIALIZED_NAME_ORDER_ID)
  private String orderId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PAYMENT_GROUP_ID = "PaymentGroupId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_GROUP_ID)
  private String paymentGroupId;

  public static final String SERIALIZED_NAME_PAYMENT_RESPONSE_STATUS = "PaymentResponseStatus";
  @SerializedName(SERIALIZED_NAME_PAYMENT_RESPONSE_STATUS)
  private PaymentResponseStatusId paymentResponseStatus = null;

  public static final String SERIALIZED_NAME_PAYMENT_TRANS_ATTRIBUTE = "PaymentTransAttribute";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TRANS_ATTRIBUTE)
  private List<PaymentTransAttribute> paymentTransAttribute = null;

  public static final String SERIALIZED_NAME_PAYMENT_TRANS_ENCR_ATTRIBUTE = "PaymentTransEncrAttribute";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TRANS_ENCR_ATTRIBUTE)
  private List<PaymentTransEncrAttribute> paymentTransEncrAttribute = null;

  public static final String SERIALIZED_NAME_PAYMENT_TRANSACTION_DETAIL = "PaymentTransactionDetail";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TRANSACTION_DETAIL)
  private List<PaymentTransactionDetail> paymentTransactionDetail = null;

  public static final String SERIALIZED_NAME_PAYMENT_TRANSACTION_E_M_V_TAGS = "PaymentTransactionEMVTags";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TRANSACTION_E_M_V_TAGS)
  private PaymentTransactionEMVTags paymentTransactionEMVTags = null;

  public static final String SERIALIZED_NAME_PAYMENT_TRANSACTION_ID = "PaymentTransactionId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TRANSACTION_ID)
  private String paymentTransactionId;

  public static final String SERIALIZED_NAME_PROCESSED_AMOUNT = "ProcessedAmount";
  @SerializedName(SERIALIZED_NAME_PROCESSED_AMOUNT)
  private BigDecimal processedAmount;

  public static final String SERIALIZED_NAME_PROCESSING_MODE = "ProcessingMode";
  @SerializedName(SERIALIZED_NAME_PROCESSING_MODE)
  private ProcessingModeId processingMode = null;

  public static final String SERIALIZED_NAME_RE_AUTH_ON_SETTLEMENT_FAILURE = "ReAuthOnSettlementFailure";
  @SerializedName(SERIALIZED_NAME_RE_AUTH_ON_SETTLEMENT_FAILURE)
  private Boolean reAuthOnSettlementFailure;

  public static final String SERIALIZED_NAME_REASON_ID = "ReasonId";
  @SerializedName(SERIALIZED_NAME_REASON_ID)
  private String reasonId;

  public static final String SERIALIZED_NAME_RECONCILIATION_ID = "ReconciliationId";
  @SerializedName(SERIALIZED_NAME_RECONCILIATION_ID)
  private String reconciliationId;

  public static final String SERIALIZED_NAME_REMAINING_ATTEMPTS = "RemainingAttempts";
  @SerializedName(SERIALIZED_NAME_REMAINING_ATTEMPTS)
  private Integer remainingAttempts;

  public static final String SERIALIZED_NAME_REMAINING_BALANCE = "RemainingBalance";
  @SerializedName(SERIALIZED_NAME_REMAINING_BALANCE)
  private BigDecimal remainingBalance;

  public static final String SERIALIZED_NAME_REQUEST_ID = "RequestId";
  @SerializedName(SERIALIZED_NAME_REQUEST_ID)
  private String requestId;

  public static final String SERIALIZED_NAME_REQUEST_TOKEN = "RequestToken";
  @SerializedName(SERIALIZED_NAME_REQUEST_TOKEN)
  private String requestToken;

  public static final String SERIALIZED_NAME_REQUESTED_AMOUNT = "RequestedAmount";
  @SerializedName(SERIALIZED_NAME_REQUESTED_AMOUNT)
  private BigDecimal requestedAmount;

  public static final String SERIALIZED_NAME_REQUESTED_DATE = "RequestedDate";
  @SerializedName(SERIALIZED_NAME_REQUESTED_DATE)
  private OffsetDateTime requestedDate;

  public static final String SERIALIZED_NAME_SCHEDULED_TIMESTAMP = "ScheduledTimestamp";
  @SerializedName(SERIALIZED_NAME_SCHEDULED_TIMESTAMP)
  private OffsetDateTime scheduledTimestamp;

  public static final String SERIALIZED_NAME_STATUS = "Status";
  @SerializedName(SERIALIZED_NAME_STATUS)
  private PaymentTransactionStatusId status = null;

  public static final String SERIALIZED_NAME_STORE_AND_FORWARD_NUMBER = "StoreAndForwardNumber";
  @SerializedName(SERIALIZED_NAME_STORE_AND_FORWARD_NUMBER)
  private String storeAndForwardNumber;

  public static final String SERIALIZED_NAME_TRANSACTION_DATE = "TransactionDate";
  @SerializedName(SERIALIZED_NAME_TRANSACTION_DATE)
  private OffsetDateTime transactionDate;

  public static final String SERIALIZED_NAME_TRANSACTION_EXPIRY_DATE = "TransactionExpiryDate";
  @SerializedName(SERIALIZED_NAME_TRANSACTION_EXPIRY_DATE)
  private OffsetDateTime transactionExpiryDate;

  public static final String SERIALIZED_NAME_TRANSACTION_TYPE = "TransactionType";
  @SerializedName(SERIALIZED_NAME_TRANSACTION_TYPE)
  private PaymentTransactionTypeId transactionType = null;

  public static final String SERIALIZED_NAME_TRANSMISSION_STATUS = "TransmissionStatus";
  @SerializedName(SERIALIZED_NAME_TRANSMISSION_STATUS)
  private PaymentTransmissionStatusId transmissionStatus = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public PaymentTransaction actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public PaymentTransaction putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public PaymentTransaction createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public PaymentTransaction createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public PaymentTransaction extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public PaymentTransaction externalResponseId(String externalResponseId) {
    this.externalResponseId = externalResponseId;
    return this;
  }

   /**
   * Response code of the payment transaction returned by the payment gateway. Unique to the gateway which processed the payment and used to gather further details about the outcome of a transaction. For each external response id, a description can be found in the gateway response mapping.
   * @return externalResponseId
  **/
  
  public String getExternalResponseId() {
    return externalResponseId;
  }

  public void setExternalResponseId(String externalResponseId) {
    this.externalResponseId = externalResponseId;
  }

  public PaymentTransaction followOnCount(Integer followOnCount) {
    this.followOnCount = followOnCount;
    return this;
  }

   /**
   * Number of follow-on payment transactions which exist against this parent payment transaction. Defaults to 0 if not included in the interface (e.g. for cash). Used for traceability. For example, if an authorization has two settlements, then the authorization followOnCount &#x3D; 2.
   * minimum: 0
   * maximum: 99999
   * @return followOnCount
  **/
  
  public Integer getFollowOnCount() {
    return followOnCount;
  }

  public void setFollowOnCount(Integer followOnCount) {
    this.followOnCount = followOnCount;
  }

  public PaymentTransaction followOnId(String followOnId) {
    this.followOnId = followOnId;
    return this;
  }

   /**
   * Identifier of the original requestId to be used for follow-on payment transactions. Populated only for follow-on transactions. For example, if an authorization has requestId A, then when a follow-on settlement is created, the settlement has followOnId A.
   * @return followOnId
  **/
  
  public String getFollowOnId() {
    return followOnId;
  }

  public void setFollowOnId(String followOnId) {
    this.followOnId = followOnId;
  }

  public PaymentTransaction followOnProcessedAmount(BigDecimal followOnProcessedAmount) {
    this.followOnProcessedAmount = followOnProcessedAmount;
    return this;
  }

   /**
   * Amount from this transaction which has been used in follow-on transactions; For example, the total amount which has been settled for an authorization transaction, or the total amount which has been refunded for a settlement transaction. Used to determine how much can be processed against this transaction. For example, if an authorization with $10 processed amount has $10 follow on processed amount, then this authorization cannot be used for further settlements.
   * minimum: 0
   * maximum: **************.98
   * @return followOnProcessedAmount
  **/
  
  public BigDecimal getFollowOnProcessedAmount() {
    return followOnProcessedAmount;
  }

  public void setFollowOnProcessedAmount(BigDecimal followOnProcessedAmount) {
    this.followOnProcessedAmount = followOnProcessedAmount;
  }

  public PaymentTransaction followOnToken(String followOnToken) {
    this.followOnToken = followOnToken;
    return this;
  }

   /**
   * Identifier of the original requestToken to be used for follow-on payment transactions. Populated only for follow-on transactions. For example, if an authorization has requestToken B, then when a follow-on settlement is created, the settlement has followOnToken B.
   * @return followOnToken
  **/
  
  public String getFollowOnToken() {
    return followOnToken;
  }

  public void setFollowOnToken(String followOnToken) {
    this.followOnToken = followOnToken;
  }

  public PaymentTransaction interactionMode(InteractionModeId interactionMode) {
    this.interactionMode = interactionMode;
    return this;
  }

   /**
   * Get interactionMode
   * @return interactionMode
  **/
  
  public InteractionModeId getInteractionMode() {
    return interactionMode;
  }

  public void setInteractionMode(InteractionModeId interactionMode) {
    this.interactionMode = interactionMode;
  }

  public PaymentTransaction isActivation(Boolean isActivation) {
    this.isActivation = isActivation;
    return this;
  }

   /**
   * Indicates if a refund transaction is a gift card activation transaction. For example, if a customer returns an item and is issued a refund in the form of a new gift card, then an activation transaction is processed in the payment gateway to issue the new gift card, and a refund payment transaction is saved with isActivation &#x3D; true. This differs from transactions with transaction type Activation, which is used to represent the purchasing of a gift card item and activation of the card, as opposed to activating a new gift card for a refund. This field is informational and does not affect the payment calculations.
   * @return isActivation
  **/
  
  public Boolean getIsActivation() {
    return isActivation;
  }

  public void setIsActivation(Boolean isActivation) {
    this.isActivation = isActivation;
  }

   /**
   * Indicates if a payment transaction is active and available for use. This is a read-only attribute which is true by default. This field is updated to false when an authorization expires. Payment transactions with isActive set to false are ignored for all payment processes, including follow-on settlements, payment summary updates, payment method updates, and payment status.
   * @return isActive
  **/
  
  public Boolean getIsActive() {
    return isActive;
  }

  public void setIsActive(Boolean isActive) {
    this.isActive = isActive;
  }

  public PaymentTransaction isCopied(Boolean isCopied) {
    this.isCopied = isCopied;
    return this;
  }

   /**
   * Indicates if a transaction is copied from the parent order, in case of returns. When a return is created, return credit is carried from the original order to the return order by copying a payment method and settlement transactions to the return order. Only settlements which are closed, successful, and valid for return are copied for a total processed amount equal to the return invoice total. These copied settlements are used for performing-follow on refunds in case of a pure return or an exchange where the exchange item value is less than the return item value.
   * @return isCopied
  **/
  
  public Boolean getIsCopied() {
    return isCopied;
  }

  public void setIsCopied(Boolean isCopied) {
    this.isCopied = isCopied;
  }

  public PaymentTransaction isValidForRefund(Boolean isValidForRefund) {
    this.isValidForRefund = isValidForRefund;
    return this;
  }

   /**
   * Indicates if a settlement transaction can be used for a follow-on refund. This flag is updated to false when a follow-on refund fails, or when a settlement is expired and the payment gateway does not support standalone refunds. For example, if a purchase is made on January 1 using a credit card, then the settlement expiry date is calculated as April 1 based on the gateway. When the customer returns the item on September 1, then because the settlement is expired, payment checks if the gateway supports standalone refunds. If not, then this flag is set to false on the settlement so that it does not get picked up for future refunds.
   * @return isValidForRefund
  **/
  
  public Boolean getIsValidForRefund() {
    return isValidForRefund;
  }

  public void setIsValidForRefund(Boolean isValidForRefund) {
    this.isValidForRefund = isValidForRefund;
  }

  public PaymentTransaction localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public PaymentTransaction messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

   /**
   * Order against which the payment transaction is processed. This is used to group transactions by order when executing open payment transactions.
   * @return orderId
  **/
  
  public String getOrderId() {
    return orderId;
  }

  public PaymentTransaction orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public PaymentTransaction PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

   /**
   * Order payment group against which the payment transaction is processed. This is used to group transactions by payment group when executing open payment transactions. 
   * @return paymentGroupId
  **/
  
  public String getPaymentGroupId() {
    return paymentGroupId;
  }

  public PaymentTransaction paymentResponseStatus(PaymentResponseStatusId paymentResponseStatus) {
    this.paymentResponseStatus = paymentResponseStatus;
    return this;
  }

   /**
   * Get paymentResponseStatus
   * @return paymentResponseStatus
  **/
  
  public PaymentResponseStatusId getPaymentResponseStatus() {
    return paymentResponseStatus;
  }

  public void setPaymentResponseStatus(PaymentResponseStatusId paymentResponseStatus) {
    this.paymentResponseStatus = paymentResponseStatus;
  }

  public PaymentTransaction paymentTransAttribute(List<PaymentTransAttribute> paymentTransAttribute) {
    this.paymentTransAttribute = paymentTransAttribute;
    return this;
  }

  public PaymentTransaction addPaymentTransAttributeItem(PaymentTransAttribute paymentTransAttributeItem) {
    if (this.paymentTransAttribute == null) {
      this.paymentTransAttribute = new ArrayList<PaymentTransAttribute>();
    }
    this.paymentTransAttribute.add(paymentTransAttributeItem);
    return this;
  }

   /**
   * Get paymentTransAttribute
   * @return paymentTransAttribute
  **/
  
  public List<PaymentTransAttribute> getPaymentTransAttribute() {
    return paymentTransAttribute;
  }

  public void setPaymentTransAttribute(List<PaymentTransAttribute> paymentTransAttribute) {
    this.paymentTransAttribute = paymentTransAttribute;
  }

  public PaymentTransaction paymentTransEncrAttribute(List<PaymentTransEncrAttribute> paymentTransEncrAttribute) {
    this.paymentTransEncrAttribute = paymentTransEncrAttribute;
    return this;
  }

  public PaymentTransaction addPaymentTransEncrAttributeItem(PaymentTransEncrAttribute paymentTransEncrAttributeItem) {
    if (this.paymentTransEncrAttribute == null) {
      this.paymentTransEncrAttribute = new ArrayList<PaymentTransEncrAttribute>();
    }
    this.paymentTransEncrAttribute.add(paymentTransEncrAttributeItem);
    return this;
  }

   /**
   * Get paymentTransEncrAttribute
   * @return paymentTransEncrAttribute
  **/
  
  public List<PaymentTransEncrAttribute> getPaymentTransEncrAttribute() {
    return paymentTransEncrAttribute;
  }

  public void setPaymentTransEncrAttribute(List<PaymentTransEncrAttribute> paymentTransEncrAttribute) {
    this.paymentTransEncrAttribute = paymentTransEncrAttribute;
  }

  public PaymentTransaction paymentTransactionDetail(List<PaymentTransactionDetail> paymentTransactionDetail) {
    this.paymentTransactionDetail = paymentTransactionDetail;
    return this;
  }

  public PaymentTransaction addPaymentTransactionDetailItem(PaymentTransactionDetail paymentTransactionDetailItem) {
    if (this.paymentTransactionDetail == null) {
      this.paymentTransactionDetail = new ArrayList<PaymentTransactionDetail>();
    }
    this.paymentTransactionDetail.add(paymentTransactionDetailItem);
    return this;
  }

   /**
   * Get paymentTransactionDetail
   * @return paymentTransactionDetail
  **/
  
  public List<PaymentTransactionDetail> getPaymentTransactionDetail() {
    return paymentTransactionDetail;
  }

  public void setPaymentTransactionDetail(List<PaymentTransactionDetail> paymentTransactionDetail) {
    this.paymentTransactionDetail = paymentTransactionDetail;
  }

  public PaymentTransaction paymentTransactionEMVTags(PaymentTransactionEMVTags paymentTransactionEMVTags) {
    this.paymentTransactionEMVTags = paymentTransactionEMVTags;
    return this;
  }

   /**
   * Get paymentTransactionEMVTags
   * @return paymentTransactionEMVTags
  **/
  
  public PaymentTransactionEMVTags getPaymentTransactionEMVTags() {
    return paymentTransactionEMVTags;
  }

  public void setPaymentTransactionEMVTags(PaymentTransactionEMVTags paymentTransactionEMVTags) {
    this.paymentTransactionEMVTags = paymentTransactionEMVTags;
  }

  public PaymentTransaction paymentTransactionId(String paymentTransactionId) {
    this.paymentTransactionId = paymentTransactionId;
    return this;
  }

   /**
   * Unique identifier of the payment transaction, as defined by an external system. If not defined by an external system, then the payment component populates this field.
   * @return paymentTransactionId
  **/
  
  public String getPaymentTransactionId() {
    return paymentTransactionId;
  }

  public void setPaymentTransactionId(String paymentTransactionId) {
    this.paymentTransactionId = paymentTransactionId;
  }

  public PaymentTransaction processedAmount(BigDecimal processedAmount) {
    this.processedAmount = processedAmount;
    return this;
  }

   /**
   * Amount currently processed in the payment transaction; If an authorization is reduced, this field contains the amount authorized in OM but is not indicative of the amount held by the bank.
   * minimum: 0
   * maximum: **************.98
   * @return processedAmount
  **/
  
  public BigDecimal getProcessedAmount() {
    return processedAmount;
  }

  public void setProcessedAmount(BigDecimal processedAmount) {
    this.processedAmount = processedAmount;
  }

  public PaymentTransaction processingMode(ProcessingModeId processingMode) {
    this.processingMode = processingMode;
    return this;
  }

   /**
   * Get processingMode
   * @return processingMode
  **/
  
  public ProcessingModeId getProcessingMode() {
    return processingMode;
  }

  public void setProcessingMode(ProcessingModeId processingMode) {
    this.processingMode = processingMode;
  }

  public PaymentTransaction reAuthOnSettlementFailure(Boolean reAuthOnSettlementFailure) {
    this.reAuthOnSettlementFailure = reAuthOnSettlementFailure;
    return this;
  }

   /**
   * Indicates if a new authorization should be created in case settlement fails. Used in PayPal flows when settlement is executed after the authorization honor period, in which case a new authorization must be secured before settling. For example, if the honor period is 3 days and an authorization is secured on Monday, then when the order ships on Friday, a settlement is executed against the auth. The response from PayPal indicates that settlement failed and a new authorization is required, so this flag is set to true on the failed settlement. Payment reads this flag and immediately creates a new authorization for the open amount remaining on the payment method. After securing a new auth, the settlement is executed against this auth.
   * @return reAuthOnSettlementFailure
  **/
  
  public Boolean getReAuthOnSettlementFailure() {
    return reAuthOnSettlementFailure;
  }

  public void setReAuthOnSettlementFailure(Boolean reAuthOnSettlementFailure) {
    this.reAuthOnSettlementFailure = reAuthOnSettlementFailure;
  }

  public PaymentTransaction reasonId(String reasonId) {
    this.reasonId = reasonId;
    return this;
  }

   /**
   * Reason code associated with the transaction
   * @return reasonId
  **/
  
  public String getReasonId() {
    return reasonId;
  }

  public void setReasonId(String reasonId) {
    this.reasonId = reasonId;
  }

  public PaymentTransaction reconciliationId(String reconciliationId) {
    this.reconciliationId = reconciliationId;
    return this;
  }

   /**
   * Reconciliation id provided by the payment gateway. Used for reconciling transactions between the payment component and the payment gateway.
   * @return reconciliationId
  **/
  
  public String getReconciliationId() {
    return reconciliationId;
  }

  public void setReconciliationId(String reconciliationId) {
    this.reconciliationId = reconciliationId;
  }

  public PaymentTransaction remainingAttempts(Integer remainingAttempts) {
    this.remainingAttempts = remainingAttempts;
    return this;
  }

   /**
   * Number of attempts remaining for the transaction to be processed. Populated during transaction creation by reading the payment configuration. If connectivity to the gateway cannot be established and the responseStatus is unavailable, then the remaining attempts count is used to determine how many times the payment component can retry sending this transaction to the gateway. For example, if remainingAttempts is 2 and and unavailable response is received, then a new transaction is created with remainingAttempts &#x3D; 1, and that transaction is sent. When remainingAttempts &#x3D; 0, a transaction is not retried.
   * minimum: 0
   * maximum: 99999
   * @return remainingAttempts
  **/
  
  public Integer getRemainingAttempts() {
    return remainingAttempts;
  }

  public void setRemainingAttempts(Integer remainingAttempts) {
    this.remainingAttempts = remainingAttempts;
  }

  public PaymentTransaction remainingBalance(BigDecimal remainingBalance) {
    this.remainingBalance = remainingBalance;
    return this;
  }

   /**
   * Remaining account balance for gift card and store credit payments. Used for display and informational purposes. The remaining balance is populated in balance check transactions based on the payment gateway response. For example, if requested amount is 10 and account balance is 5, then remaining balance is 0. If requested amount is 10 and account balance is 100, then remaining balance is 90.
   * minimum: 0
   * maximum: **************.98
   * @return remainingBalance
  **/
  
  public BigDecimal getRemainingBalance() {
    return remainingBalance;
  }

  public void setRemainingBalance(BigDecimal remainingBalance) {
    this.remainingBalance = remainingBalance;
  }

  public PaymentTransaction requestId(String requestId) {
    this.requestId = requestId;
    return this;
  }

   /**
   * Identifier of the request sent to the payment processor. Populated by the payment gateway and used to uniquely identify the transaction in the third party system for any reconciliation or troubleshooting. Used to populate the followOnId for follow-on payment transactions.
   * @return requestId
  **/
  
  public String getRequestId() {
    return requestId;
  }

  public void setRequestId(String requestId) {
    this.requestId = requestId;
  }

  public PaymentTransaction requestToken(String requestToken) {
    this.requestToken = requestToken;
    return this;
  }

   /**
   * Unique token of the request sent to the payment processor. Populated by the payment gateway and used to uniquely identify the transaction in the third party system for any reconciliation or troubleshooting. Used to populate the followOnToken for follow-on payment transactions.
   * @return requestToken
  **/
  
  public String getRequestToken() {
    return requestToken;
  }

  public void setRequestToken(String requestToken) {
    this.requestToken = requestToken;
  }

  public PaymentTransaction requestedAmount(BigDecimal requestedAmount) {
    this.requestedAmount = requestedAmount;
    return this;
  }

   /**
   * Amount requested to be processed, as input by the customer. Used as a record of the requested amount as compared with the actual processed amount. For example, if a customer purchases a $200 item in store and pays using credit card, then a settlement transaction with $200 requested amount is sent to the gateway. If the gateway responds with a processed amount of only $50, then the requested amount remains $200 as a record of what was requested.
   * minimum: 0
   * maximum: **************.98
   * @return requestedAmount
  **/
  
  public BigDecimal getRequestedAmount() {
    return requestedAmount;
  }

  public void setRequestedAmount(BigDecimal requestedAmount) {
    this.requestedAmount = requestedAmount;
  }

  public PaymentTransaction requestedDate(OffsetDateTime requestedDate) {
    this.requestedDate = requestedDate;
    return this;
  }

   /**
   * Timestamp that the request was sent to the payment gateway. Used for auditing purposes.
   * @return requestedDate
  **/
  
  public OffsetDateTime getRequestedDate() {
    return requestedDate;
  }

  public void setRequestedDate(OffsetDateTime requestedDate) {
    this.requestedDate = requestedDate;
  }

  public PaymentTransaction scheduledTimestamp(OffsetDateTime scheduledTimestamp) {
    this.scheduledTimestamp = scheduledTimestamp;
    return this;
  }

   /**
   * The time at which this transaction is schedule for execution. When the payment scheduler picks up open transactions and sends them to the payment gateway for execution, it picks up transactions which have a scheduledTimestamp which is in the past or null. This timestamp is populated based on the paymentConfiguration table for the selected payment rule. For example, if the payment configuration attribute is set to 3 hours and a transaction is created on January 1 at 1:00 PM, then the scheduled timestamp is set to January 1 at 4:00 PM. 
   * @return scheduledTimestamp
  **/
  
  public OffsetDateTime getScheduledTimestamp() {
    return scheduledTimestamp;
  }

  public void setScheduledTimestamp(OffsetDateTime scheduledTimestamp) {
    this.scheduledTimestamp = scheduledTimestamp;
  }

  public PaymentTransaction status(PaymentTransactionStatusId status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  
  public PaymentTransactionStatusId getStatus() {
    return status;
  }

  public void setStatus(PaymentTransactionStatusId status) {
    this.status = status;
  }

  public PaymentTransaction storeAndForwardNumber(String storeAndForwardNumber) {
    this.storeAndForwardNumber = storeAndForwardNumber;
    return this;
  }

   /**
   * This field is populated with the number generated by payment collecting devices like MX terminal, when it processes the payment internally,  if it is SAF enabled. This takes place when it is unable to get the payment processed by interacting with payment processes.  This field is relevant to  Point of Sale (POS), where payment devices are in use.
   * @return storeAndForwardNumber
  **/
  
  public String getStoreAndForwardNumber() {
    return storeAndForwardNumber;
  }

  public void setStoreAndForwardNumber(String storeAndForwardNumber) {
    this.storeAndForwardNumber = storeAndForwardNumber;
  }

  public PaymentTransaction transactionDate(OffsetDateTime transactionDate) {
    this.transactionDate = transactionDate;
    return this;
  }

   /**
   * Timestamp that the transaction was processed by the payment gateway. Populated by the payment gateway. Used for auditing purposes.
   * @return transactionDate
  **/
  
  public OffsetDateTime getTransactionDate() {
    return transactionDate;
  }

  public void setTransactionDate(OffsetDateTime transactionDate) {
    this.transactionDate = transactionDate;
  }

  public PaymentTransaction transactionExpiryDate(OffsetDateTime transactionExpiryDate) {
    this.transactionExpiryDate = transactionExpiryDate;
    return this;
  }

   /**
   * Expiration date of the transaction. Populated by the payment gateway. If not populated by the gateway, then populated using the payment configuration settlementExpiryDays or authorizationExpiryDays. Auth expiry days is used to mark an auth as expired and trigger re-authorization. Settlement expiry days is used to determine whether a follow-on refund can be performed; if a settlement is expired, a standalone refund is performed.
   * @return transactionExpiryDate
  **/
  
  public OffsetDateTime getTransactionExpiryDate() {
    return transactionExpiryDate;
  }

  public void setTransactionExpiryDate(OffsetDateTime transactionExpiryDate) {
    this.transactionExpiryDate = transactionExpiryDate;
  }

  public PaymentTransaction transactionType(PaymentTransactionTypeId transactionType) {
    this.transactionType = transactionType;
    return this;
  }

   /**
   * Get transactionType
   * @return transactionType
  **/
  
  public PaymentTransactionTypeId getTransactionType() {
    return transactionType;
  }

  public void setTransactionType(PaymentTransactionTypeId transactionType) {
    this.transactionType = transactionType;
  }

  public PaymentTransaction transmissionStatus(PaymentTransmissionStatusId transmissionStatus) {
    this.transmissionStatus = transmissionStatus;
    return this;
  }

   /**
   * Get transmissionStatus
   * @return transmissionStatus
  **/
  
  public PaymentTransmissionStatusId getTransmissionStatus() {
    return transmissionStatus;
  }

  public void setTransmissionStatus(PaymentTransmissionStatusId transmissionStatus) {
    this.transmissionStatus = transmissionStatus;
  }

  public PaymentTransaction updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public PaymentTransaction updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public PaymentTransaction entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public PaymentTransaction localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentTransaction paymentTransaction = (PaymentTransaction) o;
    return Objects.equals(this.actions, paymentTransaction.actions) &&
        Objects.equals(this.createdBy, paymentTransaction.createdBy) &&
        Objects.equals(this.createdTimestamp, paymentTransaction.createdTimestamp) &&
        Objects.equals(this.extended, paymentTransaction.extended) &&
        Objects.equals(this.externalResponseId, paymentTransaction.externalResponseId) &&
        Objects.equals(this.followOnCount, paymentTransaction.followOnCount) &&
        Objects.equals(this.followOnId, paymentTransaction.followOnId) &&
        Objects.equals(this.followOnProcessedAmount, paymentTransaction.followOnProcessedAmount) &&
        Objects.equals(this.followOnToken, paymentTransaction.followOnToken) &&
        Objects.equals(this.interactionMode, paymentTransaction.interactionMode) &&
        Objects.equals(this.isActivation, paymentTransaction.isActivation) &&
        Objects.equals(this.isActive, paymentTransaction.isActive) &&
        Objects.equals(this.isCopied, paymentTransaction.isCopied) &&
        Objects.equals(this.isValidForRefund, paymentTransaction.isValidForRefund) &&
        Objects.equals(this.localizedTo, paymentTransaction.localizedTo) &&
        Objects.equals(this.messages, paymentTransaction.messages) &&
        Objects.equals(this.orderId, paymentTransaction.orderId) &&
        Objects.equals(this.orgId, paymentTransaction.orgId) &&
        Objects.equals(this.PK, paymentTransaction.PK) &&
        Objects.equals(this.paymentGroupId, paymentTransaction.paymentGroupId) &&
        Objects.equals(this.paymentResponseStatus, paymentTransaction.paymentResponseStatus) &&
        Objects.equals(this.paymentTransAttribute, paymentTransaction.paymentTransAttribute) &&
        Objects.equals(this.paymentTransEncrAttribute, paymentTransaction.paymentTransEncrAttribute) &&
        Objects.equals(this.paymentTransactionDetail, paymentTransaction.paymentTransactionDetail) &&
        Objects.equals(this.paymentTransactionEMVTags, paymentTransaction.paymentTransactionEMVTags) &&
        Objects.equals(this.paymentTransactionId, paymentTransaction.paymentTransactionId) &&
        Objects.equals(this.processedAmount, paymentTransaction.processedAmount) &&
        Objects.equals(this.processingMode, paymentTransaction.processingMode) &&
        Objects.equals(this.reAuthOnSettlementFailure, paymentTransaction.reAuthOnSettlementFailure) &&
        Objects.equals(this.reasonId, paymentTransaction.reasonId) &&
        Objects.equals(this.reconciliationId, paymentTransaction.reconciliationId) &&
        Objects.equals(this.remainingAttempts, paymentTransaction.remainingAttempts) &&
        Objects.equals(this.remainingBalance, paymentTransaction.remainingBalance) &&
        Objects.equals(this.requestId, paymentTransaction.requestId) &&
        Objects.equals(this.requestToken, paymentTransaction.requestToken) &&
        Objects.equals(this.requestedAmount, paymentTransaction.requestedAmount) &&
        Objects.equals(this.requestedDate, paymentTransaction.requestedDate) &&
        Objects.equals(this.scheduledTimestamp, paymentTransaction.scheduledTimestamp) &&
        Objects.equals(this.status, paymentTransaction.status) &&
        Objects.equals(this.storeAndForwardNumber, paymentTransaction.storeAndForwardNumber) &&
        Objects.equals(this.transactionDate, paymentTransaction.transactionDate) &&
        Objects.equals(this.transactionExpiryDate, paymentTransaction.transactionExpiryDate) &&
        Objects.equals(this.transactionType, paymentTransaction.transactionType) &&
        Objects.equals(this.transmissionStatus, paymentTransaction.transmissionStatus) &&
        Objects.equals(this.updatedBy, paymentTransaction.updatedBy) &&
        Objects.equals(this.updatedTimestamp, paymentTransaction.updatedTimestamp) &&
        Objects.equals(this.entityName, paymentTransaction.entityName) &&
        Objects.equals(this.localize, paymentTransaction.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, extended, externalResponseId, followOnCount, followOnId, followOnProcessedAmount, followOnToken, interactionMode, isActivation, isActive, isCopied, isValidForRefund, localizedTo, messages, orderId, orgId, PK, paymentGroupId, paymentResponseStatus, paymentTransAttribute, paymentTransEncrAttribute, paymentTransactionDetail, paymentTransactionEMVTags, paymentTransactionId, processedAmount, processingMode, reAuthOnSettlementFailure, reasonId, reconciliationId, remainingAttempts, remainingBalance, requestId, requestToken, requestedAmount, requestedDate, scheduledTimestamp, status, storeAndForwardNumber, transactionDate, transactionExpiryDate, transactionType, transmissionStatus, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentTransaction {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    externalResponseId: ").append(toIndentedString(externalResponseId)).append("\n");
    sb.append("    followOnCount: ").append(toIndentedString(followOnCount)).append("\n");
    sb.append("    followOnId: ").append(toIndentedString(followOnId)).append("\n");
    sb.append("    followOnProcessedAmount: ").append(toIndentedString(followOnProcessedAmount)).append("\n");
    sb.append("    followOnToken: ").append(toIndentedString(followOnToken)).append("\n");
    sb.append("    interactionMode: ").append(toIndentedString(interactionMode)).append("\n");
    sb.append("    isActivation: ").append(toIndentedString(isActivation)).append("\n");
    sb.append("    isActive: ").append(toIndentedString(isActive)).append("\n");
    sb.append("    isCopied: ").append(toIndentedString(isCopied)).append("\n");
    sb.append("    isValidForRefund: ").append(toIndentedString(isValidForRefund)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderId: ").append(toIndentedString(orderId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    paymentGroupId: ").append(toIndentedString(paymentGroupId)).append("\n");
    sb.append("    paymentResponseStatus: ").append(toIndentedString(paymentResponseStatus)).append("\n");
    sb.append("    paymentTransAttribute: ").append(toIndentedString(paymentTransAttribute)).append("\n");
    sb.append("    paymentTransEncrAttribute: ").append(toIndentedString(paymentTransEncrAttribute)).append("\n");
    sb.append("    paymentTransactionDetail: ").append(toIndentedString(paymentTransactionDetail)).append("\n");
    sb.append("    paymentTransactionEMVTags: ").append(toIndentedString(paymentTransactionEMVTags)).append("\n");
    sb.append("    paymentTransactionId: ").append(toIndentedString(paymentTransactionId)).append("\n");
    sb.append("    processedAmount: ").append(toIndentedString(processedAmount)).append("\n");
    sb.append("    processingMode: ").append(toIndentedString(processingMode)).append("\n");
    sb.append("    reAuthOnSettlementFailure: ").append(toIndentedString(reAuthOnSettlementFailure)).append("\n");
    sb.append("    reasonId: ").append(toIndentedString(reasonId)).append("\n");
    sb.append("    reconciliationId: ").append(toIndentedString(reconciliationId)).append("\n");
    sb.append("    remainingAttempts: ").append(toIndentedString(remainingAttempts)).append("\n");
    sb.append("    remainingBalance: ").append(toIndentedString(remainingBalance)).append("\n");
    sb.append("    requestId: ").append(toIndentedString(requestId)).append("\n");
    sb.append("    requestToken: ").append(toIndentedString(requestToken)).append("\n");
    sb.append("    requestedAmount: ").append(toIndentedString(requestedAmount)).append("\n");
    sb.append("    requestedDate: ").append(toIndentedString(requestedDate)).append("\n");
    sb.append("    scheduledTimestamp: ").append(toIndentedString(scheduledTimestamp)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    storeAndForwardNumber: ").append(toIndentedString(storeAndForwardNumber)).append("\n");
    sb.append("    transactionDate: ").append(toIndentedString(transactionDate)).append("\n");
    sb.append("    transactionExpiryDate: ").append(toIndentedString(transactionExpiryDate)).append("\n");
    sb.append("    transactionType: ").append(toIndentedString(transactionType)).append("\n");
    sb.append("    transmissionStatus: ").append(toIndentedString(transmissionStatus)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

