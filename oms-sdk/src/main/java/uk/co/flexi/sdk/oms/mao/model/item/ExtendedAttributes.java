package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ExtendedAttributes
 */
public class ExtendedAttributes {
  public static final String SERIALIZED_NAME_COMPONENT = "Component";
  @SerializedName(SERIALIZED_NAME_COMPONENT)
  private String component;

  public static final String SERIALIZED_NAME_ENTITIES = "Entities";
  @SerializedName(SERIALIZED_NAME_ENTITIES)
  private List<Entities> entities = null;

  public ExtendedAttributes component(String component) {
    this.component = component;
    return this;
  }

   /**
   * Get component
   * @return component
  **/
  public String getComponent() {
    return component;
  }

  public void setComponent(String component) {
    this.component = component;
  }

  public ExtendedAttributes entities(List<Entities> entities) {
    this.entities = entities;
    return this;
  }

  public ExtendedAttributes addEntitiesItem(Entities entitiesItem) {
    if (this.entities == null) {
      this.entities = new ArrayList<Entities>();
    }
    this.entities.add(entitiesItem);
    return this;
  }

   /**
   * Get entities
   * @return entities
  **/
  public List<Entities> getEntities() {
    return entities;
  }

  public void setEntities(List<Entities> entities) {
    this.entities = entities;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ExtendedAttributes extendedAttributes = (ExtendedAttributes) o;
    return Objects.equals(this.component, extendedAttributes.component) &&
        Objects.equals(this.entities, extendedAttributes.entities);
  }

  @Override
  public int hashCode() {
    return Objects.hash(component, entities);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ExtendedAttributes {\n");
    
    sb.append("    component: ").append(toIndentedString(component)).append("\n");
    sb.append("    entities: ").append(toIndentedString(entities)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

