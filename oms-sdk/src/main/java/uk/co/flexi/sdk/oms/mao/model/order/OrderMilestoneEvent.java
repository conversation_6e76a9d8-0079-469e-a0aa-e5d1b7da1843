/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderMilestoneEvent
 */
public class OrderMilestoneEvent {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EVENT_ID = "EventId";
  @SerializedName(SERIALIZED_NAME_EVENT_ID)
  private String eventId;

  public static final String SERIALIZED_NAME_EVENT_TIME = "EventTime";
  @SerializedName(SERIALIZED_NAME_EVENT_TIME)
  private OffsetDateTime eventTime;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_MILESTONE_DEFINITION_ID = "MilestoneDefinitionId";
  @SerializedName(SERIALIZED_NAME_MILESTONE_DEFINITION_ID)
  private String milestoneDefinitionId;

  public static final String SERIALIZED_NAME_MONITORING_RULE_ID = "MonitoringRuleId";
  @SerializedName(SERIALIZED_NAME_MONITORING_RULE_ID)
  private String monitoringRuleId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderMilestoneEvent actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderMilestoneEvent putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderMilestoneEvent createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderMilestoneEvent createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderMilestoneEvent eventId(String eventId) {
    this.eventId = eventId;
    return this;
  }

   /**
   * Unique event id
   * @return eventId
  **/
  
  public String getEventId() {
    return eventId;
  }

  public void setEventId(String eventId) {
    this.eventId = eventId;
  }

  public OrderMilestoneEvent eventTime(OffsetDateTime eventTime) {
    this.eventTime = eventTime;
    return this;
  }

   /**
   * timestamp at which event got created
   * @return eventTime
  **/
  
  public OffsetDateTime getEventTime() {
    return eventTime;
  }

  public void setEventTime(OffsetDateTime eventTime) {
    this.eventTime = eventTime;
  }

  public OrderMilestoneEvent extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderMilestoneEvent localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderMilestoneEvent messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderMilestoneEvent milestoneDefinitionId(String milestoneDefinitionId) {
    this.milestoneDefinitionId = milestoneDefinitionId;
    return this;
  }

   /**
   * Reference to the milestone definition
   * @return milestoneDefinitionId
  **/
  
  public String getMilestoneDefinitionId() {
    return milestoneDefinitionId;
  }

  public void setMilestoneDefinitionId(String milestoneDefinitionId) {
    this.milestoneDefinitionId = milestoneDefinitionId;
  }

  public OrderMilestoneEvent monitoringRuleId(String monitoringRuleId) {
    this.monitoringRuleId = monitoringRuleId;
    return this;
  }

   /**
   * orderMonitoringRule due to which the event was created/raised
   * @return monitoringRuleId
  **/
  
  public String getMonitoringRuleId() {
    return monitoringRuleId;
  }

  public void setMonitoringRuleId(String monitoringRuleId) {
    this.monitoringRuleId = monitoringRuleId;
  }

  public OrderMilestoneEvent orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderMilestoneEvent PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderMilestoneEvent updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderMilestoneEvent updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderMilestoneEvent entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderMilestoneEvent localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderMilestoneEvent orderMilestoneEvent = (OrderMilestoneEvent) o;
    return Objects.equals(this.actions, orderMilestoneEvent.actions) &&
        Objects.equals(this.createdBy, orderMilestoneEvent.createdBy) &&
        Objects.equals(this.createdTimestamp, orderMilestoneEvent.createdTimestamp) &&
        Objects.equals(this.eventId, orderMilestoneEvent.eventId) &&
        Objects.equals(this.eventTime, orderMilestoneEvent.eventTime) &&
        Objects.equals(this.extended, orderMilestoneEvent.extended) &&
        Objects.equals(this.localizedTo, orderMilestoneEvent.localizedTo) &&
        Objects.equals(this.messages, orderMilestoneEvent.messages) &&
        Objects.equals(this.milestoneDefinitionId, orderMilestoneEvent.milestoneDefinitionId) &&
        Objects.equals(this.monitoringRuleId, orderMilestoneEvent.monitoringRuleId) &&
        Objects.equals(this.orgId, orderMilestoneEvent.orgId) &&
        Objects.equals(this.PK, orderMilestoneEvent.PK) &&
        Objects.equals(this.updatedBy, orderMilestoneEvent.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderMilestoneEvent.updatedTimestamp) &&
        Objects.equals(this.entityName, orderMilestoneEvent.entityName) &&
        Objects.equals(this.localize, orderMilestoneEvent.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, eventId, eventTime, extended, localizedTo, messages, milestoneDefinitionId, monitoringRuleId, orgId, PK, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderMilestoneEvent {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    eventId: ").append(toIndentedString(eventId)).append("\n");
    sb.append("    eventTime: ").append(toIndentedString(eventTime)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    milestoneDefinitionId: ").append(toIndentedString(milestoneDefinitionId)).append("\n");
    sb.append("    monitoringRuleId: ").append(toIndentedString(monitoringRuleId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

