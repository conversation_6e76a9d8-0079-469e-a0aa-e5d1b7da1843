package uk.co.flexi.sdk.oms.mao.client.interceptor;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.net.HttpURLConnection;

public class MaoAccessTokenInterceptor implements Interceptor {

  private final MaoAccessTokenRepository accessTokenRepository;

  public MaoAccessTokenInterceptor(MaoAccessTokenRepository accessTokenRepository) {
    this.accessTokenRepository = accessTokenRepository;
  }

  @NotNull
  @Override
  public Response intercept(Chain chain) throws IOException {
    String accessToken = accessTokenRepository.getAccessToken();
    Request request = newRequestWithAccessToken(chain.request(), accessToken);
    Response response = chain.proceed(request);

    if (response.code() == HttpURLConnection.HTTP_UNAUTHORIZED) {
      synchronized (this) {
        final String newAccessToken = accessTokenRepository.getAccessToken();
        // Access token is refreshed in another thread.
        if (!accessToken.equals(newAccessToken)) {
          return chain.proceed(newRequestWithAccessToken(request, newAccessToken));
        }

        // Need to refresh an access token
        final String updatedAccessToken = accessTokenRepository.refreshAccessToken();
        // Retry the request
        return chain.proceed(newRequestWithAccessToken(request, updatedAccessToken));
      }
    }
    return response;
  }

  private Request newRequestWithAccessToken(Request request, String accessToken) {
    return request.newBuilder()
            .header("Authorization", "Bearer " + accessToken)
            .build();
  }
}