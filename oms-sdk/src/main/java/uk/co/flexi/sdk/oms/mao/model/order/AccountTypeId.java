/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * AccountTypeId
 */
public class AccountTypeId {
  public static final String SERIALIZED_NAME_ACCOUNT_TYPE_ID = "AccountTypeId";
  @SerializedName(SERIALIZED_NAME_ACCOUNT_TYPE_ID)
  private String accountTypeId;

  public AccountTypeId accountTypeId(String accountTypeId) {
    this.accountTypeId = accountTypeId;
    return this;
  }

   /**
   * Identifier of the account type, to be saved on the payment method of an e-check payment
   * @return accountTypeId
  **/
  public String getAccountTypeId() {
    return accountTypeId;
  }

  public void setAccountTypeId(String accountTypeId) {
    this.accountTypeId = accountTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AccountTypeId accountTypeId = (AccountTypeId) o;
    return Objects.equals(this.accountTypeId, accountTypeId.accountTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(accountTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AccountTypeId {\n");
    
    sb.append("    accountTypeId: ").append(toIndentedString(accountTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

