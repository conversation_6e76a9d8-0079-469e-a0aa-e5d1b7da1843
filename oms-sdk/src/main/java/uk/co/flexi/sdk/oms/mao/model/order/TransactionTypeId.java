/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * TransactionTypeId
 */
public class TransactionTypeId {
  public static final String SERIALIZED_NAME_TRANSACTION_TYPE_ID = "TransactionTypeId";
  @SerializedName(SERIALIZED_NAME_TRANSACTION_TYPE_ID)
  private String transactionTypeId;

  public TransactionTypeId transactionTypeId(String transactionTypeId) {
    this.transactionTypeId = transactionTypeId;
    return this;
  }

   /**
   * Unique identifier of transaction type
   * @return transactionTypeId
  **/
  
  public String getTransactionTypeId() {
    return transactionTypeId;
  }

  public void setTransactionTypeId(String transactionTypeId) {
    this.transactionTypeId = transactionTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TransactionTypeId transactionTypeId = (TransactionTypeId) o;
    return Objects.equals(this.transactionTypeId, transactionTypeId.transactionTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(transactionTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TransactionTypeId {\n");
    
    sb.append("    transactionTypeId: ").append(toIndentedString(transactionTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

