/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * QuantityDetail
 */
public class QuantityDetail {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_ITEM_ID = "ItemId";
  @SerializedName(SERIALIZED_NAME_ITEM_ID)
  private String itemId;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_QUANTITY = "Quantity";
  @SerializedName(SERIALIZED_NAME_QUANTITY)
  private Double quantity;

  public static final String SERIALIZED_NAME_QUANTITY_DETAIL_ID = "QuantityDetailId";
  @SerializedName(SERIALIZED_NAME_QUANTITY_DETAIL_ID)
  private String quantityDetailId;

  public static final String SERIALIZED_NAME_REASON = "Reason";
  @SerializedName(SERIALIZED_NAME_REASON)
  private ReasonId reason = null;

  public static final String SERIALIZED_NAME_REASON_TYPE = "ReasonType";
  @SerializedName(SERIALIZED_NAME_REASON_TYPE)
  private ReasonTypeId reasonType = null;

  public static final String SERIALIZED_NAME_STATUS = "Status";
  @SerializedName(SERIALIZED_NAME_STATUS)
  private KeyDTO status = null;

  public static final String SERIALIZED_NAME_STATUS_ID = "StatusId";
  @SerializedName(SERIALIZED_NAME_STATUS_ID)
  private String statusId;

  public static final String SERIALIZED_NAME_SUBSTITUTION_RATIO = "SubstitutionRatio";
  @SerializedName(SERIALIZED_NAME_SUBSTITUTION_RATIO)
  private Double substitutionRatio;

  public static final String SERIALIZED_NAME_SUBSTITUTION_TYPE = "SubstitutionType";
  @SerializedName(SERIALIZED_NAME_SUBSTITUTION_TYPE)
  private SubstitutionTypeId substitutionType = null;

  public static final String SERIALIZED_NAME_U_O_M = "UOM";
  @SerializedName(SERIALIZED_NAME_U_O_M)
  private String UOM;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public QuantityDetail actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public QuantityDetail putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public QuantityDetail createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public QuantityDetail createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public QuantityDetail extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public QuantityDetail itemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

   /**
   * Identifier of the item
   * @return itemId
  **/
  
  public String getItemId() {
    return itemId;
  }

  public void setItemId(String itemId) {
    this.itemId = itemId;
  }

  public QuantityDetail localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public QuantityDetail messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public QuantityDetail orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public QuantityDetail PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public QuantityDetail quantity(Double quantity) {
    this.quantity = quantity;
    return this;
  }

   /**
   * Quantity in the specified status
   * minimum: 0
   * maximum: 999999999999.9999
   * @return quantity
  **/
  
  public Double getQuantity() {
    return quantity;
  }

  public void setQuantity(Double quantity) {
    this.quantity = quantity;
  }

  public QuantityDetail quantityDetailId(String quantityDetailId) {
    this.quantityDetailId = quantityDetailId;
    return this;
  }

   /**
   * Unique identifier of the quantity detail record
   * @return quantityDetailId
  **/
  
  public String getQuantityDetailId() {
    return quantityDetailId;
  }

  public void setQuantityDetailId(String quantityDetailId) {
    this.quantityDetailId = quantityDetailId;
  }

  public QuantityDetail reason(ReasonId reason) {
    this.reason = reason;
    return this;
  }

   /**
   * Get reason
   * @return reason
  **/
  
  public ReasonId getReason() {
    return reason;
  }

  public void setReason(ReasonId reason) {
    this.reason = reason;
  }

  public QuantityDetail reasonType(ReasonTypeId reasonType) {
    this.reasonType = reasonType;
    return this;
  }

   /**
   * Get reasonType
   * @return reasonType
  **/
  
  public ReasonTypeId getReasonType() {
    return reasonType;
  }

  public void setReasonType(ReasonTypeId reasonType) {
    this.reasonType = reasonType;
  }

  public QuantityDetail status(KeyDTO status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  
  public KeyDTO getStatus() {
    return status;
  }

  public void setStatus(KeyDTO status) {
    this.status = status;
  }

  public QuantityDetail statusId(String statusId) {
    this.statusId = statusId;
    return this;
  }

   /**
   * Status of the quantity
   * @return statusId
  **/
  
  public String getStatusId() {
    return statusId;
  }

  public void setStatusId(String statusId) {
    this.statusId = statusId;
  }

  public QuantityDetail substitutionRatio(Double substitutionRatio) {
    this.substitutionRatio = substitutionRatio;
    return this;
  }

   /**
   * Ratio of the substituted item to the ordered item
   * minimum: 0
   * maximum: 999999999999.9999
   * @return substitutionRatio
  **/
  
  public Double getSubstitutionRatio() {
    return substitutionRatio;
  }

  public void setSubstitutionRatio(Double substitutionRatio) {
    this.substitutionRatio = substitutionRatio;
  }

  public QuantityDetail substitutionType(SubstitutionTypeId substitutionType) {
    this.substitutionType = substitutionType;
    return this;
  }

   /**
   * Get substitutionType
   * @return substitutionType
  **/
  
  public SubstitutionTypeId getSubstitutionType() {
    return substitutionType;
  }

  public void setSubstitutionType(SubstitutionTypeId substitutionType) {
    this.substitutionType = substitutionType;
  }

  public QuantityDetail UOM(String UOM) {
    this.UOM = UOM;
    return this;
  }

   /**
   * Unit of measure (UOM) of the allocated quantity
   * @return UOM
  **/
  
  public String getUOM() {
    return UOM;
  }

  public void setUOM(String UOM) {
    this.UOM = UOM;
  }

  public QuantityDetail updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public QuantityDetail updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public QuantityDetail entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public QuantityDetail localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    QuantityDetail quantityDetail = (QuantityDetail) o;
    return Objects.equals(this.actions, quantityDetail.actions) &&
        Objects.equals(this.createdBy, quantityDetail.createdBy) &&
        Objects.equals(this.createdTimestamp, quantityDetail.createdTimestamp) &&
        Objects.equals(this.extended, quantityDetail.extended) &&
        Objects.equals(this.itemId, quantityDetail.itemId) &&
        Objects.equals(this.localizedTo, quantityDetail.localizedTo) &&
        Objects.equals(this.messages, quantityDetail.messages) &&
        Objects.equals(this.orgId, quantityDetail.orgId) &&
        Objects.equals(this.PK, quantityDetail.PK) &&
        Objects.equals(this.quantity, quantityDetail.quantity) &&
        Objects.equals(this.quantityDetailId, quantityDetail.quantityDetailId) &&
        Objects.equals(this.reason, quantityDetail.reason) &&
        Objects.equals(this.reasonType, quantityDetail.reasonType) &&
        Objects.equals(this.status, quantityDetail.status) &&
        Objects.equals(this.statusId, quantityDetail.statusId) &&
        Objects.equals(this.substitutionRatio, quantityDetail.substitutionRatio) &&
        Objects.equals(this.substitutionType, quantityDetail.substitutionType) &&
        Objects.equals(this.UOM, quantityDetail.UOM) &&
        Objects.equals(this.updatedBy, quantityDetail.updatedBy) &&
        Objects.equals(this.updatedTimestamp, quantityDetail.updatedTimestamp) &&
        Objects.equals(this.entityName, quantityDetail.entityName) &&
        Objects.equals(this.localize, quantityDetail.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, extended, itemId, localizedTo, messages, orgId, PK, quantity, quantityDetailId, reason, reasonType, status, statusId, substitutionRatio, substitutionType, UOM, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QuantityDetail {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    itemId: ").append(toIndentedString(itemId)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    quantityDetailId: ").append(toIndentedString(quantityDetailId)).append("\n");
    sb.append("    reason: ").append(toIndentedString(reason)).append("\n");
    sb.append("    reasonType: ").append(toIndentedString(reasonType)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    statusId: ").append(toIndentedString(statusId)).append("\n");
    sb.append("    substitutionRatio: ").append(toIndentedString(substitutionRatio)).append("\n");
    sb.append("    substitutionType: ").append(toIndentedString(substitutionType)).append("\n");
    sb.append("    UOM: ").append(toIndentedString(UOM)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

