/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ChargeTypeConfig
 */
public class ChargeTypeConfig {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CHARGE_ON_FIRST_INVOICE = "ChargeOnFirstInvoice";
  @SerializedName(SERIALIZED_NAME_CHARGE_ON_FIRST_INVOICE)
  private Boolean chargeOnFirstInvoice;

  public static final String SERIALIZED_NAME_CHARGE_TYPE = "ChargeType";
  @SerializedName(SERIALIZED_NAME_CHARGE_TYPE)
  private ChargeType chargeType = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IS_NOT_REFUNDABLE = "IsNotRefundable";
  @SerializedName(SERIALIZED_NAME_IS_NOT_REFUNDABLE)
  private Boolean isNotRefundable;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public ChargeTypeConfig actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ChargeTypeConfig putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ChargeTypeConfig chargeOnFirstInvoice(Boolean chargeOnFirstInvoice) {
    this.chargeOnFirstInvoice = chargeOnFirstInvoice;
    return this;
  }

   /**
   * Configuration to charge entire amount on first shipment of respective fulfillment group
   * @return chargeOnFirstInvoice
  **/
  
  public Boolean getChargeOnFirstInvoice() {
    return chargeOnFirstInvoice;
  }

  public void setChargeOnFirstInvoice(Boolean chargeOnFirstInvoice) {
    this.chargeOnFirstInvoice = chargeOnFirstInvoice;
  }

  public ChargeTypeConfig chargeType(ChargeType chargeType) {
    this.chargeType = chargeType;
    return this;
  }

   /**
   * Get chargeType
   * @return chargeType
  **/
  
  public ChargeType getChargeType() {
    return chargeType;
  }

  public void setChargeType(ChargeType chargeType) {
    this.chargeType = chargeType;
  }

  public ChargeTypeConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ChargeTypeConfig createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ChargeTypeConfig extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ChargeTypeConfig isNotRefundable(Boolean isNotRefundable) {
    this.isNotRefundable = isNotRefundable;
    return this;
  }

   /**
   * If the particular ChargeType is not returnable in the return flow,then this flag is set to True explicitly, default value is false which means we returnreturn the charge.default behaviour is we return all the charges.
   * @return isNotRefundable
  **/
  
  public Boolean getIsNotRefundable() {
    return isNotRefundable;
  }

  public void setIsNotRefundable(Boolean isNotRefundable) {
    this.isNotRefundable = isNotRefundable;
  }

  public ChargeTypeConfig localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public ChargeTypeConfig messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ChargeTypeConfig PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ChargeTypeConfig profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public ChargeTypeConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ChargeTypeConfig updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ChargeTypeConfig rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChargeTypeConfig chargeTypeConfig = (ChargeTypeConfig) o;
    return Objects.equals(this.actions, chargeTypeConfig.actions) &&
        Objects.equals(this.chargeOnFirstInvoice, chargeTypeConfig.chargeOnFirstInvoice) &&
        Objects.equals(this.chargeType, chargeTypeConfig.chargeType) &&
        Objects.equals(this.createdBy, chargeTypeConfig.createdBy) &&
        Objects.equals(this.createdTimestamp, chargeTypeConfig.createdTimestamp) &&
        Objects.equals(this.extended, chargeTypeConfig.extended) &&
        Objects.equals(this.isNotRefundable, chargeTypeConfig.isNotRefundable) &&
        Objects.equals(this.localizedTo, chargeTypeConfig.localizedTo) &&
        Objects.equals(this.messages, chargeTypeConfig.messages) &&
        Objects.equals(this.PK, chargeTypeConfig.PK) &&
        Objects.equals(this.profileId, chargeTypeConfig.profileId) &&
        Objects.equals(this.updatedBy, chargeTypeConfig.updatedBy) &&
        Objects.equals(this.updatedTimestamp, chargeTypeConfig.updatedTimestamp) &&
        Objects.equals(this.rootCause, chargeTypeConfig.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, chargeOnFirstInvoice, chargeType, createdBy, createdTimestamp, extended, isNotRefundable, localizedTo, messages, PK, profileId, updatedBy, updatedTimestamp, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChargeTypeConfig {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    chargeOnFirstInvoice: ").append(toIndentedString(chargeOnFirstInvoice)).append("\n");
    sb.append("    chargeType: ").append(toIndentedString(chargeType)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    isNotRefundable: ").append(toIndentedString(isNotRefundable)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

