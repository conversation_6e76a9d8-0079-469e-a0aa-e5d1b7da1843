/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ChangeLog
 */
public class ChangeLog {
  public static final String SERIALIZED_NAME_CHANGE_SET = "ChangeSet";
  @SerializedName(SERIALIZED_NAME_CHANGE_SET)
  private List<ChangeSet> changeSet = null;

  public static final String SERIALIZED_NAME_MOD_TYPES = "ModTypes";
  @SerializedName(SERIALIZED_NAME_MOD_TYPES)
  private ModTypes modTypes = null;

  public ChangeLog changeSet(List<ChangeSet> changeSet) {
    this.changeSet = changeSet;
    return this;
  }

  public ChangeLog addChangeSetItem(ChangeSet changeSetItem) {
    if (this.changeSet == null) {
      this.changeSet = new ArrayList<ChangeSet>();
    }
    this.changeSet.add(changeSetItem);
    return this;
  }

   /**
   * Get changeSet
   * @return changeSet
  **/
  
  public List<ChangeSet> getChangeSet() {
    return changeSet;
  }

  public void setChangeSet(List<ChangeSet> changeSet) {
    this.changeSet = changeSet;
  }

  public ChangeLog modTypes(ModTypes modTypes) {
    this.modTypes = modTypes;
    return this;
  }

   /**
   * Get modTypes
   * @return modTypes
  **/
  
  public ModTypes getModTypes() {
    return modTypes;
  }

  public void setModTypes(ModTypes modTypes) {
    this.modTypes = modTypes;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChangeLog changeLog = (ChangeLog) o;
    return Objects.equals(this.changeSet, changeLog.changeSet) &&
        Objects.equals(this.modTypes, changeLog.modTypes);
  }

  @Override
  public int hashCode() {
    return Objects.hash(changeSet, modTypes);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChangeLog {\n");
    
    sb.append("    changeSet: ").append(toIndentedString(changeSet)).append("\n");
    sb.append("    modTypes: ").append(toIndentedString(modTypes)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

