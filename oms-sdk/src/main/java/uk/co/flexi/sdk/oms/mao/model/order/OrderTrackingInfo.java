/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.*;

/**
 * OrderTrackingInfo
 */
public class OrderTrackingInfo {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CARRIER_CODE = "CarrierCode";
  @SerializedName(SERIALIZED_NAME_CARRIER_CODE)
  private String carrierCode;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LATEST_EVENT = "LatestEvent";
  @SerializedName(SERIALIZED_NAME_LATEST_EVENT)
  private OrderTrackingDetail latestEvent = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER = "Order";
  @SerializedName(SERIALIZED_NAME_ORDER)
  private Order order = null;

  public static final String SERIALIZED_NAME_ORDER_LINE = "OrderLine";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE)
  private List<OrderLine> orderLine = null;

  public static final String SERIALIZED_NAME_ORDER_TRACKING_DETAIL = "OrderTrackingDetail";
  @SerializedName(SERIALIZED_NAME_ORDER_TRACKING_DETAIL)
  private List<OrderTrackingDetail> orderTrackingDetail = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PACKAGE_STATUS = "PackageStatus";
  @SerializedName(SERIALIZED_NAME_PACKAGE_STATUS)
  private PackageStatusId packageStatus = null;

  public static final String SERIALIZED_NAME_SCHEDULED_DATE = "ScheduledDate";
  @SerializedName(SERIALIZED_NAME_SCHEDULED_DATE)
  private OffsetDateTime scheduledDate;

  public static final String SERIALIZED_NAME_TRACKING_NUMBER = "TrackingNumber";
  @SerializedName(SERIALIZED_NAME_TRACKING_NUMBER)
  private String trackingNumber;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderTrackingInfo actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderTrackingInfo putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderTrackingInfo carrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
    return this;
  }

   /**
   * Carrier used to ship the package. Used by the delivery tracking services to select which carrier tracking API to call. Populated based on the fulfillment detail carrier, which gets created as a result of a ship event received from execution systems such as WM.
   * @return carrierCode
  **/
  
  public String getCarrierCode() {
    return carrierCode;
  }

  public void setCarrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
  }

  public OrderTrackingInfo createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderTrackingInfo createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderTrackingInfo extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderTrackingInfo latestEvent(OrderTrackingDetail latestEvent) {
    this.latestEvent = latestEvent;
    return this;
  }

   /**
   * Get latestEvent
   * @return latestEvent
  **/
  
  public OrderTrackingDetail getLatestEvent() {
    return latestEvent;
  }

  public void setLatestEvent(OrderTrackingDetail latestEvent) {
    this.latestEvent = latestEvent;
  }

  public OrderTrackingInfo localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderTrackingInfo messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderTrackingInfo order(Order order) {
    this.order = order;
    return this;
  }

   /**
   * Get order
   * @return order
  **/
  
  public Order getOrder() {
    return order;
  }

  public void setOrder(Order order) {
    this.order = order;
  }

  public OrderTrackingInfo orderLine(List<OrderLine> orderLine) {
    this.orderLine = orderLine;
    return this;
  }

  public OrderTrackingInfo addOrderLineItem(OrderLine orderLineItem) {
    if (this.orderLine == null) {
      this.orderLine = new ArrayList<OrderLine>();
    }
    this.orderLine.add(orderLineItem);
    return this;
  }

   /**
   * Get orderLine
   * @return orderLine
  **/
  
  public List<OrderLine> getOrderLine() {
    return orderLine;
  }

  public void setOrderLine(List<OrderLine> orderLine) {
    this.orderLine = orderLine;
  }

  public OrderTrackingInfo orderTrackingDetail(List<OrderTrackingDetail> orderTrackingDetail) {
    this.orderTrackingDetail = orderTrackingDetail;
    return this;
  }

  public OrderTrackingInfo addOrderTrackingDetailItem(OrderTrackingDetail orderTrackingDetailItem) {
    if (this.orderTrackingDetail == null) {
      this.orderTrackingDetail = new ArrayList<OrderTrackingDetail>();
    }
    this.orderTrackingDetail.add(orderTrackingDetailItem);
    return this;
  }

   /**
   * Get orderTrackingDetail
   * @return orderTrackingDetail
  **/
  
  public List<OrderTrackingDetail> getOrderTrackingDetail() {
    return orderTrackingDetail;
  }

  public void setOrderTrackingDetail(List<OrderTrackingDetail> orderTrackingDetail) {
    this.orderTrackingDetail = orderTrackingDetail;
  }

  public OrderTrackingInfo orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderTrackingInfo PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderTrackingInfo packageStatus(PackageStatusId packageStatus) {
    this.packageStatus = packageStatus;
    return this;
  }

   /**
   * Get packageStatus
   * @return packageStatus
  **/
  
  public PackageStatusId getPackageStatus() {
    return packageStatus;
  }

  public void setPackageStatus(PackageStatusId packageStatus) {
    this.packageStatus = packageStatus;
  }

  public OrderTrackingInfo scheduledDate(OffsetDateTime scheduledDate) {
    this.scheduledDate = scheduledDate;
    return this;
  }

   /**
   * Field not used. Reserved for future use in delivery tracking.
   * @return scheduledDate
  **/
  
  public OffsetDateTime getScheduledDate() {
    return scheduledDate;
  }

  public void setScheduledDate(OffsetDateTime scheduledDate) {
    this.scheduledDate = scheduledDate;
  }

  public OrderTrackingInfo trackingNumber(String trackingNumber) {
    this.trackingNumber = trackingNumber;
    return this;
  }

   /**
   * Tracking number assigned to the package by the carrier. Used as the key to track packages in the third party carrier systems.
   * @return trackingNumber
  **/
  
  public String getTrackingNumber() {
    return trackingNumber;
  }

  public void setTrackingNumber(String trackingNumber) {
    this.trackingNumber = trackingNumber;
  }

  public OrderTrackingInfo updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderTrackingInfo updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderTrackingInfo entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderTrackingInfo localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderTrackingInfo orderTrackingInfo = (OrderTrackingInfo) o;
    return Objects.equals(this.actions, orderTrackingInfo.actions) &&
        Objects.equals(this.carrierCode, orderTrackingInfo.carrierCode) &&
        Objects.equals(this.createdBy, orderTrackingInfo.createdBy) &&
        Objects.equals(this.createdTimestamp, orderTrackingInfo.createdTimestamp) &&
        Objects.equals(this.extended, orderTrackingInfo.extended) &&
        Objects.equals(this.latestEvent, orderTrackingInfo.latestEvent) &&
        Objects.equals(this.localizedTo, orderTrackingInfo.localizedTo) &&
        Objects.equals(this.messages, orderTrackingInfo.messages) &&
        Objects.equals(this.order, orderTrackingInfo.order) &&
        Objects.equals(this.orderLine, orderTrackingInfo.orderLine) &&
        Objects.equals(this.orderTrackingDetail, orderTrackingInfo.orderTrackingDetail) &&
        Objects.equals(this.orgId, orderTrackingInfo.orgId) &&
        Objects.equals(this.PK, orderTrackingInfo.PK) &&
        Objects.equals(this.packageStatus, orderTrackingInfo.packageStatus) &&
        Objects.equals(this.scheduledDate, orderTrackingInfo.scheduledDate) &&
        Objects.equals(this.trackingNumber, orderTrackingInfo.trackingNumber) &&
        Objects.equals(this.updatedBy, orderTrackingInfo.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderTrackingInfo.updatedTimestamp) &&
        Objects.equals(this.entityName, orderTrackingInfo.entityName) &&
        Objects.equals(this.localize, orderTrackingInfo.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, carrierCode, createdBy, createdTimestamp, extended, latestEvent, localizedTo, messages, order, orderLine, orderTrackingDetail, orgId, PK, packageStatus, scheduledDate, trackingNumber, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderTrackingInfo {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    carrierCode: ").append(toIndentedString(carrierCode)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    latestEvent: ").append(toIndentedString(latestEvent)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    order: ").append(toIndentedString(order)).append("\n");
    sb.append("    orderLine: ").append(toIndentedString(orderLine)).append("\n");
    sb.append("    orderTrackingDetail: ").append(toIndentedString(orderTrackingDetail)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    packageStatus: ").append(toIndentedString(packageStatus)).append("\n");
    sb.append("    scheduledDate: ").append(toIndentedString(scheduledDate)).append("\n");
    sb.append("    trackingNumber: ").append(toIndentedString(trackingNumber)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

