package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;


@Data
public class RelatedOrderLine {

    @SerializedName("ItemId")
    private String ItemId;

    @SerializedName("FulfillmentDetail")
    private List<RelatedOrderFulfillmentDetails> relatedOrderFulfillmentDetail;

    public String getItemId() {
        return ItemId;
    }

    public List<RelatedOrderFulfillmentDetails> getRelatedOrderFulfillmentDetail() {
        return relatedOrderFulfillmentDetail;
    }
}
