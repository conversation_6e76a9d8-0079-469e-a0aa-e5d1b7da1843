/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * InvoiceLineChargeDetailRequest
 */
public class InvoiceLineChargeDetailRequest {
  public static final String SERIALIZED_NAME_CHARGE_PERCENT = "chargePercent";
  @SerializedName(SERIALIZED_NAME_CHARGE_PERCENT)
  private Double chargePercent;

  public static final String SERIALIZED_NAME_CHARGE_REFERENCE_ID = "chargeReferenceId";
  @SerializedName(SERIALIZED_NAME_CHARGE_REFERENCE_ID)
  private String chargeReferenceId;

  public static final String SERIALIZED_NAME_CHARGE_SEQUENCE = "chargeSequence";
  @SerializedName(SERIALIZED_NAME_CHARGE_SEQUENCE)
  private Long chargeSequence;

  public static final String SERIALIZED_NAME_CHARGE_TOTAL = "chargeTotal";
  @SerializedName(SERIALIZED_NAME_CHARGE_TOTAL)
  private BigDecimal chargeTotal;

  public static final String SERIALIZED_NAME_CHARGE_TYPE_ID = "chargeTypeId";
  @SerializedName(SERIALIZED_NAME_CHARGE_TYPE_ID)
  private String chargeTypeId;

  public static final String SERIALIZED_NAME_COMMENTS = "comments";
  @SerializedName(SERIALIZED_NAME_COMMENTS)
  private String comments;

  public static final String SERIALIZED_NAME_DISCOUNT_ON_ID = "discountOnId";
  @SerializedName(SERIALIZED_NAME_DISCOUNT_ON_ID)
  private String discountOnId;

  public static final String SERIALIZED_NAME_EXTENDED = "extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FULFILLMENT_GROUP_ID = "fulfillmentGroupId";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_GROUP_ID)
  private String fulfillmentGroupId;

  public static final String SERIALIZED_NAME_HEADER_CHARGE_DETAIL_ID = "headerChargeDetailId";
  @SerializedName(SERIALIZED_NAME_HEADER_CHARGE_DETAIL_ID)
  private String headerChargeDetailId;

  public static final String SERIALIZED_NAME_IS_INFORMATIONAL = "isInformational";
  @SerializedName(SERIALIZED_NAME_IS_INFORMATIONAL)
  private Boolean isInformational;

  public static final String SERIALIZED_NAME_IS_POST_RETURN = "isPostReturn";
  @SerializedName(SERIALIZED_NAME_IS_POST_RETURN)
  private Boolean isPostReturn;

  public static final String SERIALIZED_NAME_IS_PRORATED_AT_SAME_LEVEL = "isProratedAtSameLevel";
  @SerializedName(SERIALIZED_NAME_IS_PRORATED_AT_SAME_LEVEL)
  private Boolean isProratedAtSameLevel;

  public static final String SERIALIZED_NAME_IS_RETURN_CHARGE = "isReturnCharge";
  @SerializedName(SERIALIZED_NAME_IS_RETURN_CHARGE)
  private Boolean isReturnCharge;

  public static final String SERIALIZED_NAME_IS_TAX_INCLUDED = "isTaxIncluded";
  @SerializedName(SERIALIZED_NAME_IS_TAX_INCLUDED)
  private Boolean isTaxIncluded;

  public static final String SERIALIZED_NAME_ORDER_LINE_CHARGE_DETAIL_ID = "orderLineChargeDetailId";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_CHARGE_DETAIL_ID)
  private String orderLineChargeDetailId;

  public static final String SERIALIZED_NAME_PARENT_CHARGE_DETAIL_ID = "parentChargeDetailId";
  @SerializedName(SERIALIZED_NAME_PARENT_CHARGE_DETAIL_ID)
  private String parentChargeDetailId;

  public static final String SERIALIZED_NAME_RELATED_CHARGE_DETAIL_ID = "relatedChargeDetailId";
  @SerializedName(SERIALIZED_NAME_RELATED_CHARGE_DETAIL_ID)
  private String relatedChargeDetailId;

  public static final String SERIALIZED_NAME_RELATED_CHARGE_TYPE = "relatedChargeType";
  @SerializedName(SERIALIZED_NAME_RELATED_CHARGE_TYPE)
  private String relatedChargeType;

  public static final String SERIALIZED_NAME_RELATED_ORDER_LINE_ID = "relatedOrderLineId";
  @SerializedName(SERIALIZED_NAME_RELATED_ORDER_LINE_ID)
  private String relatedOrderLineId;

  public static final String SERIALIZED_NAME_REQUESTED_AMOUNT = "requestedAmount";
  @SerializedName(SERIALIZED_NAME_REQUESTED_AMOUNT)
  private BigDecimal requestedAmount;

  public static final String SERIALIZED_NAME_TAX_CODE = "taxCode";
  @SerializedName(SERIALIZED_NAME_TAX_CODE)
  private String taxCode;

  public static final String SERIALIZED_NAME_UNIT_CHARGE = "unitCharge";
  @SerializedName(SERIALIZED_NAME_UNIT_CHARGE)
  private Double unitCharge;

  public InvoiceLineChargeDetailRequest chargePercent(Double chargePercent) {
    this.chargePercent = chargePercent;
    return this;
  }

   /**
   * Get chargePercent
   * @return chargePercent
  **/
  
  public Double getChargePercent() {
    return chargePercent;
  }

  public void setChargePercent(Double chargePercent) {
    this.chargePercent = chargePercent;
  }

  public InvoiceLineChargeDetailRequest chargeReferenceId(String chargeReferenceId) {
    this.chargeReferenceId = chargeReferenceId;
    return this;
  }

   /**
   * Get chargeReferenceId
   * @return chargeReferenceId
  **/
  
  public String getChargeReferenceId() {
    return chargeReferenceId;
  }

  public void setChargeReferenceId(String chargeReferenceId) {
    this.chargeReferenceId = chargeReferenceId;
  }

  public InvoiceLineChargeDetailRequest chargeSequence(Long chargeSequence) {
    this.chargeSequence = chargeSequence;
    return this;
  }

   /**
   * Get chargeSequence
   * @return chargeSequence
  **/
  
  public Long getChargeSequence() {
    return chargeSequence;
  }

  public void setChargeSequence(Long chargeSequence) {
    this.chargeSequence = chargeSequence;
  }

  public InvoiceLineChargeDetailRequest chargeTotal(BigDecimal chargeTotal) {
    this.chargeTotal = chargeTotal;
    return this;
  }

   /**
   * Get chargeTotal
   * @return chargeTotal
  **/
  
  public BigDecimal getChargeTotal() {
    return chargeTotal;
  }

  public void setChargeTotal(BigDecimal chargeTotal) {
    this.chargeTotal = chargeTotal;
  }

  public InvoiceLineChargeDetailRequest chargeTypeId(String chargeTypeId) {
    this.chargeTypeId = chargeTypeId;
    return this;
  }

   /**
   * Get chargeTypeId
   * @return chargeTypeId
  **/
  
  public String getChargeTypeId() {
    return chargeTypeId;
  }

  public void setChargeTypeId(String chargeTypeId) {
    this.chargeTypeId = chargeTypeId;
  }

  public InvoiceLineChargeDetailRequest comments(String comments) {
    this.comments = comments;
    return this;
  }

   /**
   * Get comments
   * @return comments
  **/
  
  public String getComments() {
    return comments;
  }

  public void setComments(String comments) {
    this.comments = comments;
  }

  public InvoiceLineChargeDetailRequest discountOnId(String discountOnId) {
    this.discountOnId = discountOnId;
    return this;
  }

   /**
   * Get discountOnId
   * @return discountOnId
  **/
  
  public String getDiscountOnId() {
    return discountOnId;
  }

  public void setDiscountOnId(String discountOnId) {
    this.discountOnId = discountOnId;
  }

  public InvoiceLineChargeDetailRequest extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Get extended
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public InvoiceLineChargeDetailRequest fulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
    return this;
  }

   /**
   * Get fulfillmentGroupId
   * @return fulfillmentGroupId
  **/
  
  public String getFulfillmentGroupId() {
    return fulfillmentGroupId;
  }

  public void setFulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
  }

  public InvoiceLineChargeDetailRequest headerChargeDetailId(String headerChargeDetailId) {
    this.headerChargeDetailId = headerChargeDetailId;
    return this;
  }

   /**
   * Get headerChargeDetailId
   * @return headerChargeDetailId
  **/
  
  public String getHeaderChargeDetailId() {
    return headerChargeDetailId;
  }

  public void setHeaderChargeDetailId(String headerChargeDetailId) {
    this.headerChargeDetailId = headerChargeDetailId;
  }

  public InvoiceLineChargeDetailRequest isInformational(Boolean isInformational) {
    this.isInformational = isInformational;
    return this;
  }

   /**
   * Get isInformational
   * @return isInformational
  **/
  
  public Boolean getIsInformational() {
    return isInformational;
  }

  public void setIsInformational(Boolean isInformational) {
    this.isInformational = isInformational;
  }

  public InvoiceLineChargeDetailRequest isPostReturn(Boolean isPostReturn) {
    this.isPostReturn = isPostReturn;
    return this;
  }

   /**
   * Get isPostReturn
   * @return isPostReturn
  **/
  
  public Boolean getIsPostReturn() {
    return isPostReturn;
  }

  public void setIsPostReturn(Boolean isPostReturn) {
    this.isPostReturn = isPostReturn;
  }

  public InvoiceLineChargeDetailRequest isProratedAtSameLevel(Boolean isProratedAtSameLevel) {
    this.isProratedAtSameLevel = isProratedAtSameLevel;
    return this;
  }

   /**
   * Get isProratedAtSameLevel
   * @return isProratedAtSameLevel
  **/
  
  public Boolean getIsProratedAtSameLevel() {
    return isProratedAtSameLevel;
  }

  public void setIsProratedAtSameLevel(Boolean isProratedAtSameLevel) {
    this.isProratedAtSameLevel = isProratedAtSameLevel;
  }

  public InvoiceLineChargeDetailRequest isReturnCharge(Boolean isReturnCharge) {
    this.isReturnCharge = isReturnCharge;
    return this;
  }

   /**
   * Get isReturnCharge
   * @return isReturnCharge
  **/
  
  public Boolean getIsReturnCharge() {
    return isReturnCharge;
  }

  public void setIsReturnCharge(Boolean isReturnCharge) {
    this.isReturnCharge = isReturnCharge;
  }

  public InvoiceLineChargeDetailRequest isTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
    return this;
  }

   /**
   * Get isTaxIncluded
   * @return isTaxIncluded
  **/
  
  public Boolean getIsTaxIncluded() {
    return isTaxIncluded;
  }

  public void setIsTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
  }

  public InvoiceLineChargeDetailRequest orderLineChargeDetailId(String orderLineChargeDetailId) {
    this.orderLineChargeDetailId = orderLineChargeDetailId;
    return this;
  }

   /**
   * Get orderLineChargeDetailId
   * @return orderLineChargeDetailId
  **/
  
  public String getOrderLineChargeDetailId() {
    return orderLineChargeDetailId;
  }

  public void setOrderLineChargeDetailId(String orderLineChargeDetailId) {
    this.orderLineChargeDetailId = orderLineChargeDetailId;
  }

  public InvoiceLineChargeDetailRequest parentChargeDetailId(String parentChargeDetailId) {
    this.parentChargeDetailId = parentChargeDetailId;
    return this;
  }

   /**
   * Get parentChargeDetailId
   * @return parentChargeDetailId
  **/
  
  public String getParentChargeDetailId() {
    return parentChargeDetailId;
  }

  public void setParentChargeDetailId(String parentChargeDetailId) {
    this.parentChargeDetailId = parentChargeDetailId;
  }

  public InvoiceLineChargeDetailRequest relatedChargeDetailId(String relatedChargeDetailId) {
    this.relatedChargeDetailId = relatedChargeDetailId;
    return this;
  }

   /**
   * Get relatedChargeDetailId
   * @return relatedChargeDetailId
  **/
  
  public String getRelatedChargeDetailId() {
    return relatedChargeDetailId;
  }

  public void setRelatedChargeDetailId(String relatedChargeDetailId) {
    this.relatedChargeDetailId = relatedChargeDetailId;
  }

  public InvoiceLineChargeDetailRequest relatedChargeType(String relatedChargeType) {
    this.relatedChargeType = relatedChargeType;
    return this;
  }

   /**
   * Get relatedChargeType
   * @return relatedChargeType
  **/
  
  public String getRelatedChargeType() {
    return relatedChargeType;
  }

  public void setRelatedChargeType(String relatedChargeType) {
    this.relatedChargeType = relatedChargeType;
  }

  public InvoiceLineChargeDetailRequest relatedOrderLineId(String relatedOrderLineId) {
    this.relatedOrderLineId = relatedOrderLineId;
    return this;
  }

   /**
   * Get relatedOrderLineId
   * @return relatedOrderLineId
  **/
  
  public String getRelatedOrderLineId() {
    return relatedOrderLineId;
  }

  public void setRelatedOrderLineId(String relatedOrderLineId) {
    this.relatedOrderLineId = relatedOrderLineId;
  }

  public InvoiceLineChargeDetailRequest requestedAmount(BigDecimal requestedAmount) {
    this.requestedAmount = requestedAmount;
    return this;
  }

   /**
   * Get requestedAmount
   * @return requestedAmount
  **/
  
  public BigDecimal getRequestedAmount() {
    return requestedAmount;
  }

  public void setRequestedAmount(BigDecimal requestedAmount) {
    this.requestedAmount = requestedAmount;
  }

  public InvoiceLineChargeDetailRequest taxCode(String taxCode) {
    this.taxCode = taxCode;
    return this;
  }

   /**
   * Get taxCode
   * @return taxCode
  **/
  
  public String getTaxCode() {
    return taxCode;
  }

  public void setTaxCode(String taxCode) {
    this.taxCode = taxCode;
  }

  public InvoiceLineChargeDetailRequest unitCharge(Double unitCharge) {
    this.unitCharge = unitCharge;
    return this;
  }

   /**
   * Get unitCharge
   * @return unitCharge
  **/
  
  public Double getUnitCharge() {
    return unitCharge;
  }

  public void setUnitCharge(Double unitCharge) {
    this.unitCharge = unitCharge;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvoiceLineChargeDetailRequest invoiceLineChargeDetailRequest = (InvoiceLineChargeDetailRequest) o;
    return Objects.equals(this.chargePercent, invoiceLineChargeDetailRequest.chargePercent) &&
        Objects.equals(this.chargeReferenceId, invoiceLineChargeDetailRequest.chargeReferenceId) &&
        Objects.equals(this.chargeSequence, invoiceLineChargeDetailRequest.chargeSequence) &&
        Objects.equals(this.chargeTotal, invoiceLineChargeDetailRequest.chargeTotal) &&
        Objects.equals(this.chargeTypeId, invoiceLineChargeDetailRequest.chargeTypeId) &&
        Objects.equals(this.comments, invoiceLineChargeDetailRequest.comments) &&
        Objects.equals(this.discountOnId, invoiceLineChargeDetailRequest.discountOnId) &&
        Objects.equals(this.extended, invoiceLineChargeDetailRequest.extended) &&
        Objects.equals(this.fulfillmentGroupId, invoiceLineChargeDetailRequest.fulfillmentGroupId) &&
        Objects.equals(this.headerChargeDetailId, invoiceLineChargeDetailRequest.headerChargeDetailId) &&
        Objects.equals(this.isInformational, invoiceLineChargeDetailRequest.isInformational) &&
        Objects.equals(this.isPostReturn, invoiceLineChargeDetailRequest.isPostReturn) &&
        Objects.equals(this.isProratedAtSameLevel, invoiceLineChargeDetailRequest.isProratedAtSameLevel) &&
        Objects.equals(this.isReturnCharge, invoiceLineChargeDetailRequest.isReturnCharge) &&
        Objects.equals(this.isTaxIncluded, invoiceLineChargeDetailRequest.isTaxIncluded) &&
        Objects.equals(this.orderLineChargeDetailId, invoiceLineChargeDetailRequest.orderLineChargeDetailId) &&
        Objects.equals(this.parentChargeDetailId, invoiceLineChargeDetailRequest.parentChargeDetailId) &&
        Objects.equals(this.relatedChargeDetailId, invoiceLineChargeDetailRequest.relatedChargeDetailId) &&
        Objects.equals(this.relatedChargeType, invoiceLineChargeDetailRequest.relatedChargeType) &&
        Objects.equals(this.relatedOrderLineId, invoiceLineChargeDetailRequest.relatedOrderLineId) &&
        Objects.equals(this.requestedAmount, invoiceLineChargeDetailRequest.requestedAmount) &&
        Objects.equals(this.taxCode, invoiceLineChargeDetailRequest.taxCode) &&
        Objects.equals(this.unitCharge, invoiceLineChargeDetailRequest.unitCharge);
  }

  @Override
  public int hashCode() {
    return Objects.hash(chargePercent, chargeReferenceId, chargeSequence, chargeTotal, chargeTypeId, comments, discountOnId, extended, fulfillmentGroupId, headerChargeDetailId, isInformational, isPostReturn, isProratedAtSameLevel, isReturnCharge, isTaxIncluded, orderLineChargeDetailId, parentChargeDetailId, relatedChargeDetailId, relatedChargeType, relatedOrderLineId, requestedAmount, taxCode, unitCharge);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvoiceLineChargeDetailRequest {\n");
    
    sb.append("    chargePercent: ").append(toIndentedString(chargePercent)).append("\n");
    sb.append("    chargeReferenceId: ").append(toIndentedString(chargeReferenceId)).append("\n");
    sb.append("    chargeSequence: ").append(toIndentedString(chargeSequence)).append("\n");
    sb.append("    chargeTotal: ").append(toIndentedString(chargeTotal)).append("\n");
    sb.append("    chargeTypeId: ").append(toIndentedString(chargeTypeId)).append("\n");
    sb.append("    comments: ").append(toIndentedString(comments)).append("\n");
    sb.append("    discountOnId: ").append(toIndentedString(discountOnId)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    fulfillmentGroupId: ").append(toIndentedString(fulfillmentGroupId)).append("\n");
    sb.append("    headerChargeDetailId: ").append(toIndentedString(headerChargeDetailId)).append("\n");
    sb.append("    isInformational: ").append(toIndentedString(isInformational)).append("\n");
    sb.append("    isPostReturn: ").append(toIndentedString(isPostReturn)).append("\n");
    sb.append("    isProratedAtSameLevel: ").append(toIndentedString(isProratedAtSameLevel)).append("\n");
    sb.append("    isReturnCharge: ").append(toIndentedString(isReturnCharge)).append("\n");
    sb.append("    isTaxIncluded: ").append(toIndentedString(isTaxIncluded)).append("\n");
    sb.append("    orderLineChargeDetailId: ").append(toIndentedString(orderLineChargeDetailId)).append("\n");
    sb.append("    parentChargeDetailId: ").append(toIndentedString(parentChargeDetailId)).append("\n");
    sb.append("    relatedChargeDetailId: ").append(toIndentedString(relatedChargeDetailId)).append("\n");
    sb.append("    relatedChargeType: ").append(toIndentedString(relatedChargeType)).append("\n");
    sb.append("    relatedOrderLineId: ").append(toIndentedString(relatedOrderLineId)).append("\n");
    sb.append("    requestedAmount: ").append(toIndentedString(requestedAmount)).append("\n");
    sb.append("    taxCode: ").append(toIndentedString(taxCode)).append("\n");
    sb.append("    unitCharge: ").append(toIndentedString(unitCharge)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

