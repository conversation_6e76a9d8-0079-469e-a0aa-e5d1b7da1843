package uk.co.flexi.sdk.oms.mao.client;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.google.gson.Gson;
import okhttp3.*;
import okhttp3.Request.Builder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import uk.co.flexi.sdk.oms.mao.client.config.MaoConfig;
import uk.co.flexi.sdk.oms.mao.client.gson.GsonFactoryBean;
import uk.co.flexi.sdk.oms.mao.client.interceptor.LoggingInterceptor;
import uk.co.flexi.sdk.oms.mao.client.interceptor.MaoAccessTokenInterceptor;
import uk.co.flexi.sdk.oms.mao.client.interceptor.MaoAccessTokenRepository;
import uk.co.flexi.sdk.oms.mao.model.item.Item;
import uk.co.flexi.sdk.oms.mao.model.item.RestApiResponseItemDTO;
import uk.co.flexi.sdk.oms.mao.model.order.*;
import uk.co.flexi.sdk.oms.model.ItemData;
import uk.co.flexi.sdk.oms.model.OrderData;
import uk.co.flexi.sdk.oms.model.OrderLineData;
import uk.co.flexi.sdk.oms.model.OrderLineItemData;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;


public class MaoOmsClient implements OMSClient<MaoConfig> {

    private static final Logger log = LoggerFactory.getLogger(MaoOmsClient.class);

    private final OkHttpClient client;
    private final MaoConfig maoConfig;

    private static final ObjectWriter ow = new ObjectMapper().writer().withDefaultPrettyPrinter();


    public MaoOmsClient(MaoConfig maoConfig) {
        this.maoConfig = maoConfig;
        this.client = new OkHttpClient().newBuilder()
                .addInterceptor(
                        new MaoAccessTokenInterceptor(
                                new MaoAccessTokenRepository(new MaoAuthClient(
                                        maoConfig.getAuthUrl(),
                                        maoConfig.getBasicAuthUser(),
                                        maoConfig.getBasicAuthPassword(),
                                        maoConfig.getUsername(),
                                        maoConfig.getPassword()
                                ))
                        )
                )
                .addInterceptor(new LoggingInterceptor()).build();
    }

    @Override
    public OrderData getReturnOrderByTrackingNumber(String referenceId) {
        try {
            final Map<String, Object> template = new HashMap<>();
            final Map<String, Object> partnerAttributes = new HashMap<>();
            partnerAttributes.put("PartnerId", "");
            partnerAttributes.put("PartnerName", "");
            template.put("OrderId", "");
            template.put("Extended", partnerAttributes);
            template.put("OrderLine", "");
            template.put("ReturnTrackingDetail", "");
            template.put("ReturnLabel", "");
            template.put("OrderTrackingInfo", "");
            template.put("CustomerId", "");
            template.put("CustomerEmail", "");
            template.put("OrderExtension1", "");

            Query query = new Query();
            query.setQuery("ReturnTrackingDetail.ReturnTrackingNumber=" + referenceId);
            query.setPage(0);
            query.setSize(20);
            query.setTemplate(template);
            Request request = new Builder()
                    .url(maoConfig.getBasePath() + "/order/api/order/order/search")
                    .header("Content-Type", "application/json")
                    .header("OrganizationList", maoConfig.getSearchOrganizations())
                    .post(RequestBody.create(new Gson().toJson(query).getBytes(StandardCharsets.UTF_8)))
                    .build();
            Response response = client.newCall(request).execute();
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                RestApiResponseOrderListDTO apiResponse = GsonFactoryBean.getInstance().fromJson(responseBody, RestApiResponseOrderListDTO.class);
                List<Order> orders = apiResponse.getData();
                if (!orders.isEmpty())
                    return convert(orders.getFirst());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public OrderData getOrderByProductSerialNumber(String serialNumber) {
        try {
            final Map<String, Object> template = new HashMap<>();
            template.put("OrderId", "");
            Query query = new Query();
            query.setQuery("FulfillmentDetail.SerialNumber=" + serialNumber);
            query.setPage(0);
            query.setSize(20);
            query.setTemplate(template);
            Request request = new Builder()
                    .url(maoConfig.getBasePath() + "/order/api/order/order/orderLine/search")
                    .header("Content-Type", "application/json")
                    .header("OrganizationList", maoConfig.getSearchOrganizations())
                    .post(RequestBody.create(new Gson().toJson(query).getBytes(StandardCharsets.UTF_8)))
                    .build();
            Response response = client.newCall(request).execute();
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                RestApiResponseOrderLineListDTO apiResponse = GsonFactoryBean.getInstance().fromJson(
                        responseBody, RestApiResponseOrderLineListDTO.class);
                List<OrderLine> orderLines = apiResponse.getData();
                if (!orderLines.isEmpty())
                    return getOrderById(orderLines.getFirst().getOrderId());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public OrderData getOrderByReturnTrackingNumber(String referenceId) {
        try {
            final Map<String, Object> template = new HashMap<>();
            template.put("OrderId", "");
            Query query = new Query();
            query.setQuery("FulfillmentDetail.ReturnTrackingNumber=" + referenceId);
            query.setPage(0);
            query.setSize(20);
            query.setTemplate(template);
            Request request = new Builder()
                    .url(maoConfig.getBasePath() + "/order/api/order/order/orderLine/search")
                    .header("Content-Type", "application/json")
                    .header("OrganizationList", maoConfig.getSearchOrganizations())
                    .post(RequestBody.create(new Gson().toJson(query).getBytes(StandardCharsets.UTF_8)))
                    .build();
            Response response = client.newCall(request).execute();
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                RestApiResponseOrderLineListDTO apiResponse = GsonFactoryBean.getInstance().fromJson(
                        responseBody, RestApiResponseOrderLineListDTO.class);
                List<OrderLine> orderLines = apiResponse.getData();
                if (!orderLines.isEmpty())
                    return getOrderById(orderLines.getFirst().getOrderId());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    @Override
    public OrderData getOrderById(String orderId) {

        try {
            HttpUrl.Builder urlBuilder = Objects.requireNonNull(
                    HttpUrl.parse(maoConfig.getBasePath() + "/order/api/order/order"))
                    .newBuilder()
                    .addQueryParameter("query", "OrderId=" + orderId);
            Request request = new Builder()
                    .url(urlBuilder.build())
                    .header("Content-Type", "application/json")
                    .header("OrganizationList", maoConfig.getSearchOrganizations())
                    .get().build();
            Response response = client.newCall(request).execute();
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                RestApiResponseOrderListDTO apiResponse = GsonFactoryBean.getInstance().fromJson(responseBody, RestApiResponseOrderListDTO.class);
                List<Order> orders = apiResponse.getData();
                if (!orders.isEmpty())
                    return convert(orders.getFirst());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    private OrderData convert(Order order) {
        return new OrderData().setOrderId(order.getOrderId())
                .setTrackingNumber(order.getReturnTrackingDetail() != null && !order.getReturnTrackingDetail().isEmpty() ?
                    order.getReturnTrackingDetail().getFirst().getReturnTrackingNumber() : null)
                .setParentOrderId(order.getOrderLine() != null && !order.getOrderLine().isEmpty() ? order.getOrderLine().getFirst().getParentOrderId()
                        : null)
                .setPartnerName(order.getExtended() == null || order.getExtended().get("PartnerName") == null
                        ? "" : (String)order.getExtended().get("PartnerName"))
                .setSellingChannel(order.getSellingChannel() != null ? order.getSellingChannel().getSellingChannelId() : "")
                .setCustomerFirstName(order.getCustomerFirstName())
                .setCustomerLastName(order.getCustomerLastName())
                .setCustomerEmail(order.getCustomerEmail())
                .setOrderLines(order.getOrderLine().stream().map(ol -> {
                    OrderLineData ld = new OrderLineData().setOrderLineId(ol.getOrderLineId())
                        .setItem(new OrderLineItemData()
                                .setQuantity(ol.getQuantity())
                                .setSku(ol.getItemId())
                                .setStyle(Objects.toString(ol.getItemStyle(), ""))
                                .setSize(Objects.toString(ol.getItemSize(), ""))
                                .setName(Objects.toString(ol.getItemShortDescription(), ""))
                                .setColor(Objects.toString(ol.getItemColorDescription(), ""))
                                .setDescription(Objects.toString(ol.getItemDescription(), ""))
                                .setImages(Collections.singletonList(ol.getSmallImageURI()))
                                .setDepartmentName(ol.getItemDepartmentName())
                                .setCurrencyCode(order.getCurrencyCode())
                                .setUnitPrice(
                                        ol.getUnitPrice() != null ? ol.getUnitPrice().abs() : null
                                )
                        );
                    ItemData item = getItem(ol.getItemId());
                    if (item != null) {
                        OrderLineItemData id = ld.getItem();
                        List<String> images = new ArrayList<>();
                        if (id.getImages() != null) {
                            images.addAll(ld.getItem().getImages());
                        }
                        images.add(item.getLargeImage());
                        id.setImages(images);
                        id.setBrand(Objects.toString(item.getBrand()));
                        id.setSeason(Objects.toString(item.getSeason()));
                        id.setProductClass(Objects.toString(item.getProductClass()));
                        id.setProductSubclass(Objects.toString(item.getProductSubclass()));
                    }

                    //Additional attributes
                    OrderLineAdditional additional = ol.getOrderLineAdditional();
                    if (additional != null) {
                        ld.setReturnReason(additional.getReturnReason());
                        ld.setItemCondition(additional.getItemCondition() != null ? additional.getItemCondition().getItemConditionId() : null);
                    }

                    ShipToAddress address = ol.getIsReturn() == Boolean.TRUE ? ol.getShipFromAddress() : ol.getShipToAddress();
                    if (address != null) {
                        ld.setCountry(address.getAddress().getCountry());
                        ld.setRegion(address.getAddress().getState());
                        ld.setCity(address.getAddress().getCity());
                    }
                    return ld;
                }).toList());
    }

    private ItemData getItem(String sku) {
        try {
            Request request = new Builder()
                    .url(maoConfig.getBasePath() + "/item/api/item/item/itemId/" + sku)
                    .header("Content-Type", "application/json")
                    .header("Organization", maoConfig.getProductOrganization())
                    .get().build();
            Response response = client.newCall(request).execute();
            String responseBody = response.body().string();
            if (response.isSuccessful()) {
                RestApiResponseItemDTO apiResponse = GsonFactoryBean.getInstance().fromJson(
                        responseBody, RestApiResponseItemDTO.class);
                Item item = apiResponse.getData();
                if (item != null)
                    return convert(item);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    private ItemData convert(Item item) {
        return new ItemData().setStyle(Objects.toString(item.getStyle(), ""))
                .setProductClass(Objects.toString(item.getProductClass(), ""))
                .setProductSubclass(Objects.toString(item.getProductSubClass(), ""))
                .setSmallImage(Objects.toString(item.getSmallImageURI(), ""))
                .setLargeImage(Objects.toString(item.getLargeImageURI(), ""))
                .setSeason(Objects.toString(item.getSeason(), ""));
    }
}
