/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderLinePickupDetail
 */
public class OrderLinePickupDetail {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EMAIL = "Email";
  @SerializedName(SERIALIZED_NAME_EMAIL)
  private String email;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FIRST_NAME = "FirstName";
  @SerializedName(SERIALIZED_NAME_FIRST_NAME)
  private String firstName;

  public static final String SERIALIZED_NAME_IS_PRIMARY = "IsPrimary";
  @SerializedName(SERIALIZED_NAME_IS_PRIMARY)
  private Boolean isPrimary;

  public static final String SERIALIZED_NAME_LAST_NAME = "LastName";
  @SerializedName(SERIALIZED_NAME_LAST_NAME)
  private String lastName;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_PICKUP_DETAIL_ID = "OrderLinePickupDetailId";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_PICKUP_DETAIL_ID)
  private String orderLinePickupDetailId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_ORDER_LINE = "ParentOrderLine";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER_LINE)
  private PrimaryKey parentOrderLine = null;

  public static final String SERIALIZED_NAME_PHONE = "Phone";
  @SerializedName(SERIALIZED_NAME_PHONE)
  private String phone;

  public static final String SERIALIZED_NAME_PICKUP_END_DATE = "PickupEndDate";
  @SerializedName(SERIALIZED_NAME_PICKUP_END_DATE)
  private OffsetDateTime pickupEndDate;

  public static final String SERIALIZED_NAME_PICKUP_START_DATE = "PickupStartDate";
  @SerializedName(SERIALIZED_NAME_PICKUP_START_DATE)
  private OffsetDateTime pickupStartDate;

  public static final String SERIALIZED_NAME_PREFERRED_CONTACT_METHOD = "PreferredContactMethod";
  @SerializedName(SERIALIZED_NAME_PREFERRED_CONTACT_METHOD)
  private String preferredContactMethod;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderLinePickupDetail actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderLinePickupDetail putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderLinePickupDetail createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderLinePickupDetail createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderLinePickupDetail email(String email) {
    this.email = email;
    return this;
  }

   /**
   * Email Id
   * @return email
  **/
  
  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public OrderLinePickupDetail extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderLinePickupDetail firstName(String firstName) {
    this.firstName = firstName;
    return this;
  }

   /**
   * First Name
   * @return firstName
  **/
  
  public String getFirstName() {
    return firstName;
  }

  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  public OrderLinePickupDetail isPrimary(Boolean isPrimary) {
    this.isPrimary = isPrimary;
    return this;
  }

   /**
   * Indicates if the person is designated as the primary pick up person
   * @return isPrimary
  **/
  
  public Boolean getIsPrimary() {
    return isPrimary;
  }

  public void setIsPrimary(Boolean isPrimary) {
    this.isPrimary = isPrimary;
  }

  public OrderLinePickupDetail lastName(String lastName) {
    this.lastName = lastName;
    return this;
  }

   /**
   * Last Name
   * @return lastName
  **/
  
  public String getLastName() {
    return lastName;
  }

  public void setLastName(String lastName) {
    this.lastName = lastName;
  }

  public OrderLinePickupDetail localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderLinePickupDetail messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderLinePickupDetail orderLinePickupDetailId(String orderLinePickupDetailId) {
    this.orderLinePickupDetailId = orderLinePickupDetailId;
    return this;
  }

   /**
   * Unique identifier of order Line Pickup Detail table
   * @return orderLinePickupDetailId
  **/
  
  public String getOrderLinePickupDetailId() {
    return orderLinePickupDetailId;
  }

  public void setOrderLinePickupDetailId(String orderLinePickupDetailId) {
    this.orderLinePickupDetailId = orderLinePickupDetailId;
  }

  public OrderLinePickupDetail orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderLinePickupDetail PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderLinePickupDetail parentOrderLine(PrimaryKey parentOrderLine) {
    this.parentOrderLine = parentOrderLine;
    return this;
  }

   /**
   * Get parentOrderLine
   * @return parentOrderLine
  **/
  
  public PrimaryKey getParentOrderLine() {
    return parentOrderLine;
  }

  public void setParentOrderLine(PrimaryKey parentOrderLine) {
    this.parentOrderLine = parentOrderLine;
  }

  public OrderLinePickupDetail phone(String phone) {
    this.phone = phone;
    return this;
  }

   /**
   * Phone Number
   * @return phone
  **/
  
  public String getPhone() {
    return phone;
  }

  public void setPhone(String phone) {
    this.phone = phone;
  }

  public OrderLinePickupDetail pickupEndDate(OffsetDateTime pickupEndDate) {
    this.pickupEndDate = pickupEndDate;
    return this;
  }

   /**
   * Pick up End Date
   * @return pickupEndDate
  **/
  
  public OffsetDateTime getPickupEndDate() {
    return pickupEndDate;
  }

  public void setPickupEndDate(OffsetDateTime pickupEndDate) {
    this.pickupEndDate = pickupEndDate;
  }

  public OrderLinePickupDetail pickupStartDate(OffsetDateTime pickupStartDate) {
    this.pickupStartDate = pickupStartDate;
    return this;
  }

   /**
   * Pick up start Date
   * @return pickupStartDate
  **/
  
  public OffsetDateTime getPickupStartDate() {
    return pickupStartDate;
  }

  public void setPickupStartDate(OffsetDateTime pickupStartDate) {
    this.pickupStartDate = pickupStartDate;
  }

  public OrderLinePickupDetail preferredContactMethod(String preferredContactMethod) {
    this.preferredContactMethod = preferredContactMethod;
    return this;
  }

   /**
   * Preferred Contact Method
   * @return preferredContactMethod
  **/
  
  public String getPreferredContactMethod() {
    return preferredContactMethod;
  }

  public void setPreferredContactMethod(String preferredContactMethod) {
    this.preferredContactMethod = preferredContactMethod;
  }

  public OrderLinePickupDetail updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderLinePickupDetail updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderLinePickupDetail entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderLinePickupDetail localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderLinePickupDetail orderLinePickupDetail = (OrderLinePickupDetail) o;
    return Objects.equals(this.actions, orderLinePickupDetail.actions) &&
        Objects.equals(this.createdBy, orderLinePickupDetail.createdBy) &&
        Objects.equals(this.createdTimestamp, orderLinePickupDetail.createdTimestamp) &&
        Objects.equals(this.email, orderLinePickupDetail.email) &&
        Objects.equals(this.extended, orderLinePickupDetail.extended) &&
        Objects.equals(this.firstName, orderLinePickupDetail.firstName) &&
        Objects.equals(this.isPrimary, orderLinePickupDetail.isPrimary) &&
        Objects.equals(this.lastName, orderLinePickupDetail.lastName) &&
        Objects.equals(this.localizedTo, orderLinePickupDetail.localizedTo) &&
        Objects.equals(this.messages, orderLinePickupDetail.messages) &&
        Objects.equals(this.orderLinePickupDetailId, orderLinePickupDetail.orderLinePickupDetailId) &&
        Objects.equals(this.orgId, orderLinePickupDetail.orgId) &&
        Objects.equals(this.PK, orderLinePickupDetail.PK) &&
        Objects.equals(this.parentOrderLine, orderLinePickupDetail.parentOrderLine) &&
        Objects.equals(this.phone, orderLinePickupDetail.phone) &&
        Objects.equals(this.pickupEndDate, orderLinePickupDetail.pickupEndDate) &&
        Objects.equals(this.pickupStartDate, orderLinePickupDetail.pickupStartDate) &&
        Objects.equals(this.preferredContactMethod, orderLinePickupDetail.preferredContactMethod) &&
        Objects.equals(this.updatedBy, orderLinePickupDetail.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderLinePickupDetail.updatedTimestamp) &&
        Objects.equals(this.entityName, orderLinePickupDetail.entityName) &&
        Objects.equals(this.localize, orderLinePickupDetail.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, email, extended, firstName, isPrimary, lastName, localizedTo, messages, orderLinePickupDetailId, orgId, PK, parentOrderLine, phone, pickupEndDate, pickupStartDate, preferredContactMethod, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderLinePickupDetail {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
    sb.append("    isPrimary: ").append(toIndentedString(isPrimary)).append("\n");
    sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderLinePickupDetailId: ").append(toIndentedString(orderLinePickupDetailId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentOrderLine: ").append(toIndentedString(parentOrderLine)).append("\n");
    sb.append("    phone: ").append(toIndentedString(phone)).append("\n");
    sb.append("    pickupEndDate: ").append(toIndentedString(pickupEndDate)).append("\n");
    sb.append("    pickupStartDate: ").append(toIndentedString(pickupStartDate)).append("\n");
    sb.append("    preferredContactMethod: ").append(toIndentedString(preferredContactMethod)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

