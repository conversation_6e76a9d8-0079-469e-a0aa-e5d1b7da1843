package uk.co.flexi.sdk.oms.mao.client.config;

import uk.co.flexi.sdk.oms.model.OMSClientConfig;

public class MockConfig implements OMSClientConfig {

    private final String basePath;

    private final String userName;

    private final String password;

    public MockConfig(String basePath, String userName, String password){
        this.basePath = basePath;
        this.userName = userName;
        this.password = password;
    }

    public String getBasePath() {
        return basePath;
    }

    public String getUserName() {
        return userName;
    }

    public String getPassword() {
        return password;
    }
}
