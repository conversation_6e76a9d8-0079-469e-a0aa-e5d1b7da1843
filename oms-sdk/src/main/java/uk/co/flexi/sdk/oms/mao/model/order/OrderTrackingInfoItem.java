package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class OrderTrackingInfoItem {

    @SerializedName("TrackingNumber")
    private String trackingNumber;

    @SerializedName("CarrierCode")
    private String carrierCode;

    @SerializedName("OrderTrackingDetail")
    private List<OrderTrackingDetailItem> orderTrackingDetailItem;

    @SerializedName("PK")
    private String pK;

    @SerializedName("PackageStatus")
    private PackageStatus packageStatus;

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public String getCarrierCode() {
        return carrierCode;
    }

    public List<OrderTrackingDetailItem> getOrderTrackingDetailItem() {
        return orderTrackingDetailItem;
    }

    public String getpK() {
        return pK;
    }

    public PackageStatus getPackageStatus() {
        return packageStatus;
    }
}
