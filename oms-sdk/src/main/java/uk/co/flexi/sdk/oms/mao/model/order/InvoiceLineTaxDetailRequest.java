/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * InvoiceLineTaxDetailRequest
 */
public class InvoiceLineTaxDetailRequest {
  public static final String SERIALIZED_NAME_FULFILLMENT_GROUP_ID = "fulfillmentGroupId";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_GROUP_ID)
  private String fulfillmentGroupId;

  public static final String SERIALIZED_NAME_HEADER_TAX_DETAIL_ID = "headerTaxDetailId";
  @SerializedName(SERIALIZED_NAME_HEADER_TAX_DETAIL_ID)
  private Object headerTaxDetailId = null;

  public static final String SERIALIZED_NAME_IS_INFORMATIONAL = "isInformational";
  @SerializedName(SERIALIZED_NAME_IS_INFORMATIONAL)
  private Boolean isInformational;

  public static final String SERIALIZED_NAME_IS_INVOICE_TAX = "isInvoiceTax";
  @SerializedName(SERIALIZED_NAME_IS_INVOICE_TAX)
  private Boolean isInvoiceTax;

  public static final String SERIALIZED_NAME_JURISDICTION = "jurisdiction";
  @SerializedName(SERIALIZED_NAME_JURISDICTION)
  private String jurisdiction;

  public static final String SERIALIZED_NAME_JURISDICTION_TYPE_ID = "jurisdictionTypeId";
  @SerializedName(SERIALIZED_NAME_JURISDICTION_TYPE_ID)
  private String jurisdictionTypeId;

  public static final String SERIALIZED_NAME_LINE_TAX = "lineTax";
  @SerializedName(SERIALIZED_NAME_LINE_TAX)
  private Boolean lineTax;

  public static final String SERIALIZED_NAME_TAX_AMOUNT = "taxAmount";
  @SerializedName(SERIALIZED_NAME_TAX_AMOUNT)
  private BigDecimal taxAmount;

  public static final String SERIALIZED_NAME_TAX_CODE = "taxCode";
  @SerializedName(SERIALIZED_NAME_TAX_CODE)
  private String taxCode;

  public static final String SERIALIZED_NAME_TAX_DATE = "taxDate";
  @SerializedName(SERIALIZED_NAME_TAX_DATE)
  private OffsetDateTime taxDate;

  public static final String SERIALIZED_NAME_TAX_ENGINE_ID = "taxEngineId";
  @SerializedName(SERIALIZED_NAME_TAX_ENGINE_ID)
  private String taxEngineId;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER1 = "taxIdentifier1";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER1)
  private String taxIdentifier1;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER2 = "taxIdentifier2";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER2)
  private String taxIdentifier2;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER3 = "taxIdentifier3";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER3)
  private String taxIdentifier3;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER4 = "taxIdentifier4";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER4)
  private String taxIdentifier4;

  public static final String SERIALIZED_NAME_TAX_IDENTIFIER5 = "taxIdentifier5";
  @SerializedName(SERIALIZED_NAME_TAX_IDENTIFIER5)
  private String taxIdentifier5;

  public static final String SERIALIZED_NAME_TAX_RATE = "taxRate";
  @SerializedName(SERIALIZED_NAME_TAX_RATE)
  private Double taxRate;

  public static final String SERIALIZED_NAME_TAX_TYPE_ID = "taxTypeId";
  @SerializedName(SERIALIZED_NAME_TAX_TYPE_ID)
  private String taxTypeId;

  public static final String SERIALIZED_NAME_TAXABLE_AMOUNT = "taxableAmount";
  @SerializedName(SERIALIZED_NAME_TAXABLE_AMOUNT)
  private BigDecimal taxableAmount;

  public InvoiceLineTaxDetailRequest fulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
    return this;
  }

   /**
   * Get fulfillmentGroupId
   * @return fulfillmentGroupId
  **/
  
  public String getFulfillmentGroupId() {
    return fulfillmentGroupId;
  }

  public void setFulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
  }

  public InvoiceLineTaxDetailRequest headerTaxDetailId(Object headerTaxDetailId) {
    this.headerTaxDetailId = headerTaxDetailId;
    return this;
  }

   /**
   * Get headerTaxDetailId
   * @return headerTaxDetailId
  **/
  
  public Object getHeaderTaxDetailId() {
    return headerTaxDetailId;
  }

  public void setHeaderTaxDetailId(Object headerTaxDetailId) {
    this.headerTaxDetailId = headerTaxDetailId;
  }

  public InvoiceLineTaxDetailRequest isInformational(Boolean isInformational) {
    this.isInformational = isInformational;
    return this;
  }

   /**
   * Get isInformational
   * @return isInformational
  **/
  
  public Boolean getIsInformational() {
    return isInformational;
  }

  public void setIsInformational(Boolean isInformational) {
    this.isInformational = isInformational;
  }

  public InvoiceLineTaxDetailRequest isInvoiceTax(Boolean isInvoiceTax) {
    this.isInvoiceTax = isInvoiceTax;
    return this;
  }

   /**
   * Get isInvoiceTax
   * @return isInvoiceTax
  **/
  
  public Boolean getIsInvoiceTax() {
    return isInvoiceTax;
  }

  public void setIsInvoiceTax(Boolean isInvoiceTax) {
    this.isInvoiceTax = isInvoiceTax;
  }

  public InvoiceLineTaxDetailRequest jurisdiction(String jurisdiction) {
    this.jurisdiction = jurisdiction;
    return this;
  }

   /**
   * Get jurisdiction
   * @return jurisdiction
  **/
  
  public String getJurisdiction() {
    return jurisdiction;
  }

  public void setJurisdiction(String jurisdiction) {
    this.jurisdiction = jurisdiction;
  }

  public InvoiceLineTaxDetailRequest jurisdictionTypeId(String jurisdictionTypeId) {
    this.jurisdictionTypeId = jurisdictionTypeId;
    return this;
  }

   /**
   * Get jurisdictionTypeId
   * @return jurisdictionTypeId
  **/
  
  public String getJurisdictionTypeId() {
    return jurisdictionTypeId;
  }

  public void setJurisdictionTypeId(String jurisdictionTypeId) {
    this.jurisdictionTypeId = jurisdictionTypeId;
  }

  public InvoiceLineTaxDetailRequest lineTax(Boolean lineTax) {
    this.lineTax = lineTax;
    return this;
  }

   /**
   * Get lineTax
   * @return lineTax
  **/
  
  public Boolean getLineTax() {
    return lineTax;
  }

  public void setLineTax(Boolean lineTax) {
    this.lineTax = lineTax;
  }

  public InvoiceLineTaxDetailRequest taxAmount(BigDecimal taxAmount) {
    this.taxAmount = taxAmount;
    return this;
  }

   /**
   * Get taxAmount
   * @return taxAmount
  **/
  
  public BigDecimal getTaxAmount() {
    return taxAmount;
  }

  public void setTaxAmount(BigDecimal taxAmount) {
    this.taxAmount = taxAmount;
  }

  public InvoiceLineTaxDetailRequest taxCode(String taxCode) {
    this.taxCode = taxCode;
    return this;
  }

   /**
   * Get taxCode
   * @return taxCode
  **/
  
  public String getTaxCode() {
    return taxCode;
  }

  public void setTaxCode(String taxCode) {
    this.taxCode = taxCode;
  }

  public InvoiceLineTaxDetailRequest taxDate(OffsetDateTime taxDate) {
    this.taxDate = taxDate;
    return this;
  }

   /**
   * Get taxDate
   * @return taxDate
  **/
  
  public OffsetDateTime getTaxDate() {
    return taxDate;
  }

  public void setTaxDate(OffsetDateTime taxDate) {
    this.taxDate = taxDate;
  }

  public InvoiceLineTaxDetailRequest taxEngineId(String taxEngineId) {
    this.taxEngineId = taxEngineId;
    return this;
  }

   /**
   * Get taxEngineId
   * @return taxEngineId
  **/
  
  public String getTaxEngineId() {
    return taxEngineId;
  }

  public void setTaxEngineId(String taxEngineId) {
    this.taxEngineId = taxEngineId;
  }

  public InvoiceLineTaxDetailRequest taxIdentifier1(String taxIdentifier1) {
    this.taxIdentifier1 = taxIdentifier1;
    return this;
  }

   /**
   * Get taxIdentifier1
   * @return taxIdentifier1
  **/
  
  public String getTaxIdentifier1() {
    return taxIdentifier1;
  }

  public void setTaxIdentifier1(String taxIdentifier1) {
    this.taxIdentifier1 = taxIdentifier1;
  }

  public InvoiceLineTaxDetailRequest taxIdentifier2(String taxIdentifier2) {
    this.taxIdentifier2 = taxIdentifier2;
    return this;
  }

   /**
   * Get taxIdentifier2
   * @return taxIdentifier2
  **/
  
  public String getTaxIdentifier2() {
    return taxIdentifier2;
  }

  public void setTaxIdentifier2(String taxIdentifier2) {
    this.taxIdentifier2 = taxIdentifier2;
  }

  public InvoiceLineTaxDetailRequest taxIdentifier3(String taxIdentifier3) {
    this.taxIdentifier3 = taxIdentifier3;
    return this;
  }

   /**
   * Get taxIdentifier3
   * @return taxIdentifier3
  **/
  
  public String getTaxIdentifier3() {
    return taxIdentifier3;
  }

  public void setTaxIdentifier3(String taxIdentifier3) {
    this.taxIdentifier3 = taxIdentifier3;
  }

  public InvoiceLineTaxDetailRequest taxIdentifier4(String taxIdentifier4) {
    this.taxIdentifier4 = taxIdentifier4;
    return this;
  }

   /**
   * Get taxIdentifier4
   * @return taxIdentifier4
  **/
  
  public String getTaxIdentifier4() {
    return taxIdentifier4;
  }

  public void setTaxIdentifier4(String taxIdentifier4) {
    this.taxIdentifier4 = taxIdentifier4;
  }

  public InvoiceLineTaxDetailRequest taxIdentifier5(String taxIdentifier5) {
    this.taxIdentifier5 = taxIdentifier5;
    return this;
  }

   /**
   * Get taxIdentifier5
   * @return taxIdentifier5
  **/
  
  public String getTaxIdentifier5() {
    return taxIdentifier5;
  }

  public void setTaxIdentifier5(String taxIdentifier5) {
    this.taxIdentifier5 = taxIdentifier5;
  }

  public InvoiceLineTaxDetailRequest taxRate(Double taxRate) {
    this.taxRate = taxRate;
    return this;
  }

   /**
   * Get taxRate
   * @return taxRate
  **/
  
  public Double getTaxRate() {
    return taxRate;
  }

  public void setTaxRate(Double taxRate) {
    this.taxRate = taxRate;
  }

  public InvoiceLineTaxDetailRequest taxTypeId(String taxTypeId) {
    this.taxTypeId = taxTypeId;
    return this;
  }

   /**
   * Get taxTypeId
   * @return taxTypeId
  **/
  
  public String getTaxTypeId() {
    return taxTypeId;
  }

  public void setTaxTypeId(String taxTypeId) {
    this.taxTypeId = taxTypeId;
  }

  public InvoiceLineTaxDetailRequest taxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
    return this;
  }

   /**
   * Get taxableAmount
   * @return taxableAmount
  **/
  
  public BigDecimal getTaxableAmount() {
    return taxableAmount;
  }

  public void setTaxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvoiceLineTaxDetailRequest invoiceLineTaxDetailRequest = (InvoiceLineTaxDetailRequest) o;
    return Objects.equals(this.fulfillmentGroupId, invoiceLineTaxDetailRequest.fulfillmentGroupId) &&
        Objects.equals(this.headerTaxDetailId, invoiceLineTaxDetailRequest.headerTaxDetailId) &&
        Objects.equals(this.isInformational, invoiceLineTaxDetailRequest.isInformational) &&
        Objects.equals(this.isInvoiceTax, invoiceLineTaxDetailRequest.isInvoiceTax) &&
        Objects.equals(this.jurisdiction, invoiceLineTaxDetailRequest.jurisdiction) &&
        Objects.equals(this.jurisdictionTypeId, invoiceLineTaxDetailRequest.jurisdictionTypeId) &&
        Objects.equals(this.lineTax, invoiceLineTaxDetailRequest.lineTax) &&
        Objects.equals(this.taxAmount, invoiceLineTaxDetailRequest.taxAmount) &&
        Objects.equals(this.taxCode, invoiceLineTaxDetailRequest.taxCode) &&
        Objects.equals(this.taxDate, invoiceLineTaxDetailRequest.taxDate) &&
        Objects.equals(this.taxEngineId, invoiceLineTaxDetailRequest.taxEngineId) &&
        Objects.equals(this.taxIdentifier1, invoiceLineTaxDetailRequest.taxIdentifier1) &&
        Objects.equals(this.taxIdentifier2, invoiceLineTaxDetailRequest.taxIdentifier2) &&
        Objects.equals(this.taxIdentifier3, invoiceLineTaxDetailRequest.taxIdentifier3) &&
        Objects.equals(this.taxIdentifier4, invoiceLineTaxDetailRequest.taxIdentifier4) &&
        Objects.equals(this.taxIdentifier5, invoiceLineTaxDetailRequest.taxIdentifier5) &&
        Objects.equals(this.taxRate, invoiceLineTaxDetailRequest.taxRate) &&
        Objects.equals(this.taxTypeId, invoiceLineTaxDetailRequest.taxTypeId) &&
        Objects.equals(this.taxableAmount, invoiceLineTaxDetailRequest.taxableAmount);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fulfillmentGroupId, headerTaxDetailId, isInformational, isInvoiceTax, jurisdiction, jurisdictionTypeId, lineTax, taxAmount, taxCode, taxDate, taxEngineId, taxIdentifier1, taxIdentifier2, taxIdentifier3, taxIdentifier4, taxIdentifier5, taxRate, taxTypeId, taxableAmount);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvoiceLineTaxDetailRequest {\n");
    
    sb.append("    fulfillmentGroupId: ").append(toIndentedString(fulfillmentGroupId)).append("\n");
    sb.append("    headerTaxDetailId: ").append(toIndentedString(headerTaxDetailId)).append("\n");
    sb.append("    isInformational: ").append(toIndentedString(isInformational)).append("\n");
    sb.append("    isInvoiceTax: ").append(toIndentedString(isInvoiceTax)).append("\n");
    sb.append("    jurisdiction: ").append(toIndentedString(jurisdiction)).append("\n");
    sb.append("    jurisdictionTypeId: ").append(toIndentedString(jurisdictionTypeId)).append("\n");
    sb.append("    lineTax: ").append(toIndentedString(lineTax)).append("\n");
    sb.append("    taxAmount: ").append(toIndentedString(taxAmount)).append("\n");
    sb.append("    taxCode: ").append(toIndentedString(taxCode)).append("\n");
    sb.append("    taxDate: ").append(toIndentedString(taxDate)).append("\n");
    sb.append("    taxEngineId: ").append(toIndentedString(taxEngineId)).append("\n");
    sb.append("    taxIdentifier1: ").append(toIndentedString(taxIdentifier1)).append("\n");
    sb.append("    taxIdentifier2: ").append(toIndentedString(taxIdentifier2)).append("\n");
    sb.append("    taxIdentifier3: ").append(toIndentedString(taxIdentifier3)).append("\n");
    sb.append("    taxIdentifier4: ").append(toIndentedString(taxIdentifier4)).append("\n");
    sb.append("    taxIdentifier5: ").append(toIndentedString(taxIdentifier5)).append("\n");
    sb.append("    taxRate: ").append(toIndentedString(taxRate)).append("\n");
    sb.append("    taxTypeId: ").append(toIndentedString(taxTypeId)).append("\n");
    sb.append("    taxableAmount: ").append(toIndentedString(taxableAmount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

