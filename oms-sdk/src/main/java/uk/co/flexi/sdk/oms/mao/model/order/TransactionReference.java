/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * TransactionReference
 */
public class TransactionReference {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DEVICE_ID = "DeviceId";
  @SerializedName(SERIALIZED_NAME_DEVICE_ID)
  private String deviceId;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IS_CANCELLED = "IsCancelled";
  @SerializedName(SERIALIZED_NAME_IS_CANCELLED)
  private Boolean isCancelled;

  public static final String SERIALIZED_NAME_IS_CONFIRMED = "IsConfirmed";
  @SerializedName(SERIALIZED_NAME_IS_CONFIRMED)
  private Boolean isConfirmed;

  public static final String SERIALIZED_NAME_IS_READY_FOR_TENDER = "IsReadyForTender";
  @SerializedName(SERIALIZED_NAME_IS_READY_FOR_TENDER)
  private Boolean isReadyForTender;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_OPERATOR_ID = "OperatorId";
  @SerializedName(SERIALIZED_NAME_OPERATOR_ID)
  private String operatorId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_POSTVOID_SIGNATURE = "PostvoidSignature";
  @SerializedName(SERIALIZED_NAME_POSTVOID_SIGNATURE)
  private String postvoidSignature;

  public static final String SERIALIZED_NAME_REGISTER_ID = "RegisterId";
  @SerializedName(SERIALIZED_NAME_REGISTER_ID)
  private String registerId;

  public static final String SERIALIZED_NAME_SELLING_CHANNEL = "SellingChannel";
  @SerializedName(SERIALIZED_NAME_SELLING_CHANNEL)
  private SellingChannelId sellingChannel = null;

  public static final String SERIALIZED_NAME_TILL_ID = "TillId";
  @SerializedName(SERIALIZED_NAME_TILL_ID)
  private String tillId;

  public static final String SERIALIZED_NAME_TRANSACTION_REFERENCE_ID = "TransactionReferenceId";
  @SerializedName(SERIALIZED_NAME_TRANSACTION_REFERENCE_ID)
  private String transactionReferenceId;

  public static final String SERIALIZED_NAME_TRANSACTION_TYPE = "TransactionType";
  @SerializedName(SERIALIZED_NAME_TRANSACTION_TYPE)
  private TransactionReferenceTypeId transactionType = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public TransactionReference actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public TransactionReference putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public TransactionReference createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public TransactionReference createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public TransactionReference deviceId(String deviceId) {
    this.deviceId = deviceId;
    return this;
  }

   /**
   * Unique ID of device
   * @return deviceId
  **/
  
  public String getDeviceId() {
    return deviceId;
  }

  public void setDeviceId(String deviceId) {
    this.deviceId = deviceId;
  }

  public TransactionReference extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public TransactionReference isCancelled(Boolean isCancelled) {
    this.isCancelled = isCancelled;
    return this;
  }

   /**
   * Flag to cancel all the OrderLines which have the current transaction reference id.
   * @return isCancelled
  **/
  
  public Boolean getIsCancelled() {
    return isCancelled;
  }

  public void setIsCancelled(Boolean isCancelled) {
    this.isCancelled = isCancelled;
  }

  public TransactionReference isConfirmed(Boolean isConfirmed) {
    this.isConfirmed = isConfirmed;
    return this;
  }

   /**
   * Flag to confirm the transaction. Performs all actions that are configured for IsConfirmed attribute at Order level for the lines added as part of current transaction. 
   * @return isConfirmed
  **/
  
  public Boolean getIsConfirmed() {
    return isConfirmed;
  }

  public void setIsConfirmed(Boolean isConfirmed) {
    this.isConfirmed = isConfirmed;
  }

  public TransactionReference isReadyForTender(Boolean isReadyForTender) {
    this.isReadyForTender = isReadyForTender;
    return this;
  }

   /**
   * Flag to set IsReadyForTender for the transaction. Once it is set the Invoice is generated for the lines added as part of current transaction. 
   * @return isReadyForTender
  **/
  
  public Boolean getIsReadyForTender() {
    return isReadyForTender;
  }

  public void setIsReadyForTender(Boolean isReadyForTender) {
    this.isReadyForTender = isReadyForTender;
  }

  public TransactionReference localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public TransactionReference messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public TransactionReference operatorId(String operatorId) {
    this.operatorId = operatorId;
    return this;
  }

   /**
   * operator who performed the referenced transaction
   * @return operatorId
  **/
  
  public String getOperatorId() {
    return operatorId;
  }

  public void setOperatorId(String operatorId) {
    this.operatorId = operatorId;
  }

  public TransactionReference orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public TransactionReference PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public TransactionReference postvoidSignature(String postvoidSignature) {
    this.postvoidSignature = postvoidSignature;
    return this;
  }

   /**
   * Post void operation will require a store manager permission, this field will capture the signature of an user having manager permission
   * @return postvoidSignature
  **/
  
  public String getPostvoidSignature() {
    return postvoidSignature;
  }

  public void setPostvoidSignature(String postvoidSignature) {
    this.postvoidSignature = postvoidSignature;
  }

  public TransactionReference registerId(String registerId) {
    this.registerId = registerId;
    return this;
  }

   /**
   * register where transaction was completed
   * @return registerId
  **/
  
  public String getRegisterId() {
    return registerId;
  }

  public void setRegisterId(String registerId) {
    this.registerId = registerId;
  }

  public TransactionReference sellingChannel(SellingChannelId sellingChannel) {
    this.sellingChannel = sellingChannel;
    return this;
  }

   /**
   * Get sellingChannel
   * @return sellingChannel
  **/
  
  public SellingChannelId getSellingChannel() {
    return sellingChannel;
  }

  public void setSellingChannel(SellingChannelId sellingChannel) {
    this.sellingChannel = sellingChannel;
  }

  public TransactionReference tillId(String tillId) {
    this.tillId = tillId;
    return this;
  }

   /**
   * till (cash drawer) where transaction was completed
   * @return tillId
  **/
  
  public String getTillId() {
    return tillId;
  }

  public void setTillId(String tillId) {
    this.tillId = tillId;
  }

  public TransactionReference transactionReferenceId(String transactionReferenceId) {
    this.transactionReferenceId = transactionReferenceId;
    return this;
  }

   /**
   * Unique identifier for the referenced order
   * @return transactionReferenceId
  **/
  
  public String getTransactionReferenceId() {
    return transactionReferenceId;
  }

  public void setTransactionReferenceId(String transactionReferenceId) {
    this.transactionReferenceId = transactionReferenceId;
  }

  public TransactionReference transactionType(TransactionReferenceTypeId transactionType) {
    this.transactionType = transactionType;
    return this;
  }

   /**
   * Get transactionType
   * @return transactionType
  **/
  
  public TransactionReferenceTypeId getTransactionType() {
    return transactionType;
  }

  public void setTransactionType(TransactionReferenceTypeId transactionType) {
    this.transactionType = transactionType;
  }

  public TransactionReference updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public TransactionReference updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public TransactionReference entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public TransactionReference localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TransactionReference transactionReference = (TransactionReference) o;
    return Objects.equals(this.actions, transactionReference.actions) &&
        Objects.equals(this.createdBy, transactionReference.createdBy) &&
        Objects.equals(this.createdTimestamp, transactionReference.createdTimestamp) &&
        Objects.equals(this.deviceId, transactionReference.deviceId) &&
        Objects.equals(this.extended, transactionReference.extended) &&
        Objects.equals(this.isCancelled, transactionReference.isCancelled) &&
        Objects.equals(this.isConfirmed, transactionReference.isConfirmed) &&
        Objects.equals(this.isReadyForTender, transactionReference.isReadyForTender) &&
        Objects.equals(this.localizedTo, transactionReference.localizedTo) &&
        Objects.equals(this.messages, transactionReference.messages) &&
        Objects.equals(this.operatorId, transactionReference.operatorId) &&
        Objects.equals(this.orgId, transactionReference.orgId) &&
        Objects.equals(this.PK, transactionReference.PK) &&
        Objects.equals(this.postvoidSignature, transactionReference.postvoidSignature) &&
        Objects.equals(this.registerId, transactionReference.registerId) &&
        Objects.equals(this.sellingChannel, transactionReference.sellingChannel) &&
        Objects.equals(this.tillId, transactionReference.tillId) &&
        Objects.equals(this.transactionReferenceId, transactionReference.transactionReferenceId) &&
        Objects.equals(this.transactionType, transactionReference.transactionType) &&
        Objects.equals(this.updatedBy, transactionReference.updatedBy) &&
        Objects.equals(this.updatedTimestamp, transactionReference.updatedTimestamp) &&
        Objects.equals(this.entityName, transactionReference.entityName) &&
        Objects.equals(this.localize, transactionReference.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, deviceId, extended, isCancelled, isConfirmed, isReadyForTender, localizedTo, messages, operatorId, orgId, PK, postvoidSignature, registerId, sellingChannel, tillId, transactionReferenceId, transactionType, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TransactionReference {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    deviceId: ").append(toIndentedString(deviceId)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    isCancelled: ").append(toIndentedString(isCancelled)).append("\n");
    sb.append("    isConfirmed: ").append(toIndentedString(isConfirmed)).append("\n");
    sb.append("    isReadyForTender: ").append(toIndentedString(isReadyForTender)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    operatorId: ").append(toIndentedString(operatorId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    postvoidSignature: ").append(toIndentedString(postvoidSignature)).append("\n");
    sb.append("    registerId: ").append(toIndentedString(registerId)).append("\n");
    sb.append("    sellingChannel: ").append(toIndentedString(sellingChannel)).append("\n");
    sb.append("    tillId: ").append(toIndentedString(tillId)).append("\n");
    sb.append("    transactionReferenceId: ").append(toIndentedString(transactionReferenceId)).append("\n");
    sb.append("    transactionType: ").append(toIndentedString(transactionType)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

