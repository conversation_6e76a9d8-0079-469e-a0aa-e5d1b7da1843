/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * NoteCategoryId
 */
public class NoteCategoryId {
  public static final String SERIALIZED_NAME_NOTE_CATEGORY_ID = "NoteCategoryId";
  @SerializedName(SERIALIZED_NAME_NOTE_CATEGORY_ID)
  private String noteCategoryId;

  public NoteCategoryId noteCategoryId(String noteCategoryId) {
    this.noteCategoryId = noteCategoryId;
    return this;
  }

   /**
   * Unique identifier of note category
   * @return noteCategoryId
  **/
  
  public String getNoteCategoryId() {
    return noteCategoryId;
  }

  public void setNoteCategoryId(String noteCategoryId) {
    this.noteCategoryId = noteCategoryId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NoteCategoryId noteCategoryId = (NoteCategoryId) o;
    return Objects.equals(this.noteCategoryId, noteCategoryId.noteCategoryId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(noteCategoryId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NoteCategoryId {\n");
    
    sb.append("    noteCategoryId: ").append(toIndentedString(noteCategoryId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

