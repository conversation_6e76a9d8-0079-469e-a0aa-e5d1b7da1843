package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

@Data
public class OrderTrackingDetailItem {

    @SerializedName("City")
    private String city;

    @SerializedName("ContextId")
    private String contextId;

    @SerializedName("Country")
    private Object country;

    @SerializedName("CreatedBy")
    private String createdBy;

    @SerializedName("CreatedTimestamp")
    private String createdTimestamp;

    @SerializedName("Date")
    private String date;

    @SerializedName("OrgId")
    private String orgId;

    @SerializedName("PK")
    private String pK;

    @SerializedName("PostalCode")
    private Object postalCode;

    @SerializedName("Process")
    private Object process;

    @SerializedName("PurgeDate")
    private Object purgeDate;

    @SerializedName("State")
    private Object state;

    @SerializedName("Status")
    private String status;

    @SerializedName("StatusDescription")
    private String statusDescription;

    @SerializedName("StatusSubType")
    private Object statusSubType;

    @SerializedName("StatusSubTypeDescription")
    private Object statusSubTypeDescription;

    @SerializedName("TrackingDetailId")
    private String trackingDetailId;

    @SerializedName("Unique_Identifier")
    private String uniqueIdentifier;

    @SerializedName("UpdatedBy")
    private String updatedBy;

    @SerializedName("UpdatedTimestamp")
    private String updatedTimestamp;


    public String getCity() {
        return city;
    }

    public String getContextId() {
        return contextId;
    }

    public Object getCountry() {
        return country;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public String getCreatedTimestamp() {
        return createdTimestamp;
    }

    public String getDate() {
        return date;
    }

    public String getOrgId() {
        return orgId;
    }

    public String getpK() {
        return pK;
    }

    public Object getPostalCode() {
        return postalCode;
    }

    public Object getProcess() {
        return process;
    }

    public Object getPurgeDate() {
        return purgeDate;
    }

    public Object getState() {
        return state;
    }

    public String getStatus() {
        return status;
    }

    public String getStatusDescription() {
        return statusDescription;
    }

    public Object getStatusSubType() {
        return statusSubType;
    }

    public Object getStatusSubTypeDescription() {
        return statusSubTypeDescription;
    }

    public String getTrackingDetailId() {
        return trackingDetailId;
    }

    public String getUniqueIdentifier() {
        return uniqueIdentifier;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public String getUpdatedTimestamp() {
        return updatedTimestamp;
    }

}