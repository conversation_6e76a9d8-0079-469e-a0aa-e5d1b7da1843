/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ModTypes
 */
public class ModTypes {

  public static final String SERIALIZED_NAME_ORDER = "Order";
  @SerializedName(SERIALIZED_NAME_ORDER)
  private List<String> order = null;


  public ModTypes order(List<String> order) {
    this.order = order;
    return this;
  }

  public ModTypes addOrderItem(String orderItem) {
    if (this.order == null) {
      this.order = new ArrayList<String>();
    }
    this.order.add(orderItem);
    return this;
  }

  /**
   * Get order
   * @return order
   **/
  
  public List<String> getOrder() {
    return order;
  }

  public void setOrder(List<String> order) {
    this.order = order;
  }

  public static final String SERIALIZED_NAME_ORDER_LINE = "OrderLine";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE)
  private List<String> orderLine = null;

  public ModTypes orderLine(List<String> orderLine) {
    this.orderLine = orderLine;
    return this;
  }

  public ModTypes addOrderLineItem(String orderLineItem) {
    if (this.orderLine == null) {
      this.orderLine = new ArrayList<String>();
    }
    this.orderLine.add(orderLineItem);
    return this;
  }

   /**
   * Get orderLine
   * @return orderLine
  **/
  
  public List<String> getOrderLine() {
    return orderLine;
  }

  public void setOrderLine(List<String> orderLine) {
    this.orderLine = orderLine;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ModTypes modTypes = (ModTypes) o;
    return Objects.equals(this.orderLine, modTypes.orderLine);
  }

  @Override
  public int hashCode() {
    return Objects.hash(orderLine);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ModTypes {\n");
    
    sb.append("    orderLine: ").append(toIndentedString(orderLine)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

