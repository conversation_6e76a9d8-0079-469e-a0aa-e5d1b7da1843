/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * PackageStatusId
 */
public class PackageStatusId {
  public static final String SERIALIZED_NAME_PACKAGE_STATUS_ID = "PackageStatusId";
  @SerializedName(SERIALIZED_NAME_PACKAGE_STATUS_ID)
  private String packageStatusId;

  public PackageStatusId packageStatusId(String packageStatusId) {
    this.packageStatusId = packageStatusId;
    return this;
  }

   /**
   * Status of the package. If status is Open, then the package is shipped but not yet delivered, so the delivery tracking service continues to make calls to the carrier tracking API to check the status of the package. If the status is Closed, then the package has been delivered, and no additional calls to the carrier tracking API are made.
   * @return packageStatusId
  **/
  
  public String getPackageStatusId() {
    return packageStatusId;
  }

  public void setPackageStatusId(String packageStatusId) {
    this.packageStatusId = packageStatusId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PackageStatusId packageStatusId = (PackageStatusId) o;
    return Objects.equals(this.packageStatusId, packageStatusId.packageStatusId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(packageStatusId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PackageStatusId {\n");
    
    sb.append("    packageStatusId: ").append(toIndentedString(packageStatusId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

