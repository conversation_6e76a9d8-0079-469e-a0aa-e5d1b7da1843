package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

@Data
public class OrderLineItem {

    @SerializedName("FulfillmentDetail")
    private List<FulfillmentDetailItem> fulfillmentDetail;

    @SerializedName("ItemId")
    private String itemId;

    @SerializedName("Quantity")
    private Double quantity;

    @SerializedName("UnitPrice")
    private Double unitPrice;

    @SerializedName("OrderLineTotal")
    private Double orderLineTotal;

    @SerializedName("OrderLineId")
    private String orderLineId;

    @SerializedName("DeliveryMethod")
    private DeliveryMethod deliveryMethod;

    @SerializedName("ShipToAddress")
    private AddressWrapper shipToAddress;

    @SerializedName("ShipFromAddress")
    private AddressWrapper shipFromAddress;

    @SerializedName("ItemDepartmentName")
    private String itemDepartmentName;

    @SerializedName("ParentOrderId")
    private String parentOrderId;

    @SerializedName("IsReturn")
    private Boolean isReturn = false;

    @SerializedName("IsHazmat")
    private Boolean isHazmat = false;

    @SerializedName("Extended")
    private Extended extended;

    @SerializedName("OrderLineExtension2")
    private List<OrderLineExtension2> orderLineExtension2;

    @Data
    public static class Extended {
        @SerializedName("CustomsCode")
        private String customsCode;
    }

    @Data
    public static class OrderLineExtension2 {
        @SerializedName("Extended")
        private Extended extended;

        public Extended getExtended() {
            return extended;
        }

        @Data
        public static class Extended {
            @SerializedName("EnglishShortDescription")
            private String englishShortDescription;

            @SerializedName("CountryofOrigin")
            private String countryofOrigin;

            public String getEnglishShortDescription() {
                return englishShortDescription;
            }

            public String getCountryofOrigin() {
                return countryofOrigin;
            }
        }
    }

    @Data
    public static class AddressWrapper {
        @SerializedName("Address")
        private Address address;

        @SerializedName("Extended")
        private Extended extended;

        @Data
        public static class Address {
            @SerializedName("FirstName")
            private String firstName;

            @SerializedName("LastName")
            private String lastName;

            @SerializedName("Address1")
            private String address1;

            @SerializedName("Address2")
            private String address2;

            @SerializedName("Address3")
            private String address3;

            @SerializedName("PostalCode")
            private String postalCode;

            @SerializedName("City")
            private String city;

            @SerializedName("State")
            private String state;

            @SerializedName("Country")
            private String country;

            @SerializedName("Phone")
            private String phone;

            @SerializedName("Email")
            private String email;
        }

        @Data
        public static class Extended {
            @SerializedName("KatakanaFirstName")
            private String katakanaFirstName;

            @SerializedName("KatakanaLastName")
            private String katakanaLastName;

            @SerializedName("CompanyName")
            private String companyName;
        }
    }

    public List<FulfillmentDetailItem> getFulfillmentDetail() {
        return fulfillmentDetail;
    }

    public String getItemId() {
        return itemId;
    }

    public Double getQuantity() {
        return quantity;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public Double getOrderLineTotal() {
        return orderLineTotal;
    }

    public String getOrderLineId() {
        return orderLineId;
    }

    public DeliveryMethod getDeliveryMethod() {
        return deliveryMethod;
    }

    public AddressWrapper getShipToAddress() {
        return shipToAddress;
    }

    public AddressWrapper getShipFromAddress() {
        return shipFromAddress;
    }

    public String getItemDepartmentName() {
        return itemDepartmentName;
    }

    public String getParentOrderId() {
        return parentOrderId;
    }

    public Boolean getReturn() {
        return isReturn;
    }

    public Boolean getHazmat() {
        return isHazmat;
    }

    public Extended getExtended() {
        return extended;
    }

    public List<OrderLineExtension2> getOrderLineExtension2() {
        return orderLineExtension2;
    }

}
