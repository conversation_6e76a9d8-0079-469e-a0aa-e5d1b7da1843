/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderLineCancelHistory
 */
public class OrderLineCancelHistory {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CANCEL_COMMENTS = "CancelComments";
  @SerializedName(SERIALIZED_NAME_CANCEL_COMMENTS)
  private String cancelComments;

  public static final String SERIALIZED_NAME_CANCEL_QUANTITY = "CancelQuantity";
  @SerializedName(SERIALIZED_NAME_CANCEL_QUANTITY)
  private Double cancelQuantity;

  public static final String SERIALIZED_NAME_CANCEL_REASON = "CancelReason";
  @SerializedName(SERIALIZED_NAME_CANCEL_REASON)
  private ReasonId cancelReason = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderLineCancelHistory actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderLineCancelHistory putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderLineCancelHistory cancelComments(String cancelComments) {
    this.cancelComments = cancelComments;
    return this;
  }

   /**
   * Cancellation Comments for the order line or Line quantity.
   * @return cancelComments
  **/
  
  public String getCancelComments() {
    return cancelComments;
  }

  public void setCancelComments(String cancelComments) {
    this.cancelComments = cancelComments;
  }

  public OrderLineCancelHistory cancelQuantity(Double cancelQuantity) {
    this.cancelQuantity = cancelQuantity;
    return this;
  }

   /**
   * Order line quantity  that got cancelled.
   * minimum: 0
   * maximum: 999999999999.9999
   * @return cancelQuantity
  **/
  
  public Double getCancelQuantity() {
    return cancelQuantity;
  }

  public void setCancelQuantity(Double cancelQuantity) {
    this.cancelQuantity = cancelQuantity;
  }

  public OrderLineCancelHistory cancelReason(ReasonId cancelReason) {
    this.cancelReason = cancelReason;
    return this;
  }

   /**
   * Get cancelReason
   * @return cancelReason
  **/
  
  public ReasonId getCancelReason() {
    return cancelReason;
  }

  public void setCancelReason(ReasonId cancelReason) {
    this.cancelReason = cancelReason;
  }

  public OrderLineCancelHistory createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderLineCancelHistory createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderLineCancelHistory extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderLineCancelHistory localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderLineCancelHistory messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderLineCancelHistory orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderLineCancelHistory PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderLineCancelHistory updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderLineCancelHistory updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderLineCancelHistory entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderLineCancelHistory localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderLineCancelHistory orderLineCancelHistory = (OrderLineCancelHistory) o;
    return Objects.equals(this.actions, orderLineCancelHistory.actions) &&
        Objects.equals(this.cancelComments, orderLineCancelHistory.cancelComments) &&
        Objects.equals(this.cancelQuantity, orderLineCancelHistory.cancelQuantity) &&
        Objects.equals(this.cancelReason, orderLineCancelHistory.cancelReason) &&
        Objects.equals(this.createdBy, orderLineCancelHistory.createdBy) &&
        Objects.equals(this.createdTimestamp, orderLineCancelHistory.createdTimestamp) &&
        Objects.equals(this.extended, orderLineCancelHistory.extended) &&
        Objects.equals(this.localizedTo, orderLineCancelHistory.localizedTo) &&
        Objects.equals(this.messages, orderLineCancelHistory.messages) &&
        Objects.equals(this.orgId, orderLineCancelHistory.orgId) &&
        Objects.equals(this.PK, orderLineCancelHistory.PK) &&
        Objects.equals(this.updatedBy, orderLineCancelHistory.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderLineCancelHistory.updatedTimestamp) &&
        Objects.equals(this.entityName, orderLineCancelHistory.entityName) &&
        Objects.equals(this.localize, orderLineCancelHistory.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, cancelComments, cancelQuantity, cancelReason, createdBy, createdTimestamp, extended, localizedTo, messages, orgId, PK, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderLineCancelHistory {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    cancelComments: ").append(toIndentedString(cancelComments)).append("\n");
    sb.append("    cancelQuantity: ").append(toIndentedString(cancelQuantity)).append("\n");
    sb.append("    cancelReason: ").append(toIndentedString(cancelReason)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

