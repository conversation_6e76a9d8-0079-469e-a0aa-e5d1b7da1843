package uk.co.flexi.sdk.oms.model;

import java.util.List;

public class OrderData {

    private String orderId;

    private String parentOrderId;

    private String partnerName;

    private String sellingChannel;

    private String trackingNumber;

    private String customerFirstName;

    private String customerLastName;

    private String customerEmail;

    private List<OrderLineData> orderLines;

    public String getCustomerFirstName() {
        return customerFirstName;
    }

    public OrderData setCustomerFirstName(String customerFirstName) {
        this.customerFirstName = customerFirstName;
        return this;
    }

    public String getCustomerLastName() {
        return customerLastName;
    }

    public OrderData setCustomerLastName(String customerLastName) {
        this.customerLastName = customerLastName;
        return this;
    }

    public String getCustomerEmail() {
        return customerEmail;
    }

    public OrderData setCustomerEmail(String customerEmail) {
        this.customerEmail = customerEmail;
        return this;
    }

    public String getTrackingNumber() {
        return trackingNumber;
    }

    public OrderData setTrackingNumber(String trackingNumber) {
        this.trackingNumber = trackingNumber;
        return this;
    }

    public String getOrderId() {
        return orderId;
    }

    public OrderData setOrderId(String orderId) {
        this.orderId = orderId;
        return this;
    }

    public String getParentOrderId() {
        return parentOrderId;
    }

    public String getSellingChannel() {
        return sellingChannel;
    }

    public OrderData setSellingChannel(String sellingChannel) {
        this.sellingChannel = sellingChannel;
        return this;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public OrderData setPartnerName(String partnerName) {
        this.partnerName = partnerName;
        return this;
    }

    public OrderData setParentOrderId(String parentOrderId) {
        this.parentOrderId = parentOrderId;
        return this;
    }

    @Override
    public String toString() {
        return "OrderData{" +
                "orderId='" + orderId + '\'' +
                ", parentOrderId='" + parentOrderId + '\'' +
                ", partnerName='" + partnerName + '\'' +
                ", sellingChannel='" + sellingChannel + '\'' +
                ", trackingNumber='" + trackingNumber + '\'' +
                ", orderLines=" + orderLines +
                '}';
    }

    public List<OrderLineData> getOrderLines() {
        return orderLines;
    }

    public OrderData setOrderLines(List<OrderLineData> orderLines) {
        this.orderLines = orderLines;
        return this;
    }

}