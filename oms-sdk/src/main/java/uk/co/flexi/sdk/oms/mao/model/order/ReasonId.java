/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * ReasonId
 */
public class ReasonId {
  public static final String SERIALIZED_NAME_REASON_ID = "ReasonId";
  @SerializedName(SERIALIZED_NAME_REASON_ID)
  private String reasonId;

  public ReasonId reasonId(String reasonId) {
    this.reasonId = reasonId;
    return this;
  }

   /**
   * Different reason for example, Address verification failed, payment auth failed, AVS unavilable, Items needs user action,Address not available
   * @return reasonId
  **/
  
  public String getReasonId() {
    return reasonId;
  }

  public void setReasonId(String reasonId) {
    this.reasonId = reasonId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReasonId reasonId = (ReasonId) o;
    return Objects.equals(this.reasonId, reasonId.reasonId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(reasonId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReasonId {\n");
    
    sb.append("    reasonId: ").append(toIndentedString(reasonId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

