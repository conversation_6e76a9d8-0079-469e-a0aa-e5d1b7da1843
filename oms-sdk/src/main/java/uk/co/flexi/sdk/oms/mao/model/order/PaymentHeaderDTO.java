/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.*;

/**
 * PaymentHeaderDTO
 */
public class PaymentHeaderDTO {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ALLOW_RETRY = "AllowRetry";
  @SerializedName(SERIALIZED_NAME_ALLOW_RETRY)
  private Boolean allowRetry;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_CUSTOMER_ID = "CustomerId";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_ID)
  private String customerId;

  public static final String SERIALIZED_NAME_EXECUTION_MODE = "ExecutionMode";
  @SerializedName(SERIALIZED_NAME_EXECUTION_MODE)
  private String executionMode;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_INTERACTION_MODE = "InteractionMode";
  @SerializedName(SERIALIZED_NAME_INTERACTION_MODE)
  private String interactionMode;

  public static final String SERIALIZED_NAME_IS_CANCELLED = "IsCancelled";
  @SerializedName(SERIALIZED_NAME_IS_CANCELLED)
  private Boolean isCancelled;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_ID = "OrderId";
  @SerializedName(SERIALIZED_NAME_ORDER_ID)
  private String orderId;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PAYMENT_GROUP_ID = "PaymentGroupId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_GROUP_ID)
  private String paymentGroupId;

  public static final String SERIALIZED_NAME_PAYMENT_METHOD = "PaymentMethod";
  @SerializedName(SERIALIZED_NAME_PAYMENT_METHOD)
  private List<PaymentMethodDTO> paymentMethod = null;

  public static final String SERIALIZED_NAME_STATUS = "Status";
  @SerializedName(SERIALIZED_NAME_STATUS)
  private PaymentStatusId status = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public static final String SERIALIZED_NAME_PAYMENT_TYPE = "PaymentType";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TYPE)
  private PaymentTypeId paymentType = null;


  public PaymentHeaderDTO actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public PaymentHeaderDTO putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public PaymentHeaderDTO allowRetry(Boolean allowRetry) {
    this.allowRetry = allowRetry;
    return this;
  }

   /**
   * Get allowRetry
   * @return allowRetry
  **/
  
  public Boolean getAllowRetry() {
    return allowRetry;
  }

  public void setAllowRetry(Boolean allowRetry) {
    this.allowRetry = allowRetry;
  }

  public PaymentHeaderDTO createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public PaymentHeaderDTO createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public PaymentHeaderDTO customerId(String customerId) {
    this.customerId = customerId;
    return this;
  }

   /**
   * Identifier of the customer associated with the order. Used to retrieve customer information during the payment gateway request. For example, if customer email address is required for a third party payment gateway to process e-gift cards, then the customer id is used via user exit which retrieves customer information.
   * @return customerId
  **/
  
  public String getCustomerId() {
    return customerId;
  }

  public void setCustomerId(String customerId) {
    this.customerId = customerId;
  }

  public PaymentHeaderDTO executionMode(String executionMode) {
    this.executionMode = executionMode;
    return this;
  }

   /**
   * Get executionMode
   * @return executionMode
  **/
  
  public String getExecutionMode() {
    return executionMode;
  }

  public void setExecutionMode(String executionMode) {
    this.executionMode = executionMode;
  }

  public PaymentHeaderDTO extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public PaymentHeaderDTO interactionMode(String interactionMode) {
    this.interactionMode = interactionMode;
    return this;
  }

   /**
   * Get interactionMode
   * @return interactionMode
  **/
  
  public String getInteractionMode() {
    return interactionMode;
  }

  public void setInteractionMode(String interactionMode) {
    this.interactionMode = interactionMode;
  }

  public PaymentHeaderDTO isCancelled(Boolean isCancelled) {
    this.isCancelled = isCancelled;
    return this;
  }

   /**
   * Indicates if an order has been cancelled or post voided. If true, then the payment component attempts to void each payment method on the order by reversing authorizations, voiding or refunding settlements, and voiding refunds. For cash-based payment methods, the payment component creates an open void transaction which must be closed by a system such as POS which can issue or accept cash payments. For digital payments like credit cards, the payment component can execute void transactions by sending them to the gateway.
   * @return isCancelled
  **/
  
  public Boolean getIsCancelled() {
    return isCancelled;
  }

  public void setIsCancelled(Boolean isCancelled) {
    this.isCancelled = isCancelled;
  }

  public PaymentHeaderDTO localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public PaymentHeaderDTO messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public PaymentHeaderDTO orderId(String orderId) {
    this.orderId = orderId;
    return this;
  }

   /**
   * Identifier of the order for which payment needs to be processed.
   * @return orderId
  **/
  
  public String getOrderId() {
    return orderId;
  }

  public void setOrderId(String orderId) {
    this.orderId = orderId;
  }

  public PaymentHeaderDTO orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public PaymentHeaderDTO PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public PaymentHeaderDTO paymentGroupId(String paymentGroupId) {
    this.paymentGroupId = paymentGroupId;
    return this;
  }

   /**
   * Identifier of the order payment group, or group of items within an order, for which payment needs to be processed. Payment groups are required for scenarios where item(s) on an order need to relate to specific payment method(s) on the order.
   * @return paymentGroupId
  **/
  
  public String getPaymentGroupId() {
    return paymentGroupId;
  }

  public void setPaymentGroupId(String paymentGroupId) {
    this.paymentGroupId = paymentGroupId;
  }

  public PaymentHeaderDTO paymentMethod(List<PaymentMethodDTO> paymentMethod) {
    this.paymentMethod = paymentMethod;
    return this;
  }

  public PaymentHeaderDTO addPaymentMethodItem(PaymentMethodDTO paymentMethodItem) {
    if (this.paymentMethod == null) {
      this.paymentMethod = new ArrayList<PaymentMethodDTO>();
    }
    this.paymentMethod.add(paymentMethodItem);
    return this;
  }

   /**
   * Get paymentMethod
   * @return paymentMethod
  **/
  
  public List<PaymentMethodDTO> getPaymentMethod() {
    return paymentMethod;
  }

  public void setPaymentMethod(List<PaymentMethodDTO> paymentMethod) {
    this.paymentMethod = paymentMethod;
  }

  public PaymentHeaderDTO status(PaymentStatusId status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  
  public PaymentStatusId getStatus() {
    return status;
  }

  public void setStatus(PaymentStatusId status) {
    this.status = status;
  }

  public PaymentHeaderDTO updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public PaymentHeaderDTO updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public PaymentHeaderDTO entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public PaymentHeaderDTO localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }

  public PaymentHeaderDTO paymentType(PaymentTypeId paymentType) {
    this.paymentType = paymentType;
    return this;
  }

  /**
   * Get paymentType
   * @return paymentType
   **/
  
  public PaymentTypeId getPaymentType() {
    return paymentType;
  }

  public void setPaymentType(PaymentTypeId paymentType) {
    this.paymentType = paymentType;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentHeaderDTO paymentHeaderDTO = (PaymentHeaderDTO) o;
    return Objects.equals(this.actions, paymentHeaderDTO.actions) &&
        Objects.equals(this.allowRetry, paymentHeaderDTO.allowRetry) &&
        Objects.equals(this.createdBy, paymentHeaderDTO.createdBy) &&
        Objects.equals(this.createdTimestamp, paymentHeaderDTO.createdTimestamp) &&
        Objects.equals(this.customerId, paymentHeaderDTO.customerId) &&
        Objects.equals(this.executionMode, paymentHeaderDTO.executionMode) &&
        Objects.equals(this.extended, paymentHeaderDTO.extended) &&
        Objects.equals(this.interactionMode, paymentHeaderDTO.interactionMode) &&
        Objects.equals(this.isCancelled, paymentHeaderDTO.isCancelled) &&
        Objects.equals(this.localizedTo, paymentHeaderDTO.localizedTo) &&
        Objects.equals(this.messages, paymentHeaderDTO.messages) &&
        Objects.equals(this.orderId, paymentHeaderDTO.orderId) &&
        Objects.equals(this.orgId, paymentHeaderDTO.orgId) &&
        Objects.equals(this.PK, paymentHeaderDTO.PK) &&
        Objects.equals(this.paymentGroupId, paymentHeaderDTO.paymentGroupId) &&
        Objects.equals(this.paymentMethod, paymentHeaderDTO.paymentMethod) &&
        Objects.equals(this.status, paymentHeaderDTO.status) &&
        Objects.equals(this.updatedBy, paymentHeaderDTO.updatedBy) &&
        Objects.equals(this.updatedTimestamp, paymentHeaderDTO.updatedTimestamp) &&
        Objects.equals(this.entityName, paymentHeaderDTO.entityName) &&
        Objects.equals(this.localize, paymentHeaderDTO.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, allowRetry, createdBy, createdTimestamp, customerId, executionMode, extended, interactionMode, isCancelled, localizedTo, messages, orderId, orgId, PK, paymentGroupId, paymentMethod, status, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentHeaderDTO {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    allowRetry: ").append(toIndentedString(allowRetry)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    customerId: ").append(toIndentedString(customerId)).append("\n");
    sb.append("    executionMode: ").append(toIndentedString(executionMode)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    interactionMode: ").append(toIndentedString(interactionMode)).append("\n");
    sb.append("    isCancelled: ").append(toIndentedString(isCancelled)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderId: ").append(toIndentedString(orderId)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    paymentGroupId: ").append(toIndentedString(paymentGroupId)).append("\n");
    sb.append("    paymentMethod: ").append(toIndentedString(paymentMethod)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

