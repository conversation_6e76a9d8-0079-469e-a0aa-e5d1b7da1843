package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * LocalizedData
 */
public class ExtendedLocalizedData {
  public static final String SERIALIZED_NAME_LANGUAGE_KEY = "LanguageKey";
  @SerializedName(SERIALIZED_NAME_LANGUAGE_KEY)
  private String languageKey;

  public static final String SERIALIZED_NAME_VARIATION_DESCRIPTION = "VariationDescription";
  @SerializedName(SERIALIZED_NAME_VARIATION_DESCRIPTION)
  private String variationDescription;

  public static final String SERIALIZED_NAME_SIZE_DESCRIPTION = "SizeDescription";
  @SerializedName(SERIALIZED_NAME_SIZE_DESCRIPTION)
  private String sizeDescription;

  public ExtendedLocalizedData languageKey(String languageKey) {
    this.languageKey = languageKey;
    return this;
  }

   /**
   * Get languageKey
   * @return languageKey
  **/
  public String getLanguageKey() {
    return languageKey;
  }

  public void setLanguageKey(String languageKey) {
    this.languageKey = languageKey;
  }

  public ExtendedLocalizedData variationDescription(String variationDescription) {
    this.variationDescription = variationDescription;
    return this;
  }

  /**
   * Get shortDescription
   * @return shortDescription
   **/
  public String getVariationDescription() {
    return variationDescription;
  }

  public void setVariationDescription(String variationDescription) {
    this.variationDescription = variationDescription;
  }

  public ExtendedLocalizedData sizeDescription(String sizeDescription) {
    this.sizeDescription = sizeDescription;
    return this;
  }

  /**
   * Get shortDescription
   * @return shortDescription
   **/
  public String getSizeDescription() {
    return sizeDescription;
  }

  public void setSizeDescription(String sizeDescription) {
    this.sizeDescription = sizeDescription;
  }
  @Override
  public int hashCode() {
    return Objects.hash(languageKey, sizeDescription);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LocalizedData {\n");
    sb.append("    languageKey: ").append(toIndentedString(languageKey)).append("\n");
    sb.append("    description: ").append(toIndentedString(sizeDescription)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

