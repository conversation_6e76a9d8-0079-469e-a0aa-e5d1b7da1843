package uk.co.flexi.sdk.oms.mao.model.order;

public class OMSOrderDTO {

    private String orderId;
    private String trackingId;
    private String productName;
    private int expectedQuantity;
    private int receivedQuantity;

    public OMSOrderDTO(String orderId, String trackingId, String productName, int expectedQuantity, int receivedQuantity) {
        this.orderId = orderId;
        this.trackingId = trackingId;
        this.productName = productName;
        this.expectedQuantity = expectedQuantity;
        this.receivedQuantity = receivedQuantity;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getTrackingId() {
        return trackingId;
    }

    public void setTrackingId(String trackingId) {
        this.trackingId = trackingId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public int getExpectedQuantity() {
        return expectedQuantity;
    }

    public void setExpectedQuantity(int expectedQuantity) {
        this.expectedQuantity = expectedQuantity;
    }

    public int getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(int receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }
}
