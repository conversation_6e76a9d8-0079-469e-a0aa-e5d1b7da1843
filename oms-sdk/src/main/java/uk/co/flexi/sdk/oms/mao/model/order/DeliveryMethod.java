/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * DeliveryMethod
 */
public class DeliveryMethod {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DELIVERY_METHOD_ID = "DeliveryMethodId";
  @SerializedName(SERIALIZED_NAME_DELIVERY_METHOD_ID)
  private String deliveryMethodId;

  public static final String SERIALIZED_NAME_DESCRIPTION = "Description";
  @SerializedName(SERIALIZED_NAME_DESCRIPTION)
  private String description;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IS_SHIPPING_CHARGE_REQUIRED = "IsShippingChargeRequired";
  @SerializedName(SERIALIZED_NAME_IS_SHIPPING_CHARGE_REQUIRED)
  private Boolean isShippingChargeRequired;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_SHORT_DESCRIPTION = "ShortDescription";
  @SerializedName(SERIALIZED_NAME_SHORT_DESCRIPTION)
  private String shortDescription;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public DeliveryMethod actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public DeliveryMethod putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public DeliveryMethod createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public DeliveryMethod createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public DeliveryMethod deliveryMethodId(String deliveryMethodId) {
    this.deliveryMethodId = deliveryMethodId;
    return this;
  }

   /**
   * Unique identifier of the delivery method
   * @return deliveryMethodId
  **/
  
  public String getDeliveryMethodId() {
    return deliveryMethodId;
  }

  public void setDeliveryMethodId(String deliveryMethodId) {
    this.deliveryMethodId = deliveryMethodId;
  }

  public DeliveryMethod description(String description) {
    this.description = description;
    return this;
  }

   /**
   * Description of the deliveryMethod
   * @return description
  **/
  
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public DeliveryMethod extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public DeliveryMethod isShippingChargeRequired(Boolean isShippingChargeRequired) {
    this.isShippingChargeRequired = isShippingChargeRequired;
    return this;
  }

   /**
   * This flag indicates either shipping charge is required for the delivery method.
   * @return isShippingChargeRequired
  **/
  
  public Boolean getIsShippingChargeRequired() {
    return isShippingChargeRequired;
  }

  public void setIsShippingChargeRequired(Boolean isShippingChargeRequired) {
    this.isShippingChargeRequired = isShippingChargeRequired;
  }

  public DeliveryMethod localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public DeliveryMethod messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public DeliveryMethod PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public DeliveryMethod profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public DeliveryMethod shortDescription(String shortDescription) {
    this.shortDescription = shortDescription;
    return this;
  }

   /**
   * Short Description of the deliveryMethod
   * @return shortDescription
  **/
  
  public String getShortDescription() {
    return shortDescription;
  }

  public void setShortDescription(String shortDescription) {
    this.shortDescription = shortDescription;
  }

  public DeliveryMethod updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public DeliveryMethod updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public DeliveryMethod rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DeliveryMethod deliveryMethod = (DeliveryMethod) o;
    return Objects.equals(this.actions, deliveryMethod.actions) &&
        Objects.equals(this.createdBy, deliveryMethod.createdBy) &&
        Objects.equals(this.createdTimestamp, deliveryMethod.createdTimestamp) &&
        Objects.equals(this.deliveryMethodId, deliveryMethod.deliveryMethodId) &&
        Objects.equals(this.description, deliveryMethod.description) &&
        Objects.equals(this.extended, deliveryMethod.extended) &&
        Objects.equals(this.isShippingChargeRequired, deliveryMethod.isShippingChargeRequired) &&
        Objects.equals(this.localizedTo, deliveryMethod.localizedTo) &&
        Objects.equals(this.messages, deliveryMethod.messages) &&
        Objects.equals(this.PK, deliveryMethod.PK) &&
        Objects.equals(this.profileId, deliveryMethod.profileId) &&
        Objects.equals(this.shortDescription, deliveryMethod.shortDescription) &&
        Objects.equals(this.updatedBy, deliveryMethod.updatedBy) &&
        Objects.equals(this.updatedTimestamp, deliveryMethod.updatedTimestamp) &&
        Objects.equals(this.rootCause, deliveryMethod.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, deliveryMethodId, description, extended, isShippingChargeRequired, localizedTo, messages, PK, profileId, shortDescription, updatedBy, updatedTimestamp, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DeliveryMethod {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    deliveryMethodId: ").append(toIndentedString(deliveryMethodId)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    isShippingChargeRequired: ").append(toIndentedString(isShippingChargeRequired)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    shortDescription: ").append(toIndentedString(shortDescription)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

