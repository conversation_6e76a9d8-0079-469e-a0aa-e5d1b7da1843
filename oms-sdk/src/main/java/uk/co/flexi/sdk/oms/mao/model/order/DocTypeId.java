/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * DocTypeId
 */
public class DocTypeId {
  public static final String SERIALIZED_NAME_DOC_TYPE_ID = "DocTypeId";
  @SerializedName(SERIALIZED_NAME_DOC_TYPE_ID)
  private String docTypeId;

  public DocTypeId docTypeId(String docTypeId) {
    this.docTypeId = docTypeId;
    return this;
  }

   /**
   * Unique identifier of the Doc type
   * @return docTypeId
  **/
  
  public String getDocTypeId() {
    return docTypeId;
  }

  public void setDocTypeId(String docTypeId) {
    this.docTypeId = docTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DocTypeId docTypeId = (DocTypeId) o;
    return Objects.equals(this.docTypeId, docTypeId.docTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(docTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DocTypeId {\n");
    
    sb.append("    docTypeId: ").append(toIndentedString(docTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

