/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * InvoiceTypeId
 */
public class InvoiceTypeId {
  public static final String SERIALIZED_NAME_INVOICE_TYPE_ID = "InvoiceTypeId";
  @SerializedName(SERIALIZED_NAME_INVOICE_TYPE_ID)
  private String invoiceTypeId;

  public InvoiceTypeId invoiceTypeId(String invoiceTypeId) {
    this.invoiceTypeId = invoiceTypeId;
    return this;
  }

   /**
   * Unique identifier of the Invoice Type id
   * @return invoiceTypeId
  **/
  
  public String getInvoiceTypeId() {
    return invoiceTypeId;
  }

  public void setInvoiceTypeId(String invoiceTypeId) {
    this.invoiceTypeId = invoiceTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvoiceTypeId invoiceTypeId = (InvoiceTypeId) o;
    return Objects.equals(this.invoiceTypeId, invoiceTypeId.invoiceTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(invoiceTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvoiceTypeId {\n");
    
    sb.append("    invoiceTypeId: ").append(toIndentedString(invoiceTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

