package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ItemTranslation
 */
public class ItemTranslation {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_BASE_U_O_M = "BaseUOM";
  @SerializedName(SERIALIZED_NAME_BASE_U_O_M)
  private String baseUOM;

  public static final String SERIALIZED_NAME_BRAND = "Brand";
  @SerializedName(SERIALIZED_NAME_BRAND)
  private String brand;

  public static final String SERIALIZED_NAME_COLOR = "Color";
  @SerializedName(SERIALIZED_NAME_COLOR)
  private String color;

  public static final String SERIALIZED_NAME_COLOR_IMAGE_U_R_I = "ColorImageURI";
  @SerializedName(SERIALIZED_NAME_COLOR_IMAGE_U_R_I)
  private String colorImageURI;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DEPARTMENT_NAME = "DepartmentName";
  @SerializedName(SERIALIZED_NAME_DEPARTMENT_NAME)
  private String departmentName;

  public static final String SERIALIZED_NAME_DESCRIPTION = "Description";
  @SerializedName(SERIALIZED_NAME_DESCRIPTION)
  private String description;

  public static final String SERIALIZED_NAME_DIMENSION_U_O_M = "DimensionUOM";
  @SerializedName(SERIALIZED_NAME_DIMENSION_U_O_M)
  private String dimensionUOM;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LANGUAGE_KEY = "LanguageKey";
  @SerializedName(SERIALIZED_NAME_LANGUAGE_KEY)
  private String languageKey;

  public static final String SERIALIZED_NAME_LARGE_IMAGE_U_R_I = "LargeImageURI";
  @SerializedName(SERIALIZED_NAME_LARGE_IMAGE_U_R_I)
  private String largeImageURI;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_ITEM = "ParentItem";
  @SerializedName(SERIALIZED_NAME_PARENT_ITEM)
  private PrimaryKey parentItem = null;

  public static final String SERIALIZED_NAME_PRODUCT_CLASS = "ProductClass";
  @SerializedName(SERIALIZED_NAME_PRODUCT_CLASS)
  private String productClass;

  public static final String SERIALIZED_NAME_PRODUCT_SUB_CLASS = "ProductSubClass";
  @SerializedName(SERIALIZED_NAME_PRODUCT_SUB_CLASS)
  private String productSubClass;

  public static final String SERIALIZED_NAME_SEASON = "Season";
  @SerializedName(SERIALIZED_NAME_SEASON)
  private String season;

  public static final String SERIALIZED_NAME_SHORT_DESCRIPTION = "ShortDescription";
  @SerializedName(SERIALIZED_NAME_SHORT_DESCRIPTION)
  private String shortDescription;

  public static final String SERIALIZED_NAME_SIZE = "Size";
  @SerializedName(SERIALIZED_NAME_SIZE)
  private String size;

  public static final String SERIALIZED_NAME_SMALL_IMAGE_U_R_I = "SmallImageURI";
  @SerializedName(SERIALIZED_NAME_SMALL_IMAGE_U_R_I)
  private String smallImageURI;

  public static final String SERIALIZED_NAME_STORE_DEPARTMENT = "StoreDepartment";
  @SerializedName(SERIALIZED_NAME_STORE_DEPARTMENT)
  private String storeDepartment;

  public static final String SERIALIZED_NAME_STYLE = "Style";
  @SerializedName(SERIALIZED_NAME_STYLE)
  private String style;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_VOLUME_U_O_M = "VolumeUOM";
  @SerializedName(SERIALIZED_NAME_VOLUME_U_O_M)
  private String volumeUOM;

  public static final String SERIALIZED_NAME_WEB_U_R_L = "WebURL";
  @SerializedName(SERIALIZED_NAME_WEB_U_R_L)
  private String webURL;

  public static final String SERIALIZED_NAME_WEIGHT_U_O_M = "WeightUOM";
  @SerializedName(SERIALIZED_NAME_WEIGHT_U_O_M)
  private String weightUOM;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public ItemTranslation actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ItemTranslation putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ItemTranslation baseUOM(String baseUOM) {
    this.baseUOM = baseUOM;
    return this;
  }

   /**
   * Localized. Base unit of measure (UOM) of item
   * @return baseUOM
  **/
  public String getBaseUOM() {
    return baseUOM;
  }

  public void setBaseUOM(String baseUOM) {
    this.baseUOM = baseUOM;
  }

  public ItemTranslation brand(String brand) {
    this.brand = brand;
    return this;
  }

   /**
   * Localized. Item brand
   * @return brand
  **/
  public String getBrand() {
    return brand;
  }

  public void setBrand(String brand) {
    this.brand = brand;
  }

  public ItemTranslation color(String color) {
    this.color = color;
    return this;
  }

   /**
   * Localized. Item color
   * @return color
  **/
  public String getColor() {
    return color;
  }

  public void setColor(String color) {
    this.color = color;
  }

  public ItemTranslation colorImageURI(String colorImageURI) {
    this.colorImageURI = colorImageURI;
    return this;
  }

   /**
   * Localized. Color image URI of the item.
   * @return colorImageURI
  **/
  public String getColorImageURI() {
    return colorImageURI;
  }

  public void setColorImageURI(String colorImageURI) {
    this.colorImageURI = colorImageURI;
  }

  public ItemTranslation createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ItemTranslation createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ItemTranslation departmentName(String departmentName) {
    this.departmentName = departmentName;
    return this;
  }

   /**
   * Localized. Item&#39;s department name.
   * @return departmentName
  **/
  public String getDepartmentName() {
    return departmentName;
  }

  public void setDepartmentName(String departmentName) {
    this.departmentName = departmentName;
  }

  public ItemTranslation description(String description) {
    this.description = description;
    return this;
  }

   /**
   * Localized. Item description
   * @return description
  **/
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public ItemTranslation dimensionUOM(String dimensionUOM) {
    this.dimensionUOM = dimensionUOM;
    return this;
  }

   /**
   * Localized. Required if Length, Width, or Height is not null
   * @return dimensionUOM
  **/
  public String getDimensionUOM() {
    return dimensionUOM;
  }

  public void setDimensionUOM(String dimensionUOM) {
    this.dimensionUOM = dimensionUOM;
  }

  public ItemTranslation extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ItemTranslation languageKey(String languageKey) {
    this.languageKey = languageKey;
    return this;
  }

   /**
   * language Key
   * @return languageKey
  **/
  public String getLanguageKey() {
    return languageKey;
  }

  public void setLanguageKey(String languageKey) {
    this.languageKey = languageKey;
  }

  public ItemTranslation largeImageURI(String largeImageURI) {
    this.largeImageURI = largeImageURI;
    return this;
  }

   /**
   * Localized. URI of the large image for the item
   * @return largeImageURI
  **/
  public String getLargeImageURI() {
    return largeImageURI;
  }

  public void setLargeImageURI(String largeImageURI) {
    this.largeImageURI = largeImageURI;
  }

  public ItemTranslation localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public ItemTranslation messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ItemTranslation PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ItemTranslation parentItem(PrimaryKey parentItem) {
    this.parentItem = parentItem;
    return this;
  }

   /**
   * Get parentItem
   * @return parentItem
  **/
  public PrimaryKey getParentItem() {
    return parentItem;
  }

  public void setParentItem(PrimaryKey parentItem) {
    this.parentItem = parentItem;
  }

  public ItemTranslation productClass(String productClass) {
    this.productClass = productClass;
    return this;
  }

   /**
   * Localized. Used to categorize a group of item
   * @return productClass
  **/
  public String getProductClass() {
    return productClass;
  }

  public void setProductClass(String productClass) {
    this.productClass = productClass;
  }

  public ItemTranslation productSubClass(String productSubClass) {
    this.productSubClass = productSubClass;
    return this;
  }

   /**
   * Localized. The sub class to which it belongs
   * @return productSubClass
  **/
  public String getProductSubClass() {
    return productSubClass;
  }

  public void setProductSubClass(String productSubClass) {
    this.productSubClass = productSubClass;
  }

  public ItemTranslation season(String season) {
    this.season = season;
    return this;
  }

   /**
   * Localized. Item season
   * @return season
  **/
  public String getSeason() {
    return season;
  }

  public void setSeason(String season) {
    this.season = season;
  }

  public ItemTranslation shortDescription(String shortDescription) {
    this.shortDescription = shortDescription;
    return this;
  }

   /**
   * Localized. Short description
   * @return shortDescription
  **/
  public String getShortDescription() {
    return shortDescription;
  }

  public void setShortDescription(String shortDescription) {
    this.shortDescription = shortDescription;
  }

  public ItemTranslation size(String size) {
    this.size = size;
    return this;
  }

   /**
   * Localized. Item size
   * @return size
  **/
  public String getSize() {
    return size;
  }

  public void setSize(String size) {
    this.size = size;
  }

  public ItemTranslation smallImageURI(String smallImageURI) {
    this.smallImageURI = smallImageURI;
    return this;
  }

   /**
   * Localized. URI of the small image for the item
   * @return smallImageURI
  **/
  public String getSmallImageURI() {
    return smallImageURI;
  }

  public void setSmallImageURI(String smallImageURI) {
    this.smallImageURI = smallImageURI;
  }

  public ItemTranslation storeDepartment(String storeDepartment) {
    this.storeDepartment = storeDepartment;
    return this;
  }

   /**
   * Localized. Item department
   * @return storeDepartment
  **/
  public String getStoreDepartment() {
    return storeDepartment;
  }

  public void setStoreDepartment(String storeDepartment) {
    this.storeDepartment = storeDepartment;
  }

  public ItemTranslation style(String style) {
    this.style = style;
    return this;
  }

   /**
   * Localized. Item style
   * @return style
  **/
  public String getStyle() {
    return style;
  }

  public void setStyle(String style) {
    this.style = style;
  }

  public ItemTranslation updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ItemTranslation updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ItemTranslation volumeUOM(String volumeUOM) {
    this.volumeUOM = volumeUOM;
    return this;
  }

   /**
   * Localized. Required if Volume is not null
   * @return volumeUOM
  **/
  public String getVolumeUOM() {
    return volumeUOM;
  }

  public void setVolumeUOM(String volumeUOM) {
    this.volumeUOM = volumeUOM;
  }

  public ItemTranslation webURL(String webURL) {
    this.webURL = webURL;
    return this;
  }

   /**
   * Localized. URL of the web page for the item
   * @return webURL
  **/
  public String getWebURL() {
    return webURL;
  }

  public void setWebURL(String webURL) {
    this.webURL = webURL;
  }

  public ItemTranslation weightUOM(String weightUOM) {
    this.weightUOM = weightUOM;
    return this;
  }

   /**
   * Localized. Required if Weight is not null
   * @return weightUOM
  **/
  public String getWeightUOM() {
    return weightUOM;
  }

  public void setWeightUOM(String weightUOM) {
    this.weightUOM = weightUOM;
  }

  public ItemTranslation entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public ItemTranslation localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ItemTranslation itemTranslation = (ItemTranslation) o;
    return Objects.equals(this.actions, itemTranslation.actions) &&
        Objects.equals(this.baseUOM, itemTranslation.baseUOM) &&
        Objects.equals(this.brand, itemTranslation.brand) &&
        Objects.equals(this.color, itemTranslation.color) &&
        Objects.equals(this.colorImageURI, itemTranslation.colorImageURI) &&
        Objects.equals(this.createdBy, itemTranslation.createdBy) &&
        Objects.equals(this.createdTimestamp, itemTranslation.createdTimestamp) &&
        Objects.equals(this.departmentName, itemTranslation.departmentName) &&
        Objects.equals(this.description, itemTranslation.description) &&
        Objects.equals(this.dimensionUOM, itemTranslation.dimensionUOM) &&
        Objects.equals(this.extended, itemTranslation.extended) &&
        Objects.equals(this.languageKey, itemTranslation.languageKey) &&
        Objects.equals(this.largeImageURI, itemTranslation.largeImageURI) &&
        Objects.equals(this.localizedTo, itemTranslation.localizedTo) &&
        Objects.equals(this.messages, itemTranslation.messages) &&
        Objects.equals(this.PK, itemTranslation.PK) &&
        Objects.equals(this.parentItem, itemTranslation.parentItem) &&
        Objects.equals(this.productClass, itemTranslation.productClass) &&
        Objects.equals(this.productSubClass, itemTranslation.productSubClass) &&
        Objects.equals(this.season, itemTranslation.season) &&
        Objects.equals(this.shortDescription, itemTranslation.shortDescription) &&
        Objects.equals(this.size, itemTranslation.size) &&
        Objects.equals(this.smallImageURI, itemTranslation.smallImageURI) &&
        Objects.equals(this.storeDepartment, itemTranslation.storeDepartment) &&
        Objects.equals(this.style, itemTranslation.style) &&
        Objects.equals(this.updatedBy, itemTranslation.updatedBy) &&
        Objects.equals(this.updatedTimestamp, itemTranslation.updatedTimestamp) &&
        Objects.equals(this.volumeUOM, itemTranslation.volumeUOM) &&
        Objects.equals(this.webURL, itemTranslation.webURL) &&
        Objects.equals(this.weightUOM, itemTranslation.weightUOM) &&
        Objects.equals(this.entityName, itemTranslation.entityName) &&
        Objects.equals(this.localize, itemTranslation.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, baseUOM, brand, color, colorImageURI, createdBy, createdTimestamp, departmentName, description, dimensionUOM, extended, languageKey, largeImageURI, localizedTo, messages, PK, parentItem, productClass, productSubClass, season, shortDescription, size, smallImageURI, storeDepartment, style, updatedBy, updatedTimestamp, volumeUOM, webURL, weightUOM, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ItemTranslation {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    baseUOM: ").append(toIndentedString(baseUOM)).append("\n");
    sb.append("    brand: ").append(toIndentedString(brand)).append("\n");
    sb.append("    color: ").append(toIndentedString(color)).append("\n");
    sb.append("    colorImageURI: ").append(toIndentedString(colorImageURI)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    departmentName: ").append(toIndentedString(departmentName)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    dimensionUOM: ").append(toIndentedString(dimensionUOM)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    languageKey: ").append(toIndentedString(languageKey)).append("\n");
    sb.append("    largeImageURI: ").append(toIndentedString(largeImageURI)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentItem: ").append(toIndentedString(parentItem)).append("\n");
    sb.append("    productClass: ").append(toIndentedString(productClass)).append("\n");
    sb.append("    productSubClass: ").append(toIndentedString(productSubClass)).append("\n");
    sb.append("    season: ").append(toIndentedString(season)).append("\n");
    sb.append("    shortDescription: ").append(toIndentedString(shortDescription)).append("\n");
    sb.append("    size: ").append(toIndentedString(size)).append("\n");
    sb.append("    smallImageURI: ").append(toIndentedString(smallImageURI)).append("\n");
    sb.append("    storeDepartment: ").append(toIndentedString(storeDepartment)).append("\n");
    sb.append("    style: ").append(toIndentedString(style)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    volumeUOM: ").append(toIndentedString(volumeUOM)).append("\n");
    sb.append("    webURL: ").append(toIndentedString(webURL)).append("\n");
    sb.append("    weightUOM: ").append(toIndentedString(weightUOM)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

