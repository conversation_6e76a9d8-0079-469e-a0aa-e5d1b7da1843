/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * ItemConditionId
 */
public class ItemConditionId {
  public static final String SERIALIZED_NAME_ITEM_CONDITION_ID = "ItemConditionId";
  @SerializedName(SERIALIZED_NAME_ITEM_CONDITION_ID)
  private String itemConditionId;

  public ItemConditionId itemConditionId(String itemConditionId) {
    this.itemConditionId = itemConditionId;
    return this;
  }

   /**
   * Expected physical condition of the return item. Item condition is captured for call center or self-service returns, where the customer gives the retailer advance notice of a return shipment, and the retailer asks the customer for the expected item condition. The actual item condition at the time of receipt is captured in a separate received item condition field. Item condition can be used to configure return line fees. For example, if the customer indicates that the item is damaged, then a fee is applied; if the item is in new condition, then no fee is applied.
   * @return itemConditionId
  **/
  
  public String getItemConditionId() {
    return itemConditionId;
  }

  public void setItemConditionId(String itemConditionId) {
    this.itemConditionId = itemConditionId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ItemConditionId itemConditionId = (ItemConditionId) o;
    return Objects.equals(this.itemConditionId, itemConditionId.itemConditionId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(itemConditionId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ItemConditionId {\n");
    
    sb.append("    itemConditionId: ").append(toIndentedString(itemConditionId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

