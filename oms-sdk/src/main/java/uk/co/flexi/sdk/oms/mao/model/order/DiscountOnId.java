/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * DiscountOnId
 */
public class DiscountOnId {
  public static final String SERIALIZED_NAME_DISCOUNT_ON_ID = "DiscountOnId";
  @SerializedName(SERIALIZED_NAME_DISCOUNT_ON_ID)
  private String discountOnId;

  public DiscountOnId discountOnId(String discountOnId) {
    this.discountOnId = discountOnId;
    return this;
  }

   /**
   * Finite value of the supported discounts 
   * @return discountOnId
  **/
  
  public String getDiscountOnId() {
    return discountOnId;
  }

  public void setDiscountOnId(String discountOnId) {
    this.discountOnId = discountOnId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DiscountOnId discountOnId = (DiscountOnId) o;
    return Objects.equals(this.discountOnId, discountOnId.discountOnId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(discountOnId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DiscountOnId {\n");
    
    sb.append("    discountOnId: ").append(toIndentedString(discountOnId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

