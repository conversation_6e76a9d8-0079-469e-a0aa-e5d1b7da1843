/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * ValueAddedServiceTypeId
 */
public class ValueAddedServiceTypeId {
  public static final String SERIALIZED_NAME_VALUE_ADDED_SERVICE_TYPE_ID = "ValueAddedServiceTypeId";
  @SerializedName(SERIALIZED_NAME_VALUE_ADDED_SERVICE_TYPE_ID)
  private String valueAddedServiceTypeId;

  public ValueAddedServiceTypeId valueAddedServiceTypeId(String valueAddedServiceTypeId) {
    this.valueAddedServiceTypeId = valueAddedServiceTypeId;
    return this;
  }

   /**
   * Unique id for value added services
   * @return valueAddedServiceTypeId
  **/
  public String getValueAddedServiceTypeId() {
    return valueAddedServiceTypeId;
  }

  public void setValueAddedServiceTypeId(String valueAddedServiceTypeId) {
    this.valueAddedServiceTypeId = valueAddedServiceTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ValueAddedServiceTypeId valueAddedServiceTypeId = (ValueAddedServiceTypeId) o;
    return Objects.equals(this.valueAddedServiceTypeId, valueAddedServiceTypeId.valueAddedServiceTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(valueAddedServiceTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ValueAddedServiceTypeId {\n");
    
    sb.append("    valueAddedServiceTypeId: ").append(toIndentedString(valueAddedServiceTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

