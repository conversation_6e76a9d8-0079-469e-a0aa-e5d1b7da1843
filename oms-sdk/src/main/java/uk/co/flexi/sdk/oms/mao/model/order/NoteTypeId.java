/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * NoteTypeId
 */
public class NoteTypeId {
  public static final String SERIALIZED_NAME_NOTE_TYPE_ID = "NoteTypeId";
  @SerializedName(SERIALIZED_NAME_NOTE_TYPE_ID)
  private String noteTypeId;

  public NoteTypeId noteTypeId(String noteTypeId) {
    this.noteTypeId = noteTypeId;
    return this;
  }

   /**
   * Note type per category can vary, for example, For instructions, we can have Gift to, Gift from, Gift message
   * @return noteTypeId
  **/
  
  public String getNoteTypeId() {
    return noteTypeId;
  }

  public void setNoteTypeId(String noteTypeId) {
    this.noteTypeId = noteTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NoteTypeId noteTypeId = (NoteTypeId) o;
    return Objects.equals(this.noteTypeId, noteTypeId.noteTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(noteTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NoteTypeId {\n");
    
    sb.append("    noteTypeId: ").append(toIndentedString(noteTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

