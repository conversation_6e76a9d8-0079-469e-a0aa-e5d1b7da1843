/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.*;

/**
 * PaymentType
 */
public class PaymentType {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CANCEL_BEHAVIOR = "CancelBehavior";
  @SerializedName(SERIALIZED_NAME_CANCEL_BEHAVIOR)
  private CancelBehavior cancelBehavior = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DENOMINATION = "Denomination";
  @SerializedName(SERIALIZED_NAME_DENOMINATION)
  private List<Denomination> denomination = null;

  public static final String SERIALIZED_NAME_DESCRIPTION = "Description";
  @SerializedName(SERIALIZED_NAME_DESCRIPTION)
  private String description;

  public static final String SERIALIZED_NAME_ELIGIBLE_REFUND_PAYMENT_TYPE = "EligibleRefundPaymentType";
  @SerializedName(SERIALIZED_NAME_ELIGIBLE_REFUND_PAYMENT_TYPE)
  private List<EligibleRefundPaymentType> eligibleRefundPaymentType = null;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IS_PREPAID = "IsPrepaid";
  @SerializedName(SERIALIZED_NAME_IS_PREPAID)
  private Boolean isPrepaid;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_NAME = "Name";
  @SerializedName(SERIALIZED_NAME_NAME)
  private String name;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PAYMENT_TYPE_ID = "PaymentTypeId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TYPE_ID)
  private String paymentTypeId;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_REFUND_BEHAVIOR = "RefundBehavior";
  @SerializedName(SERIALIZED_NAME_REFUND_BEHAVIOR)
  private RefundBehavior refundBehavior = null;

  public static final String SERIALIZED_NAME_REFUND_BEHAVIOR_FOR_EDGE = "RefundBehaviorForEdge";
  @SerializedName(SERIALIZED_NAME_REFUND_BEHAVIOR_FOR_EDGE)
  private RefundBehavior refundBehaviorForEdge = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_VOID_ACTION = "VoidAction";
  @SerializedName(SERIALIZED_NAME_VOID_ACTION)
  private VoidAction voidAction = null;

  public static final String SERIALIZED_NAME_VOID_BEHAVIOR = "VoidBehavior";
  @SerializedName(SERIALIZED_NAME_VOID_BEHAVIOR)
  private VoidBehavior voidBehavior = null;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public PaymentType actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public PaymentType putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public PaymentType cancelBehavior(CancelBehavior cancelBehavior) {
    this.cancelBehavior = cancelBehavior;
    return this;
  }

   /**
   * Get cancelBehavior
   * @return cancelBehavior
  **/
  
  public CancelBehavior getCancelBehavior() {
    return cancelBehavior;
  }

  public void setCancelBehavior(CancelBehavior cancelBehavior) {
    this.cancelBehavior = cancelBehavior;
  }

  public PaymentType createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public PaymentType createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public PaymentType denomination(List<Denomination> denomination) {
    this.denomination = denomination;
    return this;
  }

  public PaymentType addDenominationItem(Denomination denominationItem) {
    if (this.denomination == null) {
      this.denomination = new ArrayList<Denomination>();
    }
    this.denomination.add(denominationItem);
    return this;
  }

   /**
   * Get denomination
   * @return denomination
  **/
  
  public List<Denomination> getDenomination() {
    return denomination;
  }

  public void setDenomination(List<Denomination> denomination) {
    this.denomination = denomination;
  }

  public PaymentType description(String description) {
    this.description = description;
    return this;
  }

   /**
   * Description of the payment type. Used for informational purposes.
   * @return description
  **/
  
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public PaymentType eligibleRefundPaymentType(List<EligibleRefundPaymentType> eligibleRefundPaymentType) {
    this.eligibleRefundPaymentType = eligibleRefundPaymentType;
    return this;
  }

  public PaymentType addEligibleRefundPaymentTypeItem(EligibleRefundPaymentType eligibleRefundPaymentTypeItem) {
    if (this.eligibleRefundPaymentType == null) {
      this.eligibleRefundPaymentType = new ArrayList<EligibleRefundPaymentType>();
    }
    this.eligibleRefundPaymentType.add(eligibleRefundPaymentTypeItem);
    return this;
  }

   /**
   * Get eligibleRefundPaymentType
   * @return eligibleRefundPaymentType
  **/
  
  public List<EligibleRefundPaymentType> getEligibleRefundPaymentType() {
    return eligibleRefundPaymentType;
  }

  public void setEligibleRefundPaymentType(List<EligibleRefundPaymentType> eligibleRefundPaymentType) {
    this.eligibleRefundPaymentType = eligibleRefundPaymentType;
  }

  public PaymentType extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public PaymentType isPrepaid(Boolean isPrepaid) {
    this.isPrepaid = isPrepaid;
    return this;
  }

   /**
   * Indicates if payment method is considered pre-paid when saved. Used by the save payment header service to determine if any payment transactions need to be created against a payment method. If true, then a successful, closed settlement is created upon save. For example, cash is configured as pre-paid. When a $60 cash payment is saved, a closed, successful $60 settlement is created against the payment method. If false, then no payment transactions are created upon save, but rather they are created when the calculate service determines what transaction type to create by using the payment summary. For example, credit cards are not configured as pre-paid. 
   * @return isPrepaid
  **/
  
  public Boolean getIsPrepaid() {
    return isPrepaid;
  }

  public void setIsPrepaid(Boolean isPrepaid) {
    this.isPrepaid = isPrepaid;
  }

  public PaymentType localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public PaymentType messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public PaymentType name(String name) {
    this.name = name;
    return this;
  }

   /**
   * Name of the payment type. Used for informational purposes.
   * @return name
  **/
  
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public PaymentType PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public PaymentType paymentTypeId(String paymentTypeId) {
    this.paymentTypeId = paymentTypeId;
    return this;
  }

   /**
   * Unique identifier of the payment type. Used to identify the payment type in the payment methods, payment rule, and payment gateway response mapping.
   * @return paymentTypeId
  **/
  
  public String getPaymentTypeId() {
    return paymentTypeId;
  }

  public void setPaymentTypeId(String paymentTypeId) {
    this.paymentTypeId = paymentTypeId;
  }

  public PaymentType profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public PaymentType refundBehavior(RefundBehavior refundBehavior) {
    this.refundBehavior = refundBehavior;
    return this;
  }

   /**
   * Get refundBehavior
   * @return refundBehavior
  **/
  
  public RefundBehavior getRefundBehavior() {
    return refundBehavior;
  }

  public void setRefundBehavior(RefundBehavior refundBehavior) {
    this.refundBehavior = refundBehavior;
  }

  public PaymentType refundBehaviorForEdge(RefundBehavior refundBehaviorForEdge) {
    this.refundBehaviorForEdge = refundBehaviorForEdge;
    return this;
  }

   /**
   * Get refundBehaviorForEdge
   * @return refundBehaviorForEdge
  **/
  
  public RefundBehavior getRefundBehaviorForEdge() {
    return refundBehaviorForEdge;
  }

  public void setRefundBehaviorForEdge(RefundBehavior refundBehaviorForEdge) {
    this.refundBehaviorForEdge = refundBehaviorForEdge;
  }

  public PaymentType updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public PaymentType updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public PaymentType voidAction(VoidAction voidAction) {
    this.voidAction = voidAction;
    return this;
  }

   /**
   * Get voidAction
   * @return voidAction
  **/
  
  public VoidAction getVoidAction() {
    return voidAction;
  }

  public void setVoidAction(VoidAction voidAction) {
    this.voidAction = voidAction;
  }

  public PaymentType voidBehavior(VoidBehavior voidBehavior) {
    this.voidBehavior = voidBehavior;
    return this;
  }

   /**
   * Get voidBehavior
   * @return voidBehavior
  **/
  
  public VoidBehavior getVoidBehavior() {
    return voidBehavior;
  }

  public void setVoidBehavior(VoidBehavior voidBehavior) {
    this.voidBehavior = voidBehavior;
  }

  public PaymentType rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentType paymentType = (PaymentType) o;
    return Objects.equals(this.actions, paymentType.actions) &&
        Objects.equals(this.cancelBehavior, paymentType.cancelBehavior) &&
        Objects.equals(this.createdBy, paymentType.createdBy) &&
        Objects.equals(this.createdTimestamp, paymentType.createdTimestamp) &&
        Objects.equals(this.denomination, paymentType.denomination) &&
        Objects.equals(this.description, paymentType.description) &&
        Objects.equals(this.eligibleRefundPaymentType, paymentType.eligibleRefundPaymentType) &&
        Objects.equals(this.extended, paymentType.extended) &&
        Objects.equals(this.isPrepaid, paymentType.isPrepaid) &&
        Objects.equals(this.localizedTo, paymentType.localizedTo) &&
        Objects.equals(this.messages, paymentType.messages) &&
        Objects.equals(this.name, paymentType.name) &&
        Objects.equals(this.PK, paymentType.PK) &&
        Objects.equals(this.paymentTypeId, paymentType.paymentTypeId) &&
        Objects.equals(this.profileId, paymentType.profileId) &&
        Objects.equals(this.refundBehavior, paymentType.refundBehavior) &&
        Objects.equals(this.refundBehaviorForEdge, paymentType.refundBehaviorForEdge) &&
        Objects.equals(this.updatedBy, paymentType.updatedBy) &&
        Objects.equals(this.updatedTimestamp, paymentType.updatedTimestamp) &&
        Objects.equals(this.voidAction, paymentType.voidAction) &&
        Objects.equals(this.voidBehavior, paymentType.voidBehavior) &&
        Objects.equals(this.rootCause, paymentType.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, cancelBehavior, createdBy, createdTimestamp, denomination, description, eligibleRefundPaymentType, extended, isPrepaid, localizedTo, messages, name, PK, paymentTypeId, profileId, refundBehavior, refundBehaviorForEdge, updatedBy, updatedTimestamp, voidAction, voidBehavior, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentType {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    cancelBehavior: ").append(toIndentedString(cancelBehavior)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    denomination: ").append(toIndentedString(denomination)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    eligibleRefundPaymentType: ").append(toIndentedString(eligibleRefundPaymentType)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    isPrepaid: ").append(toIndentedString(isPrepaid)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    paymentTypeId: ").append(toIndentedString(paymentTypeId)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    refundBehavior: ").append(toIndentedString(refundBehavior)).append("\n");
    sb.append("    refundBehaviorForEdge: ").append(toIndentedString(refundBehaviorForEdge)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    voidAction: ").append(toIndentedString(voidAction)).append("\n");
    sb.append("    voidBehavior: ").append(toIndentedString(voidBehavior)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

