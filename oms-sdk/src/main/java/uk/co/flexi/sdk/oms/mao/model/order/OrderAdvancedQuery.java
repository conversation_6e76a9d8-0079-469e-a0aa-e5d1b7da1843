/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * OrderAdvancedQuery
 */
public class OrderAdvancedQuery {
  public static final String SERIALIZED_NAME_ORDER_HOLD_QUERY = "OrderHoldQuery";
  @SerializedName(SERIALIZED_NAME_ORDER_HOLD_QUERY)
  private String orderHoldQuery;

  public static final String SERIALIZED_NAME_ORDER_LINE_HOLD_QUERY = "OrderLineHoldQuery";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_HOLD_QUERY)
  private String orderLineHoldQuery;

  public static final String SERIALIZED_NAME_ORDER_LINE_QUERY = "OrderLineQuery";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_QUERY)
  private String orderLineQuery;

  public static final String SERIALIZED_NAME_ORDER_UPDATED_BY_QUERY = "OrderUpdatedByQuery";
  @SerializedName(SERIALIZED_NAME_ORDER_UPDATED_BY_QUERY)
  private String orderUpdatedByQuery;

  public static final String SERIALIZED_NAME_PAGE = "Page";
  @SerializedName(SERIALIZED_NAME_PAGE)
  private Integer page;

  public static final String SERIALIZED_NAME_QUERY = "Query";
  @SerializedName(SERIALIZED_NAME_QUERY)
  private String query;

  public static final String SERIALIZED_NAME_SIZE = "Size";
  @SerializedName(SERIALIZED_NAME_SIZE)
  private Integer size;

  public static final String SERIALIZED_NAME_SORT = "Sort";
  @SerializedName(SERIALIZED_NAME_SORT)
  private List<Map<String,String>> sort = null;

  public static final String SERIALIZED_NAME_TEMPLATE = "Template";
  @SerializedName(SERIALIZED_NAME_TEMPLATE)
  private Object template = null;

  public OrderAdvancedQuery orderHoldQuery(String orderHoldQuery) {
    this.orderHoldQuery = orderHoldQuery;
    return this;
  }

   /**
   * Get orderHoldQuery
   * @return orderHoldQuery
  **/
  
  public String getOrderHoldQuery() {
    return orderHoldQuery;
  }

  public void setOrderHoldQuery(String orderHoldQuery) {
    this.orderHoldQuery = orderHoldQuery;
  }

  public OrderAdvancedQuery orderLineHoldQuery(String orderLineHoldQuery) {
    this.orderLineHoldQuery = orderLineHoldQuery;
    return this;
  }

   /**
   * Get orderLineHoldQuery
   * @return orderLineHoldQuery
  **/
  
  public String getOrderLineHoldQuery() {
    return orderLineHoldQuery;
  }

  public void setOrderLineHoldQuery(String orderLineHoldQuery) {
    this.orderLineHoldQuery = orderLineHoldQuery;
  }

  public OrderAdvancedQuery orderLineQuery(String orderLineQuery) {
    this.orderLineQuery = orderLineQuery;
    return this;
  }

   /**
   * Get orderLineQuery
   * @return orderLineQuery
  **/
  
  public String getOrderLineQuery() {
    return orderLineQuery;
  }

  public void setOrderLineQuery(String orderLineQuery) {
    this.orderLineQuery = orderLineQuery;
  }

  public OrderAdvancedQuery orderUpdatedByQuery(String orderUpdatedByQuery) {
    this.orderUpdatedByQuery = orderUpdatedByQuery;
    return this;
  }

   /**
   * Get orderUpdatedByQuery
   * @return orderUpdatedByQuery
  **/
  
  public String getOrderUpdatedByQuery() {
    return orderUpdatedByQuery;
  }

  public void setOrderUpdatedByQuery(String orderUpdatedByQuery) {
    this.orderUpdatedByQuery = orderUpdatedByQuery;
  }

  public OrderAdvancedQuery page(Integer page) {
    this.page = page;
    return this;
  }

   /**
   * Get page
   * @return page
  **/
  
  public Integer getPage() {
    return page;
  }

  public void setPage(Integer page) {
    this.page = page;
  }

  public OrderAdvancedQuery query(String query) {
    this.query = query;
    return this;
  }

   /**
   * Get query
   * @return query
  **/
  
  public String getQuery() {
    return query;
  }

  public void setQuery(String query) {
    this.query = query;
  }

  public OrderAdvancedQuery size(Integer size) {
    this.size = size;
    return this;
  }

   /**
   * Get size
   * @return size
  **/
  
  public Integer getSize() {
    return size;
  }

  public void setSize(Integer size) {
    this.size = size;
  }

  public OrderAdvancedQuery sort(List<Map<String,String>> sort) {
    this.sort = sort;
    return this;
  }

  public OrderAdvancedQuery addSortItem(Map<String, String> sortItem) {
    if (this.sort == null) {
      this.sort = new ArrayList<Map<String,String>>();
    }
    this.sort.add(sortItem);
    return this;
  }

   /**
   * Get sort
   * @return sort
  **/
  
  public List<Map<String,String>> getSort() {
    return sort;
  }

  public void setSort(List<Map<String,String>> sort) {
    this.sort = sort;
  }

  public OrderAdvancedQuery template(Object template) {
    this.template = template;
    return this;
  }

   /**
   * Get template
   * @return template
  **/
  
  public Object getTemplate() {
    return template;
  }

  public void setTemplate(Object template) {
    this.template = template;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderAdvancedQuery orderAdvancedQuery = (OrderAdvancedQuery) o;
    return Objects.equals(this.orderHoldQuery, orderAdvancedQuery.orderHoldQuery) &&
        Objects.equals(this.orderLineHoldQuery, orderAdvancedQuery.orderLineHoldQuery) &&
        Objects.equals(this.orderLineQuery, orderAdvancedQuery.orderLineQuery) &&
        Objects.equals(this.orderUpdatedByQuery, orderAdvancedQuery.orderUpdatedByQuery) &&
        Objects.equals(this.page, orderAdvancedQuery.page) &&
        Objects.equals(this.query, orderAdvancedQuery.query) &&
        Objects.equals(this.size, orderAdvancedQuery.size) &&
        Objects.equals(this.sort, orderAdvancedQuery.sort) &&
        Objects.equals(this.template, orderAdvancedQuery.template);
  }

  @Override
  public int hashCode() {
    return Objects.hash(orderHoldQuery, orderLineHoldQuery, orderLineQuery, orderUpdatedByQuery, page, query, size, sort, template);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderAdvancedQuery {\n");
    
    sb.append("    orderHoldQuery: ").append(toIndentedString(orderHoldQuery)).append("\n");
    sb.append("    orderLineHoldQuery: ").append(toIndentedString(orderLineHoldQuery)).append("\n");
    sb.append("    orderLineQuery: ").append(toIndentedString(orderLineQuery)).append("\n");
    sb.append("    orderUpdatedByQuery: ").append(toIndentedString(orderUpdatedByQuery)).append("\n");
    sb.append("    page: ").append(toIndentedString(page)).append("\n");
    sb.append("    query: ").append(toIndentedString(query)).append("\n");
    sb.append("    size: ").append(toIndentedString(size)).append("\n");
    sb.append("    sort: ").append(toIndentedString(sort)).append("\n");
    sb.append("    template: ").append(toIndentedString(template)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

