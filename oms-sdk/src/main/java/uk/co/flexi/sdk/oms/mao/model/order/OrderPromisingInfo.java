/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderPromisingInfo
 */
public class OrderPromisingInfo {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CARRIER_CODE = "CarrierCode";
  @SerializedName(SERIALIZED_NAME_CARRIER_CODE)
  private String carrierCode;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DO_NOT_SHIP_BEFORE_DATE = "DoNotShipBeforeDate";
  @SerializedName(SERIALIZED_NAME_DO_NOT_SHIP_BEFORE_DATE)
  private OffsetDateTime doNotShipBeforeDate;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_EXTERNAL_ROUTE_ID = "ExternalRouteId";
  @SerializedName(SERIALIZED_NAME_EXTERNAL_ROUTE_ID)
  private String externalRouteId;

  public static final String SERIALIZED_NAME_LATEST_DELIVERY_DATE = "LatestDeliveryDate";
  @SerializedName(SERIALIZED_NAME_LATEST_DELIVERY_DATE)
  private OffsetDateTime latestDeliveryDate;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROMISED_DELIVERY_DATE = "PromisedDeliveryDate";
  @SerializedName(SERIALIZED_NAME_PROMISED_DELIVERY_DATE)
  private OffsetDateTime promisedDeliveryDate;

  public static final String SERIALIZED_NAME_REQUESTED_DELIVERY_DATE = "RequestedDeliveryDate";
  @SerializedName(SERIALIZED_NAME_REQUESTED_DELIVERY_DATE)
  private OffsetDateTime requestedDeliveryDate;

  public static final String SERIALIZED_NAME_SERVICE_LEVEL_CODE = "ServiceLevelCode";
  @SerializedName(SERIALIZED_NAME_SERVICE_LEVEL_CODE)
  private String serviceLevelCode;

  public static final String SERIALIZED_NAME_SHIP_FROM_LOCATION_ID = "ShipFromLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_FROM_LOCATION_ID)
  private String shipFromLocationId;

  public static final String SERIALIZED_NAME_SHIP_THROUGH_LOCATION_ID = "ShipThroughLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_THROUGH_LOCATION_ID)
  private String shipThroughLocationId;

  public static final String SERIALIZED_NAME_SHIP_TO_LOCATION_ID = "ShipToLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_TO_LOCATION_ID)
  private String shipToLocationId;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderPromisingInfo actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderPromisingInfo putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderPromisingInfo carrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
    return this;
  }

   /**
   * Carrier which should be used to fulfill the order
   * @return carrierCode
  **/
  
  public String getCarrierCode() {
    return carrierCode;
  }

  public void setCarrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
  }

  public OrderPromisingInfo createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderPromisingInfo createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderPromisingInfo doNotShipBeforeDate(OffsetDateTime doNotShipBeforeDate) {
    this.doNotShipBeforeDate = doNotShipBeforeDate;
    return this;
  }

   /**
   * Earliest date on which the order line should be shipped; This is read only if IsPackAndHold is true
   * @return doNotShipBeforeDate
  **/
  
  public OffsetDateTime getDoNotShipBeforeDate() {
    return doNotShipBeforeDate;
  }

  public void setDoNotShipBeforeDate(OffsetDateTime doNotShipBeforeDate) {
    this.doNotShipBeforeDate = doNotShipBeforeDate;
  }

  public OrderPromisingInfo extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderPromisingInfo externalRouteId(String externalRouteId) {
    this.externalRouteId = externalRouteId;
    return this;
  }

   /**
   * Route identifier, if a merge route is specified for the order. If no merge route is specified, then the system selects a route based on configuration. For example, if an external routing system determines that a merge route from DC1 to DC2 to customer should be used to deliver all items in a single package, then the route id should be passed in this field. This value acts as a matching criteria for merge route configured for an inventory location during promising.
   * @return externalRouteId
  **/
  
  public String getExternalRouteId() {
    return externalRouteId;
  }

  public void setExternalRouteId(String externalRouteId) {
    this.externalRouteId = externalRouteId;
  }

  public OrderPromisingInfo latestDeliveryDate(OffsetDateTime latestDeliveryDate) {
    this.latestDeliveryDate = latestDeliveryDate;
    return this;
  }

   /**
   * Latest possible delivery date on which the items should be delivered to the destination
   * @return latestDeliveryDate
  **/
  
  public OffsetDateTime getLatestDeliveryDate() {
    return latestDeliveryDate;
  }

  public void setLatestDeliveryDate(OffsetDateTime latestDeliveryDate) {
    this.latestDeliveryDate = latestDeliveryDate;
  }

  public OrderPromisingInfo localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderPromisingInfo messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderPromisingInfo orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderPromisingInfo PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderPromisingInfo promisedDeliveryDate(OffsetDateTime promisedDeliveryDate) {
    this.promisedDeliveryDate = promisedDeliveryDate;
    return this;
  }

   /**
   * Delivery date which was originally promised to the customer
   * @return promisedDeliveryDate
  **/
  
  public OffsetDateTime getPromisedDeliveryDate() {
    return promisedDeliveryDate;
  }

  public void setPromisedDeliveryDate(OffsetDateTime promisedDeliveryDate) {
    this.promisedDeliveryDate = promisedDeliveryDate;
  }

  public OrderPromisingInfo requestedDeliveryDate(OffsetDateTime requestedDeliveryDate) {
    this.requestedDeliveryDate = requestedDeliveryDate;
    return this;
  }

   /**
   * Requested date on which the items should be delivered to the destination
   * @return requestedDeliveryDate
  **/
  
  public OffsetDateTime getRequestedDeliveryDate() {
    return requestedDeliveryDate;
  }

  public void setRequestedDeliveryDate(OffsetDateTime requestedDeliveryDate) {
    this.requestedDeliveryDate = requestedDeliveryDate;
  }

  public OrderPromisingInfo serviceLevelCode(String serviceLevelCode) {
    this.serviceLevelCode = serviceLevelCode;
    return this;
  }

   /**
   * Service Level which should be used to fulfill the orde
   * @return serviceLevelCode
  **/
  
  public String getServiceLevelCode() {
    return serviceLevelCode;
  }

  public void setServiceLevelCode(String serviceLevelCode) {
    this.serviceLevelCode = serviceLevelCode;
  }

  public OrderPromisingInfo shipFromLocationId(String shipFromLocationId) {
    this.shipFromLocationId = shipFromLocationId;
    return this;
  }

   /**
   * Preferred Store Location for Allocation
   * @return shipFromLocationId
  **/
  
  public String getShipFromLocationId() {
    return shipFromLocationId;
  }

  public void setShipFromLocationId(String shipFromLocationId) {
    this.shipFromLocationId = shipFromLocationId;
  }

  public OrderPromisingInfo shipThroughLocationId(String shipThroughLocationId) {
    this.shipThroughLocationId = shipThroughLocationId;
    return this;
  }

   /**
   * Shipping order through a facility which does direct-to-consumer shipments, if the fulfilling facility (e.g. vendor) doesn&#39;t ship directly to customers
   * @return shipThroughLocationId
  **/
  
  public String getShipThroughLocationId() {
    return shipThroughLocationId;
  }

  public void setShipThroughLocationId(String shipThroughLocationId) {
    this.shipThroughLocationId = shipThroughLocationId;
  }

  public OrderPromisingInfo shipToLocationId(String shipToLocationId) {
    this.shipToLocationId = shipToLocationId;
    return this;
  }

   /**
   * Ship-to location ID, in case of ship to store, pick up in store, or any flow where the items are shipping to a destination which is configured as a location in the network
   * @return shipToLocationId
  **/
  
  public String getShipToLocationId() {
    return shipToLocationId;
  }

  public void setShipToLocationId(String shipToLocationId) {
    this.shipToLocationId = shipToLocationId;
  }

  public OrderPromisingInfo updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderPromisingInfo updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderPromisingInfo entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderPromisingInfo localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderPromisingInfo orderPromisingInfo = (OrderPromisingInfo) o;
    return Objects.equals(this.actions, orderPromisingInfo.actions) &&
        Objects.equals(this.carrierCode, orderPromisingInfo.carrierCode) &&
        Objects.equals(this.createdBy, orderPromisingInfo.createdBy) &&
        Objects.equals(this.createdTimestamp, orderPromisingInfo.createdTimestamp) &&
        Objects.equals(this.doNotShipBeforeDate, orderPromisingInfo.doNotShipBeforeDate) &&
        Objects.equals(this.extended, orderPromisingInfo.extended) &&
        Objects.equals(this.externalRouteId, orderPromisingInfo.externalRouteId) &&
        Objects.equals(this.latestDeliveryDate, orderPromisingInfo.latestDeliveryDate) &&
        Objects.equals(this.localizedTo, orderPromisingInfo.localizedTo) &&
        Objects.equals(this.messages, orderPromisingInfo.messages) &&
        Objects.equals(this.orgId, orderPromisingInfo.orgId) &&
        Objects.equals(this.PK, orderPromisingInfo.PK) &&
        Objects.equals(this.promisedDeliveryDate, orderPromisingInfo.promisedDeliveryDate) &&
        Objects.equals(this.requestedDeliveryDate, orderPromisingInfo.requestedDeliveryDate) &&
        Objects.equals(this.serviceLevelCode, orderPromisingInfo.serviceLevelCode) &&
        Objects.equals(this.shipFromLocationId, orderPromisingInfo.shipFromLocationId) &&
        Objects.equals(this.shipThroughLocationId, orderPromisingInfo.shipThroughLocationId) &&
        Objects.equals(this.shipToLocationId, orderPromisingInfo.shipToLocationId) &&
        Objects.equals(this.updatedBy, orderPromisingInfo.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderPromisingInfo.updatedTimestamp) &&
        Objects.equals(this.entityName, orderPromisingInfo.entityName) &&
        Objects.equals(this.localize, orderPromisingInfo.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, carrierCode, createdBy, createdTimestamp, doNotShipBeforeDate, extended, externalRouteId, latestDeliveryDate, localizedTo, messages, orgId, PK, promisedDeliveryDate, requestedDeliveryDate, serviceLevelCode, shipFromLocationId, shipThroughLocationId, shipToLocationId, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderPromisingInfo {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    carrierCode: ").append(toIndentedString(carrierCode)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    doNotShipBeforeDate: ").append(toIndentedString(doNotShipBeforeDate)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    externalRouteId: ").append(toIndentedString(externalRouteId)).append("\n");
    sb.append("    latestDeliveryDate: ").append(toIndentedString(latestDeliveryDate)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    promisedDeliveryDate: ").append(toIndentedString(promisedDeliveryDate)).append("\n");
    sb.append("    requestedDeliveryDate: ").append(toIndentedString(requestedDeliveryDate)).append("\n");
    sb.append("    serviceLevelCode: ").append(toIndentedString(serviceLevelCode)).append("\n");
    sb.append("    shipFromLocationId: ").append(toIndentedString(shipFromLocationId)).append("\n");
    sb.append("    shipThroughLocationId: ").append(toIndentedString(shipThroughLocationId)).append("\n");
    sb.append("    shipToLocationId: ").append(toIndentedString(shipToLocationId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

