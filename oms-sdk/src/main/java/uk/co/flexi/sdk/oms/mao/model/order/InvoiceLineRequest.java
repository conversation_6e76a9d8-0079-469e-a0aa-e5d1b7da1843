/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * InvoiceLineRequest
 */
public class InvoiceLineRequest {
  public static final String SERIALIZED_NAME_FULFILLMENT_GROUP_ID = "fulfillmentGroupId";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_GROUP_ID)
  private String fulfillmentGroupId;

  public static final String SERIALIZED_NAME_INVOICE_LINE_CHARGE_DETAIL_REQUEST_LIST = "invoiceLineChargeDetailRequestList";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE_CHARGE_DETAIL_REQUEST_LIST)
  private List<InvoiceLineChargeDetailRequest> invoiceLineChargeDetailRequestList = null;

  public static final String SERIALIZED_NAME_INVOICE_LINE_SUB_TOTAL = "invoiceLineSubTotal";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE_SUB_TOTAL)
  private BigDecimal invoiceLineSubTotal;

  public static final String SERIALIZED_NAME_INVOICE_LINE_TAX_DETAIL_REQUEST_LIST = "invoiceLineTaxDetailRequestList";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE_TAX_DETAIL_REQUEST_LIST)
  private List<InvoiceLineTaxDetailRequest> invoiceLineTaxDetailRequestList = null;

  public static final String SERIALIZED_NAME_IS_REFUND_GIFT_CARD = "isRefundGiftCard";
  @SerializedName(SERIALIZED_NAME_IS_REFUND_GIFT_CARD)
  private Boolean isRefundGiftCard;

  public static final String SERIALIZED_NAME_ORDER_LINE_CREATED_TIMESTAMP = "orderLineCreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_CREATED_TIMESTAMP)
  private OffsetDateTime orderLineCreatedTimestamp;

  public static final String SERIALIZED_NAME_ORDER_LINE_ID = "orderLineId";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_ID)
  private String orderLineId;

  public static final String SERIALIZED_NAME_PARENT_LINE_CREATED_TIMESTAMP = "parentLineCreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_PARENT_LINE_CREATED_TIMESTAMP)
  private OffsetDateTime parentLineCreatedTimestamp;

  public static final String SERIALIZED_NAME_PRODUCT_CLASS = "productClass";
  @SerializedName(SERIALIZED_NAME_PRODUCT_CLASS)
  private String productClass;

  public static final String SERIALIZED_NAME_QUANTITY = "quantity";
  @SerializedName(SERIALIZED_NAME_QUANTITY)
  private Double quantity;

  public static final String SERIALIZED_NAME_TAX_SHIP_FROM_LOCATION_ID = "taxShipFromLocationId";
  @SerializedName(SERIALIZED_NAME_TAX_SHIP_FROM_LOCATION_ID)
  private String taxShipFromLocationId;

  public static final String SERIALIZED_NAME_TOTAL_CHARGES = "totalCharges";
  @SerializedName(SERIALIZED_NAME_TOTAL_CHARGES)
  private BigDecimal totalCharges;

  public static final String SERIALIZED_NAME_TOTAL_DISCOUNTS = "totalDiscounts";
  @SerializedName(SERIALIZED_NAME_TOTAL_DISCOUNTS)
  private BigDecimal totalDiscounts;

  public static final String SERIALIZED_NAME_UNIT_PRICE = "unitPrice";
  @SerializedName(SERIALIZED_NAME_UNIT_PRICE)
  private BigDecimal unitPrice;

  public InvoiceLineRequest fulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
    return this;
  }

   /**
   * Get fulfillmentGroupId
   * @return fulfillmentGroupId
  **/
  
  public String getFulfillmentGroupId() {
    return fulfillmentGroupId;
  }

  public void setFulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
  }

  public InvoiceLineRequest invoiceLineChargeDetailRequestList(List<InvoiceLineChargeDetailRequest> invoiceLineChargeDetailRequestList) {
    this.invoiceLineChargeDetailRequestList = invoiceLineChargeDetailRequestList;
    return this;
  }

  public InvoiceLineRequest addInvoiceLineChargeDetailRequestListItem(InvoiceLineChargeDetailRequest invoiceLineChargeDetailRequestListItem) {
    if (this.invoiceLineChargeDetailRequestList == null) {
      this.invoiceLineChargeDetailRequestList = new ArrayList<InvoiceLineChargeDetailRequest>();
    }
    this.invoiceLineChargeDetailRequestList.add(invoiceLineChargeDetailRequestListItem);
    return this;
  }

   /**
   * Get invoiceLineChargeDetailRequestList
   * @return invoiceLineChargeDetailRequestList
  **/
  
  public List<InvoiceLineChargeDetailRequest> getInvoiceLineChargeDetailRequestList() {
    return invoiceLineChargeDetailRequestList;
  }

  public void setInvoiceLineChargeDetailRequestList(List<InvoiceLineChargeDetailRequest> invoiceLineChargeDetailRequestList) {
    this.invoiceLineChargeDetailRequestList = invoiceLineChargeDetailRequestList;
  }

  public InvoiceLineRequest invoiceLineSubTotal(BigDecimal invoiceLineSubTotal) {
    this.invoiceLineSubTotal = invoiceLineSubTotal;
    return this;
  }

   /**
   * Get invoiceLineSubTotal
   * @return invoiceLineSubTotal
  **/
  
  public BigDecimal getInvoiceLineSubTotal() {
    return invoiceLineSubTotal;
  }

  public void setInvoiceLineSubTotal(BigDecimal invoiceLineSubTotal) {
    this.invoiceLineSubTotal = invoiceLineSubTotal;
  }

  public InvoiceLineRequest invoiceLineTaxDetailRequestList(List<InvoiceLineTaxDetailRequest> invoiceLineTaxDetailRequestList) {
    this.invoiceLineTaxDetailRequestList = invoiceLineTaxDetailRequestList;
    return this;
  }

  public InvoiceLineRequest addInvoiceLineTaxDetailRequestListItem(InvoiceLineTaxDetailRequest invoiceLineTaxDetailRequestListItem) {
    if (this.invoiceLineTaxDetailRequestList == null) {
      this.invoiceLineTaxDetailRequestList = new ArrayList<InvoiceLineTaxDetailRequest>();
    }
    this.invoiceLineTaxDetailRequestList.add(invoiceLineTaxDetailRequestListItem);
    return this;
  }

   /**
   * Get invoiceLineTaxDetailRequestList
   * @return invoiceLineTaxDetailRequestList
  **/
  
  public List<InvoiceLineTaxDetailRequest> getInvoiceLineTaxDetailRequestList() {
    return invoiceLineTaxDetailRequestList;
  }

  public void setInvoiceLineTaxDetailRequestList(List<InvoiceLineTaxDetailRequest> invoiceLineTaxDetailRequestList) {
    this.invoiceLineTaxDetailRequestList = invoiceLineTaxDetailRequestList;
  }

  public InvoiceLineRequest isRefundGiftCard(Boolean isRefundGiftCard) {
    this.isRefundGiftCard = isRefundGiftCard;
    return this;
  }

   /**
   * Get isRefundGiftCard
   * @return isRefundGiftCard
  **/
  
  public Boolean getIsRefundGiftCard() {
    return isRefundGiftCard;
  }

  public void setIsRefundGiftCard(Boolean isRefundGiftCard) {
    this.isRefundGiftCard = isRefundGiftCard;
  }

  public InvoiceLineRequest orderLineCreatedTimestamp(OffsetDateTime orderLineCreatedTimestamp) {
    this.orderLineCreatedTimestamp = orderLineCreatedTimestamp;
    return this;
  }

   /**
   * Get orderLineCreatedTimestamp
   * @return orderLineCreatedTimestamp
  **/
  
  public OffsetDateTime getOrderLineCreatedTimestamp() {
    return orderLineCreatedTimestamp;
  }

  public void setOrderLineCreatedTimestamp(OffsetDateTime orderLineCreatedTimestamp) {
    this.orderLineCreatedTimestamp = orderLineCreatedTimestamp;
  }

  public InvoiceLineRequest orderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
    return this;
  }

   /**
   * Get orderLineId
   * @return orderLineId
  **/
  
  public String getOrderLineId() {
    return orderLineId;
  }

  public void setOrderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
  }

  public InvoiceLineRequest parentLineCreatedTimestamp(OffsetDateTime parentLineCreatedTimestamp) {
    this.parentLineCreatedTimestamp = parentLineCreatedTimestamp;
    return this;
  }

   /**
   * Get parentLineCreatedTimestamp
   * @return parentLineCreatedTimestamp
  **/
  
  public OffsetDateTime getParentLineCreatedTimestamp() {
    return parentLineCreatedTimestamp;
  }

  public void setParentLineCreatedTimestamp(OffsetDateTime parentLineCreatedTimestamp) {
    this.parentLineCreatedTimestamp = parentLineCreatedTimestamp;
  }

  public InvoiceLineRequest productClass(String productClass) {
    this.productClass = productClass;
    return this;
  }

   /**
   * Get productClass
   * @return productClass
  **/
  
  public String getProductClass() {
    return productClass;
  }

  public void setProductClass(String productClass) {
    this.productClass = productClass;
  }

  public InvoiceLineRequest quantity(Double quantity) {
    this.quantity = quantity;
    return this;
  }

   /**
   * Get quantity
   * @return quantity
  **/
  
  public Double getQuantity() {
    return quantity;
  }

  public void setQuantity(Double quantity) {
    this.quantity = quantity;
  }

  public InvoiceLineRequest taxShipFromLocationId(String taxShipFromLocationId) {
    this.taxShipFromLocationId = taxShipFromLocationId;
    return this;
  }

   /**
   * Get taxShipFromLocationId
   * @return taxShipFromLocationId
  **/
  
  public String getTaxShipFromLocationId() {
    return taxShipFromLocationId;
  }

  public void setTaxShipFromLocationId(String taxShipFromLocationId) {
    this.taxShipFromLocationId = taxShipFromLocationId;
  }

  public InvoiceLineRequest totalCharges(BigDecimal totalCharges) {
    this.totalCharges = totalCharges;
    return this;
  }

   /**
   * Get totalCharges
   * @return totalCharges
  **/
  
  public BigDecimal getTotalCharges() {
    return totalCharges;
  }

  public void setTotalCharges(BigDecimal totalCharges) {
    this.totalCharges = totalCharges;
  }

  public InvoiceLineRequest totalDiscounts(BigDecimal totalDiscounts) {
    this.totalDiscounts = totalDiscounts;
    return this;
  }

   /**
   * Get totalDiscounts
   * @return totalDiscounts
  **/
  
  public BigDecimal getTotalDiscounts() {
    return totalDiscounts;
  }

  public void setTotalDiscounts(BigDecimal totalDiscounts) {
    this.totalDiscounts = totalDiscounts;
  }

  public InvoiceLineRequest unitPrice(BigDecimal unitPrice) {
    this.unitPrice = unitPrice;
    return this;
  }

   /**
   * Get unitPrice
   * @return unitPrice
  **/
  
  public BigDecimal getUnitPrice() {
    return unitPrice;
  }

  public void setUnitPrice(BigDecimal unitPrice) {
    this.unitPrice = unitPrice;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvoiceLineRequest invoiceLineRequest = (InvoiceLineRequest) o;
    return Objects.equals(this.fulfillmentGroupId, invoiceLineRequest.fulfillmentGroupId) &&
        Objects.equals(this.invoiceLineChargeDetailRequestList, invoiceLineRequest.invoiceLineChargeDetailRequestList) &&
        Objects.equals(this.invoiceLineSubTotal, invoiceLineRequest.invoiceLineSubTotal) &&
        Objects.equals(this.invoiceLineTaxDetailRequestList, invoiceLineRequest.invoiceLineTaxDetailRequestList) &&
        Objects.equals(this.isRefundGiftCard, invoiceLineRequest.isRefundGiftCard) &&
        Objects.equals(this.orderLineCreatedTimestamp, invoiceLineRequest.orderLineCreatedTimestamp) &&
        Objects.equals(this.orderLineId, invoiceLineRequest.orderLineId) &&
        Objects.equals(this.parentLineCreatedTimestamp, invoiceLineRequest.parentLineCreatedTimestamp) &&
        Objects.equals(this.productClass, invoiceLineRequest.productClass) &&
        Objects.equals(this.quantity, invoiceLineRequest.quantity) &&
        Objects.equals(this.taxShipFromLocationId, invoiceLineRequest.taxShipFromLocationId) &&
        Objects.equals(this.totalCharges, invoiceLineRequest.totalCharges) &&
        Objects.equals(this.totalDiscounts, invoiceLineRequest.totalDiscounts) &&
        Objects.equals(this.unitPrice, invoiceLineRequest.unitPrice);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fulfillmentGroupId, invoiceLineChargeDetailRequestList, invoiceLineSubTotal, invoiceLineTaxDetailRequestList, isRefundGiftCard, orderLineCreatedTimestamp, orderLineId, parentLineCreatedTimestamp, productClass, quantity, taxShipFromLocationId, totalCharges, totalDiscounts, unitPrice);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvoiceLineRequest {\n");
    
    sb.append("    fulfillmentGroupId: ").append(toIndentedString(fulfillmentGroupId)).append("\n");
    sb.append("    invoiceLineChargeDetailRequestList: ").append(toIndentedString(invoiceLineChargeDetailRequestList)).append("\n");
    sb.append("    invoiceLineSubTotal: ").append(toIndentedString(invoiceLineSubTotal)).append("\n");
    sb.append("    invoiceLineTaxDetailRequestList: ").append(toIndentedString(invoiceLineTaxDetailRequestList)).append("\n");
    sb.append("    isRefundGiftCard: ").append(toIndentedString(isRefundGiftCard)).append("\n");
    sb.append("    orderLineCreatedTimestamp: ").append(toIndentedString(orderLineCreatedTimestamp)).append("\n");
    sb.append("    orderLineId: ").append(toIndentedString(orderLineId)).append("\n");
    sb.append("    parentLineCreatedTimestamp: ").append(toIndentedString(parentLineCreatedTimestamp)).append("\n");
    sb.append("    productClass: ").append(toIndentedString(productClass)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    taxShipFromLocationId: ").append(toIndentedString(taxShipFromLocationId)).append("\n");
    sb.append("    totalCharges: ").append(toIndentedString(totalCharges)).append("\n");
    sb.append("    totalDiscounts: ").append(toIndentedString(totalDiscounts)).append("\n");
    sb.append("    unitPrice: ").append(toIndentedString(unitPrice)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

