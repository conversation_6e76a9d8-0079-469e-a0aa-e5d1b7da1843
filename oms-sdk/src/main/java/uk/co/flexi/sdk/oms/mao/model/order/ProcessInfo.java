/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ProcessInfo
 */
public class ProcessInfo {
  public static final String SERIALIZED_NAME_ACCOUNT_RECEIVABLE_ACCOUNT_NUMBER = "AccountReceivableAccountNumber";
  @SerializedName(SERIALIZED_NAME_ACCOUNT_RECEIVABLE_ACCOUNT_NUMBER)
  private String accountReceivableAccountNumber;

  public static final String SERIALIZED_NAME_ACCOUNT_RECEIVABLE_CODE = "AccountReceivableCode";
  @SerializedName(SERIALIZED_NAME_ACCOUNT_RECEIVABLE_CODE)
  private String accountReceivableCode;

  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ADVERTISING_CODE = "AdvertisingCode";
  @SerializedName(SERIALIZED_NAME_ADVERTISING_CODE)
  private String advertisingCode;

  public static final String SERIALIZED_NAME_ADVERTISING_DATE = "AdvertisingDate";
  @SerializedName(SERIALIZED_NAME_ADVERTISING_DATE)
  private String advertisingDate;

  public static final String SERIALIZED_NAME_BILL_OF_LADING_BREAK_ATTRIBUTE = "BillOfLadingBreakAttribute";
  @SerializedName(SERIALIZED_NAME_BILL_OF_LADING_BREAK_ATTRIBUTE)
  private String billOfLadingBreakAttribute;

  public static final String SERIALIZED_NAME_CASH_ON_DELIVERY_FUND = "CashOnDeliveryFund";
  @SerializedName(SERIALIZED_NAME_CASH_ON_DELIVERY_FUND)
  private Boolean cashOnDeliveryFund;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_CUSTOMER_BROKER_ACCOUNT_NUMBER = "CustomerBrokerAccountNumber";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_BROKER_ACCOUNT_NUMBER)
  private String customerBrokerAccountNumber;

  public static final String SERIALIZED_NAME_CUSTOMER_DEPARTMENT = "CustomerDepartment";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_DEPARTMENT)
  private String customerDepartment;

  public static final String SERIALIZED_NAME_DECLARED_VALUE = "DeclaredValue";
  @SerializedName(SERIALIZED_NAME_DECLARED_VALUE)
  private Double declaredValue;

  public static final String SERIALIZED_NAME_DECLARED_VALUE_CURRENCY_CODE = "DeclaredValueCurrencyCode";
  @SerializedName(SERIALIZED_NAME_DECLARED_VALUE_CURRENCY_CODE)
  private String declaredValueCurrencyCode;

  public static final String SERIALIZED_NAME_DESIGNATED_EQUIPMENT_ID = "DesignatedEquipmentId";
  @SerializedName(SERIALIZED_NAME_DESIGNATED_EQUIPMENT_ID)
  private Double designatedEquipmentId;

  public static final String SERIALIZED_NAME_DESIGNATED_SHIP_VIA = "DesignatedShipVia";
  @SerializedName(SERIALIZED_NAME_DESIGNATED_SHIP_VIA)
  private String designatedShipVia;

  public static final String SERIALIZED_NAME_DESTINATION_SHIP_THROUGH_LOCATION = "DestinationShipThroughLocation";
  @SerializedName(SERIALIZED_NAME_DESTINATION_SHIP_THROUGH_LOCATION)
  private String destinationShipThroughLocation;

  public static final String SERIALIZED_NAME_DSG_STATIC_ROUTE_ID = "DsgStaticRouteId";
  @SerializedName(SERIALIZED_NAME_DSG_STATIC_ROUTE_ID)
  private String dsgStaticRouteId;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FT_S_R_NUMBER = "FTSRNumber";
  @SerializedName(SERIALIZED_NAME_FT_S_R_NUMBER)
  private String ftSRNumber;

  public static final String SERIALIZED_NAME_FEDEX_DUTY_TAX_ACCOUNT_I_D = "FedexDutyTaxAccountID";
  @SerializedName(SERIALIZED_NAME_FEDEX_DUTY_TAX_ACCOUNT_I_D)
  private String fedexDutyTaxAccountID;

  public static final String SERIALIZED_NAME_FEDEX_DUTY_TAX_PAYMENT_TYPE = "FedexDutyTaxPaymentType";
  @SerializedName(SERIALIZED_NAME_FEDEX_DUTY_TAX_PAYMENT_TYPE)
  private Double fedexDutyTaxPaymentType;

  public static final String SERIALIZED_NAME_FREIGHT_FORWARD_ACCOUNT_NUMBER = "FreightForwardAccountNumber";
  @SerializedName(SERIALIZED_NAME_FREIGHT_FORWARD_ACCOUNT_NUMBER)
  private String freightForwardAccountNumber;

  public static final String SERIALIZED_NAME_GLOBAL_LOCATION_NUMBER = "GlobalLocationNumber";
  @SerializedName(SERIALIZED_NAME_GLOBAL_LOCATION_NUMBER)
  private String globalLocationNumber;

  public static final String SERIALIZED_NAME_IMPORTER_DEFINITION = "ImporterDefinition";
  @SerializedName(SERIALIZED_NAME_IMPORTER_DEFINITION)
  private String importerDefinition;

  public static final String SERIALIZED_NAME_INTERNAL_GOODS_DESCRIPTION = "InternalGoodsDescription";
  @SerializedName(SERIALIZED_NAME_INTERNAL_GOODS_DESCRIPTION)
  private String internalGoodsDescription;

  public static final String SERIALIZED_NAME_INTERNATIONAL_GOODS_DESCRIPTION = "InternationalGoodsDescription";
  @SerializedName(SERIALIZED_NAME_INTERNATIONAL_GOODS_DESCRIPTION)
  private String internationalGoodsDescription;

  public static final String SERIALIZED_NAME_IS_AUTO_CONSOLIDATION_BLOCKED = "IsAutoConsolidationBlocked";
  @SerializedName(SERIALIZED_NAME_IS_AUTO_CONSOLIDATION_BLOCKED)
  private Boolean isAutoConsolidationBlocked;

  public static final String SERIALIZED_NAME_IS_AUTO_CREATE_BLOCKED = "IsAutoCreateBlocked";
  @SerializedName(SERIALIZED_NAME_IS_AUTO_CREATE_BLOCKED)
  private Boolean isAutoCreateBlocked;

  public static final String SERIALIZED_NAME_IS_BACK_ORDERED = "IsBackOrdered";
  @SerializedName(SERIALIZED_NAME_IS_BACK_ORDERED)
  private Boolean isBackOrdered;

  public static final String SERIALIZED_NAME_IS_CARTON_MIN_WEIGHT = "IsCartonMinWeight";
  @SerializedName(SERIALIZED_NAME_IS_CARTON_MIN_WEIGHT)
  private Boolean isCartonMinWeight;

  public static final String SERIALIZED_NAME_IS_SHIPMENT_DOCUMENTS_ONLY = "IsShipmentDocumentsOnly";
  @SerializedName(SERIALIZED_NAME_IS_SHIPMENT_DOCUMENTS_ONLY)
  private Boolean isShipmentDocumentsOnly;

  public static final String SERIALIZED_NAME_IS_WAREHOUSE_TRANSFER = "IsWarehouseTransfer";
  @SerializedName(SERIALIZED_NAME_IS_WAREHOUSE_TRANSFER)
  private Boolean isWarehouseTransfer;

  public static final String SERIALIZED_NAME_LP_N_CUBING_INDICATOR = "LPNCubingIndicator";
  @SerializedName(SERIALIZED_NAME_LP_N_CUBING_INDICATOR)
  private String lpNCubingIndicator;

  public static final String SERIALIZED_NAME_LANGUAGE = "Language";
  @SerializedName(SERIALIZED_NAME_LANGUAGE)
  private String language;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_MOVE_TYPE = "MoveType";
  @SerializedName(SERIALIZED_NAME_MOVE_TYPE)
  private String moveType;

  public static final String SERIALIZED_NAME_MOVEMENT_OPTION = "MovementOption";
  @SerializedName(SERIALIZED_NAME_MOVEMENT_OPTION)
  private String movementOption;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PALLET_CUBING_INDICATOR = "PalletCubingIndicator";
  @SerializedName(SERIALIZED_NAME_PALLET_CUBING_INDICATOR)
  private Double palletCubingIndicator;

  public static final String SERIALIZED_NAME_PLANNING_DESTINATION_FACILITY_ID = "PlanningDestinationFacilityId";
  @SerializedName(SERIALIZED_NAME_PLANNING_DESTINATION_FACILITY_ID)
  private String planningDestinationFacilityId;

  public static final String SERIALIZED_NAME_PLANNING_ORIGIN_FACILITY_ID = "PlanningOriginFacilityId";
  @SerializedName(SERIALIZED_NAME_PLANNING_ORIGIN_FACILITY_ID)
  private String planningOriginFacilityId;

  public static final String SERIALIZED_NAME_PRIMARY_MAXICODE_ADDRESS_NUMBER = "PrimaryMaxicodeAddressNumber";
  @SerializedName(SERIALIZED_NAME_PRIMARY_MAXICODE_ADDRESS_NUMBER)
  private String primaryMaxicodeAddressNumber;

  public static final String SERIALIZED_NAME_PRIORITY = "Priority";
  @SerializedName(SERIALIZED_NAME_PRIORITY)
  private String priority;

  public static final String SERIALIZED_NAME_ROUTE_TO = "RouteTo";
  @SerializedName(SERIALIZED_NAME_ROUTE_TO)
  private String routeTo;

  public static final String SERIALIZED_NAME_ROUTE_TYPE1 = "RouteType1";
  @SerializedName(SERIALIZED_NAME_ROUTE_TYPE1)
  private String routeType1;

  public static final String SERIALIZED_NAME_ROUTE_TYPE2 = "RouteType2";
  @SerializedName(SERIALIZED_NAME_ROUTE_TYPE2)
  private String routeType2;

  public static final String SERIALIZED_NAME_ROUTE_WAVE_NUMBER = "RouteWaveNumber";
  @SerializedName(SERIALIZED_NAME_ROUTE_WAVE_NUMBER)
  private String routeWaveNumber;

  public static final String SERIALIZED_NAME_ROUTING_ATTRIBUTES = "RoutingAttributes";
  @SerializedName(SERIALIZED_NAME_ROUTING_ATTRIBUTES)
  private String routingAttributes;

  public static final String SERIALIZED_NAME_SCHEDULE_DELIVERY_DATE = "ScheduleDeliveryDate";
  @SerializedName(SERIALIZED_NAME_SCHEDULE_DELIVERY_DATE)
  private OffsetDateTime scheduleDeliveryDate;

  public static final String SERIALIZED_NAME_SECONDRY_MAXICODE_ADDRESS_NUMBER = "SecondryMaxicodeAddressNumber";
  @SerializedName(SERIALIZED_NAME_SECONDRY_MAXICODE_ADDRESS_NUMBER)
  private String secondryMaxicodeAddressNumber;

  public static final String SERIALIZED_NAME_SHIP_LOCATION_CONTROL = "ShipLocationControl";
  @SerializedName(SERIALIZED_NAME_SHIP_LOCATION_CONTROL)
  private String shipLocationControl;

  public static final String SERIALIZED_NAME_SHIPMENT_PLANNING_SCHEDULE_DAY = "ShipmentPlanningScheduleDay";
  @SerializedName(SERIALIZED_NAME_SHIPMENT_PLANNING_SCHEDULE_DAY)
  private String shipmentPlanningScheduleDay;

  public static final String SERIALIZED_NAME_SHIPMMENT_PLANNING_SCHEDULE_DAY = "ShipmmentPlanningScheduleDay";
  @SerializedName(SERIALIZED_NAME_SHIPMMENT_PLANNING_SCHEDULE_DAY)
  private String shipmmentPlanningScheduleDay;

  public static final String SERIALIZED_NAME_TRANSLATIONS = "Translations";
  @SerializedName(SERIALIZED_NAME_TRANSLATIONS)
  private Map<String, Map<String, String>> translations = null;

  public static final String SERIALIZED_NAME_TRANSPORTATION_WAVE_OPTION_ID = "TransportationWaveOptionId";
  @SerializedName(SERIALIZED_NAME_TRANSPORTATION_WAVE_OPTION_ID)
  private String transportationWaveOptionId;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_WAVE_ID = "WaveId";
  @SerializedName(SERIALIZED_NAME_WAVE_ID)
  private Double waveId;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public ProcessInfo accountReceivableAccountNumber(String accountReceivableAccountNumber) {
    this.accountReceivableAccountNumber = accountReceivableAccountNumber;
    return this;
  }

   /**
   * Account receivable account number (informational)
   * @return accountReceivableAccountNumber
  **/
  
  public String getAccountReceivableAccountNumber() {
    return accountReceivableAccountNumber;
  }

  public void setAccountReceivableAccountNumber(String accountReceivableAccountNumber) {
    this.accountReceivableAccountNumber = accountReceivableAccountNumber;
  }

  public ProcessInfo accountReceivableCode(String accountReceivableCode) {
    this.accountReceivableCode = accountReceivableCode;
    return this;
  }

   /**
   * Account receivable code (informational)
   * @return accountReceivableCode
  **/
  
  public String getAccountReceivableCode() {
    return accountReceivableCode;
  }

  public void setAccountReceivableCode(String accountReceivableCode) {
    this.accountReceivableCode = accountReceivableCode;
  }

  public ProcessInfo actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ProcessInfo putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ProcessInfo advertisingCode(String advertisingCode) {
    this.advertisingCode = advertisingCode;
    return this;
  }

   /**
   * Advertising Code
   * @return advertisingCode
  **/
  
  public String getAdvertisingCode() {
    return advertisingCode;
  }

  public void setAdvertisingCode(String advertisingCode) {
    this.advertisingCode = advertisingCode;
  }

  public ProcessInfo advertisingDate(String advertisingDate) {
    this.advertisingDate = advertisingDate;
    return this;
  }

   /**
   * Advertising Date
   * @return advertisingDate
  **/
  
  public String getAdvertisingDate() {
    return advertisingDate;
  }

  public void setAdvertisingDate(String advertisingDate) {
    this.advertisingDate = advertisingDate;
  }

  public ProcessInfo billOfLadingBreakAttribute(String billOfLadingBreakAttribute) {
    this.billOfLadingBreakAttribute = billOfLadingBreakAttribute;
    return this;
  }

   /**
   * in WM a new BOL number is generated every time a shipment is added to a load and the WHSE CO DIV SOLDTO SHIPTO SHIPFOR FREIGHT_TERM and BOL_BREAK_ATTR is different from any other shipment already on the load.
   * @return billOfLadingBreakAttribute
  **/
  
  public String getBillOfLadingBreakAttribute() {
    return billOfLadingBreakAttribute;
  }

  public void setBillOfLadingBreakAttribute(String billOfLadingBreakAttribute) {
    this.billOfLadingBreakAttribute = billOfLadingBreakAttribute;
  }

  public ProcessInfo cashOnDeliveryFund(Boolean cashOnDeliveryFund) {
    this.cashOnDeliveryFund = cashOnDeliveryFund;
    return this;
  }

   /**
   * Used in Parcel Carrier shipping; Setting used to identify the suitable method of payment of COD charges.
   * @return cashOnDeliveryFund
  **/
  
  public Boolean getCashOnDeliveryFund() {
    return cashOnDeliveryFund;
  }

  public void setCashOnDeliveryFund(Boolean cashOnDeliveryFund) {
    this.cashOnDeliveryFund = cashOnDeliveryFund;
  }

  public ProcessInfo createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ProcessInfo createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ProcessInfo customerBrokerAccountNumber(String customerBrokerAccountNumber) {
    this.customerBrokerAccountNumber = customerBrokerAccountNumber;
    return this;
  }

   /**
   * Customs Broker Account Number
   * @return customerBrokerAccountNumber
  **/
  
  public String getCustomerBrokerAccountNumber() {
    return customerBrokerAccountNumber;
  }

  public void setCustomerBrokerAccountNumber(String customerBrokerAccountNumber) {
    this.customerBrokerAccountNumber = customerBrokerAccountNumber;
  }

  public ProcessInfo customerDepartment(String customerDepartment) {
    this.customerDepartment = customerDepartment;
    return this;
  }

   /**
   * Customer department (informational)
   * @return customerDepartment
  **/
  
  public String getCustomerDepartment() {
    return customerDepartment;
  }

  public void setCustomerDepartment(String customerDepartment) {
    this.customerDepartment = customerDepartment;
  }

  public ProcessInfo declaredValue(Double declaredValue) {
    this.declaredValue = declaredValue;
    return this;
  }

   /**
   * The Declared Value of goods on the order
   * minimum: 0
   * maximum: ************.9999
   * @return declaredValue
  **/
  
  public Double getDeclaredValue() {
    return declaredValue;
  }

  public void setDeclaredValue(Double declaredValue) {
    this.declaredValue = declaredValue;
  }

  public ProcessInfo declaredValueCurrencyCode(String declaredValueCurrencyCode) {
    this.declaredValueCurrencyCode = declaredValueCurrencyCode;
    return this;
  }

   /**
   * Currency Code of the declared value of the goods
   * @return declaredValueCurrencyCode
  **/
  
  public String getDeclaredValueCurrencyCode() {
    return declaredValueCurrencyCode;
  }

  public void setDeclaredValueCurrencyCode(String declaredValueCurrencyCode) {
    this.declaredValueCurrencyCode = declaredValueCurrencyCode;
  }

  public ProcessInfo designatedEquipmentId(Double designatedEquipmentId) {
    this.designatedEquipmentId = designatedEquipmentId;
    return this;
  }

   /**
   * Designated equipment for order. Defines a specific type of equipment to use for shipping this order. Planning components should respect this designation.
   * minimum: 0
   * maximum: ************.9999
   * @return designatedEquipmentId
  **/
  
  public Double getDesignatedEquipmentId() {
    return designatedEquipmentId;
  }

  public void setDesignatedEquipmentId(Double designatedEquipmentId) {
    this.designatedEquipmentId = designatedEquipmentId;
  }

  public ProcessInfo designatedShipVia(String designatedShipVia) {
    this.designatedShipVia = designatedShipVia;
    return this;
  }

   /**
   * Shipping method as returned by the S and H engine
   * @return designatedShipVia
  **/
  
  public String getDesignatedShipVia() {
    return designatedShipVia;
  }

  public void setDesignatedShipVia(String designatedShipVia) {
    this.designatedShipVia = designatedShipVia;
  }

  public ProcessInfo destinationShipThroughLocation(String destinationShipThroughLocation) {
    this.destinationShipThroughLocation = destinationShipThroughLocation;
    return this;
  }

   /**
   * Destination airport for the shipment
   * @return destinationShipThroughLocation
  **/
  
  public String getDestinationShipThroughLocation() {
    return destinationShipThroughLocation;
  }

  public void setDestinationShipThroughLocation(String destinationShipThroughLocation) {
    this.destinationShipThroughLocation = destinationShipThroughLocation;
  }

  public ProcessInfo dsgStaticRouteId(String dsgStaticRouteId) {
    this.dsgStaticRouteId = dsgStaticRouteId;
    return this;
  }

   /**
   * The route Id associated with the order
   * @return dsgStaticRouteId
  **/
  
  public String getDsgStaticRouteId() {
    return dsgStaticRouteId;
  }

  public void setDsgStaticRouteId(String dsgStaticRouteId) {
    this.dsgStaticRouteId = dsgStaticRouteId;
  }

  public ProcessInfo extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ProcessInfo ftSRNumber(String ftSRNumber) {
    this.ftSRNumber = ftSRNumber;
    return this;
  }

   /**
   * FTSR Number; Code used for international shipping
   * @return ftSRNumber
  **/
  
  public String getFtSRNumber() {
    return ftSRNumber;
  }

  public void setFtSRNumber(String ftSRNumber) {
    this.ftSRNumber = ftSRNumber;
  }

  public ProcessInfo fedexDutyTaxAccountID(String fedexDutyTaxAccountID) {
    this.fedexDutyTaxAccountID = fedexDutyTaxAccountID;
    return this;
  }

   /**
   * Field for the FedEx account number to be billed for the duty and tax for international shipments
   * @return fedexDutyTaxAccountID
  **/
  
  public String getFedexDutyTaxAccountID() {
    return fedexDutyTaxAccountID;
  }

  public void setFedexDutyTaxAccountID(String fedexDutyTaxAccountID) {
    this.fedexDutyTaxAccountID = fedexDutyTaxAccountID;
  }

  public ProcessInfo fedexDutyTaxPaymentType(Double fedexDutyTaxPaymentType) {
    this.fedexDutyTaxPaymentType = fedexDutyTaxPaymentType;
    return this;
  }

   /**
   * Field to indicate the duty and tax payment type for FedEx international shipments
   * minimum: 0
   * maximum: ************.9999
   * @return fedexDutyTaxPaymentType
  **/
  
  public Double getFedexDutyTaxPaymentType() {
    return fedexDutyTaxPaymentType;
  }

  public void setFedexDutyTaxPaymentType(Double fedexDutyTaxPaymentType) {
    this.fedexDutyTaxPaymentType = fedexDutyTaxPaymentType;
  }

  public ProcessInfo freightForwardAccountNumber(String freightForwardAccountNumber) {
    this.freightForwardAccountNumber = freightForwardAccountNumber;
    return this;
  }

   /**
   * Description is blank
   * @return freightForwardAccountNumber
  **/
  
  public String getFreightForwardAccountNumber() {
    return freightForwardAccountNumber;
  }

  public void setFreightForwardAccountNumber(String freightForwardAccountNumber) {
    this.freightForwardAccountNumber = freightForwardAccountNumber;
  }

  public ProcessInfo globalLocationNumber(String globalLocationNumber) {
    this.globalLocationNumber = globalLocationNumber;
    return this;
  }

   /**
   * Global Location Number is a global unique identifier of a shipping facility used for EAN compliance.
   * @return globalLocationNumber
  **/
  
  public String getGlobalLocationNumber() {
    return globalLocationNumber;
  }

  public void setGlobalLocationNumber(String globalLocationNumber) {
    this.globalLocationNumber = globalLocationNumber;
  }

  public ProcessInfo importerDefinition(String importerDefinition) {
    this.importerDefinition = importerDefinition;
    return this;
  }

   /**
   * Importer Defintion (S 605)
   * @return importerDefinition
  **/
  
  public String getImporterDefinition() {
    return importerDefinition;
  }

  public void setImporterDefinition(String importerDefinition) {
    this.importerDefinition = importerDefinition;
  }

  public ProcessInfo internalGoodsDescription(String internalGoodsDescription) {
    this.internalGoodsDescription = internalGoodsDescription;
    return this;
  }

   /**
   * Internal Goods Descriptor
   * @return internalGoodsDescription
  **/
  
  public String getInternalGoodsDescription() {
    return internalGoodsDescription;
  }

  public void setInternalGoodsDescription(String internalGoodsDescription) {
    this.internalGoodsDescription = internalGoodsDescription;
  }

  public ProcessInfo internationalGoodsDescription(String internationalGoodsDescription) {
    this.internationalGoodsDescription = internationalGoodsDescription;
    return this;
  }

   /**
   * International Goods Description
   * @return internationalGoodsDescription
  **/
  
  public String getInternationalGoodsDescription() {
    return internationalGoodsDescription;
  }

  public void setInternationalGoodsDescription(String internationalGoodsDescription) {
    this.internationalGoodsDescription = internationalGoodsDescription;
  }

  public ProcessInfo isAutoConsolidationBlocked(Boolean isAutoConsolidationBlocked) {
    this.isAutoConsolidationBlocked = isAutoConsolidationBlocked;
    return this;
  }

   /**
   * Block Auto Consolidate - Flag indicating the order should not be automatically consolidated on shipments
   * @return isAutoConsolidationBlocked
  **/
  
  public Boolean getIsAutoConsolidationBlocked() {
    return isAutoConsolidationBlocked;
  }

  public void setIsAutoConsolidationBlocked(Boolean isAutoConsolidationBlocked) {
    this.isAutoConsolidationBlocked = isAutoConsolidationBlocked;
  }

  public ProcessInfo isAutoCreateBlocked(Boolean isAutoCreateBlocked) {
    this.isAutoCreateBlocked = isAutoCreateBlocked;
    return this;
  }

   /**
   * Block Auto Create - Flag indicating the order should not be automatically created on shipments
   * @return isAutoCreateBlocked
  **/
  
  public Boolean getIsAutoCreateBlocked() {
    return isAutoCreateBlocked;
  }

  public void setIsAutoCreateBlocked(Boolean isAutoCreateBlocked) {
    this.isAutoCreateBlocked = isAutoCreateBlocked;
  }

  public ProcessInfo isBackOrdered(Boolean isBackOrdered) {
    this.isBackOrdered = isBackOrdered;
    return this;
  }

   /**
   * Back ordered flag; Used for info./selection/display
   * @return isBackOrdered
  **/
  
  public Boolean getIsBackOrdered() {
    return isBackOrdered;
  }

  public void setIsBackOrdered(Boolean isBackOrdered) {
    this.isBackOrdered = isBackOrdered;
  }

  public ProcessInfo isCartonMinWeight(Boolean isCartonMinWeight) {
    this.isCartonMinWeight = isCartonMinWeight;
    return this;
  }

   /**
   * Defines, if remainder weight in carton is less than PARTL_CARTON_MIN_WT.
   * @return isCartonMinWeight
  **/
  
  public Boolean getIsCartonMinWeight() {
    return isCartonMinWeight;
  }

  public void setIsCartonMinWeight(Boolean isCartonMinWeight) {
    this.isCartonMinWeight = isCartonMinWeight;
  }

  public ProcessInfo isShipmentDocumentsOnly(Boolean isShipmentDocumentsOnly) {
    this.isShipmentDocumentsOnly = isShipmentDocumentsOnly;
    return this;
  }

   /**
   * Flag that specifies whether the shipment is documents only and requires no dimensions for parcel rating. 
   * @return isShipmentDocumentsOnly
  **/
  
  public Boolean getIsShipmentDocumentsOnly() {
    return isShipmentDocumentsOnly;
  }

  public void setIsShipmentDocumentsOnly(Boolean isShipmentDocumentsOnly) {
    this.isShipmentDocumentsOnly = isShipmentDocumentsOnly;
  }

  public ProcessInfo isWarehouseTransfer(Boolean isWarehouseTransfer) {
    this.isWarehouseTransfer = isWarehouseTransfer;
    return this;
  }

   /**
   * Indicates if the order is a warehouse transfer
   * @return isWarehouseTransfer
  **/
  
  public Boolean getIsWarehouseTransfer() {
    return isWarehouseTransfer;
  }

  public void setIsWarehouseTransfer(Boolean isWarehouseTransfer) {
    this.isWarehouseTransfer = isWarehouseTransfer;
  }

  public ProcessInfo lpNCubingIndicator(String lpNCubingIndicator) {
    this.lpNCubingIndicator = lpNCubingIndicator;
    return this;
  }

   /**
   * Defines the cubing algorithm used
   * @return lpNCubingIndicator
  **/
  
  public String getLpNCubingIndicator() {
    return lpNCubingIndicator;
  }

  public void setLpNCubingIndicator(String lpNCubingIndicator) {
    this.lpNCubingIndicator = lpNCubingIndicator;
  }

  public ProcessInfo language(String language) {
    this.language = language;
    return this;
  }

   /**
   * Default language used by the carrier
   * @return language
  **/
  
  public String getLanguage() {
    return language;
  }

  public void setLanguage(String language) {
    this.language = language;
  }

  public ProcessInfo messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ProcessInfo moveType(String moveType) {
    this.moveType = moveType;
    return this;
  }

   /**
   * Move Type of the Order. For example, Reverse Logistics Shipment, Backhaul Shipment, Shipment, Common Carrier Shipment
   * @return moveType
  **/
  
  public String getMoveType() {
    return moveType;
  }

  public void setMoveType(String moveType) {
    this.moveType = moveType;
  }

  public ProcessInfo movementOption(String movementOption) {
    this.movementOption = movementOption;
    return this;
  }

   /**
   * The Movememnt Option which is used for shipment planning
   * @return movementOption
  **/
  
  public String getMovementOption() {
    return movementOption;
  }

  public void setMovementOption(String movementOption) {
    this.movementOption = movementOption;
  }

  public ProcessInfo orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public ProcessInfo PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ProcessInfo palletCubingIndicator(Double palletCubingIndicator) {
    this.palletCubingIndicator = palletCubingIndicator;
    return this;
  }

   /**
   * Pallet Cubing Indicator (S-265)
   * minimum: 0
   * maximum: ************.9999
   * @return palletCubingIndicator
  **/
  
  public Double getPalletCubingIndicator() {
    return palletCubingIndicator;
  }

  public void setPalletCubingIndicator(Double palletCubingIndicator) {
    this.palletCubingIndicator = palletCubingIndicator;
  }

  public ProcessInfo planningDestinationFacilityId(String planningDestinationFacilityId) {
    this.planningDestinationFacilityId = planningDestinationFacilityId;
    return this;
  }

   /**
   * Planning Destination Facility Id
   * @return planningDestinationFacilityId
  **/
  
  public String getPlanningDestinationFacilityId() {
    return planningDestinationFacilityId;
  }

  public void setPlanningDestinationFacilityId(String planningDestinationFacilityId) {
    this.planningDestinationFacilityId = planningDestinationFacilityId;
  }

  public ProcessInfo planningOriginFacilityId(String planningOriginFacilityId) {
    this.planningOriginFacilityId = planningOriginFacilityId;
    return this;
  }

   /**
   * Planning Origin Facility Id
   * @return planningOriginFacilityId
  **/
  
  public String getPlanningOriginFacilityId() {
    return planningOriginFacilityId;
  }

  public void setPlanningOriginFacilityId(String planningOriginFacilityId) {
    this.planningOriginFacilityId = planningOriginFacilityId;
  }

  public ProcessInfo primaryMaxicodeAddressNumber(String primaryMaxicodeAddressNumber) {
    this.primaryMaxicodeAddressNumber = primaryMaxicodeAddressNumber;
    return this;
  }

   /**
   * The primary address number for use in the Maxicode printed on UPS labels
   * @return primaryMaxicodeAddressNumber
  **/
  
  public String getPrimaryMaxicodeAddressNumber() {
    return primaryMaxicodeAddressNumber;
  }

  public void setPrimaryMaxicodeAddressNumber(String primaryMaxicodeAddressNumber) {
    this.primaryMaxicodeAddressNumber = primaryMaxicodeAddressNumber;
  }

  public ProcessInfo priority(String priority) {
    this.priority = priority;
    return this;
  }

   /**
   * Priority of the order
   * @return priority
  **/
  
  public String getPriority() {
    return priority;
  }

  public void setPriority(String priority) {
    this.priority = priority;
  }

  public ProcessInfo routeTo(String routeTo) {
    this.routeTo = routeTo;
    return this;
  }

   /**
   * Parameters for Static Routing Guide - passed from the host
   * @return routeTo
  **/
  
  public String getRouteTo() {
    return routeTo;
  }

  public void setRouteTo(String routeTo) {
    this.routeTo = routeTo;
  }

  public ProcessInfo routeType1(String routeType1) {
    this.routeType1 = routeType1;
    return this;
  }

   /**
   * Parameters for Static Routing Guide - passed from the host
   * @return routeType1
  **/
  
  public String getRouteType1() {
    return routeType1;
  }

  public void setRouteType1(String routeType1) {
    this.routeType1 = routeType1;
  }

  public ProcessInfo routeType2(String routeType2) {
    this.routeType2 = routeType2;
    return this;
  }

   /**
   * Parameters for Static Routing Guide - passed from the host
   * @return routeType2
  **/
  
  public String getRouteType2() {
    return routeType2;
  }

  public void setRouteType2(String routeType2) {
    this.routeType2 = routeType2;
  }

  public ProcessInfo routeWaveNumber(String routeWaveNumber) {
    this.routeWaveNumber = routeWaveNumber;
    return this;
  }

   /**
   * Update by Warehouse Management, assigned during routing for all picktickets grouped together in routing using the routing attribute
   * @return routeWaveNumber
  **/
  
  public String getRouteWaveNumber() {
    return routeWaveNumber;
  }

  public void setRouteWaveNumber(String routeWaveNumber) {
    this.routeWaveNumber = routeWaveNumber;
  }

  public ProcessInfo routingAttributes(String routingAttributes) {
    this.routingAttributes = routingAttributes;
    return this;
  }

   /**
   * Parameters for Static Routing Guide - passed from the host
   * @return routingAttributes
  **/
  
  public String getRoutingAttributes() {
    return routingAttributes;
  }

  public void setRoutingAttributes(String routingAttributes) {
    this.routingAttributes = routingAttributes;
  }

  public ProcessInfo scheduleDeliveryDate(OffsetDateTime scheduleDeliveryDate) {
    this.scheduleDeliveryDate = scheduleDeliveryDate;
    return this;
  }

   /**
   * The specific date/time the order should be delivered to the customer.
   * @return scheduleDeliveryDate
  **/
  
  public OffsetDateTime getScheduleDeliveryDate() {
    return scheduleDeliveryDate;
  }

  public void setScheduleDeliveryDate(OffsetDateTime scheduleDeliveryDate) {
    this.scheduleDeliveryDate = scheduleDeliveryDate;
  }

  public ProcessInfo secondryMaxicodeAddressNumber(String secondryMaxicodeAddressNumber) {
    this.secondryMaxicodeAddressNumber = secondryMaxicodeAddressNumber;
    return this;
  }

   /**
   * The secondary address number for use in the Maxicode printed on UPS labels
   * @return secondryMaxicodeAddressNumber
  **/
  
  public String getSecondryMaxicodeAddressNumber() {
    return secondryMaxicodeAddressNumber;
  }

  public void setSecondryMaxicodeAddressNumber(String secondryMaxicodeAddressNumber) {
    this.secondryMaxicodeAddressNumber = secondryMaxicodeAddressNumber;
  }

  public ProcessInfo shipLocationControl(String shipLocationControl) {
    this.shipLocationControl = shipLocationControl;
    return this;
  }

   /**
   * Warehouse Control number.
   * @return shipLocationControl
  **/
  
  public String getShipLocationControl() {
    return shipLocationControl;
  }

  public void setShipLocationControl(String shipLocationControl) {
    this.shipLocationControl = shipLocationControl;
  }

  public ProcessInfo shipmentPlanningScheduleDay(String shipmentPlanningScheduleDay) {
    this.shipmentPlanningScheduleDay = shipmentPlanningScheduleDay;
    return this;
  }

   /**
   * Scheduled day of the week of the shipment to be planned - Used in shipment planning
   * @return shipmentPlanningScheduleDay
  **/
  
  public String getShipmentPlanningScheduleDay() {
    return shipmentPlanningScheduleDay;
  }

  public void setShipmentPlanningScheduleDay(String shipmentPlanningScheduleDay) {
    this.shipmentPlanningScheduleDay = shipmentPlanningScheduleDay;
  }

  public ProcessInfo shipmmentPlanningScheduleDay(String shipmmentPlanningScheduleDay) {
    this.shipmmentPlanningScheduleDay = shipmmentPlanningScheduleDay;
    return this;
  }

   /**
   * Scheduled day of the week of the shipment to be planned - Used in shipment planning
   * @return shipmmentPlanningScheduleDay
  **/
  
  public String getShipmmentPlanningScheduleDay() {
    return shipmmentPlanningScheduleDay;
  }

  public void setShipmmentPlanningScheduleDay(String shipmmentPlanningScheduleDay) {
    this.shipmmentPlanningScheduleDay = shipmmentPlanningScheduleDay;
  }

  public ProcessInfo translations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
    return this;
  }

  public ProcessInfo putTranslationsItem(String key, Map<String, String> translationsItem) {
    if (this.translations == null) {
      this.translations = new HashMap<String, Map<String, String>>();
    }
    this.translations.put(key, translationsItem);
    return this;
  }

   /**
   * Get translations
   * @return translations
  **/
  
  public Map<String, Map<String, String>> getTranslations() {
    return translations;
  }

  public void setTranslations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
  }

  public ProcessInfo transportationWaveOptionId(String transportationWaveOptionId) {
    this.transportationWaveOptionId = transportationWaveOptionId;
    return this;
  }

   /**
   * The transportation wave required for the items
   * @return transportationWaveOptionId
  **/
  
  public String getTransportationWaveOptionId() {
    return transportationWaveOptionId;
  }

  public void setTransportationWaveOptionId(String transportationWaveOptionId) {
    this.transportationWaveOptionId = transportationWaveOptionId;
  }

  public ProcessInfo updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ProcessInfo updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ProcessInfo waveId(Double waveId) {
    this.waveId = waveId;
    return this;
  }

   /**
   * Wave Id - Used in Fleet
   * minimum: 0
   * maximum: ************.9999
   * @return waveId
  **/
  
  public Double getWaveId() {
    return waveId;
  }

  public void setWaveId(Double waveId) {
    this.waveId = waveId;
  }

  public ProcessInfo entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ProcessInfo processInfo = (ProcessInfo) o;
    return Objects.equals(this.accountReceivableAccountNumber, processInfo.accountReceivableAccountNumber) &&
        Objects.equals(this.accountReceivableCode, processInfo.accountReceivableCode) &&
        Objects.equals(this.actions, processInfo.actions) &&
        Objects.equals(this.advertisingCode, processInfo.advertisingCode) &&
        Objects.equals(this.advertisingDate, processInfo.advertisingDate) &&
        Objects.equals(this.billOfLadingBreakAttribute, processInfo.billOfLadingBreakAttribute) &&
        Objects.equals(this.cashOnDeliveryFund, processInfo.cashOnDeliveryFund) &&
        Objects.equals(this.createdBy, processInfo.createdBy) &&
        Objects.equals(this.createdTimestamp, processInfo.createdTimestamp) &&
        Objects.equals(this.customerBrokerAccountNumber, processInfo.customerBrokerAccountNumber) &&
        Objects.equals(this.customerDepartment, processInfo.customerDepartment) &&
        Objects.equals(this.declaredValue, processInfo.declaredValue) &&
        Objects.equals(this.declaredValueCurrencyCode, processInfo.declaredValueCurrencyCode) &&
        Objects.equals(this.designatedEquipmentId, processInfo.designatedEquipmentId) &&
        Objects.equals(this.designatedShipVia, processInfo.designatedShipVia) &&
        Objects.equals(this.destinationShipThroughLocation, processInfo.destinationShipThroughLocation) &&
        Objects.equals(this.dsgStaticRouteId, processInfo.dsgStaticRouteId) &&
        Objects.equals(this.extended, processInfo.extended) &&
        Objects.equals(this.ftSRNumber, processInfo.ftSRNumber) &&
        Objects.equals(this.fedexDutyTaxAccountID, processInfo.fedexDutyTaxAccountID) &&
        Objects.equals(this.fedexDutyTaxPaymentType, processInfo.fedexDutyTaxPaymentType) &&
        Objects.equals(this.freightForwardAccountNumber, processInfo.freightForwardAccountNumber) &&
        Objects.equals(this.globalLocationNumber, processInfo.globalLocationNumber) &&
        Objects.equals(this.importerDefinition, processInfo.importerDefinition) &&
        Objects.equals(this.internalGoodsDescription, processInfo.internalGoodsDescription) &&
        Objects.equals(this.internationalGoodsDescription, processInfo.internationalGoodsDescription) &&
        Objects.equals(this.isAutoConsolidationBlocked, processInfo.isAutoConsolidationBlocked) &&
        Objects.equals(this.isAutoCreateBlocked, processInfo.isAutoCreateBlocked) &&
        Objects.equals(this.isBackOrdered, processInfo.isBackOrdered) &&
        Objects.equals(this.isCartonMinWeight, processInfo.isCartonMinWeight) &&
        Objects.equals(this.isShipmentDocumentsOnly, processInfo.isShipmentDocumentsOnly) &&
        Objects.equals(this.isWarehouseTransfer, processInfo.isWarehouseTransfer) &&
        Objects.equals(this.lpNCubingIndicator, processInfo.lpNCubingIndicator) &&
        Objects.equals(this.language, processInfo.language) &&
        Objects.equals(this.messages, processInfo.messages) &&
        Objects.equals(this.moveType, processInfo.moveType) &&
        Objects.equals(this.movementOption, processInfo.movementOption) &&
        Objects.equals(this.orgId, processInfo.orgId) &&
        Objects.equals(this.PK, processInfo.PK) &&
        Objects.equals(this.palletCubingIndicator, processInfo.palletCubingIndicator) &&
        Objects.equals(this.planningDestinationFacilityId, processInfo.planningDestinationFacilityId) &&
        Objects.equals(this.planningOriginFacilityId, processInfo.planningOriginFacilityId) &&
        Objects.equals(this.primaryMaxicodeAddressNumber, processInfo.primaryMaxicodeAddressNumber) &&
        Objects.equals(this.priority, processInfo.priority) &&
        Objects.equals(this.routeTo, processInfo.routeTo) &&
        Objects.equals(this.routeType1, processInfo.routeType1) &&
        Objects.equals(this.routeType2, processInfo.routeType2) &&
        Objects.equals(this.routeWaveNumber, processInfo.routeWaveNumber) &&
        Objects.equals(this.routingAttributes, processInfo.routingAttributes) &&
        Objects.equals(this.scheduleDeliveryDate, processInfo.scheduleDeliveryDate) &&
        Objects.equals(this.secondryMaxicodeAddressNumber, processInfo.secondryMaxicodeAddressNumber) &&
        Objects.equals(this.shipLocationControl, processInfo.shipLocationControl) &&
        Objects.equals(this.shipmentPlanningScheduleDay, processInfo.shipmentPlanningScheduleDay) &&
        Objects.equals(this.shipmmentPlanningScheduleDay, processInfo.shipmmentPlanningScheduleDay) &&
        Objects.equals(this.translations, processInfo.translations) &&
        Objects.equals(this.transportationWaveOptionId, processInfo.transportationWaveOptionId) &&
        Objects.equals(this.updatedBy, processInfo.updatedBy) &&
        Objects.equals(this.updatedTimestamp, processInfo.updatedTimestamp) &&
        Objects.equals(this.waveId, processInfo.waveId) &&
        Objects.equals(this.entityName, processInfo.entityName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(accountReceivableAccountNumber, accountReceivableCode, actions, advertisingCode, advertisingDate, billOfLadingBreakAttribute, cashOnDeliveryFund, createdBy, createdTimestamp, customerBrokerAccountNumber, customerDepartment, declaredValue, declaredValueCurrencyCode, designatedEquipmentId, designatedShipVia, destinationShipThroughLocation, dsgStaticRouteId, extended, ftSRNumber, fedexDutyTaxAccountID, fedexDutyTaxPaymentType, freightForwardAccountNumber, globalLocationNumber, importerDefinition, internalGoodsDescription, internationalGoodsDescription, isAutoConsolidationBlocked, isAutoCreateBlocked, isBackOrdered, isCartonMinWeight, isShipmentDocumentsOnly, isWarehouseTransfer, lpNCubingIndicator, language, messages, moveType, movementOption, orgId, PK, palletCubingIndicator, planningDestinationFacilityId, planningOriginFacilityId, primaryMaxicodeAddressNumber, priority, routeTo, routeType1, routeType2, routeWaveNumber, routingAttributes, scheduleDeliveryDate, secondryMaxicodeAddressNumber, shipLocationControl, shipmentPlanningScheduleDay, shipmmentPlanningScheduleDay, translations, transportationWaveOptionId, updatedBy, updatedTimestamp, waveId, entityName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProcessInfo {\n");
    
    sb.append("    accountReceivableAccountNumber: ").append(toIndentedString(accountReceivableAccountNumber)).append("\n");
    sb.append("    accountReceivableCode: ").append(toIndentedString(accountReceivableCode)).append("\n");
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    advertisingCode: ").append(toIndentedString(advertisingCode)).append("\n");
    sb.append("    advertisingDate: ").append(toIndentedString(advertisingDate)).append("\n");
    sb.append("    billOfLadingBreakAttribute: ").append(toIndentedString(billOfLadingBreakAttribute)).append("\n");
    sb.append("    cashOnDeliveryFund: ").append(toIndentedString(cashOnDeliveryFund)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    customerBrokerAccountNumber: ").append(toIndentedString(customerBrokerAccountNumber)).append("\n");
    sb.append("    customerDepartment: ").append(toIndentedString(customerDepartment)).append("\n");
    sb.append("    declaredValue: ").append(toIndentedString(declaredValue)).append("\n");
    sb.append("    declaredValueCurrencyCode: ").append(toIndentedString(declaredValueCurrencyCode)).append("\n");
    sb.append("    designatedEquipmentId: ").append(toIndentedString(designatedEquipmentId)).append("\n");
    sb.append("    designatedShipVia: ").append(toIndentedString(designatedShipVia)).append("\n");
    sb.append("    destinationShipThroughLocation: ").append(toIndentedString(destinationShipThroughLocation)).append("\n");
    sb.append("    dsgStaticRouteId: ").append(toIndentedString(dsgStaticRouteId)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    ftSRNumber: ").append(toIndentedString(ftSRNumber)).append("\n");
    sb.append("    fedexDutyTaxAccountID: ").append(toIndentedString(fedexDutyTaxAccountID)).append("\n");
    sb.append("    fedexDutyTaxPaymentType: ").append(toIndentedString(fedexDutyTaxPaymentType)).append("\n");
    sb.append("    freightForwardAccountNumber: ").append(toIndentedString(freightForwardAccountNumber)).append("\n");
    sb.append("    globalLocationNumber: ").append(toIndentedString(globalLocationNumber)).append("\n");
    sb.append("    importerDefinition: ").append(toIndentedString(importerDefinition)).append("\n");
    sb.append("    internalGoodsDescription: ").append(toIndentedString(internalGoodsDescription)).append("\n");
    sb.append("    internationalGoodsDescription: ").append(toIndentedString(internationalGoodsDescription)).append("\n");
    sb.append("    isAutoConsolidationBlocked: ").append(toIndentedString(isAutoConsolidationBlocked)).append("\n");
    sb.append("    isAutoCreateBlocked: ").append(toIndentedString(isAutoCreateBlocked)).append("\n");
    sb.append("    isBackOrdered: ").append(toIndentedString(isBackOrdered)).append("\n");
    sb.append("    isCartonMinWeight: ").append(toIndentedString(isCartonMinWeight)).append("\n");
    sb.append("    isShipmentDocumentsOnly: ").append(toIndentedString(isShipmentDocumentsOnly)).append("\n");
    sb.append("    isWarehouseTransfer: ").append(toIndentedString(isWarehouseTransfer)).append("\n");
    sb.append("    lpNCubingIndicator: ").append(toIndentedString(lpNCubingIndicator)).append("\n");
    sb.append("    language: ").append(toIndentedString(language)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    moveType: ").append(toIndentedString(moveType)).append("\n");
    sb.append("    movementOption: ").append(toIndentedString(movementOption)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    palletCubingIndicator: ").append(toIndentedString(palletCubingIndicator)).append("\n");
    sb.append("    planningDestinationFacilityId: ").append(toIndentedString(planningDestinationFacilityId)).append("\n");
    sb.append("    planningOriginFacilityId: ").append(toIndentedString(planningOriginFacilityId)).append("\n");
    sb.append("    primaryMaxicodeAddressNumber: ").append(toIndentedString(primaryMaxicodeAddressNumber)).append("\n");
    sb.append("    priority: ").append(toIndentedString(priority)).append("\n");
    sb.append("    routeTo: ").append(toIndentedString(routeTo)).append("\n");
    sb.append("    routeType1: ").append(toIndentedString(routeType1)).append("\n");
    sb.append("    routeType2: ").append(toIndentedString(routeType2)).append("\n");
    sb.append("    routeWaveNumber: ").append(toIndentedString(routeWaveNumber)).append("\n");
    sb.append("    routingAttributes: ").append(toIndentedString(routingAttributes)).append("\n");
    sb.append("    scheduleDeliveryDate: ").append(toIndentedString(scheduleDeliveryDate)).append("\n");
    sb.append("    secondryMaxicodeAddressNumber: ").append(toIndentedString(secondryMaxicodeAddressNumber)).append("\n");
    sb.append("    shipLocationControl: ").append(toIndentedString(shipLocationControl)).append("\n");
    sb.append("    shipmentPlanningScheduleDay: ").append(toIndentedString(shipmentPlanningScheduleDay)).append("\n");
    sb.append("    shipmmentPlanningScheduleDay: ").append(toIndentedString(shipmmentPlanningScheduleDay)).append("\n");
    sb.append("    translations: ").append(toIndentedString(translations)).append("\n");
    sb.append("    transportationWaveOptionId: ").append(toIndentedString(transportationWaveOptionId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    waveId: ").append(toIndentedString(waveId)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

