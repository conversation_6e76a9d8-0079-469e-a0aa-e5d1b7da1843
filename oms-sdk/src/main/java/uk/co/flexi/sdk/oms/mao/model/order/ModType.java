/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ModType
 */
public class ModType {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CALCULATE_EFFECTIVE_RANK = "CalculateEffectiveRank";
  @SerializedName(SERIALIZED_NAME_CALCULATE_EFFECTIVE_RANK)
  private Boolean calculateEffectiveRank;

  public static final String SERIALIZED_NAME_CALCULATE_PRICE = "CalculatePrice";
  @SerializedName(SERIALIZED_NAME_CALCULATE_PRICE)
  private Boolean calculatePrice;

  public static final String SERIALIZED_NAME_CALCULATE_RETURN_FEE = "CalculateReturnFee";
  @SerializedName(SERIALIZED_NAME_CALCULATE_RETURN_FEE)
  private Boolean calculateReturnFee;

  public static final String SERIALIZED_NAME_CALCULATE_SNH = "CalculateSnh";
  @SerializedName(SERIALIZED_NAME_CALCULATE_SNH)
  private Boolean calculateSnh;

  public static final String SERIALIZED_NAME_CALCULATE_TAX = "CalculateTax";
  @SerializedName(SERIALIZED_NAME_CALCULATE_TAX)
  private Boolean calculateTax;

  public static final String SERIALIZED_NAME_CANCEL_RESERVATION = "CancelReservation";
  @SerializedName(SERIALIZED_NAME_CANCEL_RESERVATION)
  private Boolean cancelReservation;

  public static final String SERIALIZED_NAME_CHANGE_EVENT_ENTITY = "ChangeEventEntity";
  @SerializedName(SERIALIZED_NAME_CHANGE_EVENT_ENTITY)
  private String changeEventEntity;

  public static final String SERIALIZED_NAME_CHANGE_EVENT_TYPE = "ChangeEventType";
  @SerializedName(SERIALIZED_NAME_CHANGE_EVENT_TYPE)
  private String changeEventType;

  public static final String SERIALIZED_NAME_CHANGE_STATUS = "ChangeStatus";
  @SerializedName(SERIALIZED_NAME_CHANGE_STATUS)
  private String changeStatus;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DISPLAY_SEQUENCE = "DisplaySequence";
  @SerializedName(SERIALIZED_NAME_DISPLAY_SEQUENCE)
  private Long displaySequence;

  public static final String SERIALIZED_NAME_EVALUATE_PROMOTION = "EvaluatePromotion";
  @SerializedName(SERIALIZED_NAME_EVALUATE_PROMOTION)
  private Boolean evaluatePromotion;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FIELD_NAME = "FieldName";
  @SerializedName(SERIALIZED_NAME_FIELD_NAME)
  private String fieldName;

  public static final String SERIALIZED_NAME_IS_CALCULATE_PRICE_EDITABLE = "IsCalculatePriceEditable";
  @SerializedName(SERIALIZED_NAME_IS_CALCULATE_PRICE_EDITABLE)
  private Boolean isCalculatePriceEditable;

  public static final String SERIALIZED_NAME_IS_CALCULATE_RETURN_FEE_EDITABLE = "IsCalculateReturnFeeEditable";
  @SerializedName(SERIALIZED_NAME_IS_CALCULATE_RETURN_FEE_EDITABLE)
  private Boolean isCalculateReturnFeeEditable;

  public static final String SERIALIZED_NAME_IS_CALCULATE_SNH_EDITABLE = "IsCalculateSnhEditable";
  @SerializedName(SERIALIZED_NAME_IS_CALCULATE_SNH_EDITABLE)
  private Boolean isCalculateSnhEditable;

  public static final String SERIALIZED_NAME_IS_CALCULATE_TAX_EDITABLE = "IsCalculateTaxEditable";
  @SerializedName(SERIALIZED_NAME_IS_CALCULATE_TAX_EDITABLE)
  private Boolean isCalculateTaxEditable;

  public static final String SERIALIZED_NAME_IS_CANCEL_RESERVATION_EDITABLE = "IsCancelReservationEditable";
  @SerializedName(SERIALIZED_NAME_IS_CANCEL_RESERVATION_EDITABLE)
  private Boolean isCancelReservationEditable;

  public static final String SERIALIZED_NAME_IS_CHANGE_STATUS_EDITABLE = "IsChangeStatusEditable";
  @SerializedName(SERIALIZED_NAME_IS_CHANGE_STATUS_EDITABLE)
  private Boolean isChangeStatusEditable;

  public static final String SERIALIZED_NAME_IS_EFFECTIVE_RANK_EDITABLE = "IsEffectiveRankEditable";
  @SerializedName(SERIALIZED_NAME_IS_EFFECTIVE_RANK_EDITABLE)
  private Boolean isEffectiveRankEditable;

  public static final String SERIALIZED_NAME_IS_EVALUATE_PROMOTION_EDITABLE = "IsEvaluatePromotionEditable";
  @SerializedName(SERIALIZED_NAME_IS_EVALUATE_PROMOTION_EDITABLE)
  private Boolean isEvaluatePromotionEditable;

  public static final String SERIALIZED_NAME_IS_REEVALUATE_PIPELINE_EDITABLE = "IsReevaluatePipelineEditable";
  @SerializedName(SERIALIZED_NAME_IS_REEVALUATE_PIPELINE_EDITABLE)
  private Boolean isReevaluatePipelineEditable;

  public static final String SERIALIZED_NAME_IS_RESUBMIT_PIPELINE_EDITABLE = "IsResubmitPipelineEditable";
  @SerializedName(SERIALIZED_NAME_IS_RESUBMIT_PIPELINE_EDITABLE)
  private Boolean isResubmitPipelineEditable;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_MOD_LEVEL = "ModLevel";
  @SerializedName(SERIALIZED_NAME_MOD_LEVEL)
  private String modLevel;

  public static final String SERIALIZED_NAME_MOD_TYPE_ID = "ModTypeId";
  @SerializedName(SERIALIZED_NAME_MOD_TYPE_ID)
  private String modTypeId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_REEVALUATE_PIPELINE = "ReevaluatePipeline";
  @SerializedName(SERIALIZED_NAME_REEVALUATE_PIPELINE)
  private Boolean reevaluatePipeline;

  public static final String SERIALIZED_NAME_RESTRICT_FROM_RETURN_STATUS = "RestrictFromReturnStatus";
  @SerializedName(SERIALIZED_NAME_RESTRICT_FROM_RETURN_STATUS)
  private String restrictFromReturnStatus;

  public static final String SERIALIZED_NAME_RESTRICT_FROM_STATUS = "RestrictFromStatus";
  @SerializedName(SERIALIZED_NAME_RESTRICT_FROM_STATUS)
  private String restrictFromStatus;

  public static final String SERIALIZED_NAME_RESTRICT_POST_CONFIRMATION = "RestrictPostConfirmation";
  @SerializedName(SERIALIZED_NAME_RESTRICT_POST_CONFIRMATION)
  private Boolean restrictPostConfirmation;

  public static final String SERIALIZED_NAME_RESUBMIT_PIPELINE = "ResubmitPipeline";
  @SerializedName(SERIALIZED_NAME_RESUBMIT_PIPELINE)
  private Boolean resubmitPipeline;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public ModType actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ModType putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ModType calculateEffectiveRank(Boolean calculateEffectiveRank) {
    this.calculateEffectiveRank = calculateEffectiveRank;
    return this;
  }

   /**
   * Flag to indicate  whether to calculate effective rank or not
   * @return calculateEffectiveRank
  **/
  
  public Boolean getCalculateEffectiveRank() {
    return calculateEffectiveRank;
  }

  public void setCalculateEffectiveRank(Boolean calculateEffectiveRank) {
    this.calculateEffectiveRank = calculateEffectiveRank;
  }

  public ModType calculatePrice(Boolean calculatePrice) {
    this.calculatePrice = calculatePrice;
    return this;
  }

   /**
   * Flag to indicate that item pricing service will be called for specific modofication type 
   * @return calculatePrice
  **/
  
  public Boolean getCalculatePrice() {
    return calculatePrice;
  }

  public void setCalculatePrice(Boolean calculatePrice) {
    this.calculatePrice = calculatePrice;
  }

  public ModType calculateReturnFee(Boolean calculateReturnFee) {
    this.calculateReturnFee = calculateReturnFee;
    return this;
  }

   /**
   * Flag to indicate  whether to calculate return fee or not
   * @return calculateReturnFee
  **/
  
  public Boolean getCalculateReturnFee() {
    return calculateReturnFee;
  }

  public void setCalculateReturnFee(Boolean calculateReturnFee) {
    this.calculateReturnFee = calculateReturnFee;
  }

  public ModType calculateSnh(Boolean calculateSnh) {
    this.calculateSnh = calculateSnh;
    return this;
  }

   /**
   * Flag to indicate  SnH service will be enabled for specific modification type 
   * @return calculateSnh
  **/
  
  public Boolean getCalculateSnh() {
    return calculateSnh;
  }

  public void setCalculateSnh(Boolean calculateSnh) {
    this.calculateSnh = calculateSnh;
  }

  public ModType calculateTax(Boolean calculateTax) {
    this.calculateTax = calculateTax;
    return this;
  }

   /**
   * Flag to indicate  Tax serivice will be enabled for specific modification type 
   * @return calculateTax
  **/
  
  public Boolean getCalculateTax() {
    return calculateTax;
  }

  public void setCalculateTax(Boolean calculateTax) {
    this.calculateTax = calculateTax;
  }

  public ModType cancelReservation(Boolean cancelReservation) {
    this.cancelReservation = cancelReservation;
    return this;
  }

   /**
   * Flag to indicate whether to cancel reservation of allocated inventories for the specified modification type
   * @return cancelReservation
  **/
  
  public Boolean getCancelReservation() {
    return cancelReservation;
  }

  public void setCancelReservation(Boolean cancelReservation) {
    this.cancelReservation = cancelReservation;
  }

  public ModType changeEventEntity(String changeEventEntity) {
    this.changeEventEntity = changeEventEntity;
    return this;
  }

   /**
   * Entity for which this modTypeId is associated(Order/Orderline/QuantityDetail)
   * @return changeEventEntity
  **/
  
  public String getChangeEventEntity() {
    return changeEventEntity;
  }

  public void setChangeEventEntity(String changeEventEntity) {
    this.changeEventEntity = changeEventEntity;
  }

  public ModType changeEventType(String changeEventType) {
    this.changeEventType = changeEventType;
    return this;
  }

   /**
   * Change event type used for specific modfication type
   * @return changeEventType
  **/
  
  public String getChangeEventType() {
    return changeEventType;
  }

  public void setChangeEventType(String changeEventType) {
    this.changeEventType = changeEventType;
  }

  public ModType changeStatus(String changeStatus) {
    this.changeStatus = changeStatus;
    return this;
  }

   /**
   * Status the line will be changed to when modification happens line level
   * @return changeStatus
  **/
  
  public String getChangeStatus() {
    return changeStatus;
  }

  public void setChangeStatus(String changeStatus) {
    this.changeStatus = changeStatus;
  }

  public ModType createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ModType createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ModType displaySequence(Long displaySequence) {
    this.displaySequence = displaySequence;
    return this;
  }

   /**
   * Display sequence of mod types in UI
   * minimum: 0
   * maximum: -8446744073709551617
   * @return displaySequence
  **/
  
  public Long getDisplaySequence() {
    return displaySequence;
  }

  public void setDisplaySequence(Long displaySequence) {
    this.displaySequence = displaySequence;
  }

  public ModType evaluatePromotion(Boolean evaluatePromotion) {
    this.evaluatePromotion = evaluatePromotion;
    return this;
  }

   /**
   * Flag to indicate that item pricing service will be called for specific modofication type 
   * @return evaluatePromotion
  **/
  
  public Boolean getEvaluatePromotion() {
    return evaluatePromotion;
  }

  public void setEvaluatePromotion(Boolean evaluatePromotion) {
    this.evaluatePromotion = evaluatePromotion;
  }

  public ModType extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ModType fieldName(String fieldName) {
    this.fieldName = fieldName;
    return this;
  }

   /**
   * Field with which the modType is associated.
   * @return fieldName
  **/
  
  public String getFieldName() {
    return fieldName;
  }

  public void setFieldName(String fieldName) {
    this.fieldName = fieldName;
  }

  public ModType isCalculatePriceEditable(Boolean isCalculatePriceEditable) {
    this.isCalculatePriceEditable = isCalculatePriceEditable;
    return this;
  }

   /**
   * Is Calculate Price service editable  for this mod type
   * @return isCalculatePriceEditable
  **/
  
  public Boolean getIsCalculatePriceEditable() {
    return isCalculatePriceEditable;
  }

  public void setIsCalculatePriceEditable(Boolean isCalculatePriceEditable) {
    this.isCalculatePriceEditable = isCalculatePriceEditable;
  }

  public ModType isCalculateReturnFeeEditable(Boolean isCalculateReturnFeeEditable) {
    this.isCalculateReturnFeeEditable = isCalculateReturnFeeEditable;
    return this;
  }

   /**
   * Is Calculate return fee service editable  for this mod type
   * @return isCalculateReturnFeeEditable
  **/
  
  public Boolean getIsCalculateReturnFeeEditable() {
    return isCalculateReturnFeeEditable;
  }

  public void setIsCalculateReturnFeeEditable(Boolean isCalculateReturnFeeEditable) {
    this.isCalculateReturnFeeEditable = isCalculateReturnFeeEditable;
  }

  public ModType isCalculateSnhEditable(Boolean isCalculateSnhEditable) {
    this.isCalculateSnhEditable = isCalculateSnhEditable;
    return this;
  }

   /**
   * Is Calculate Snh service editable  for this mod type
   * @return isCalculateSnhEditable
  **/
  
  public Boolean getIsCalculateSnhEditable() {
    return isCalculateSnhEditable;
  }

  public void setIsCalculateSnhEditable(Boolean isCalculateSnhEditable) {
    this.isCalculateSnhEditable = isCalculateSnhEditable;
  }

  public ModType isCalculateTaxEditable(Boolean isCalculateTaxEditable) {
    this.isCalculateTaxEditable = isCalculateTaxEditable;
    return this;
  }

   /**
   * Is Calculate Tax service editable  for this mod type
   * @return isCalculateTaxEditable
  **/
  
  public Boolean getIsCalculateTaxEditable() {
    return isCalculateTaxEditable;
  }

  public void setIsCalculateTaxEditable(Boolean isCalculateTaxEditable) {
    this.isCalculateTaxEditable = isCalculateTaxEditable;
  }

  public ModType isCancelReservationEditable(Boolean isCancelReservationEditable) {
    this.isCancelReservationEditable = isCancelReservationEditable;
    return this;
  }

   /**
   * Is Cancel Reservation service editable  for this mod type
   * @return isCancelReservationEditable
  **/
  
  public Boolean getIsCancelReservationEditable() {
    return isCancelReservationEditable;
  }

  public void setIsCancelReservationEditable(Boolean isCancelReservationEditable) {
    this.isCancelReservationEditable = isCancelReservationEditable;
  }

  public ModType isChangeStatusEditable(Boolean isChangeStatusEditable) {
    this.isChangeStatusEditable = isChangeStatusEditable;
    return this;
  }

   /**
   * Is Change Status service editable  for this mod type
   * @return isChangeStatusEditable
  **/
  
  public Boolean getIsChangeStatusEditable() {
    return isChangeStatusEditable;
  }

  public void setIsChangeStatusEditable(Boolean isChangeStatusEditable) {
    this.isChangeStatusEditable = isChangeStatusEditable;
  }

  public ModType isEffectiveRankEditable(Boolean isEffectiveRankEditable) {
    this.isEffectiveRankEditable = isEffectiveRankEditable;
    return this;
  }

   /**
   * Is prioritization service is editable for this mod type
   * @return isEffectiveRankEditable
  **/
  
  public Boolean getIsEffectiveRankEditable() {
    return isEffectiveRankEditable;
  }

  public void setIsEffectiveRankEditable(Boolean isEffectiveRankEditable) {
    this.isEffectiveRankEditable = isEffectiveRankEditable;
  }

  public ModType isEvaluatePromotionEditable(Boolean isEvaluatePromotionEditable) {
    this.isEvaluatePromotionEditable = isEvaluatePromotionEditable;
    return this;
  }

   /**
   * Is Evaluate Promotion service editable  for this mod type
   * @return isEvaluatePromotionEditable
  **/
  
  public Boolean getIsEvaluatePromotionEditable() {
    return isEvaluatePromotionEditable;
  }

  public void setIsEvaluatePromotionEditable(Boolean isEvaluatePromotionEditable) {
    this.isEvaluatePromotionEditable = isEvaluatePromotionEditable;
  }

  public ModType isReevaluatePipelineEditable(Boolean isReevaluatePipelineEditable) {
    this.isReevaluatePipelineEditable = isReevaluatePipelineEditable;
    return this;
  }

   /**
   * Is Reevaluate Pipeline service editable  for this mod type
   * @return isReevaluatePipelineEditable
  **/
  
  public Boolean getIsReevaluatePipelineEditable() {
    return isReevaluatePipelineEditable;
  }

  public void setIsReevaluatePipelineEditable(Boolean isReevaluatePipelineEditable) {
    this.isReevaluatePipelineEditable = isReevaluatePipelineEditable;
  }

  public ModType isResubmitPipelineEditable(Boolean isResubmitPipelineEditable) {
    this.isResubmitPipelineEditable = isResubmitPipelineEditable;
    return this;
  }

   /**
   * Is Resubmit Pipeline service editable  for this mod type
   * @return isResubmitPipelineEditable
  **/
  
  public Boolean getIsResubmitPipelineEditable() {
    return isResubmitPipelineEditable;
  }

  public void setIsResubmitPipelineEditable(Boolean isResubmitPipelineEditable) {
    this.isResubmitPipelineEditable = isResubmitPipelineEditable;
  }

  public ModType localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public ModType messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ModType modLevel(String modLevel) {
    this.modLevel = modLevel;
    return this;
  }

   /**
   * Level of modification (Order/Orderline/Release)
   * @return modLevel
  **/
  
  public String getModLevel() {
    return modLevel;
  }

  public void setModLevel(String modLevel) {
    this.modLevel = modLevel;
  }

  public ModType modTypeId(String modTypeId) {
    this.modTypeId = modTypeId;
    return this;
  }

   /**
   * Unique identifier for order event modfication type
   * @return modTypeId
  **/
  
  public String getModTypeId() {
    return modTypeId;
  }

  public void setModTypeId(String modTypeId) {
    this.modTypeId = modTypeId;
  }

  public ModType PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ModType profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public ModType reevaluatePipeline(Boolean reevaluatePipeline) {
    this.reevaluatePipeline = reevaluatePipeline;
    return this;
  }

   /**
   * Flag to indicate whether to re-evaluate pipeline Id for the specified modification type
   * @return reevaluatePipeline
  **/
  
  public Boolean getReevaluatePipeline() {
    return reevaluatePipeline;
  }

  public void setReevaluatePipeline(Boolean reevaluatePipeline) {
    this.reevaluatePipeline = reevaluatePipeline;
  }

  public ModType restrictFromReturnStatus(String restrictFromReturnStatus) {
    this.restrictFromReturnStatus = restrictFromReturnStatus;
    return this;
  }

   /**
   * Maximum status that is allowed for this modType is lessthan restrictFromReturnStatus.This property is applicable only for Return ModTypes
   * @return restrictFromReturnStatus
  **/
  
  public String getRestrictFromReturnStatus() {
    return restrictFromReturnStatus;
  }

  public void setRestrictFromReturnStatus(String restrictFromReturnStatus) {
    this.restrictFromReturnStatus = restrictFromReturnStatus;
  }

  public ModType restrictFromStatus(String restrictFromStatus) {
    this.restrictFromStatus = restrictFromStatus;
    return this;
  }

   /**
   * Maximum status that is allowed for this modType is lessthan systemRestrictFromStatus
   * @return restrictFromStatus
  **/
  
  public String getRestrictFromStatus() {
    return restrictFromStatus;
  }

  public void setRestrictFromStatus(String restrictFromStatus) {
    this.restrictFromStatus = restrictFromStatus;
  }

  public ModType restrictPostConfirmation(Boolean restrictPostConfirmation) {
    this.restrictPostConfirmation = restrictPostConfirmation;
    return this;
  }

   /**
   * Flag to indicate wether current modification is allowed once order is confirmed
   * @return restrictPostConfirmation
  **/
  
  public Boolean getRestrictPostConfirmation() {
    return restrictPostConfirmation;
  }

  public void setRestrictPostConfirmation(Boolean restrictPostConfirmation) {
    this.restrictPostConfirmation = restrictPostConfirmation;
  }

  public ModType resubmitPipeline(Boolean resubmitPipeline) {
    this.resubmitPipeline = resubmitPipeline;
    return this;
  }

   /**
   * Flag to indicate whether to re-submit pipeline for the specified modification type
   * @return resubmitPipeline
  **/
  
  public Boolean getResubmitPipeline() {
    return resubmitPipeline;
  }

  public void setResubmitPipeline(Boolean resubmitPipeline) {
    this.resubmitPipeline = resubmitPipeline;
  }

  public ModType updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ModType updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ModType rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ModType modType = (ModType) o;
    return Objects.equals(this.actions, modType.actions) &&
        Objects.equals(this.calculateEffectiveRank, modType.calculateEffectiveRank) &&
        Objects.equals(this.calculatePrice, modType.calculatePrice) &&
        Objects.equals(this.calculateReturnFee, modType.calculateReturnFee) &&
        Objects.equals(this.calculateSnh, modType.calculateSnh) &&
        Objects.equals(this.calculateTax, modType.calculateTax) &&
        Objects.equals(this.cancelReservation, modType.cancelReservation) &&
        Objects.equals(this.changeEventEntity, modType.changeEventEntity) &&
        Objects.equals(this.changeEventType, modType.changeEventType) &&
        Objects.equals(this.changeStatus, modType.changeStatus) &&
        Objects.equals(this.createdBy, modType.createdBy) &&
        Objects.equals(this.createdTimestamp, modType.createdTimestamp) &&
        Objects.equals(this.displaySequence, modType.displaySequence) &&
        Objects.equals(this.evaluatePromotion, modType.evaluatePromotion) &&
        Objects.equals(this.extended, modType.extended) &&
        Objects.equals(this.fieldName, modType.fieldName) &&
        Objects.equals(this.isCalculatePriceEditable, modType.isCalculatePriceEditable) &&
        Objects.equals(this.isCalculateReturnFeeEditable, modType.isCalculateReturnFeeEditable) &&
        Objects.equals(this.isCalculateSnhEditable, modType.isCalculateSnhEditable) &&
        Objects.equals(this.isCalculateTaxEditable, modType.isCalculateTaxEditable) &&
        Objects.equals(this.isCancelReservationEditable, modType.isCancelReservationEditable) &&
        Objects.equals(this.isChangeStatusEditable, modType.isChangeStatusEditable) &&
        Objects.equals(this.isEffectiveRankEditable, modType.isEffectiveRankEditable) &&
        Objects.equals(this.isEvaluatePromotionEditable, modType.isEvaluatePromotionEditable) &&
        Objects.equals(this.isReevaluatePipelineEditable, modType.isReevaluatePipelineEditable) &&
        Objects.equals(this.isResubmitPipelineEditable, modType.isResubmitPipelineEditable) &&
        Objects.equals(this.localizedTo, modType.localizedTo) &&
        Objects.equals(this.messages, modType.messages) &&
        Objects.equals(this.modLevel, modType.modLevel) &&
        Objects.equals(this.modTypeId, modType.modTypeId) &&
        Objects.equals(this.PK, modType.PK) &&
        Objects.equals(this.profileId, modType.profileId) &&
        Objects.equals(this.reevaluatePipeline, modType.reevaluatePipeline) &&
        Objects.equals(this.restrictFromReturnStatus, modType.restrictFromReturnStatus) &&
        Objects.equals(this.restrictFromStatus, modType.restrictFromStatus) &&
        Objects.equals(this.restrictPostConfirmation, modType.restrictPostConfirmation) &&
        Objects.equals(this.resubmitPipeline, modType.resubmitPipeline) &&
        Objects.equals(this.updatedBy, modType.updatedBy) &&
        Objects.equals(this.updatedTimestamp, modType.updatedTimestamp) &&
        Objects.equals(this.rootCause, modType.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, calculateEffectiveRank, calculatePrice, calculateReturnFee, calculateSnh, calculateTax, cancelReservation, changeEventEntity, changeEventType, changeStatus, createdBy, createdTimestamp, displaySequence, evaluatePromotion, extended, fieldName, isCalculatePriceEditable, isCalculateReturnFeeEditable, isCalculateSnhEditable, isCalculateTaxEditable, isCancelReservationEditable, isChangeStatusEditable, isEffectiveRankEditable, isEvaluatePromotionEditable, isReevaluatePipelineEditable, isResubmitPipelineEditable, localizedTo, messages, modLevel, modTypeId, PK, profileId, reevaluatePipeline, restrictFromReturnStatus, restrictFromStatus, restrictPostConfirmation, resubmitPipeline, updatedBy, updatedTimestamp, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ModType {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    calculateEffectiveRank: ").append(toIndentedString(calculateEffectiveRank)).append("\n");
    sb.append("    calculatePrice: ").append(toIndentedString(calculatePrice)).append("\n");
    sb.append("    calculateReturnFee: ").append(toIndentedString(calculateReturnFee)).append("\n");
    sb.append("    calculateSnh: ").append(toIndentedString(calculateSnh)).append("\n");
    sb.append("    calculateTax: ").append(toIndentedString(calculateTax)).append("\n");
    sb.append("    cancelReservation: ").append(toIndentedString(cancelReservation)).append("\n");
    sb.append("    changeEventEntity: ").append(toIndentedString(changeEventEntity)).append("\n");
    sb.append("    changeEventType: ").append(toIndentedString(changeEventType)).append("\n");
    sb.append("    changeStatus: ").append(toIndentedString(changeStatus)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    displaySequence: ").append(toIndentedString(displaySequence)).append("\n");
    sb.append("    evaluatePromotion: ").append(toIndentedString(evaluatePromotion)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    fieldName: ").append(toIndentedString(fieldName)).append("\n");
    sb.append("    isCalculatePriceEditable: ").append(toIndentedString(isCalculatePriceEditable)).append("\n");
    sb.append("    isCalculateReturnFeeEditable: ").append(toIndentedString(isCalculateReturnFeeEditable)).append("\n");
    sb.append("    isCalculateSnhEditable: ").append(toIndentedString(isCalculateSnhEditable)).append("\n");
    sb.append("    isCalculateTaxEditable: ").append(toIndentedString(isCalculateTaxEditable)).append("\n");
    sb.append("    isCancelReservationEditable: ").append(toIndentedString(isCancelReservationEditable)).append("\n");
    sb.append("    isChangeStatusEditable: ").append(toIndentedString(isChangeStatusEditable)).append("\n");
    sb.append("    isEffectiveRankEditable: ").append(toIndentedString(isEffectiveRankEditable)).append("\n");
    sb.append("    isEvaluatePromotionEditable: ").append(toIndentedString(isEvaluatePromotionEditable)).append("\n");
    sb.append("    isReevaluatePipelineEditable: ").append(toIndentedString(isReevaluatePipelineEditable)).append("\n");
    sb.append("    isResubmitPipelineEditable: ").append(toIndentedString(isResubmitPipelineEditable)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    modLevel: ").append(toIndentedString(modLevel)).append("\n");
    sb.append("    modTypeId: ").append(toIndentedString(modTypeId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    reevaluatePipeline: ").append(toIndentedString(reevaluatePipeline)).append("\n");
    sb.append("    restrictFromReturnStatus: ").append(toIndentedString(restrictFromReturnStatus)).append("\n");
    sb.append("    restrictFromStatus: ").append(toIndentedString(restrictFromStatus)).append("\n");
    sb.append("    restrictPostConfirmation: ").append(toIndentedString(restrictPostConfirmation)).append("\n");
    sb.append("    resubmitPipeline: ").append(toIndentedString(resubmitPipeline)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

