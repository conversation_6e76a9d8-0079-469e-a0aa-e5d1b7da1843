/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * SellingChannelId
 */
public class SellingChannelId {
  public static final String SERIALIZED_NAME_SELLING_CHANNEL_ID = "SellingChannelId";
  @SerializedName(SERIALIZED_NAME_SELLING_CHANNEL_ID)
  private String sellingChannelId;

  public SellingChannelId sellingChannelId(String sellingChannelId) {
    this.sellingChannelId = sellingChannelId;
    return this;
  }

   /**
   * Unique identifier of the Selling Channel ID
   * @return sellingChannelId
  **/
  
  public String getSellingChannelId() {
    return sellingChannelId;
  }

  public void setSellingChannelId(String sellingChannelId) {
    this.sellingChannelId = sellingChannelId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SellingChannelId sellingChannelId = (SellingChannelId) o;
    return Objects.equals(this.sellingChannelId, sellingChannelId.sellingChannelId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(sellingChannelId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SellingChannelId {\n");
    
    sb.append("    sellingChannelId: ").append(toIndentedString(sellingChannelId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

