/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


/**
 * Allocation
 */
public class Allocation {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ALLOCATED_ON = "AllocatedOn";
  @SerializedName(SERIALIZED_NAME_ALLOCATED_ON)
  private OffsetDateTime allocatedOn;

  public static final String SERIALIZED_NAME_ALLOCATION_DEPENDENCY_ID = "AllocationDependencyId";
  @SerializedName(SERIALIZED_NAME_ALLOCATION_DEPENDENCY_ID)
  private String allocationDependencyId;

  public static final String SERIALIZED_NAME_ALLOCATION_ID = "AllocationId";
  @SerializedName(SERIALIZED_NAME_ALLOCATION_ID)
  private String allocationId;

  public static final String SERIALIZED_NAME_ALLOCATION_TYPE = "AllocationType";
  @SerializedName(SERIALIZED_NAME_ALLOCATION_TYPE)
  private String allocationType;

  public static final String SERIALIZED_NAME_ASN_DETAIL_ID = "AsnDetailId";
  @SerializedName(SERIALIZED_NAME_ASN_DETAIL_ID)
  private String asnDetailId;

  public static final String SERIALIZED_NAME_ASN_ID = "AsnId";
  @SerializedName(SERIALIZED_NAME_ASN_ID)
  private String asnId;

  public static final String SERIALIZED_NAME_BATCH_NUMBER = "BatchNumber";
  @SerializedName(SERIALIZED_NAME_BATCH_NUMBER)
  private String batchNumber;

  public static final String SERIALIZED_NAME_CALCULATED_VALUES = "CalculatedValues";
  @SerializedName(SERIALIZED_NAME_CALCULATED_VALUES)
  private Object calculatedValues = null;

  public static final String SERIALIZED_NAME_CARRIER_CODE = "CarrierCode";
  @SerializedName(SERIALIZED_NAME_CARRIER_CODE)
  private String carrierCode;

  public static final String SERIALIZED_NAME_COMMITTED_DELIVERY_DATE = "CommittedDeliveryDate";
  @SerializedName(SERIALIZED_NAME_COMMITTED_DELIVERY_DATE)
  private OffsetDateTime committedDeliveryDate;

  public static final String SERIALIZED_NAME_COMMITTED_SHIP_DATE = "CommittedShipDate";
  @SerializedName(SERIALIZED_NAME_COMMITTED_SHIP_DATE)
  private OffsetDateTime committedShipDate;

  public static final String SERIALIZED_NAME_COUNTRY_OF_ORIGIN = "CountryOfOrigin";
  @SerializedName(SERIALIZED_NAME_COUNTRY_OF_ORIGIN)
  private String countryOfOrigin;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EARLIEST_DELIVERY_DATE = "EarliestDeliveryDate";
  @SerializedName(SERIALIZED_NAME_EARLIEST_DELIVERY_DATE)
  private OffsetDateTime earliestDeliveryDate;

  public static final String SERIALIZED_NAME_EARLIEST_SHIP_DATE = "EarliestShipDate";
  @SerializedName(SERIALIZED_NAME_EARLIEST_SHIP_DATE)
  private OffsetDateTime earliestShipDate;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_GROUP_ID = "GroupId";
  @SerializedName(SERIALIZED_NAME_GROUP_ID)
  private String groupId;

  public static final String SERIALIZED_NAME_INVENTORY_ATTRIBUTE1 = "InventoryAttribute1";
  @SerializedName(SERIALIZED_NAME_INVENTORY_ATTRIBUTE1)
  private String inventoryAttribute1;

  public static final String SERIALIZED_NAME_INVENTORY_ATTRIBUTE2 = "InventoryAttribute2";
  @SerializedName(SERIALIZED_NAME_INVENTORY_ATTRIBUTE2)
  private String inventoryAttribute2;

  public static final String SERIALIZED_NAME_INVENTORY_ATTRIBUTE3 = "InventoryAttribute3";
  @SerializedName(SERIALIZED_NAME_INVENTORY_ATTRIBUTE3)
  private String inventoryAttribute3;

  public static final String SERIALIZED_NAME_INVENTORY_ATTRIBUTE4 = "InventoryAttribute4";
  @SerializedName(SERIALIZED_NAME_INVENTORY_ATTRIBUTE4)
  private String inventoryAttribute4;

  public static final String SERIALIZED_NAME_INVENTORY_ATTRIBUTE5 = "InventoryAttribute5";
  @SerializedName(SERIALIZED_NAME_INVENTORY_ATTRIBUTE5)
  private String inventoryAttribute5;

  public static final String SERIALIZED_NAME_INVENTORY_SEGMENT_ID = "InventorySegmentId";
  @SerializedName(SERIALIZED_NAME_INVENTORY_SEGMENT_ID)
  private String inventorySegmentId;

  public static final String SERIALIZED_NAME_INVENTORY_TYPE_ID = "InventoryTypeId";
  @SerializedName(SERIALIZED_NAME_INVENTORY_TYPE_ID)
  private String inventoryTypeId;

  public static final String SERIALIZED_NAME_IS_VIRTUAL = "IsVirtual";
  @SerializedName(SERIALIZED_NAME_IS_VIRTUAL)
  private Boolean isVirtual;

  public static final String SERIALIZED_NAME_ITEM_ID = "ItemId";
  @SerializedName(SERIALIZED_NAME_ITEM_ID)
  private String itemId;

  public static final String SERIALIZED_NAME_LATEST_RELEASE_DATE = "LatestReleaseDate";
  @SerializedName(SERIALIZED_NAME_LATEST_RELEASE_DATE)
  private OffsetDateTime latestReleaseDate;

  public static final String SERIALIZED_NAME_LATEST_SHIP_DATE = "LatestShipDate";
  @SerializedName(SERIALIZED_NAME_LATEST_SHIP_DATE)
  private OffsetDateTime latestShipDate;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PO_DETAIL_ID = "PoDetailId";
  @SerializedName(SERIALIZED_NAME_PO_DETAIL_ID)
  private String poDetailId;

  public static final String SERIALIZED_NAME_PO_ID = "PoId";
  @SerializedName(SERIALIZED_NAME_PO_ID)
  private String poId;

  public static final String SERIALIZED_NAME_PRODUCT_STATUS_ID = "ProductStatusId";
  @SerializedName(SERIALIZED_NAME_PRODUCT_STATUS_ID)
  private String productStatusId;

  public static final String SERIALIZED_NAME_QUANTITY = "Quantity";
  @SerializedName(SERIALIZED_NAME_QUANTITY)
  private Double quantity;

  public static final String SERIALIZED_NAME_RESERVATION_REQUEST_DETAIL_ID = "ReservationRequestDetailId";
  @SerializedName(SERIALIZED_NAME_RESERVATION_REQUEST_DETAIL_ID)
  private String reservationRequestDetailId;

  public static final String SERIALIZED_NAME_RESERVATION_REQUEST_ID = "ReservationRequestId";
  @SerializedName(SERIALIZED_NAME_RESERVATION_REQUEST_ID)
  private String reservationRequestId;

  public static final String SERIALIZED_NAME_SERVICE_LEVEL_CODE = "ServiceLevelCode";
  @SerializedName(SERIALIZED_NAME_SERVICE_LEVEL_CODE)
  private String serviceLevelCode;

  public static final String SERIALIZED_NAME_SHIP_FROM_LOCATION_ID = "ShipFromLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_FROM_LOCATION_ID)
  private String shipFromLocationId;

  public static final String SERIALIZED_NAME_SHIP_TO_LOCATION_ID = "ShipToLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_TO_LOCATION_ID)
  private String shipToLocationId;

  public static final String SERIALIZED_NAME_SHIP_VIA_ID = "ShipViaId";
  @SerializedName(SERIALIZED_NAME_SHIP_VIA_ID)
  private String shipViaId;

  public static final String SERIALIZED_NAME_STATUS = "Status";
  @SerializedName(SERIALIZED_NAME_STATUS)
  private AllocationStatusId status = null;

  public static final String SERIALIZED_NAME_SUBSTITUTION_RATIO = "SubstitutionRatio";
  @SerializedName(SERIALIZED_NAME_SUBSTITUTION_RATIO)
  private Double substitutionRatio;

  public static final String SERIALIZED_NAME_SUBSTITUTION_TYPE_ID = "SubstitutionTypeId";
  @SerializedName(SERIALIZED_NAME_SUBSTITUTION_TYPE_ID)
  private String substitutionTypeId;

  public static final String SERIALIZED_NAME_U_O_M = "UOM";
  @SerializedName(SERIALIZED_NAME_U_O_M)
  private String UOM;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public Allocation actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public Allocation putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public Allocation allocatedOn(OffsetDateTime allocatedOn) {
    this.allocatedOn = allocatedOn;
    return this;
  }

   /**
   * Date and time when the allocation is created or updated by order promising
   * @return allocatedOn
  **/
  public OffsetDateTime getAllocatedOn() {
    return allocatedOn;
  }

  public void setAllocatedOn(OffsetDateTime allocatedOn) {
    this.allocatedOn = allocatedOn;
  }

  public Allocation allocationDependencyId(String allocationDependencyId) {
    this.allocationDependencyId = allocationDependencyId;
    return this;
  }

   /**
   * Id of the dependency, if applicable
   * @return allocationDependencyId
  **/
  public String getAllocationDependencyId() {
    return allocationDependencyId;
  }

  public void setAllocationDependencyId(String allocationDependencyId) {
    this.allocationDependencyId = allocationDependencyId;
  }

  public Allocation allocationId(String allocationId) {
    this.allocationId = allocationId;
    return this;
  }

   /**
   * Unique identifier of the allocation
   * @return allocationId
  **/
  public String getAllocationId() {
    return allocationId;
  }

  public void setAllocationId(String allocationId) {
    this.allocationId = allocationId;
  }

  public Allocation allocationType(String allocationType) {
    this.allocationType = allocationType;
    return this;
  }

   /**
   * Type of allocation
   * @return allocationType
  **/
  public String getAllocationType() {
    return allocationType;
  }

  public void setAllocationType(String allocationType) {
    this.allocationType = allocationType;
  }

  public Allocation asnDetailId(String asnDetailId) {
    this.asnDetailId = asnDetailId;
    return this;
  }

   /**
   * Id of the ASN detail, if allocated against in transit inventory
   * @return asnDetailId
  **/
  public String getAsnDetailId() {
    return asnDetailId;
  }

  public void setAsnDetailId(String asnDetailId) {
    this.asnDetailId = asnDetailId;
  }

  public Allocation asnId(String asnId) {
    this.asnId = asnId;
    return this;
  }

   /**
   * Id of the ASN, if allocated against in transit inventory
   * @return asnId
  **/
  public String getAsnId() {
    return asnId;
  }

  public void setAsnId(String asnId) {
    this.asnId = asnId;
  }

  public Allocation batchNumber(String batchNumber) {
    this.batchNumber = batchNumber;
    return this;
  }

   /**
   * Batch number of the allocated inventory
   * @return batchNumber
  **/
  
  public String getBatchNumber() {
    return batchNumber;
  }

  public void setBatchNumber(String batchNumber) {
    this.batchNumber = batchNumber;
  }

  public Allocation calculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
    return this;
  }

   /**
   * Get calculatedValues
   * @return calculatedValues
  **/
  
  public Object getCalculatedValues() {
    return calculatedValues;
  }

  public void setCalculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
  }

  public Allocation carrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
    return this;
  }

   /**
   * Code of the carrier which is used for scheduling the allocation
   * @return carrierCode
  **/
  
  public String getCarrierCode() {
    return carrierCode;
  }

  public void setCarrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
  }

  public Allocation committedDeliveryDate(OffsetDateTime committedDeliveryDate) {
    this.committedDeliveryDate = committedDeliveryDate;
    return this;
  }

   /**
   * Delivery date which is promised to the customer as the Estimated Time of Arrival (ETA)
   * @return committedDeliveryDate
  **/
  
  public OffsetDateTime getCommittedDeliveryDate() {
    return committedDeliveryDate;
  }

  public void setCommittedDeliveryDate(OffsetDateTime committedDeliveryDate) {
    this.committedDeliveryDate = committedDeliveryDate;
  }

  public Allocation committedShipDate(OffsetDateTime committedShipDate) {
    this.committedShipDate = committedShipDate;
    return this;
  }

   /**
   * Delivery date which is promised to the customer as the Estimated Time of Arrival (ETA), This is calculated by Promising and populated by the Promising Response
   * @return committedShipDate
  **/
  
  public OffsetDateTime getCommittedShipDate() {
    return committedShipDate;
  }

  public void setCommittedShipDate(OffsetDateTime committedShipDate) {
    this.committedShipDate = committedShipDate;
  }

  public Allocation countryOfOrigin(String countryOfOrigin) {
    this.countryOfOrigin = countryOfOrigin;
    return this;
  }

   /**
   * Country of origin of the allocated inventory
   * @return countryOfOrigin
  **/
  
  public String getCountryOfOrigin() {
    return countryOfOrigin;
  }

  public void setCountryOfOrigin(String countryOfOrigin) {
    this.countryOfOrigin = countryOfOrigin;
  }

  public Allocation createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public Allocation createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public Allocation earliestDeliveryDate(OffsetDateTime earliestDeliveryDate) {
    this.earliestDeliveryDate = earliestDeliveryDate;
    return this;
  }

   /**
   * Earliest date when the order line can be delivered, calculated by the Promising Component
   * @return earliestDeliveryDate
  **/
  
  public OffsetDateTime getEarliestDeliveryDate() {
    return earliestDeliveryDate;
  }

  public void setEarliestDeliveryDate(OffsetDateTime earliestDeliveryDate) {
    this.earliestDeliveryDate = earliestDeliveryDate;
  }

  public Allocation earliestShipDate(OffsetDateTime earliestShipDate) {
    this.earliestShipDate = earliestShipDate;
    return this;
  }

   /**
   * Earliest date when the order line can be shipped, calculated by the Promising Component.
   * @return earliestShipDate
  **/
  
  public OffsetDateTime getEarliestShipDate() {
    return earliestShipDate;
  }

  public void setEarliestShipDate(OffsetDateTime earliestShipDate) {
    this.earliestShipDate = earliestShipDate;
  }

  public Allocation extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public Allocation groupId(String groupId) {
    this.groupId = groupId;
    return this;
  }

   /**
   * Group Id, if allocations are grouped to be fulfilled and cancelled together
   * @return groupId
  **/
  
  public String getGroupId() {
    return groupId;
  }

  public void setGroupId(String groupId) {
    this.groupId = groupId;
  }

  public Allocation inventoryAttribute1(String inventoryAttribute1) {
    this.inventoryAttribute1 = inventoryAttribute1;
    return this;
  }

   /**
   * Inventory attribute1 of the allocated inventory
   * @return inventoryAttribute1
  **/
  
  public String getInventoryAttribute1() {
    return inventoryAttribute1;
  }

  public void setInventoryAttribute1(String inventoryAttribute1) {
    this.inventoryAttribute1 = inventoryAttribute1;
  }

  public Allocation inventoryAttribute2(String inventoryAttribute2) {
    this.inventoryAttribute2 = inventoryAttribute2;
    return this;
  }

   /**
   * Inventory attribute2 of the allocated inventory
   * @return inventoryAttribute2
  **/
  
  public String getInventoryAttribute2() {
    return inventoryAttribute2;
  }

  public void setInventoryAttribute2(String inventoryAttribute2) {
    this.inventoryAttribute2 = inventoryAttribute2;
  }

  public Allocation inventoryAttribute3(String inventoryAttribute3) {
    this.inventoryAttribute3 = inventoryAttribute3;
    return this;
  }

   /**
   * Inventory attribute3 of the allocated inventory
   * @return inventoryAttribute3
  **/
  
  public String getInventoryAttribute3() {
    return inventoryAttribute3;
  }

  public void setInventoryAttribute3(String inventoryAttribute3) {
    this.inventoryAttribute3 = inventoryAttribute3;
  }

  public Allocation inventoryAttribute4(String inventoryAttribute4) {
    this.inventoryAttribute4 = inventoryAttribute4;
    return this;
  }

   /**
   * Inventory attribute4 of the allocated inventory
   * @return inventoryAttribute4
  **/
  
  public String getInventoryAttribute4() {
    return inventoryAttribute4;
  }

  public void setInventoryAttribute4(String inventoryAttribute4) {
    this.inventoryAttribute4 = inventoryAttribute4;
  }

  public Allocation inventoryAttribute5(String inventoryAttribute5) {
    this.inventoryAttribute5 = inventoryAttribute5;
    return this;
  }

   /**
   * Inventory attribute5 of the allocated inventory
   * @return inventoryAttribute5
  **/
  
  public String getInventoryAttribute5() {
    return inventoryAttribute5;
  }

  public void setInventoryAttribute5(String inventoryAttribute5) {
    this.inventoryAttribute5 = inventoryAttribute5;
  }

  public Allocation inventorySegmentId(String inventorySegmentId) {
    this.inventorySegmentId = inventorySegmentId;
    return this;
  }

   /**
   * Segment against which order line is allocated
   * @return inventorySegmentId
  **/
  
  public String getInventorySegmentId() {
    return inventorySegmentId;
  }

  public void setInventorySegmentId(String inventorySegmentId) {
    this.inventorySegmentId = inventorySegmentId;
  }

  public Allocation inventoryTypeId(String inventoryTypeId) {
    this.inventoryTypeId = inventoryTypeId;
    return this;
  }

   /**
   * Inventory type of the allocated inventory
   * @return inventoryTypeId
  **/
  
  public String getInventoryTypeId() {
    return inventoryTypeId;
  }

  public void setInventoryTypeId(String inventoryTypeId) {
    this.inventoryTypeId = inventoryTypeId;
  }

  public Allocation isVirtual(Boolean isVirtual) {
    this.isVirtual = isVirtual;
    return this;
  }

   /**
   * Indicates virtual reservartion, i.e. reserved against a location which doesn&#39;t maintain inventory
   * @return isVirtual
  **/
  
  public Boolean getIsVirtual() {
    return isVirtual;
  }

  public void setIsVirtual(Boolean isVirtual) {
    this.isVirtual = isVirtual;
  }

  public Allocation itemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

   /**
   * The item which is being allocated
   * @return itemId
  **/
  
  public String getItemId() {
    return itemId;
  }

  public void setItemId(String itemId) {
    this.itemId = itemId;
  }

  public Allocation latestReleaseDate(OffsetDateTime latestReleaseDate) {
    this.latestReleaseDate = latestReleaseDate;
    return this;
  }

   /**
   * Latest date on which the allocation should be released to meet the latest delivery date on the order line.  This is calculated by Promising and populated by the Promising Response
   * @return latestReleaseDate
  **/
  
  public OffsetDateTime getLatestReleaseDate() {
    return latestReleaseDate;
  }

  public void setLatestReleaseDate(OffsetDateTime latestReleaseDate) {
    this.latestReleaseDate = latestReleaseDate;
  }

  public Allocation latestShipDate(OffsetDateTime latestShipDate) {
    this.latestShipDate = latestShipDate;
    return this;
  }

   /**
   * Latest date on which items should ship to meet the latest delivery date on the order line. This is calculated by Promising and populated by the Promising Response
   * @return latestShipDate
  **/
  
  public OffsetDateTime getLatestShipDate() {
    return latestShipDate;
  }

  public void setLatestShipDate(OffsetDateTime latestShipDate) {
    this.latestShipDate = latestShipDate;
  }

  public Allocation localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public Allocation messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public Allocation orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public Allocation PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public Allocation poDetailId(String poDetailId) {
    this.poDetailId = poDetailId;
    return this;
  }

   /**
   * Id of the PO detail, if allocated against on order inventory
   * @return poDetailId
  **/
  
  public String getPoDetailId() {
    return poDetailId;
  }

  public void setPoDetailId(String poDetailId) {
    this.poDetailId = poDetailId;
  }

  public Allocation poId(String poId) {
    this.poId = poId;
    return this;
  }

   /**
   * Id of the PO, if allocated against on order inventory
   * @return poId
  **/
  
  public String getPoId() {
    return poId;
  }

  public void setPoId(String poId) {
    this.poId = poId;
  }

  public Allocation productStatusId(String productStatusId) {
    this.productStatusId = productStatusId;
    return this;
  }

   /**
   * Product status of the allocated inventory
   * @return productStatusId
  **/
  
  public String getProductStatusId() {
    return productStatusId;
  }

  public void setProductStatusId(String productStatusId) {
    this.productStatusId = productStatusId;
  }

  public Allocation quantity(Double quantity) {
    this.quantity = quantity;
    return this;
  }

   /**
   * The allocated quantity
   * minimum: 0
   * maximum: 999999999999.9999
   * @return quantity
  **/
  
  public Double getQuantity() {
    return quantity;
  }

  public void setQuantity(Double quantity) {
    this.quantity = quantity;
  }

  public Allocation reservationRequestDetailId(String reservationRequestDetailId) {
    this.reservationRequestDetailId = reservationRequestDetailId;
    return this;
  }

   /**
   * Unique Identifier of the reservation request detail in the Inventory component which maps to this allocation
   * @return reservationRequestDetailId
  **/
  
  public String getReservationRequestDetailId() {
    return reservationRequestDetailId;
  }

  public void setReservationRequestDetailId(String reservationRequestDetailId) {
    this.reservationRequestDetailId = reservationRequestDetailId;
  }

  public Allocation reservationRequestId(String reservationRequestId) {
    this.reservationRequestId = reservationRequestId;
    return this;
  }

   /**
   * Unique Identifier of the parent reservation request id
   * @return reservationRequestId
  **/
  
  public String getReservationRequestId() {
    return reservationRequestId;
  }

  public void setReservationRequestId(String reservationRequestId) {
    this.reservationRequestId = reservationRequestId;
  }

  public Allocation serviceLevelCode(String serviceLevelCode) {
    this.serviceLevelCode = serviceLevelCode;
    return this;
  }

   /**
   * Service level used for scheduling the allocation
   * @return serviceLevelCode
  **/
  
  public String getServiceLevelCode() {
    return serviceLevelCode;
  }

  public void setServiceLevelCode(String serviceLevelCode) {
    this.serviceLevelCode = serviceLevelCode;
  }

  public Allocation shipFromLocationId(String shipFromLocationId) {
    this.shipFromLocationId = shipFromLocationId;
    return this;
  }

   /**
   * Location at which the item is allocated
   * @return shipFromLocationId
  **/
  
  public String getShipFromLocationId() {
    return shipFromLocationId;
  }

  public void setShipFromLocationId(String shipFromLocationId) {
    this.shipFromLocationId = shipFromLocationId;
  }

  public Allocation shipToLocationId(String shipToLocationId) {
    this.shipToLocationId = shipToLocationId;
    return this;
  }

   /**
   * Ship-to location ID, in case of ship to store, pick up in store, or any flow where the items are shipping to a destination which is configured as a location in the network
   * @return shipToLocationId
  **/
  
  public String getShipToLocationId() {
    return shipToLocationId;
  }

  public void setShipToLocationId(String shipToLocationId) {
    this.shipToLocationId = shipToLocationId;
  }

  public Allocation shipViaId(String shipViaId) {
    this.shipViaId = shipViaId;
    return this;
  }

   /**
   * Ship via which is requested to fulfill the order line. Ship via is a unique combination of carrier, mode, and service level.
   * @return shipViaId
  **/
  
  public String getShipViaId() {
    return shipViaId;
  }

  public void setShipViaId(String shipViaId) {
    this.shipViaId = shipViaId;
  }

  public Allocation status(AllocationStatusId status) {
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  
  public AllocationStatusId getStatus() {
    return status;
  }

  public void setStatus(AllocationStatusId status) {
    this.status = status;
  }

  public Allocation substitutionRatio(Double substitutionRatio) {
    this.substitutionRatio = substitutionRatio;
    return this;
  }

   /**
   * Ratio of the substituted item to the ordered item
   * minimum: 0
   * maximum: 999999999999.9999
   * @return substitutionRatio
  **/
  
  public Double getSubstitutionRatio() {
    return substitutionRatio;
  }

  public void setSubstitutionRatio(Double substitutionRatio) {
    this.substitutionRatio = substitutionRatio;
  }

  public Allocation substitutionTypeId(String substitutionTypeId) {
    this.substitutionTypeId = substitutionTypeId;
    return this;
  }

   /**
   * Type of substitution being used for this order line
   * @return substitutionTypeId
  **/
  
  public String getSubstitutionTypeId() {
    return substitutionTypeId;
  }

  public void setSubstitutionTypeId(String substitutionTypeId) {
    this.substitutionTypeId = substitutionTypeId;
  }

  public Allocation UOM(String UOM) {
    this.UOM = UOM;
    return this;
  }

   /**
   * Unit of measure (UOM) of the allocated quantity
   * @return UOM
  **/
  
  public String getUOM() {
    return UOM;
  }

  public void setUOM(String UOM) {
    this.UOM = UOM;
  }

  public Allocation updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public Allocation updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public Allocation entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public Allocation localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Allocation allocation = (Allocation) o;
    return Objects.equals(this.actions, allocation.actions) &&
        Objects.equals(this.allocatedOn, allocation.allocatedOn) &&
        Objects.equals(this.allocationDependencyId, allocation.allocationDependencyId) &&
        Objects.equals(this.allocationId, allocation.allocationId) &&
        Objects.equals(this.allocationType, allocation.allocationType) &&
        Objects.equals(this.asnDetailId, allocation.asnDetailId) &&
        Objects.equals(this.asnId, allocation.asnId) &&
        Objects.equals(this.batchNumber, allocation.batchNumber) &&
        Objects.equals(this.calculatedValues, allocation.calculatedValues) &&
        Objects.equals(this.carrierCode, allocation.carrierCode) &&
        Objects.equals(this.committedDeliveryDate, allocation.committedDeliveryDate) &&
        Objects.equals(this.committedShipDate, allocation.committedShipDate) &&
        Objects.equals(this.countryOfOrigin, allocation.countryOfOrigin) &&
        Objects.equals(this.createdBy, allocation.createdBy) &&
        Objects.equals(this.createdTimestamp, allocation.createdTimestamp) &&
        Objects.equals(this.earliestDeliveryDate, allocation.earliestDeliveryDate) &&
        Objects.equals(this.earliestShipDate, allocation.earliestShipDate) &&
        Objects.equals(this.extended, allocation.extended) &&
        Objects.equals(this.groupId, allocation.groupId) &&
        Objects.equals(this.inventoryAttribute1, allocation.inventoryAttribute1) &&
        Objects.equals(this.inventoryAttribute2, allocation.inventoryAttribute2) &&
        Objects.equals(this.inventoryAttribute3, allocation.inventoryAttribute3) &&
        Objects.equals(this.inventoryAttribute4, allocation.inventoryAttribute4) &&
        Objects.equals(this.inventoryAttribute5, allocation.inventoryAttribute5) &&
        Objects.equals(this.inventorySegmentId, allocation.inventorySegmentId) &&
        Objects.equals(this.inventoryTypeId, allocation.inventoryTypeId) &&
        Objects.equals(this.isVirtual, allocation.isVirtual) &&
        Objects.equals(this.itemId, allocation.itemId) &&
        Objects.equals(this.latestReleaseDate, allocation.latestReleaseDate) &&
        Objects.equals(this.latestShipDate, allocation.latestShipDate) &&
        Objects.equals(this.localizedTo, allocation.localizedTo) &&
        Objects.equals(this.messages, allocation.messages) &&
        Objects.equals(this.orgId, allocation.orgId) &&
        Objects.equals(this.PK, allocation.PK) &&
        Objects.equals(this.poDetailId, allocation.poDetailId) &&
        Objects.equals(this.poId, allocation.poId) &&
        Objects.equals(this.productStatusId, allocation.productStatusId) &&
        Objects.equals(this.quantity, allocation.quantity) &&
        Objects.equals(this.reservationRequestDetailId, allocation.reservationRequestDetailId) &&
        Objects.equals(this.reservationRequestId, allocation.reservationRequestId) &&
        Objects.equals(this.serviceLevelCode, allocation.serviceLevelCode) &&
        Objects.equals(this.shipFromLocationId, allocation.shipFromLocationId) &&
        Objects.equals(this.shipToLocationId, allocation.shipToLocationId) &&
        Objects.equals(this.shipViaId, allocation.shipViaId) &&
        Objects.equals(this.status, allocation.status) &&
        Objects.equals(this.substitutionRatio, allocation.substitutionRatio) &&
        Objects.equals(this.substitutionTypeId, allocation.substitutionTypeId) &&
        Objects.equals(this.UOM, allocation.UOM) &&
        Objects.equals(this.updatedBy, allocation.updatedBy) &&
        Objects.equals(this.updatedTimestamp, allocation.updatedTimestamp) &&
        Objects.equals(this.entityName, allocation.entityName) &&
        Objects.equals(this.localize, allocation.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, allocatedOn, allocationDependencyId, allocationId, allocationType, asnDetailId, asnId, batchNumber, calculatedValues, carrierCode, committedDeliveryDate, committedShipDate, countryOfOrigin, createdBy, createdTimestamp, earliestDeliveryDate, earliestShipDate, extended, groupId, inventoryAttribute1, inventoryAttribute2, inventoryAttribute3, inventoryAttribute4, inventoryAttribute5, inventorySegmentId, inventoryTypeId, isVirtual, itemId, latestReleaseDate, latestShipDate, localizedTo, messages, orgId, PK, poDetailId, poId, productStatusId, quantity, reservationRequestDetailId, reservationRequestId, serviceLevelCode, shipFromLocationId, shipToLocationId, shipViaId, status, substitutionRatio, substitutionTypeId, UOM, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Allocation {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    allocatedOn: ").append(toIndentedString(allocatedOn)).append("\n");
    sb.append("    allocationDependencyId: ").append(toIndentedString(allocationDependencyId)).append("\n");
    sb.append("    allocationId: ").append(toIndentedString(allocationId)).append("\n");
    sb.append("    allocationType: ").append(toIndentedString(allocationType)).append("\n");
    sb.append("    asnDetailId: ").append(toIndentedString(asnDetailId)).append("\n");
    sb.append("    asnId: ").append(toIndentedString(asnId)).append("\n");
    sb.append("    batchNumber: ").append(toIndentedString(batchNumber)).append("\n");
    sb.append("    calculatedValues: ").append(toIndentedString(calculatedValues)).append("\n");
    sb.append("    carrierCode: ").append(toIndentedString(carrierCode)).append("\n");
    sb.append("    committedDeliveryDate: ").append(toIndentedString(committedDeliveryDate)).append("\n");
    sb.append("    committedShipDate: ").append(toIndentedString(committedShipDate)).append("\n");
    sb.append("    countryOfOrigin: ").append(toIndentedString(countryOfOrigin)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    earliestDeliveryDate: ").append(toIndentedString(earliestDeliveryDate)).append("\n");
    sb.append("    earliestShipDate: ").append(toIndentedString(earliestShipDate)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    groupId: ").append(toIndentedString(groupId)).append("\n");
    sb.append("    inventoryAttribute1: ").append(toIndentedString(inventoryAttribute1)).append("\n");
    sb.append("    inventoryAttribute2: ").append(toIndentedString(inventoryAttribute2)).append("\n");
    sb.append("    inventoryAttribute3: ").append(toIndentedString(inventoryAttribute3)).append("\n");
    sb.append("    inventoryAttribute4: ").append(toIndentedString(inventoryAttribute4)).append("\n");
    sb.append("    inventoryAttribute5: ").append(toIndentedString(inventoryAttribute5)).append("\n");
    sb.append("    inventorySegmentId: ").append(toIndentedString(inventorySegmentId)).append("\n");
    sb.append("    inventoryTypeId: ").append(toIndentedString(inventoryTypeId)).append("\n");
    sb.append("    isVirtual: ").append(toIndentedString(isVirtual)).append("\n");
    sb.append("    itemId: ").append(toIndentedString(itemId)).append("\n");
    sb.append("    latestReleaseDate: ").append(toIndentedString(latestReleaseDate)).append("\n");
    sb.append("    latestShipDate: ").append(toIndentedString(latestShipDate)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    poDetailId: ").append(toIndentedString(poDetailId)).append("\n");
    sb.append("    poId: ").append(toIndentedString(poId)).append("\n");
    sb.append("    productStatusId: ").append(toIndentedString(productStatusId)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    reservationRequestDetailId: ").append(toIndentedString(reservationRequestDetailId)).append("\n");
    sb.append("    reservationRequestId: ").append(toIndentedString(reservationRequestId)).append("\n");
    sb.append("    serviceLevelCode: ").append(toIndentedString(serviceLevelCode)).append("\n");
    sb.append("    shipFromLocationId: ").append(toIndentedString(shipFromLocationId)).append("\n");
    sb.append("    shipToLocationId: ").append(toIndentedString(shipToLocationId)).append("\n");
    sb.append("    shipViaId: ").append(toIndentedString(shipViaId)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    substitutionRatio: ").append(toIndentedString(substitutionRatio)).append("\n");
    sb.append("    substitutionTypeId: ").append(toIndentedString(substitutionTypeId)).append("\n");
    sb.append("    UOM: ").append(toIndentedString(UOM)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

