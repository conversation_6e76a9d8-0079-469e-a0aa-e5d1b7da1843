/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * TransactionReferenceTypeId
 */
public class TransactionReferenceTypeId {
  public static final String SERIALIZED_NAME_TRANSACTION_REFERENCE_TYPE_ID = "TransactionReferenceTypeId";
  @SerializedName(SERIALIZED_NAME_TRANSACTION_REFERENCE_TYPE_ID)
  private String transactionReferenceTypeId;

  public TransactionReferenceTypeId transactionReferenceTypeId(String transactionReferenceTypeId) {
    this.transactionReferenceTypeId = transactionReferenceTypeId;
    return this;
  }

   /**
   * Id of the referenced transaction type
   * @return transactionReferenceTypeId
  **/
  
  public String getTransactionReferenceTypeId() {
    return transactionReferenceTypeId;
  }

  public void setTransactionReferenceTypeId(String transactionReferenceTypeId) {
    this.transactionReferenceTypeId = transactionReferenceTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TransactionReferenceTypeId transactionReferenceTypeId = (TransactionReferenceTypeId) o;
    return Objects.equals(this.transactionReferenceTypeId, transactionReferenceTypeId.transactionReferenceTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(transactionReferenceTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TransactionReferenceTypeId {\n");
    
    sb.append("    transactionReferenceTypeId: ").append(toIndentedString(transactionReferenceTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

