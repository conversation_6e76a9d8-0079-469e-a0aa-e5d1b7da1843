package uk.co.flexi.sdk.oms.mao.client.gson;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.threeten.bp.LocalDate;
import org.threeten.bp.OffsetDateTime;

public class GsonFactoryBean {

    private static final String MAO_DATE_FORMAT = "yyyy'-'MM'-'dd";
    private static final String MAO_DATE_TIME_FORMAT = "yyyy'-'MM'-'dd'T'HH':'mm':'ss";

    public Gson getObject() {
        return new GsonBuilder()
                .registerTypeAdapter(LocalDate.class, new LocalDateAdapter(MAO_DATE_FORMAT))
                .registerTypeAdapter(OffsetDateTime.class, new OffSetDateTimeAdapter(MAO_DATE_TIME_FORMAT))
                .create();
    }

    public static Gson getInstance() {
        return new GsonFactoryBean().getObject();
    }
}
