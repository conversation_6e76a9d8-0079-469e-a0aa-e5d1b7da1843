/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * InvoiceStatusId
 */
public class InvoiceStatusId {
  public static final String SERIALIZED_NAME_STATUS_ID = "StatusId";
  @SerializedName(SERIALIZED_NAME_STATUS_ID)
  private String statusId;

  public InvoiceStatusId statusId(String statusId) {
    this.statusId = statusId;
    return this;
  }

   /**
   * Unique identifier of the Invoice Status Id
   * @return statusId
  **/
  
  public String getStatusId() {
    return statusId;
  }

  public void setStatusId(String statusId) {
    this.statusId = statusId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvoiceStatusId invoiceStatusId = (InvoiceStatusId) o;
    return Objects.equals(this.statusId, invoiceStatusId.statusId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(statusId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvoiceStatusId {\n");
    
    sb.append("    statusId: ").append(toIndentedString(statusId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

