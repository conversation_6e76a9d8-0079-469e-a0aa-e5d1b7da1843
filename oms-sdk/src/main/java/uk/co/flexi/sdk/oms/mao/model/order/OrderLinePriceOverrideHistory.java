/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderLinePriceOverrideHistory
 */
public class OrderLinePriceOverrideHistory {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_COMMENTS = "Comments";
  @SerializedName(SERIALIZED_NAME_COMMENTS)
  private String comments;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IS_PRICE_OVERRIDDEN = "IsPriceOverridden";
  @SerializedName(SERIALIZED_NAME_IS_PRICE_OVERRIDDEN)
  private Boolean isPriceOverridden;

  public static final String SERIALIZED_NAME_ITEM_ID = "ItemId";
  @SerializedName(SERIALIZED_NAME_ITEM_ID)
  private String itemId;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PREVIOUS_PRICE = "PreviousPrice";
  @SerializedName(SERIALIZED_NAME_PREVIOUS_PRICE)
  private BigDecimal previousPrice;

  public static final String SERIALIZED_NAME_PRICE_OVERRIDE_ID = "PriceOverrideId";
  @SerializedName(SERIALIZED_NAME_PRICE_OVERRIDE_ID)
  private String priceOverrideId;

  public static final String SERIALIZED_NAME_REASON = "Reason";
  @SerializedName(SERIALIZED_NAME_REASON)
  private ReasonId reason = null;

  public static final String SERIALIZED_NAME_TRANSLATIONS = "Translations";
  @SerializedName(SERIALIZED_NAME_TRANSLATIONS)
  private Map<String, Map<String, String>> translations = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_PRICE = "UpdatedPrice";
  @SerializedName(SERIALIZED_NAME_UPDATED_PRICE)
  private BigDecimal updatedPrice;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public OrderLinePriceOverrideHistory actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderLinePriceOverrideHistory putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderLinePriceOverrideHistory comments(String comments) {
    this.comments = comments;
    return this;
  }

   /**
   * Price Overridden Comments for the order line, if price has been overridden.
   * @return comments
  **/
  
  public String getComments() {
    return comments;
  }

  public void setComments(String comments) {
    this.comments = comments;
  }

  public OrderLinePriceOverrideHistory createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderLinePriceOverrideHistory createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderLinePriceOverrideHistory extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderLinePriceOverrideHistory isPriceOverridden(Boolean isPriceOverridden) {
    this.isPriceOverridden = isPriceOverridden;
    return this;
  }

   /**
   * If true then indicate price has been overridden and if false then indicate price override has been removed.
   * @return isPriceOverridden
  **/
  
  public Boolean getIsPriceOverridden() {
    return isPriceOverridden;
  }

  public void setIsPriceOverridden(Boolean isPriceOverridden) {
    this.isPriceOverridden = isPriceOverridden;
  }

  public OrderLinePriceOverrideHistory itemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

   /**
   * Item id which has gone through price override.
   * @return itemId
  **/
  
  public String getItemId() {
    return itemId;
  }

  public void setItemId(String itemId) {
    this.itemId = itemId;
  }

  public OrderLinePriceOverrideHistory messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderLinePriceOverrideHistory orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderLinePriceOverrideHistory PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderLinePriceOverrideHistory previousPrice(BigDecimal previousPrice) {
    this.previousPrice = previousPrice;
    return this;
  }

   /**
   * If the price override has been applied and unit price change then this the previous price of the Item.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return previousPrice
  **/
  
  public BigDecimal getPreviousPrice() {
    return previousPrice;
  }

  public void setPreviousPrice(BigDecimal previousPrice) {
    this.previousPrice = previousPrice;
  }

  public OrderLinePriceOverrideHistory priceOverrideId(String priceOverrideId) {
    this.priceOverrideId = priceOverrideId;
    return this;
  }

   /**
   * Unique identifier of the charge
   * @return priceOverrideId
  **/
  
  public String getPriceOverrideId() {
    return priceOverrideId;
  }

  public void setPriceOverrideId(String priceOverrideId) {
    this.priceOverrideId = priceOverrideId;
  }

  public OrderLinePriceOverrideHistory reason(ReasonId reason) {
    this.reason = reason;
    return this;
  }

   /**
   * Get reason
   * @return reason
  **/
  
  public ReasonId getReason() {
    return reason;
  }

  public void setReason(ReasonId reason) {
    this.reason = reason;
  }

  public OrderLinePriceOverrideHistory translations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
    return this;
  }

  public OrderLinePriceOverrideHistory putTranslationsItem(String key, Map<String, String> translationsItem) {
    if (this.translations == null) {
      this.translations = new HashMap<String, Map<String, String>>();
    }
    this.translations.put(key, translationsItem);
    return this;
  }

   /**
   * Get translations
   * @return translations
  **/
  
  public Map<String, Map<String, String>> getTranslations() {
    return translations;
  }

  public void setTranslations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
  }

  public OrderLinePriceOverrideHistory updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderLinePriceOverrideHistory updatedPrice(BigDecimal updatedPrice) {
    this.updatedPrice = updatedPrice;
    return this;
  }

   /**
   * If the price override has been applied and unit price change then this the final price of the Item.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return updatedPrice
  **/
  
  public BigDecimal getUpdatedPrice() {
    return updatedPrice;
  }

  public void setUpdatedPrice(BigDecimal updatedPrice) {
    this.updatedPrice = updatedPrice;
  }

  public OrderLinePriceOverrideHistory updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderLinePriceOverrideHistory entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderLinePriceOverrideHistory orderLinePriceOverrideHistory = (OrderLinePriceOverrideHistory) o;
    return Objects.equals(this.actions, orderLinePriceOverrideHistory.actions) &&
        Objects.equals(this.comments, orderLinePriceOverrideHistory.comments) &&
        Objects.equals(this.createdBy, orderLinePriceOverrideHistory.createdBy) &&
        Objects.equals(this.createdTimestamp, orderLinePriceOverrideHistory.createdTimestamp) &&
        Objects.equals(this.extended, orderLinePriceOverrideHistory.extended) &&
        Objects.equals(this.isPriceOverridden, orderLinePriceOverrideHistory.isPriceOverridden) &&
        Objects.equals(this.itemId, orderLinePriceOverrideHistory.itemId) &&
        Objects.equals(this.messages, orderLinePriceOverrideHistory.messages) &&
        Objects.equals(this.orgId, orderLinePriceOverrideHistory.orgId) &&
        Objects.equals(this.PK, orderLinePriceOverrideHistory.PK) &&
        Objects.equals(this.previousPrice, orderLinePriceOverrideHistory.previousPrice) &&
        Objects.equals(this.priceOverrideId, orderLinePriceOverrideHistory.priceOverrideId) &&
        Objects.equals(this.reason, orderLinePriceOverrideHistory.reason) &&
        Objects.equals(this.translations, orderLinePriceOverrideHistory.translations) &&
        Objects.equals(this.updatedBy, orderLinePriceOverrideHistory.updatedBy) &&
        Objects.equals(this.updatedPrice, orderLinePriceOverrideHistory.updatedPrice) &&
        Objects.equals(this.updatedTimestamp, orderLinePriceOverrideHistory.updatedTimestamp) &&
        Objects.equals(this.entityName, orderLinePriceOverrideHistory.entityName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, comments, createdBy, createdTimestamp, extended, isPriceOverridden, itemId, messages, orgId, PK, previousPrice, priceOverrideId, reason, translations, updatedBy, updatedPrice, updatedTimestamp, entityName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderLinePriceOverrideHistory {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    comments: ").append(toIndentedString(comments)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    isPriceOverridden: ").append(toIndentedString(isPriceOverridden)).append("\n");
    sb.append("    itemId: ").append(toIndentedString(itemId)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    previousPrice: ").append(toIndentedString(previousPrice)).append("\n");
    sb.append("    priceOverrideId: ").append(toIndentedString(priceOverrideId)).append("\n");
    sb.append("    reason: ").append(toIndentedString(reason)).append("\n");
    sb.append("    translations: ").append(toIndentedString(translations)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedPrice: ").append(toIndentedString(updatedPrice)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

