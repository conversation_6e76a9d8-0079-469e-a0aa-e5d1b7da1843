/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ParentOrderPaymentMethod
 */
public class ParentOrderPaymentMethod {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_AMOUNT = "Amount";
  @SerializedName(SERIALIZED_NAME_AMOUNT)
  private BigDecimal amount;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_ORDER_ID = "ParentOrderId";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER_ID)
  private String parentOrderId;

  public static final String SERIALIZED_NAME_PARENT_PAYMENT_GROUP_ID = "ParentPaymentGroupId";
  @SerializedName(SERIALIZED_NAME_PARENT_PAYMENT_GROUP_ID)
  private String parentPaymentGroupId;

  public static final String SERIALIZED_NAME_PARENT_PAYMENT_METHOD_ID = "ParentPaymentMethodId";
  @SerializedName(SERIALIZED_NAME_PARENT_PAYMENT_METHOD_ID)
  private String parentPaymentMethodId;

  public static final String SERIALIZED_NAME_TRANSLATIONS = "Translations";
  @SerializedName(SERIALIZED_NAME_TRANSLATIONS)
  private Map<String, Map<String, String>> translations = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public ParentOrderPaymentMethod actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ParentOrderPaymentMethod putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ParentOrderPaymentMethod amount(BigDecimal amount) {
    this.amount = amount;
    return this;
  }

   /**
   * Amount of return credit which has been borrowed from the parent order. For example, if a $30 cash refund is issued on a return order, and the parent order was paid in $50 cash, then the return credit amount is $30. This creates a return credit transaction for $30 against the $50 cash settlement, leaving $20 available to be refunded in future transactions.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return amount
  **/
  
  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public ParentOrderPaymentMethod createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ParentOrderPaymentMethod createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ParentOrderPaymentMethod extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ParentOrderPaymentMethod messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ParentOrderPaymentMethod orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public ParentOrderPaymentMethod PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ParentOrderPaymentMethod parentOrderId(String parentOrderId) {
    this.parentOrderId = parentOrderId;
    return this;
  }

   /**
   * Identifier of the order for which payment needs to be processed. When payment processes a payment request, payment makes a get order details call for this order id and updates the payment summary accordingly.
   * @return parentOrderId
  **/
  
  public String getParentOrderId() {
    return parentOrderId;
  }

  public void setParentOrderId(String parentOrderId) {
    this.parentOrderId = parentOrderId;
  }

  public ParentOrderPaymentMethod parentPaymentGroupId(String parentPaymentGroupId) {
    this.parentPaymentGroupId = parentPaymentGroupId;
    return this;
  }

   /**
   * Identifier of the order payment group, or group of items within an order, for which payment needs to be processed.
   * @return parentPaymentGroupId
  **/
  
  public String getParentPaymentGroupId() {
    return parentPaymentGroupId;
  }

  public void setParentPaymentGroupId(String parentPaymentGroupId) {
    this.parentPaymentGroupId = parentPaymentGroupId;
  }

  public ParentOrderPaymentMethod parentPaymentMethodId(String parentPaymentMethodId) {
    this.parentPaymentMethodId = parentPaymentMethodId;
    return this;
  }

   /**
   * Identifies the parent payment method from which the return credit has been borrowed. For example, if a return is created against an order which has payment method id PM01, then the return order payment method will reference PaymentMethodId PM01 if return credit was issued via a standalone return.
   * @return parentPaymentMethodId
  **/
  
  public String getParentPaymentMethodId() {
    return parentPaymentMethodId;
  }

  public void setParentPaymentMethodId(String parentPaymentMethodId) {
    this.parentPaymentMethodId = parentPaymentMethodId;
  }

  public ParentOrderPaymentMethod translations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
    return this;
  }

  public ParentOrderPaymentMethod putTranslationsItem(String key, Map<String, String> translationsItem) {
    if (this.translations == null) {
      this.translations = new HashMap<String, Map<String, String>>();
    }
    this.translations.put(key, translationsItem);
    return this;
  }

   /**
   * Get translations
   * @return translations
  **/
  
  public Map<String, Map<String, String>> getTranslations() {
    return translations;
  }

  public void setTranslations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
  }

  public ParentOrderPaymentMethod updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ParentOrderPaymentMethod updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ParentOrderPaymentMethod entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ParentOrderPaymentMethod parentOrderPaymentMethod = (ParentOrderPaymentMethod) o;
    return Objects.equals(this.actions, parentOrderPaymentMethod.actions) &&
        Objects.equals(this.amount, parentOrderPaymentMethod.amount) &&
        Objects.equals(this.createdBy, parentOrderPaymentMethod.createdBy) &&
        Objects.equals(this.createdTimestamp, parentOrderPaymentMethod.createdTimestamp) &&
        Objects.equals(this.extended, parentOrderPaymentMethod.extended) &&
        Objects.equals(this.messages, parentOrderPaymentMethod.messages) &&
        Objects.equals(this.orgId, parentOrderPaymentMethod.orgId) &&
        Objects.equals(this.PK, parentOrderPaymentMethod.PK) &&
        Objects.equals(this.parentOrderId, parentOrderPaymentMethod.parentOrderId) &&
        Objects.equals(this.parentPaymentGroupId, parentOrderPaymentMethod.parentPaymentGroupId) &&
        Objects.equals(this.parentPaymentMethodId, parentOrderPaymentMethod.parentPaymentMethodId) &&
        Objects.equals(this.translations, parentOrderPaymentMethod.translations) &&
        Objects.equals(this.updatedBy, parentOrderPaymentMethod.updatedBy) &&
        Objects.equals(this.updatedTimestamp, parentOrderPaymentMethod.updatedTimestamp) &&
        Objects.equals(this.entityName, parentOrderPaymentMethod.entityName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, amount, createdBy, createdTimestamp, extended, messages, orgId, PK, parentOrderId, parentPaymentGroupId, parentPaymentMethodId, translations, updatedBy, updatedTimestamp, entityName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ParentOrderPaymentMethod {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentOrderId: ").append(toIndentedString(parentOrderId)).append("\n");
    sb.append("    parentPaymentGroupId: ").append(toIndentedString(parentPaymentGroupId)).append("\n");
    sb.append("    parentPaymentMethodId: ").append(toIndentedString(parentPaymentMethodId)).append("\n");
    sb.append("    translations: ").append(toIndentedString(translations)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

