package uk.co.flexi.sdk.oms.mao.client.config;

import uk.co.flexi.sdk.oms.model.OMSClientConfig;

public class MaoConfig implements OMSClientConfig {


    private final String authUrl;
    private final String basePath;
    private final String username;
    private final String password;
    private final String basicAuthUser;
    private final String basicAuthPassword;

    private final String searchOrganizations;

    private final String productOrganization;

    public String getAuthUrl() {
        return authUrl;
    }

    public String getBasePath() {
        return basePath;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public String getBasicAuthUser() {
        return basicAuthUser;
    }

    public String getBasicAuthPassword() {
        return basicAuthPassword;
    }

    public String getSearchOrganizations() {
        return searchOrganizations;
    }

    public String getProductOrganization() {
        return productOrganization;
    }

    public MaoConfig(
            String authUrl, String basePath,
            String basicAuthUser, String basicAuthPassword,
            String username, String password, String searchOrganizations,
            String productOrganizations
    ) {
        this.authUrl = authUrl;
        this.basePath = basePath;
        this.basicAuthUser = basicAuthUser;
        this.basicAuthPassword = basicAuthPassword;
        this.username = username;
        this.password = password;
        this.searchOrganizations = searchOrganizations;
        this.productOrganization = productOrganizations;
    }
}
