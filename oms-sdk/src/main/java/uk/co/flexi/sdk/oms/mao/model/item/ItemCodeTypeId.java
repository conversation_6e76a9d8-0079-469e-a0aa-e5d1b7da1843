package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * ItemCodeTypeId
 */
public class ItemCodeTypeId {
  public static final String SERIALIZED_NAME_CODE_TYPE_ID = "CodeTypeId";
  @SerializedName(SERIALIZED_NAME_CODE_TYPE_ID)
  private String codeTypeId;

  public ItemCodeTypeId codeTypeId(String codeTypeId) {
    this.codeTypeId = codeTypeId;
    return this;
  }

   /**
   * Unique id for Primary UPC, Case UPC, GTIN, EAN, Shared UPC
   * @return codeTypeId
  **/
  public String getCodeTypeId() {
    return codeTypeId;
  }

  public void setCodeTypeId(String codeTypeId) {
    this.codeTypeId = codeTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ItemCodeTypeId itemCodeTypeId = (ItemCodeTypeId) o;
    return Objects.equals(this.codeTypeId, itemCodeTypeId.codeTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(codeTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ItemCodeTypeId {\n");
    
    sb.append("    codeTypeId: ").append(toIndentedString(codeTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

