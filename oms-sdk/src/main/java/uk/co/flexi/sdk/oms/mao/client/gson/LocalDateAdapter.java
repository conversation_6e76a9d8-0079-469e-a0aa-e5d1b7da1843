package uk.co.flexi.sdk.oms.mao.client.gson;

import com.google.gson.*;
import org.threeten.bp.LocalDate;
import org.threeten.bp.format.DateTimeFormatter;

import java.lang.reflect.Type;

public class LocalDateAdapter implements JsonSerializer<LocalDate>, JsonDeserializer<LocalDate> {

    private final String pattern;

    public LocalDateAdapter(String pattern) {
        this.pattern = pattern;
    }

    public JsonElement serialize(LocalDate date, Type typeOfSrc, JsonSerializationContext context) {
        return new JsonPrimitive(date.format(DateTimeFormatter.ofPattern(pattern))); // "yyyy-mm-dd"
    }

    @Override
    public LocalDate deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext jsonDeserializationContext) throws JsonParseException {
        String dateValue = jsonElement.toString().replaceAll("\"", "");
        return DateTimeFormatter.ofPattern(pattern).parse(dateValue, LocalDate::from);
    }
}
