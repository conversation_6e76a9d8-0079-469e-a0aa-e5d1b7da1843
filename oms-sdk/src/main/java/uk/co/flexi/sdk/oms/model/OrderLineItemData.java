package uk.co.flexi.sdk.oms.model;

import java.math.BigDecimal;
import java.util.List;

public class OrderLineItemData {

    private String sku;

    private String style;
    private String name;

    private String color;

    private String size;

    private String description;

    private String productClass;

    private String departmentName;

    private String productSubclass;

    private String season;

    private String brand;

    private double quantity;

    private String currencyCode;

    private BigDecimal unitPrice;

    private List<String> images;

    public String getSku() {
        return sku;
    }

    public OrderLineItemData setSku(String sku) {
        this.sku = sku;
        return this;
    }

    public String getName() {
        return name;
    }

    public OrderLineItemData setName(String name) {
        this.name = name;
        return this;
    }

    public String getColor() {
        return color;
    }

    public OrderLineItemData setColor(String color) {
        this.color = color;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public OrderLineItemData setDescription(String description) {
        this.description = description;
        return this;
    }

    public double getQuantity() {
        return quantity;
    }

    public OrderLineItemData setQuantity(double quantity) {
        this.quantity = quantity;
        return this;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public OrderLineItemData setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
        return this;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public OrderLineItemData setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
        return this;
    }

    public List<String> getImages() {
        return images;
    }

    public OrderLineItemData setImages(List<String> images) {
        this.images = images;
        return this;
    }

    public String getStyle() {
        return style;
    }

    public OrderLineItemData setStyle(String style) {
        this.style = style;
        return this;
    }

    public String getSize() {
        return size;
    }

    public OrderLineItemData setSize(String size) {
        this.size = size;
        return this;
    }

    public String getProductClass() {
        return productClass;
    }

    public OrderLineItemData setProductClass(String productClass) {
        this.productClass = productClass;
        return this;
    }

    public String getProductSubclass() {
        return productSubclass;
    }

    public OrderLineItemData setProductSubclass(String productSubclass) {
        this.productSubclass = productSubclass;
        return this;
    }

    public String getSeason() {
        return season;
    }

    public OrderLineItemData setSeason(String season) {
        this.season = season;
        return this;
    }

    public String getBrand() {
        return brand;
    }

    public OrderLineItemData setBrand(String brand) {
        this.brand = brand;
        return this;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public OrderLineItemData setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
        return this;
    }

    @Override
    public String toString() {
        return "OrderLineItemData{" +
                "sku='" + sku + '\'' +
                ", style='" + style + '\'' +
                ", name='" + name + '\'' +
                ", color='" + color + '\'' +
                ", size='" + size + '\'' +
                ", description='" + description + '\'' +
                ", productClass='" + productClass + '\'' +
                ", departmentName='" + departmentName + '\'' +
                ", productSubclass='" + productSubclass + '\'' +
                ", season='" + season + '\'' +
                ", brand='" + brand + '\'' +
                ", quantity=" + quantity +
                ", images=" + images +
                '}';
    }
}