/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ReturnConfig
 */
public class ReturnConfig {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_AUTO_APPROVE_RECEIPT_NOT_EXPECTED = "AutoApproveReceiptNotExpected";
  @SerializedName(SERIALIZED_NAME_AUTO_APPROVE_RECEIPT_NOT_EXPECTED)
  private Boolean autoApproveReceiptNotExpected;

  public static final String SERIALIZED_NAME_AUTO_RESOLVE_QUANTITY_VARIANCE = "AutoResolveQuantityVariance";
  @SerializedName(SERIALIZED_NAME_AUTO_RESOLVE_QUANTITY_VARIANCE)
  private Boolean autoResolveQuantityVariance;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DAYS_FOR_ELIGIBLE_RETURN = "DaysForEligibleReturn";
  @SerializedName(SERIALIZED_NAME_DAYS_FOR_ELIGIBLE_RETURN)
  private Long daysForEligibleReturn;

  public static final String SERIALIZED_NAME_EXCHANGE_HOLD_TYPE = "ExchangeHoldType";
  @SerializedName(SERIALIZED_NAME_EXCHANGE_HOLD_TYPE)
  private String exchangeHoldType;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MAX_STATUS_ELIGIBLE_FOR_RETURN = "MaxStatusEligibleForReturn";
  @SerializedName(SERIALIZED_NAME_MAX_STATUS_ELIGIBLE_FOR_RETURN)
  private String maxStatusEligibleForReturn;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_MIN_STATUS_ELIGIBLE_FOR_RETURN = "MinStatusEligibleForReturn";
  @SerializedName(SERIALIZED_NAME_MIN_STATUS_ELIGIBLE_FOR_RETURN)
  private String minStatusEligibleForReturn;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PRICE_RANK = "PriceRank";
  @SerializedName(SERIALIZED_NAME_PRICE_RANK)
  private Integer priceRank;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_RETUN_LABEL_FILE_NAME = "RetunLabelFileName";
  @SerializedName(SERIALIZED_NAME_RETUN_LABEL_FILE_NAME)
  private String retunLabelFileName;

  public static final String SERIALIZED_NAME_RETURN_LABELDOCUMENT_ID = "ReturnLabeldocumentId";
  @SerializedName(SERIALIZED_NAME_RETURN_LABELDOCUMENT_ID)
  private String returnLabeldocumentId;

  public static final String SERIALIZED_NAME_RETURN_MATCH_AGE_POLICY = "ReturnMatchAgePolicy";
  @SerializedName(SERIALIZED_NAME_RETURN_MATCH_AGE_POLICY)
  private ReturnMatchAgePolicy returnMatchAgePolicy = null;

  public static final String SERIALIZED_NAME_RETURN_MATCH_PRICE_POLICY_ID = "ReturnMatchPricePolicyId";
  @SerializedName(SERIALIZED_NAME_RETURN_MATCH_PRICE_POLICY_ID)
  private ReturnMatchPricePolicy returnMatchPricePolicyId = null;

  public static final String SERIALIZED_NAME_STATUS_TO_UNHOLD_EXCHANGES = "StatusToUnholdExchanges";
  @SerializedName(SERIALIZED_NAME_STATUS_TO_UNHOLD_EXCHANGES)
  private String statusToUnholdExchanges;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public ReturnConfig actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ReturnConfig putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ReturnConfig autoApproveReceiptNotExpected(Boolean autoApproveReceiptNotExpected) {
    this.autoApproveReceiptNotExpected = autoApproveReceiptNotExpected;
    return this;
  }

   /**
   * Indicates if return lines where isReceiptExpected is false require manual approval or should be automatically approved for a refund/return credit. Applicable only to return lines with delivery method ship to return center where isReceiptExpected is false. If set to true, then on confirmation these order lines move to Returned status and a return invoice can be created. If set to false, then on confirmation these order lines move to Pending Approval status. To move the line from Pending Approval to Returned and process the invoice, a user manually approves the line. If a user does not approve the line, then no refund/return credit is issued.
   * @return autoApproveReceiptNotExpected
  **/
  
  public Boolean getAutoApproveReceiptNotExpected() {
    return autoApproveReceiptNotExpected;
  }

  public void setAutoApproveReceiptNotExpected(Boolean autoApproveReceiptNotExpected) {
    this.autoApproveReceiptNotExpected = autoApproveReceiptNotExpected;
  }

  public ReturnConfig autoResolveQuantityVariance(Boolean autoResolveQuantityVariance) {
    this.autoResolveQuantityVariance = autoResolveQuantityVariance;
    return this;
  }

   /**
   * Indicates if return lines having Quantity Variance, whether variance would be resolve automatically or put on Hold for manual approval. If value has been set &#39;false&#39; and any return line qualify for quantity variance then line will be put on &#39;QuantityVariance&#39; Hold, which will block refund invoice creation. If value has been set &#39;true&#39; and any return line qualify for quantity variance then line won&#39;t be put on hold and refund invoice would be generated based on final quantity.
   * @return autoResolveQuantityVariance
  **/
  
  public Boolean getAutoResolveQuantityVariance() {
    return autoResolveQuantityVariance;
  }

  public void setAutoResolveQuantityVariance(Boolean autoResolveQuantityVariance) {
    this.autoResolveQuantityVariance = autoResolveQuantityVariance;
  }

  public ReturnConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ReturnConfig createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ReturnConfig daysForEligibleReturn(Long daysForEligibleReturn) {
    this.daysForEligibleReturn = daysForEligibleReturn;
    return this;
  }

   /**
   * Indicates the number of days item will be eligible for return after it has been sold. For lines having delivery method as Store Sale it gets calculated from Order line creation date and for all the other delivery method it takes the fulfilment date of last unit.
   * minimum: 0
   * maximum: -8446744073709551617
   * @return daysForEligibleReturn
  **/
  
  public Long getDaysForEligibleReturn() {
    return daysForEligibleReturn;
  }

  public void setDaysForEligibleReturn(Long daysForEligibleReturn) {
    this.daysForEligibleReturn = daysForEligibleReturn;
  }

  public ReturnConfig exchangeHoldType(String exchangeHoldType) {
    this.exchangeHoldType = exchangeHoldType;
    return this;
  }

   /**
   * The type of hold which is applied when exchange lines are put on hold while waiting for return items to be received. Exchange lines are put on hold with this hold type, and the hold is resolved when all return quantity reaches the status configured in statusToUnHoldExchanges.
   * @return exchangeHoldType
  **/
  
  public String getExchangeHoldType() {
    return exchangeHoldType;
  }

  public void setExchangeHoldType(String exchangeHoldType) {
    this.exchangeHoldType = exchangeHoldType;
  }

  public ReturnConfig extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ReturnConfig localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public ReturnConfig maxStatusEligibleForReturn(String maxStatusEligibleForReturn) {
    this.maxStatusEligibleForReturn = maxStatusEligibleForReturn;
    return this;
  }

   /**
   * The maximum status of quantity detail records eligible for return. Any quantitiy details in a status less than or equal to this status and greater than or equal to the minStatusEligibleForReturn is included in the order line returnableQuantity attribute. The returnableQuantity attribute is present on the order web service response, but is not saved in the database. ReturnableQuantity is calculated as quantity eligible for return minus quantity already returned (in status 18000 - 18999).
   * @return maxStatusEligibleForReturn
  **/
  
  public String getMaxStatusEligibleForReturn() {
    return maxStatusEligibleForReturn;
  }

  public void setMaxStatusEligibleForReturn(String maxStatusEligibleForReturn) {
    this.maxStatusEligibleForReturn = maxStatusEligibleForReturn;
  }

  public ReturnConfig messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ReturnConfig minStatusEligibleForReturn(String minStatusEligibleForReturn) {
    this.minStatusEligibleForReturn = minStatusEligibleForReturn;
    return this;
  }

   /**
   * The minimum status of quantity detail records eligible for return. Any quantitiy details in a status greater than or equal to this status and less than or equal to the maxStatusEligibleForReturn is included in the order line returnableQuantity attribute. The returnableQuantity attribute is present on the order web service response, but is not saved in the database. ReturnableQuantity is calculated as quantity eligible for return minus quantity already returned (in status 18000 - 18999).
   * @return minStatusEligibleForReturn
  **/
  
  public String getMinStatusEligibleForReturn() {
    return minStatusEligibleForReturn;
  }

  public void setMinStatusEligibleForReturn(String minStatusEligibleForReturn) {
    this.minStatusEligibleForReturn = minStatusEligibleForReturn;
  }

  public ReturnConfig PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ReturnConfig priceRank(Integer priceRank) {
    this.priceRank = priceRank;
    return this;
  }

   /**
   *  Indicates the rank that will be used to match the item to matching orderlines from one or more receipts during a return. The rank will be used in conjunction with price policy/criteria to match the right orderline to the returned item. For example,Price rank 2 will match the orderline with 2nd highest or 2nd lowest selling price for the returned item.Selling price is calculated as OrderlinesubTotal - DiscountsOnItem. 
   * minimum: 0
   * maximum: 99999
   * @return priceRank
  **/
  
  public Integer getPriceRank() {
    return priceRank;
  }

  public void setPriceRank(Integer priceRank) {
    this.priceRank = priceRank;
  }

  public ReturnConfig profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public ReturnConfig retunLabelFileName(String retunLabelFileName) {
    this.retunLabelFileName = retunLabelFileName;
    return this;
  }

   /**
   * name of custome return label report
   * @return retunLabelFileName
  **/
  
  public String getRetunLabelFileName() {
    return retunLabelFileName;
  }

  public void setRetunLabelFileName(String retunLabelFileName) {
    this.retunLabelFileName = retunLabelFileName;
  }

  public ReturnConfig returnLabeldocumentId(String returnLabeldocumentId) {
    this.returnLabeldocumentId = returnLabeldocumentId;
    return this;
  }

   /**
   * identifier of document which we got it from docmanagement
   * @return returnLabeldocumentId
  **/
  
  public String getReturnLabeldocumentId() {
    return returnLabeldocumentId;
  }

  public void setReturnLabeldocumentId(String returnLabeldocumentId) {
    this.returnLabeldocumentId = returnLabeldocumentId;
  }

  public ReturnConfig returnMatchAgePolicy(ReturnMatchAgePolicy returnMatchAgePolicy) {
    this.returnMatchAgePolicy = returnMatchAgePolicy;
    return this;
  }

   /**
   * Get returnMatchAgePolicy
   * @return returnMatchAgePolicy
  **/
  
  public ReturnMatchAgePolicy getReturnMatchAgePolicy() {
    return returnMatchAgePolicy;
  }

  public void setReturnMatchAgePolicy(ReturnMatchAgePolicy returnMatchAgePolicy) {
    this.returnMatchAgePolicy = returnMatchAgePolicy;
  }

  public ReturnConfig returnMatchPricePolicyId(ReturnMatchPricePolicy returnMatchPricePolicyId) {
    this.returnMatchPricePolicyId = returnMatchPricePolicyId;
    return this;
  }

   /**
   * Get returnMatchPricePolicyId
   * @return returnMatchPricePolicyId
  **/
  
  public ReturnMatchPricePolicy getReturnMatchPricePolicyId() {
    return returnMatchPricePolicyId;
  }

  public void setReturnMatchPricePolicyId(ReturnMatchPricePolicy returnMatchPricePolicyId) {
    this.returnMatchPricePolicyId = returnMatchPricePolicyId;
  }

  public ReturnConfig statusToUnholdExchanges(String statusToUnholdExchanges) {
    this.statusToUnholdExchanges = statusToUnholdExchanges;
    return this;
  }

   /**
   * The status return items must reach in order for exchange items to be released for fulfillment. Exchange orders are put on hold if ship to return center lines exist and have not yet reached this status. This ensures the customer returns the items before exchange items ship. For example, if this status is set to Returned (18000), then an exchange order is on hold when a return line is created in Pending Return status (11000). When all return units move to Returned status (18000), then the &#39;ReturnItemsPending&#39; hold is resolved and the exchange items can be released.
   * @return statusToUnholdExchanges
  **/
  
  public String getStatusToUnholdExchanges() {
    return statusToUnholdExchanges;
  }

  public void setStatusToUnholdExchanges(String statusToUnholdExchanges) {
    this.statusToUnholdExchanges = statusToUnholdExchanges;
  }

  public ReturnConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ReturnConfig updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ReturnConfig rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReturnConfig returnConfig = (ReturnConfig) o;
    return Objects.equals(this.actions, returnConfig.actions) &&
        Objects.equals(this.autoApproveReceiptNotExpected, returnConfig.autoApproveReceiptNotExpected) &&
        Objects.equals(this.autoResolveQuantityVariance, returnConfig.autoResolveQuantityVariance) &&
        Objects.equals(this.createdBy, returnConfig.createdBy) &&
        Objects.equals(this.createdTimestamp, returnConfig.createdTimestamp) &&
        Objects.equals(this.daysForEligibleReturn, returnConfig.daysForEligibleReturn) &&
        Objects.equals(this.exchangeHoldType, returnConfig.exchangeHoldType) &&
        Objects.equals(this.extended, returnConfig.extended) &&
        Objects.equals(this.localizedTo, returnConfig.localizedTo) &&
        Objects.equals(this.maxStatusEligibleForReturn, returnConfig.maxStatusEligibleForReturn) &&
        Objects.equals(this.messages, returnConfig.messages) &&
        Objects.equals(this.minStatusEligibleForReturn, returnConfig.minStatusEligibleForReturn) &&
        Objects.equals(this.PK, returnConfig.PK) &&
        Objects.equals(this.priceRank, returnConfig.priceRank) &&
        Objects.equals(this.profileId, returnConfig.profileId) &&
        Objects.equals(this.retunLabelFileName, returnConfig.retunLabelFileName) &&
        Objects.equals(this.returnLabeldocumentId, returnConfig.returnLabeldocumentId) &&
        Objects.equals(this.returnMatchAgePolicy, returnConfig.returnMatchAgePolicy) &&
        Objects.equals(this.returnMatchPricePolicyId, returnConfig.returnMatchPricePolicyId) &&
        Objects.equals(this.statusToUnholdExchanges, returnConfig.statusToUnholdExchanges) &&
        Objects.equals(this.updatedBy, returnConfig.updatedBy) &&
        Objects.equals(this.updatedTimestamp, returnConfig.updatedTimestamp) &&
        Objects.equals(this.rootCause, returnConfig.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, autoApproveReceiptNotExpected, autoResolveQuantityVariance, createdBy, createdTimestamp, daysForEligibleReturn, exchangeHoldType, extended, localizedTo, maxStatusEligibleForReturn, messages, minStatusEligibleForReturn, PK, priceRank, profileId, retunLabelFileName, returnLabeldocumentId, returnMatchAgePolicy, returnMatchPricePolicyId, statusToUnholdExchanges, updatedBy, updatedTimestamp, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReturnConfig {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    autoApproveReceiptNotExpected: ").append(toIndentedString(autoApproveReceiptNotExpected)).append("\n");
    sb.append("    autoResolveQuantityVariance: ").append(toIndentedString(autoResolveQuantityVariance)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    daysForEligibleReturn: ").append(toIndentedString(daysForEligibleReturn)).append("\n");
    sb.append("    exchangeHoldType: ").append(toIndentedString(exchangeHoldType)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    maxStatusEligibleForReturn: ").append(toIndentedString(maxStatusEligibleForReturn)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    minStatusEligibleForReturn: ").append(toIndentedString(minStatusEligibleForReturn)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    priceRank: ").append(toIndentedString(priceRank)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    retunLabelFileName: ").append(toIndentedString(retunLabelFileName)).append("\n");
    sb.append("    returnLabeldocumentId: ").append(toIndentedString(returnLabeldocumentId)).append("\n");
    sb.append("    returnMatchAgePolicy: ").append(toIndentedString(returnMatchAgePolicy)).append("\n");
    sb.append("    returnMatchPricePolicyId: ").append(toIndentedString(returnMatchPricePolicyId)).append("\n");
    sb.append("    statusToUnholdExchanges: ").append(toIndentedString(statusToUnholdExchanges)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

