/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * CustomerIdentityDoc
 */
public class CustomerIdentityDoc {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CAPTURE_DATE = "CaptureDate";
  @SerializedName(SERIALIZED_NAME_CAPTURE_DATE)
  private OffsetDateTime captureDate;

  public static final String SERIALIZED_NAME_CAPTURE_REASON = "CaptureReason";
  @SerializedName(SERIALIZED_NAME_CAPTURE_REASON)
  private String captureReason;

  public static final String SERIALIZED_NAME_COUNTRY = "Country";
  @SerializedName(SERIALIZED_NAME_COUNTRY)
  private String country;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_CUSTOMER_IDENTITY_DOC_ID = "CustomerIdentityDocId";
  @SerializedName(SERIALIZED_NAME_CUSTOMER_IDENTITY_DOC_ID)
  private String customerIdentityDocId;

  public static final String SERIALIZED_NAME_DOC_TYPE = "DocType";
  @SerializedName(SERIALIZED_NAME_DOC_TYPE)
  private DocTypeId docType = null;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_ID_DOC_NUMBER = "IdDocNumber";
  @SerializedName(SERIALIZED_NAME_ID_DOC_NUMBER)
  private String idDocNumber;

  public static final String SERIALIZED_NAME_IDENTITY_DOC_TYPE = "IdentityDocType";
  @SerializedName(SERIALIZED_NAME_IDENTITY_DOC_TYPE)
  private IdentityDocTypeId identityDocType = null;

  public static final String SERIALIZED_NAME_IMAGE = "Image";
  @SerializedName(SERIALIZED_NAME_IMAGE)
  private String image;

  public static final String SERIALIZED_NAME_IMAGE_TYPE = "ImageType";
  @SerializedName(SERIALIZED_NAME_IMAGE_TYPE)
  private String imageType;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_ORDER = "ParentOrder";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER)
  private PrimaryKey parentOrder = null;

  public static final String SERIALIZED_NAME_STATE = "State";
  @SerializedName(SERIALIZED_NAME_STATE)
  private String state;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public CustomerIdentityDoc actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public CustomerIdentityDoc putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public CustomerIdentityDoc captureDate(OffsetDateTime captureDate) {
    this.captureDate = captureDate;
    return this;
  }

   /**
   * Date on which ID is captured
   * @return captureDate
  **/
  
  public OffsetDateTime getCaptureDate() {
    return captureDate;
  }

  public void setCaptureDate(OffsetDateTime captureDate) {
    this.captureDate = captureDate;
  }

  public CustomerIdentityDoc captureReason(String captureReason) {
    this.captureReason = captureReason;
    return this;
  }

   /**
   * Reason that ID is captured (e.g. returns ID capture, tax exemption, etc.)
   * @return captureReason
  **/
  
  public String getCaptureReason() {
    return captureReason;
  }

  public void setCaptureReason(String captureReason) {
    this.captureReason = captureReason;
  }

  public CustomerIdentityDoc country(String country) {
    this.country = country;
    return this;
  }

   /**
   * Country
   * @return country
  **/
  
  public String getCountry() {
    return country;
  }

  public void setCountry(String country) {
    this.country = country;
  }

  public CustomerIdentityDoc createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public CustomerIdentityDoc createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public CustomerIdentityDoc customerIdentityDocId(String customerIdentityDocId) {
    this.customerIdentityDocId = customerIdentityDocId;
    return this;
  }

   /**
   * Unique identifier of the Order Customer Identity Doc
   * @return customerIdentityDocId
  **/
  
  public String getCustomerIdentityDocId() {
    return customerIdentityDocId;
  }

  public void setCustomerIdentityDocId(String customerIdentityDocId) {
    this.customerIdentityDocId = customerIdentityDocId;
  }

  public CustomerIdentityDoc docType(DocTypeId docType) {
    this.docType = docType;
    return this;
  }

   /**
   * Get docType
   * @return docType
  **/
  
  public DocTypeId getDocType() {
    return docType;
  }

  public void setDocType(DocTypeId docType) {
    this.docType = docType;
  }

  public CustomerIdentityDoc extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public CustomerIdentityDoc idDocNumber(String idDocNumber) {
    this.idDocNumber = idDocNumber;
    return this;
  }

   /**
   * ID Number
   * @return idDocNumber
  **/
  
  public String getIdDocNumber() {
    return idDocNumber;
  }

  public void setIdDocNumber(String idDocNumber) {
    this.idDocNumber = idDocNumber;
  }

  public CustomerIdentityDoc identityDocType(IdentityDocTypeId identityDocType) {
    this.identityDocType = identityDocType;
    return this;
  }

   /**
   * Get identityDocType
   * @return identityDocType
  **/
  
  public IdentityDocTypeId getIdentityDocType() {
    return identityDocType;
  }

  public void setIdentityDocType(IdentityDocTypeId identityDocType) {
    this.identityDocType = identityDocType;
  }

  public CustomerIdentityDoc image(String image) {
    this.image = image;
    return this;
  }

   /**
   * Filename or blob image
   * @return image
  **/
  
  public String getImage() {
    return image;
  }

  public void setImage(String image) {
    this.image = image;
  }

  public CustomerIdentityDoc imageType(String imageType) {
    this.imageType = imageType;
    return this;
  }

   /**
   * Vector, jpeg, etc.
   * @return imageType
  **/
  
  public String getImageType() {
    return imageType;
  }

  public void setImageType(String imageType) {
    this.imageType = imageType;
  }

  public CustomerIdentityDoc localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public CustomerIdentityDoc messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public CustomerIdentityDoc orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public CustomerIdentityDoc PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public CustomerIdentityDoc parentOrder(PrimaryKey parentOrder) {
    this.parentOrder = parentOrder;
    return this;
  }

   /**
   * Get parentOrder
   * @return parentOrder
  **/
  
  public PrimaryKey getParentOrder() {
    return parentOrder;
  }

  public void setParentOrder(PrimaryKey parentOrder) {
    this.parentOrder = parentOrder;
  }

  public CustomerIdentityDoc state(String state) {
    this.state = state;
    return this;
  }

   /**
   * State
   * @return state
  **/
  
  public String getState() {
    return state;
  }

  public void setState(String state) {
    this.state = state;
  }

  public CustomerIdentityDoc updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public CustomerIdentityDoc updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public CustomerIdentityDoc entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public CustomerIdentityDoc localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CustomerIdentityDoc customerIdentityDoc = (CustomerIdentityDoc) o;
    return Objects.equals(this.actions, customerIdentityDoc.actions) &&
        Objects.equals(this.captureDate, customerIdentityDoc.captureDate) &&
        Objects.equals(this.captureReason, customerIdentityDoc.captureReason) &&
        Objects.equals(this.country, customerIdentityDoc.country) &&
        Objects.equals(this.createdBy, customerIdentityDoc.createdBy) &&
        Objects.equals(this.createdTimestamp, customerIdentityDoc.createdTimestamp) &&
        Objects.equals(this.customerIdentityDocId, customerIdentityDoc.customerIdentityDocId) &&
        Objects.equals(this.docType, customerIdentityDoc.docType) &&
        Objects.equals(this.extended, customerIdentityDoc.extended) &&
        Objects.equals(this.idDocNumber, customerIdentityDoc.idDocNumber) &&
        Objects.equals(this.identityDocType, customerIdentityDoc.identityDocType) &&
        Objects.equals(this.image, customerIdentityDoc.image) &&
        Objects.equals(this.imageType, customerIdentityDoc.imageType) &&
        Objects.equals(this.localizedTo, customerIdentityDoc.localizedTo) &&
        Objects.equals(this.messages, customerIdentityDoc.messages) &&
        Objects.equals(this.orgId, customerIdentityDoc.orgId) &&
        Objects.equals(this.PK, customerIdentityDoc.PK) &&
        Objects.equals(this.parentOrder, customerIdentityDoc.parentOrder) &&
        Objects.equals(this.state, customerIdentityDoc.state) &&
        Objects.equals(this.updatedBy, customerIdentityDoc.updatedBy) &&
        Objects.equals(this.updatedTimestamp, customerIdentityDoc.updatedTimestamp) &&
        Objects.equals(this.entityName, customerIdentityDoc.entityName) &&
        Objects.equals(this.localize, customerIdentityDoc.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, captureDate, captureReason, country, createdBy, createdTimestamp, customerIdentityDocId, docType, extended, idDocNumber, identityDocType, image, imageType, localizedTo, messages, orgId, PK, parentOrder, state, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CustomerIdentityDoc {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    captureDate: ").append(toIndentedString(captureDate)).append("\n");
    sb.append("    captureReason: ").append(toIndentedString(captureReason)).append("\n");
    sb.append("    country: ").append(toIndentedString(country)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    customerIdentityDocId: ").append(toIndentedString(customerIdentityDocId)).append("\n");
    sb.append("    docType: ").append(toIndentedString(docType)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    idDocNumber: ").append(toIndentedString(idDocNumber)).append("\n");
    sb.append("    identityDocType: ").append(toIndentedString(identityDocType)).append("\n");
    sb.append("    image: ").append(toIndentedString(image)).append("\n");
    sb.append("    imageType: ").append(toIndentedString(imageType)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentOrder: ").append(toIndentedString(parentOrder)).append("\n");
    sb.append("    state: ").append(toIndentedString(state)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

