/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * PreferenceId
 */
public class PreferenceId {
  public static final String SERIALIZED_NAME_PREFERENCE_ID = "PreferenceId";
  @SerializedName(SERIALIZED_NAME_PREFERENCE_ID)
  private String preferenceId;

  public PreferenceId preferenceId(String preferenceId) {
    this.preferenceId = preferenceId;
    return this;
  }

   /**
   * Unique identifier of the Preference Id
   * @return preferenceId
  **/
  
  public String getPreferenceId() {
    return preferenceId;
  }

  public void setPreferenceId(String preferenceId) {
    this.preferenceId = preferenceId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PreferenceId preferenceId = (PreferenceId) o;
    return Objects.equals(this.preferenceId, preferenceId.preferenceId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(preferenceId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PreferenceId {\n");
    
    sb.append("    preferenceId: ").append(toIndentedString(preferenceId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

