/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * LineProcessInfo
 */
public class LineProcessInfo {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_BATCH_REQUIREMENT_TYPE = "BatchRequirementType";
  @SerializedName(SERIALIZED_NAME_BATCH_REQUIREMENT_TYPE)
  private String batchRequirementType;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_CRITICAL_DIMENSION1 = "CriticalDimension1";
  @SerializedName(SERIALIZED_NAME_CRITICAL_DIMENSION1)
  private Double criticalDimension1;

  public static final String SERIALIZED_NAME_CRITICAL_DIMENSION2 = "CriticalDimension2";
  @SerializedName(SERIALIZED_NAME_CRITICAL_DIMENSION2)
  private Double criticalDimension2;

  public static final String SERIALIZED_NAME_CRITICAL_DIMENSION3 = "CriticalDimension3";
  @SerializedName(SERIALIZED_NAME_CRITICAL_DIMENSION3)
  private Double criticalDimension3;

  public static final String SERIALIZED_NAME_CUBE_MULTIPLE_QTY = "CubeMultipleQty";
  @SerializedName(SERIALIZED_NAME_CUBE_MULTIPLE_QTY)
  private Double cubeMultipleQty;

  public static final String SERIALIZED_NAME_DELIVERY_REFERENCE_NUMBER = "DeliveryReferenceNumber";
  @SerializedName(SERIALIZED_NAME_DELIVERY_REFERENCE_NUMBER)
  private String deliveryReferenceNumber;

  public static final String SERIALIZED_NAME_EXPORT_INFO_CODE = "ExportInfoCode";
  @SerializedName(SERIALIZED_NAME_EXPORT_INFO_CODE)
  private String exportInfoCode;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IS_SERIAL_NUMBER_REQUIRED = "IsSerialNumberRequired";
  @SerializedName(SERIALIZED_NAME_IS_SERIAL_NUMBER_REQUIRED)
  private Boolean isSerialNumberRequired;

  public static final String SERIALIZED_NAME_ITEM_BREAK_ATTRIBUTE = "ItemBreakAttribute";
  @SerializedName(SERIALIZED_NAME_ITEM_BREAK_ATTRIBUTE)
  private String itemBreakAttribute;

  public static final String SERIALIZED_NAME_ITEM_PRICE = "ItemPrice";
  @SerializedName(SERIALIZED_NAME_ITEM_PRICE)
  private BigDecimal itemPrice;

  public static final String SERIALIZED_NAME_LP_N_BREAK_ATTRIBUTE = "LPNBreakAttribute";
  @SerializedName(SERIALIZED_NAME_LP_N_BREAK_ATTRIBUTE)
  private String lpNBreakAttribute;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PICK_LOC_ASSIGNMENT_TYPE = "PickLocAssignmentType";
  @SerializedName(SERIALIZED_NAME_PICK_LOC_ASSIGNMENT_TYPE)
  private String pickLocAssignmentType;

  public static final String SERIALIZED_NAME_PICK_TICKET_CONTROL_NUMBER = "PickTicketControlNumber";
  @SerializedName(SERIALIZED_NAME_PICK_TICKET_CONTROL_NUMBER)
  private String pickTicketControlNumber;

  public static final String SERIALIZED_NAME_PICK_UP_REFERENCE_NUMBER = "PickUpReferenceNumber";
  @SerializedName(SERIALIZED_NAME_PICK_UP_REFERENCE_NUMBER)
  private String pickUpReferenceNumber;

  public static final String SERIALIZED_NAME_PRICE = "Price";
  @SerializedName(SERIALIZED_NAME_PRICE)
  private Double price;

  public static final String SERIALIZED_NAME_PRICE_TICKET_TYPE = "PriceTicketType";
  @SerializedName(SERIALIZED_NAME_PRICE_TICKET_TYPE)
  private String priceTicketType;

  public static final String SERIALIZED_NAME_SINGLE_UNIT = "SingleUnit";
  @SerializedName(SERIALIZED_NAME_SINGLE_UNIT)
  private Boolean singleUnit;

  public static final String SERIALIZED_NAME_TRANSLATIONS = "Translations";
  @SerializedName(SERIALIZED_NAME_TRANSLATIONS)
  private Map<String, Map<String, String>> translations = null;

  public static final String SERIALIZED_NAME_UNIT_VOLUME = "UnitVolume";
  @SerializedName(SERIALIZED_NAME_UNIT_VOLUME)
  private Double unitVolume;

  public static final String SERIALIZED_NAME_UNIT_WEIGHT = "UnitWeight";
  @SerializedName(SERIALIZED_NAME_UNIT_WEIGHT)
  private Double unitWeight;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_VA_S_PROCESS_TYPE = "VASProcessType";
  @SerializedName(SERIALIZED_NAME_VA_S_PROCESS_TYPE)
  private String vaSProcessType;

  public static final String SERIALIZED_NAME_WAVE_PROCESS_TYPE = "WaveProcessType";
  @SerializedName(SERIALIZED_NAME_WAVE_PROCESS_TYPE)
  private String waveProcessType;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public LineProcessInfo actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public LineProcessInfo putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public LineProcessInfo batchRequirementType(String batchRequirementType) {
    this.batchRequirementType = batchRequirementType;
    return this;
  }

   /**
   * Describes how to fulfill Pickticket Requirements when dealing with batches
   * @return batchRequirementType
  **/
  
  public String getBatchRequirementType() {
    return batchRequirementType;
  }

  public void setBatchRequirementType(String batchRequirementType) {
    this.batchRequirementType = batchRequirementType;
  }

  public LineProcessInfo createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public LineProcessInfo createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public LineProcessInfo criticalDimension1(Double criticalDimension1) {
    this.criticalDimension1 = criticalDimension1;
    return this;
  }

   /**
   * Defines length, width, height for cubing
   * minimum: 0
   * maximum: 999999999999.9999
   * @return criticalDimension1
  **/
  
  public Double getCriticalDimension1() {
    return criticalDimension1;
  }

  public void setCriticalDimension1(Double criticalDimension1) {
    this.criticalDimension1 = criticalDimension1;
  }

  public LineProcessInfo criticalDimension2(Double criticalDimension2) {
    this.criticalDimension2 = criticalDimension2;
    return this;
  }

   /**
   * Defines length, width, height for cubing
   * minimum: 0
   * maximum: 999999999999.9999
   * @return criticalDimension2
  **/
  
  public Double getCriticalDimension2() {
    return criticalDimension2;
  }

  public void setCriticalDimension2(Double criticalDimension2) {
    this.criticalDimension2 = criticalDimension2;
  }

  public LineProcessInfo criticalDimension3(Double criticalDimension3) {
    this.criticalDimension3 = criticalDimension3;
    return this;
  }

   /**
   * Defines length, width, height for cubing
   * minimum: 0
   * maximum: 999999999999.9999
   * @return criticalDimension3
  **/
  
  public Double getCriticalDimension3() {
    return criticalDimension3;
  }

  public void setCriticalDimension3(Double criticalDimension3) {
    this.criticalDimension3 = criticalDimension3;
  }

  public LineProcessInfo cubeMultipleQty(Double cubeMultipleQty) {
    this.cubeMultipleQty = cubeMultipleQty;
    return this;
  }

   /**
   * Defines length, width, height for cubing
   * minimum: 0
   * maximum: 999999999999.9999
   * @return cubeMultipleQty
  **/
  
  public Double getCubeMultipleQty() {
    return cubeMultipleQty;
  }

  public void setCubeMultipleQty(Double cubeMultipleQty) {
    this.cubeMultipleQty = cubeMultipleQty;
  }

  public LineProcessInfo deliveryReferenceNumber(String deliveryReferenceNumber) {
    this.deliveryReferenceNumber = deliveryReferenceNumber;
    return this;
  }

   /**
   * Delivery Reference Number
   * @return deliveryReferenceNumber
  **/
  
  public String getDeliveryReferenceNumber() {
    return deliveryReferenceNumber;
  }

  public void setDeliveryReferenceNumber(String deliveryReferenceNumber) {
    this.deliveryReferenceNumber = deliveryReferenceNumber;
  }

  public LineProcessInfo exportInfoCode(String exportInfoCode) {
    this.exportInfoCode = exportInfoCode;
    return this;
  }

   /**
   * Indicates the reporting requirements and/or exemption from reporting for the electronic Shipper-s Export Declaration. Pulls from system code S 459.
   * @return exportInfoCode
  **/
  
  public String getExportInfoCode() {
    return exportInfoCode;
  }

  public void setExportInfoCode(String exportInfoCode) {
    this.exportInfoCode = exportInfoCode;
  }

  public LineProcessInfo extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public LineProcessInfo isSerialNumberRequired(Boolean isSerialNumberRequired) {
    this.isSerialNumberRequired = isSerialNumberRequired;
    return this;
  }

   /**
   * Defines if Warehouse Management prompts the user for serial number during picking
   * @return isSerialNumberRequired
  **/
  
  public Boolean getIsSerialNumberRequired() {
    return isSerialNumberRequired;
  }

  public void setIsSerialNumberRequired(Boolean isSerialNumberRequired) {
    this.isSerialNumberRequired = isSerialNumberRequired;
  }

  public LineProcessInfo itemBreakAttribute(String itemBreakAttribute) {
    this.itemBreakAttribute = itemBreakAttribute;
    return this;
  }

   /**
   * When converting distros to pickticket details in the wave process, distros for the same store/sku/attributes/etc will be combined onto the same pickticket detail line only if this value is the same or blank. If it is desired to have one pickticket detail
   * @return itemBreakAttribute
  **/
  
  public String getItemBreakAttribute() {
    return itemBreakAttribute;
  }

  public void setItemBreakAttribute(String itemBreakAttribute) {
    this.itemBreakAttribute = itemBreakAttribute;
  }

  public LineProcessInfo itemPrice(BigDecimal itemPrice) {
    this.itemPrice = itemPrice;
    return this;
  }

   /**
   * Actual price of item, only for informational use
   * minimum: 0
   * maximum: 99999999999999.98
   * @return itemPrice
  **/
  
  public BigDecimal getItemPrice() {
    return itemPrice;
  }

  public void setItemPrice(BigDecimal itemPrice) {
    this.itemPrice = itemPrice;
  }

  public LineProcessInfo lpNBreakAttribute(String lpNBreakAttribute) {
    this.lpNBreakAttribute = lpNBreakAttribute;
    return this;
  }

   /**
   * Dedicated for use in future TMS integration. There is no UI support for or Warehouse Management logic using this column. Do not use for custom purposes.
   * @return lpNBreakAttribute
  **/
  
  public String getLpNBreakAttribute() {
    return lpNBreakAttribute;
  }

  public void setLpNBreakAttribute(String lpNBreakAttribute) {
    this.lpNBreakAttribute = lpNBreakAttribute;
  }

  public LineProcessInfo messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public LineProcessInfo orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public LineProcessInfo PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public LineProcessInfo pickLocAssignmentType(String pickLocAssignmentType) {
    this.pickLocAssignmentType = pickLocAssignmentType;
    return this;
  }

   /**
   * Defines the item attributes associated with assigning dynamic locations
   * @return pickLocAssignmentType
  **/
  
  public String getPickLocAssignmentType() {
    return pickLocAssignmentType;
  }

  public void setPickLocAssignmentType(String pickLocAssignmentType) {
    this.pickLocAssignmentType = pickLocAssignmentType;
  }

  public LineProcessInfo pickTicketControlNumber(String pickTicketControlNumber) {
    this.pickTicketControlNumber = pickTicketControlNumber;
    return this;
  }

   /**
   * Unique Warehouse Management PickTicket Control Number
   * @return pickTicketControlNumber
  **/
  
  public String getPickTicketControlNumber() {
    return pickTicketControlNumber;
  }

  public void setPickTicketControlNumber(String pickTicketControlNumber) {
    this.pickTicketControlNumber = pickTicketControlNumber;
  }

  public LineProcessInfo pickUpReferenceNumber(String pickUpReferenceNumber) {
    this.pickUpReferenceNumber = pickUpReferenceNumber;
    return this;
  }

   /**
   * Pickup reference number
   * @return pickUpReferenceNumber
  **/
  
  public String getPickUpReferenceNumber() {
    return pickUpReferenceNumber;
  }

  public void setPickUpReferenceNumber(String pickUpReferenceNumber) {
    this.pickUpReferenceNumber = pickUpReferenceNumber;
  }

  public LineProcessInfo price(Double price) {
    this.price = price;
    return this;
  }

   /**
   * Price of the goods
   * minimum: 0
   * maximum: 999999999999.9999
   * @return price
  **/
  
  public Double getPrice() {
    return price;
  }

  public void setPrice(Double price) {
    this.price = price;
  }

  public LineProcessInfo priceTicketType(String priceTicketType) {
    this.priceTicketType = priceTicketType;
    return this;
  }

   /**
   * Price ticket template for the associated item (B901). Defaulted initially from Pkt Dtl, Item, or Warehouse Master.
   * @return priceTicketType
  **/
  
  public String getPriceTicketType() {
    return priceTicketType;
  }

  public void setPriceTicketType(String priceTicketType) {
    this.priceTicketType = priceTicketType;
  }

  public LineProcessInfo singleUnit(Boolean singleUnit) {
    this.singleUnit = singleUnit;
    return this;
  }

   /**
   * Identifies a single unit order
   * @return singleUnit
  **/
  
  public Boolean getSingleUnit() {
    return singleUnit;
  }

  public void setSingleUnit(Boolean singleUnit) {
    this.singleUnit = singleUnit;
  }

  public LineProcessInfo translations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
    return this;
  }

  public LineProcessInfo putTranslationsItem(String key, Map<String, String> translationsItem) {
    if (this.translations == null) {
      this.translations = new HashMap<String, Map<String, String>>();
    }
    this.translations.put(key, translationsItem);
    return this;
  }

   /**
   * Get translations
   * @return translations
  **/
  
  public Map<String, Map<String, String>> getTranslations() {
    return translations;
  }

  public void setTranslations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
  }

  public LineProcessInfo unitVolume(Double unitVolume) {
    this.unitVolume = unitVolume;
    return this;
  }

   /**
   * Unit volume, used in cubing
   * minimum: 0
   * maximum: 999999999999.9999
   * @return unitVolume
  **/
  
  public Double getUnitVolume() {
    return unitVolume;
  }

  public void setUnitVolume(Double unitVolume) {
    this.unitVolume = unitVolume;
  }

  public LineProcessInfo unitWeight(Double unitWeight) {
    this.unitWeight = unitWeight;
    return this;
  }

   /**
   * Unit weight, used in cubing
   * minimum: 0
   * maximum: 999999999999.9999
   * @return unitWeight
  **/
  
  public Double getUnitWeight() {
    return unitWeight;
  }

  public void setUnitWeight(Double unitWeight) {
    this.unitWeight = unitWeight;
  }

  public LineProcessInfo updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public LineProcessInfo updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public LineProcessInfo vaSProcessType(String vaSProcessType) {
    this.vaSProcessType = vaSProcessType;
    return this;
  }

   /**
   * VAS Process Type: Indicates whether, and how, VAS must be performed upon the item in this detail. Based on sys code S257
   * @return vaSProcessType
  **/
  
  public String getVaSProcessType() {
    return vaSProcessType;
  }

  public void setVaSProcessType(String vaSProcessType) {
    this.vaSProcessType = vaSProcessType;
  }

  public LineProcessInfo waveProcessType(String waveProcessType) {
    this.waveProcessType = waveProcessType;
    return this;
  }

   /**
   * Defines the WPT for the item.The WPT defines the process of searching for inventory for the item.
   * @return waveProcessType
  **/
  
  public String getWaveProcessType() {
    return waveProcessType;
  }

  public void setWaveProcessType(String waveProcessType) {
    this.waveProcessType = waveProcessType;
  }

  public LineProcessInfo entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LineProcessInfo lineProcessInfo = (LineProcessInfo) o;
    return Objects.equals(this.actions, lineProcessInfo.actions) &&
        Objects.equals(this.batchRequirementType, lineProcessInfo.batchRequirementType) &&
        Objects.equals(this.createdBy, lineProcessInfo.createdBy) &&
        Objects.equals(this.createdTimestamp, lineProcessInfo.createdTimestamp) &&
        Objects.equals(this.criticalDimension1, lineProcessInfo.criticalDimension1) &&
        Objects.equals(this.criticalDimension2, lineProcessInfo.criticalDimension2) &&
        Objects.equals(this.criticalDimension3, lineProcessInfo.criticalDimension3) &&
        Objects.equals(this.cubeMultipleQty, lineProcessInfo.cubeMultipleQty) &&
        Objects.equals(this.deliveryReferenceNumber, lineProcessInfo.deliveryReferenceNumber) &&
        Objects.equals(this.exportInfoCode, lineProcessInfo.exportInfoCode) &&
        Objects.equals(this.extended, lineProcessInfo.extended) &&
        Objects.equals(this.isSerialNumberRequired, lineProcessInfo.isSerialNumberRequired) &&
        Objects.equals(this.itemBreakAttribute, lineProcessInfo.itemBreakAttribute) &&
        Objects.equals(this.itemPrice, lineProcessInfo.itemPrice) &&
        Objects.equals(this.lpNBreakAttribute, lineProcessInfo.lpNBreakAttribute) &&
        Objects.equals(this.messages, lineProcessInfo.messages) &&
        Objects.equals(this.orgId, lineProcessInfo.orgId) &&
        Objects.equals(this.PK, lineProcessInfo.PK) &&
        Objects.equals(this.pickLocAssignmentType, lineProcessInfo.pickLocAssignmentType) &&
        Objects.equals(this.pickTicketControlNumber, lineProcessInfo.pickTicketControlNumber) &&
        Objects.equals(this.pickUpReferenceNumber, lineProcessInfo.pickUpReferenceNumber) &&
        Objects.equals(this.price, lineProcessInfo.price) &&
        Objects.equals(this.priceTicketType, lineProcessInfo.priceTicketType) &&
        Objects.equals(this.singleUnit, lineProcessInfo.singleUnit) &&
        Objects.equals(this.translations, lineProcessInfo.translations) &&
        Objects.equals(this.unitVolume, lineProcessInfo.unitVolume) &&
        Objects.equals(this.unitWeight, lineProcessInfo.unitWeight) &&
        Objects.equals(this.updatedBy, lineProcessInfo.updatedBy) &&
        Objects.equals(this.updatedTimestamp, lineProcessInfo.updatedTimestamp) &&
        Objects.equals(this.vaSProcessType, lineProcessInfo.vaSProcessType) &&
        Objects.equals(this.waveProcessType, lineProcessInfo.waveProcessType) &&
        Objects.equals(this.entityName, lineProcessInfo.entityName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, batchRequirementType, createdBy, createdTimestamp, criticalDimension1, criticalDimension2, criticalDimension3, cubeMultipleQty, deliveryReferenceNumber, exportInfoCode, extended, isSerialNumberRequired, itemBreakAttribute, itemPrice, lpNBreakAttribute, messages, orgId, PK, pickLocAssignmentType, pickTicketControlNumber, pickUpReferenceNumber, price, priceTicketType, singleUnit, translations, unitVolume, unitWeight, updatedBy, updatedTimestamp, vaSProcessType, waveProcessType, entityName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LineProcessInfo {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    batchRequirementType: ").append(toIndentedString(batchRequirementType)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    criticalDimension1: ").append(toIndentedString(criticalDimension1)).append("\n");
    sb.append("    criticalDimension2: ").append(toIndentedString(criticalDimension2)).append("\n");
    sb.append("    criticalDimension3: ").append(toIndentedString(criticalDimension3)).append("\n");
    sb.append("    cubeMultipleQty: ").append(toIndentedString(cubeMultipleQty)).append("\n");
    sb.append("    deliveryReferenceNumber: ").append(toIndentedString(deliveryReferenceNumber)).append("\n");
    sb.append("    exportInfoCode: ").append(toIndentedString(exportInfoCode)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    isSerialNumberRequired: ").append(toIndentedString(isSerialNumberRequired)).append("\n");
    sb.append("    itemBreakAttribute: ").append(toIndentedString(itemBreakAttribute)).append("\n");
    sb.append("    itemPrice: ").append(toIndentedString(itemPrice)).append("\n");
    sb.append("    lpNBreakAttribute: ").append(toIndentedString(lpNBreakAttribute)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    pickLocAssignmentType: ").append(toIndentedString(pickLocAssignmentType)).append("\n");
    sb.append("    pickTicketControlNumber: ").append(toIndentedString(pickTicketControlNumber)).append("\n");
    sb.append("    pickUpReferenceNumber: ").append(toIndentedString(pickUpReferenceNumber)).append("\n");
    sb.append("    price: ").append(toIndentedString(price)).append("\n");
    sb.append("    priceTicketType: ").append(toIndentedString(priceTicketType)).append("\n");
    sb.append("    singleUnit: ").append(toIndentedString(singleUnit)).append("\n");
    sb.append("    translations: ").append(toIndentedString(translations)).append("\n");
    sb.append("    unitVolume: ").append(toIndentedString(unitVolume)).append("\n");
    sb.append("    unitWeight: ").append(toIndentedString(unitWeight)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    vaSProcessType: ").append(toIndentedString(vaSProcessType)).append("\n");
    sb.append("    waveProcessType: ").append(toIndentedString(waveProcessType)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

