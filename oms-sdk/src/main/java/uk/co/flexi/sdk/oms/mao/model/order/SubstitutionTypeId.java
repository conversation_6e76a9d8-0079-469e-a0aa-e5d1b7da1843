/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * SubstitutionTypeId
 */
public class SubstitutionTypeId {
  public static final String SERIALIZED_NAME_SUBSTITUTION_TYPE_ID = "SubstitutionTypeId";
  @SerializedName(SERIALIZED_NAME_SUBSTITUTION_TYPE_ID)
  private String substitutionTypeId;

  public SubstitutionTypeId substitutionTypeId(String substitutionTypeId) {
    this.substitutionTypeId = substitutionTypeId;
    return this;
  }

   /**
   * Unique identifier of the Substitution type
   * @return substitutionTypeId
  **/
  public String getSubstitutionTypeId() {
    return substitutionTypeId;
  }

  public void setSubstitutionTypeId(String substitutionTypeId) {
    this.substitutionTypeId = substitutionTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SubstitutionTypeId substitutionTypeId = (SubstitutionTypeId) o;
    return Objects.equals(this.substitutionTypeId, substitutionTypeId.substitutionTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(substitutionTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SubstitutionTypeId {\n");
    
    sb.append("    substitutionTypeId: ").append(toIndentedString(substitutionTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

