/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.LocalDate;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.*;

/**
 * OrderLine
 */
public class OrderLine {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ADDRESS_ID = "AddressId";
  @SerializedName(SERIALIZED_NAME_ADDRESS_ID)
  private String addressId;

  public static final String SERIALIZED_NAME_ALLOCATION = "Allocation";
  @SerializedName(SERIALIZED_NAME_ALLOCATION)
  private List<Allocation> allocation = null;

  public static final String SERIALIZED_NAME_ALLOCATION_CONFIG_ID = "AllocationConfigId";
  @SerializedName(SERIALIZED_NAME_ALLOCATION_CONFIG_ID)
  private String allocationConfigId;

  public static final String SERIALIZED_NAME_ALTERNATE_ORDER_LINE_ID = "AlternateOrderLineId";
  @SerializedName(SERIALIZED_NAME_ALTERNATE_ORDER_LINE_ID)
  private String alternateOrderLineId;

  public static final String SERIALIZED_NAME_BUSINESS_DATE = "BusinessDate";
  @SerializedName(SERIALIZED_NAME_BUSINESS_DATE)
  private LocalDate businessDate;

  public static final String SERIALIZED_NAME_CALCULATED_VALUES = "CalculatedValues";
  @SerializedName(SERIALIZED_NAME_CALCULATED_VALUES)
  private Object calculatedValues = null;

  public static final String SERIALIZED_NAME_CANCEL_COMMENTS = "CancelComments";
  @SerializedName(SERIALIZED_NAME_CANCEL_COMMENTS)
  private String cancelComments;

  public static final String SERIALIZED_NAME_CANCEL_REASON = "CancelReason";
  @SerializedName(SERIALIZED_NAME_CANCEL_REASON)
  private ReasonId cancelReason = null;

  public static final String SERIALIZED_NAME_CANCELLED_ORDER_LINE_SUB_TOTAL = "CancelledOrderLineSubTotal";
  @SerializedName(SERIALIZED_NAME_CANCELLED_ORDER_LINE_SUB_TOTAL)
  private BigDecimal cancelledOrderLineSubTotal;

  public static final String SERIALIZED_NAME_CANCELLED_TOTAL_DISCOUNTS = "CancelledTotalDiscounts";
  @SerializedName(SERIALIZED_NAME_CANCELLED_TOTAL_DISCOUNTS)
  private BigDecimal cancelledTotalDiscounts;

  public static final String SERIALIZED_NAME_CARRIER_CODE = "CarrierCode";
  @SerializedName(SERIALIZED_NAME_CARRIER_CODE)
  private String carrierCode;

  public static final String SERIALIZED_NAME_CHANGE_LOG = "ChangeLog";
  @SerializedName(SERIALIZED_NAME_CHANGE_LOG)
  private ChangeLog changeLog = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DELIVERY_METHOD = "DeliveryMethod";
  @SerializedName(SERIALIZED_NAME_DELIVERY_METHOD)
  private DeliveryMethodId deliveryMethod = null;

  public static final String SERIALIZED_NAME_DO_NOT_SHIP_BEFORE_DATE = "DoNotShipBeforeDate";
  @SerializedName(SERIALIZED_NAME_DO_NOT_SHIP_BEFORE_DATE)
  private OffsetDateTime doNotShipBeforeDate;

  public static final String SERIALIZED_NAME_EFFECTIVE_RANK = "EffectiveRank";
  @SerializedName(SERIALIZED_NAME_EFFECTIVE_RANK)
  private String effectiveRank;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FULFILLMENT_DETAIL = "FulfillmentDetail";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_DETAIL)
  private List<FulfillmentDetail> fulfillmentDetail = null;

  public static final String SERIALIZED_NAME_FULFILLMENT_GROUP_ID = "FulfillmentGroupId";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_GROUP_ID)
  private String fulfillmentGroupId;

  public static final String SERIALIZED_NAME_FULFILLMENT_STATUS = "FulfillmentStatus";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_STATUS)
  private String fulfillmentStatus;

  public static final String SERIALIZED_NAME_GIFT_CARD_VALUE = "GiftCardValue";
  @SerializedName(SERIALIZED_NAME_GIFT_CARD_VALUE)
  private BigDecimal giftCardValue;

  public static final String SERIALIZED_NAME_HAS_COMPONENTS = "HasComponents";
  @SerializedName(SERIALIZED_NAME_HAS_COMPONENTS)
  private Boolean hasComponents;

  public static final String SERIALIZED_NAME_IS_ACTIVATION_REQUIRED = "IsActivationRequired";
  @SerializedName(SERIALIZED_NAME_IS_ACTIVATION_REQUIRED)
  private Boolean isActivationRequired;

  public static final String SERIALIZED_NAME_IS_CANCELLED = "IsCancelled";
  @SerializedName(SERIALIZED_NAME_IS_CANCELLED)
  private Boolean isCancelled;

  public static final String SERIALIZED_NAME_IS_DISCOUNTABLE = "IsDiscountable";
  @SerializedName(SERIALIZED_NAME_IS_DISCOUNTABLE)
  private Boolean isDiscountable;

  public static final String SERIALIZED_NAME_IS_ELIGIBLE_FOR_OVERRIDE = "IsEligibleForOverride";
  @SerializedName(SERIALIZED_NAME_IS_ELIGIBLE_FOR_OVERRIDE)
  private Boolean isEligibleForOverride;

  public static final String SERIALIZED_NAME_IS_EVEN_EXCHANGE = "IsEvenExchange";
  @SerializedName(SERIALIZED_NAME_IS_EVEN_EXCHANGE)
  private Boolean isEvenExchange;

  public static final String SERIALIZED_NAME_IS_EXCHANGEABLE = "IsExchangeable";
  @SerializedName(SERIALIZED_NAME_IS_EXCHANGEABLE)
  private Boolean isExchangeable;

  public static final String SERIALIZED_NAME_IS_GIFT = "IsGift";
  @SerializedName(SERIALIZED_NAME_IS_GIFT)
  private Boolean isGift;

  public static final String SERIALIZED_NAME_IS_GIFT_CARD = "IsGiftCard";
  @SerializedName(SERIALIZED_NAME_IS_GIFT_CARD)
  private Boolean isGiftCard;

  public static final String SERIALIZED_NAME_IS_HAZMAT = "IsHazmat";
  @SerializedName(SERIALIZED_NAME_IS_HAZMAT)
  private Boolean isHazmat;

  public static final String SERIALIZED_NAME_IS_ITEM_NOT_ON_FILE = "IsItemNotOnFile";
  @SerializedName(SERIALIZED_NAME_IS_ITEM_NOT_ON_FILE)
  private Boolean isItemNotOnFile;

  public static final String SERIALIZED_NAME_IS_ITEM_TAX_EXEMPTABLE = "IsItemTaxExemptable";
  @SerializedName(SERIALIZED_NAME_IS_ITEM_TAX_EXEMPTABLE)
  private Boolean isItemTaxExemptable;

  public static final String SERIALIZED_NAME_IS_ITEM_TAX_OVERRIDABLE = "IsItemTaxOverridable";
  @SerializedName(SERIALIZED_NAME_IS_ITEM_TAX_OVERRIDABLE)
  private Boolean isItemTaxOverridable;

  public static final String SERIALIZED_NAME_IS_NON_MERCHANDISE = "IsNonMerchandise";
  @SerializedName(SERIALIZED_NAME_IS_NON_MERCHANDISE)
  private Boolean isNonMerchandise;

  public static final String SERIALIZED_NAME_IS_ON_HOLD = "IsOnHold";
  @SerializedName(SERIALIZED_NAME_IS_ON_HOLD)
  private Boolean isOnHold;

  public static final String SERIALIZED_NAME_IS_PACK_AND_HOLD = "IsPackAndHold";
  @SerializedName(SERIALIZED_NAME_IS_PACK_AND_HOLD)
  private Boolean isPackAndHold;

  public static final String SERIALIZED_NAME_IS_PERISHABLE = "IsPerishable";
  @SerializedName(SERIALIZED_NAME_IS_PERISHABLE)
  private Boolean isPerishable;

  public static final String SERIALIZED_NAME_IS_PRE_ORDER = "IsPreOrder";
  @SerializedName(SERIALIZED_NAME_IS_PRE_ORDER)
  private Boolean isPreOrder;

  public static final String SERIALIZED_NAME_IS_PRE_SALE = "IsPreSale";
  @SerializedName(SERIALIZED_NAME_IS_PRE_SALE)
  private Boolean isPreSale;

  public static final String SERIALIZED_NAME_IS_PRICE_OVERRIDDEN = "IsPriceOverridden";
  @SerializedName(SERIALIZED_NAME_IS_PRICE_OVERRIDDEN)
  private Boolean isPriceOverridden;

  public static final String SERIALIZED_NAME_IS_PRICE_OVERRIDEABLE = "IsPriceOverrideable";
  @SerializedName(SERIALIZED_NAME_IS_PRICE_OVERRIDEABLE)
  private Boolean isPriceOverrideable;

  public static final String SERIALIZED_NAME_IS_RECEIPT_EXPECTED = "IsReceiptExpected";
  @SerializedName(SERIALIZED_NAME_IS_RECEIPT_EXPECTED)
  private Boolean isReceiptExpected;

  public static final String SERIALIZED_NAME_IS_REFUND_GIFT_CARD = "IsRefundGiftCard";
  @SerializedName(SERIALIZED_NAME_IS_REFUND_GIFT_CARD)
  private Boolean isRefundGiftCard;

  public static final String SERIALIZED_NAME_IS_RETURN = "IsReturn";
  @SerializedName(SERIALIZED_NAME_IS_RETURN)
  private Boolean isReturn;

  public static final String SERIALIZED_NAME_IS_RETURN_ALLOWED_BY_AGE_POLICY = "IsReturnAllowedByAgePolicy";
  @SerializedName(SERIALIZED_NAME_IS_RETURN_ALLOWED_BY_AGE_POLICY)
  private Boolean isReturnAllowedByAgePolicy;

  public static final String SERIALIZED_NAME_IS_RETURNABLE = "IsReturnable";
  @SerializedName(SERIALIZED_NAME_IS_RETURNABLE)
  private Boolean isReturnable;

  public static final String SERIALIZED_NAME_IS_RETURNABLE_AT_STORE = "IsReturnableAtStore";
  @SerializedName(SERIALIZED_NAME_IS_RETURNABLE_AT_STORE)
  private Boolean isReturnableAtStore;

  public static final String SERIALIZED_NAME_IS_TAX_INCLUDED = "IsTaxIncluded";
  @SerializedName(SERIALIZED_NAME_IS_TAX_INCLUDED)
  private Boolean isTaxIncluded;

  public static final String SERIALIZED_NAME_IS_TAX_OVERRIDDEN = "IsTaxOverridden";
  @SerializedName(SERIALIZED_NAME_IS_TAX_OVERRIDDEN)
  private Boolean isTaxOverridden;

  public static final String SERIALIZED_NAME_ITEM_BARCODE = "ItemBarcode";
  @SerializedName(SERIALIZED_NAME_ITEM_BARCODE)
  private String itemBarcode;

  public static final String SERIALIZED_NAME_ITEM_BRAND = "ItemBrand";
  @SerializedName(SERIALIZED_NAME_ITEM_BRAND)
  private String itemBrand;

  public static final String SERIALIZED_NAME_ITEM_COLOR_DESCRIPTION = "ItemColorDescription";
  @SerializedName(SERIALIZED_NAME_ITEM_COLOR_DESCRIPTION)
  private String itemColorDescription;

  public static final String SERIALIZED_NAME_ITEM_DEPARTMENT_NAME = "ItemDepartmentName";
  @SerializedName(SERIALIZED_NAME_ITEM_DEPARTMENT_NAME)
  private String itemDepartmentName;

  public static final String SERIALIZED_NAME_ITEM_DEPARTMENT_NUMBER = "ItemDepartmentNumber";
  @SerializedName(SERIALIZED_NAME_ITEM_DEPARTMENT_NUMBER)
  private Long itemDepartmentNumber;

  public static final String SERIALIZED_NAME_ITEM_DEPT_NUMBER = "ItemDeptNumber";
  @SerializedName(SERIALIZED_NAME_ITEM_DEPT_NUMBER)
  private String itemDeptNumber;

  public static final String SERIALIZED_NAME_ITEM_DESCRIPTION = "ItemDescription";
  @SerializedName(SERIALIZED_NAME_ITEM_DESCRIPTION)
  private String itemDescription;

  public static final String SERIALIZED_NAME_ITEM_ID = "ItemId";
  @SerializedName(SERIALIZED_NAME_ITEM_ID)
  private String itemId;

  public static final String SERIALIZED_NAME_ITEM_MAX_DISCOUNT_AMOUNT = "ItemMaxDiscountAmount";
  @SerializedName(SERIALIZED_NAME_ITEM_MAX_DISCOUNT_AMOUNT)
  private BigDecimal itemMaxDiscountAmount;

  public static final String SERIALIZED_NAME_ITEM_MAX_DISCOUNT_PERCENTAGE = "ItemMaxDiscountPercentage";
  @SerializedName(SERIALIZED_NAME_ITEM_MAX_DISCOUNT_PERCENTAGE)
  private BigDecimal itemMaxDiscountPercentage;

  public static final String SERIALIZED_NAME_ITEM_SEASON = "ItemSeason";
  @SerializedName(SERIALIZED_NAME_ITEM_SEASON)
  private String itemSeason;

  public static final String SERIALIZED_NAME_ITEM_SHORT_DESCRIPTION = "ItemShortDescription";
  @SerializedName(SERIALIZED_NAME_ITEM_SHORT_DESCRIPTION)
  private String itemShortDescription;

  public static final String SERIALIZED_NAME_ITEM_SIZE = "ItemSize";
  @SerializedName(SERIALIZED_NAME_ITEM_SIZE)
  private String itemSize;

  public static final String SERIALIZED_NAME_ITEM_STYLE = "ItemStyle";
  @SerializedName(SERIALIZED_NAME_ITEM_STYLE)
  private String itemStyle;

  public static final String SERIALIZED_NAME_ITEM_TAX_CODE = "ItemTaxCode";
  @SerializedName(SERIALIZED_NAME_ITEM_TAX_CODE)
  private String itemTaxCode;

  public static final String SERIALIZED_NAME_LATEST_DELIVERY_DATE = "LatestDeliveryDate";
  @SerializedName(SERIALIZED_NAME_LATEST_DELIVERY_DATE)
  private OffsetDateTime latestDeliveryDate;

  public static final String SERIALIZED_NAME_LATEST_FULFILLED_DATE = "LatestFulfilledDate";
  @SerializedName(SERIALIZED_NAME_LATEST_FULFILLED_DATE)
  private OffsetDateTime latestFulfilledDate;

  public static final String SERIALIZED_NAME_LINE_PROCESS_INFO = "LineProcessInfo";
  @SerializedName(SERIALIZED_NAME_LINE_PROCESS_INFO)
  private LineProcessInfo lineProcessInfo = null;

  public static final String SERIALIZED_NAME_LINE_TYPE = "LineType";
  @SerializedName(SERIALIZED_NAME_LINE_TYPE)
  private LineTypeId lineType = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MAX_APPEASEMENT_AMOUNT = "MaxAppeasementAmount";
  @SerializedName(SERIALIZED_NAME_MAX_APPEASEMENT_AMOUNT)
  private BigDecimal maxAppeasementAmount;

  public static final String SERIALIZED_NAME_MAX_FULFILLMENT_STATUS = "MaxFulfillmentStatus";
  @SerializedName(SERIALIZED_NAME_MAX_FULFILLMENT_STATUS)
  private KeyDTO maxFulfillmentStatus = null;

  public static final String SERIALIZED_NAME_MAX_FULFILLMENT_STATUS_ID = "MaxFulfillmentStatusId";
  @SerializedName(SERIALIZED_NAME_MAX_FULFILLMENT_STATUS_ID)
  private String maxFulfillmentStatusId;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_MIN_FULFILLMENT_STATUS = "MinFulfillmentStatus";
  @SerializedName(SERIALIZED_NAME_MIN_FULFILLMENT_STATUS)
  private KeyDTO minFulfillmentStatus = null;

  public static final String SERIALIZED_NAME_MIN_FULFILLMENT_STATUS_ID = "MinFulfillmentStatusId";
  @SerializedName(SERIALIZED_NAME_MIN_FULFILLMENT_STATUS_ID)
  private String minFulfillmentStatusId;

  public static final String SERIALIZED_NAME_ORDER_ID = "OrderId";
  @SerializedName(SERIALIZED_NAME_ORDER_ID)
  private String orderId;

  public static final String SERIALIZED_NAME_ORDER_LINE_ADDITIONAL = "OrderLineAdditional";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_ADDITIONAL)
  private OrderLineAdditional orderLineAdditional = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_ATTRIBUTE = "OrderLineAttribute";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_ATTRIBUTE)
  private List<OrderLineAttribute> orderLineAttribute = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_CANCEL_HISTORY = "OrderLineCancelHistory";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_CANCEL_HISTORY)
  private List<OrderLineCancelHistory> orderLineCancelHistory = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_CHARGE_DETAIL = "OrderLineChargeDetail";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_CHARGE_DETAIL)
  private List<OrderLineChargeDetail> orderLineChargeDetail = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_COMPONENTS = "OrderLineComponents";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_COMPONENTS)
  private List<OrderLineComponents> orderLineComponents = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_EXTENSION1 = "OrderLineExtension1";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_EXTENSION1)
  private OrderLineExtension1 orderLineExtension1 = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_EXTENSION2 = "OrderLineExtension2";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_EXTENSION2)
  private List<OrderLineExtension2> orderLineExtension2 = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_HOLD = "OrderLineHold";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_HOLD)
  private List<OrderLineHold> orderLineHold = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_ID = "OrderLineId";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_ID)
  private String orderLineId;

  public static final String SERIALIZED_NAME_ORDER_LINE_NOTE = "OrderLineNote";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_NOTE)
  private List<OrderLineNote> orderLineNote = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_PICKUP_DETAIL = "OrderLinePickupDetail";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_PICKUP_DETAIL)
  private List<OrderLinePickupDetail> orderLinePickupDetail = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_PRICE_OVERRIDE_HISTORY = "OrderLinePriceOverrideHistory";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_PRICE_OVERRIDE_HISTORY)
  private List<OrderLinePriceOverrideHistory> orderLinePriceOverrideHistory = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_PROMISING_INFO = "OrderLinePromisingInfo";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_PROMISING_INFO)
  private OrderLinePromisingInfo orderLinePromisingInfo = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_PROMOTION_REQUEST = "OrderLinePromotionRequest";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_PROMOTION_REQUEST)
  private List<OrderLinePromotionRequest> orderLinePromotionRequest = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_SALES_ASSOCIATE = "OrderLineSalesAssociate";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_SALES_ASSOCIATE)
  private List<OrderLineSalesAssociate> orderLineSalesAssociate = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_SUB_TOTAL = "OrderLineSubTotal";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_SUB_TOTAL)
  private BigDecimal orderLineSubTotal;

  public static final String SERIALIZED_NAME_ORDER_LINE_TAG_DETAIL = "OrderLineTagDetail";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_TAG_DETAIL)
  private List<OrderLineTagDetail> orderLineTagDetail = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_TAX_DETAIL = "OrderLineTaxDetail";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_TAX_DETAIL)
  private List<OrderLineTaxDetail> orderLineTaxDetail = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_TOTAL = "OrderLineTotal";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_TOTAL)
  private BigDecimal orderLineTotal;

  public static final String SERIALIZED_NAME_ORDER_LINE_V_A_S_INSTRUCTIONS = "OrderLineVASInstructions";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_V_A_S_INSTRUCTIONS)
  private List<OrderLineVASInstructions> orderLineVASInstructions = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_ORIGINAL_UNIT_PRICE = "OriginalUnitPrice";
  @SerializedName(SERIALIZED_NAME_ORIGINAL_UNIT_PRICE)
  private BigDecimal originalUnitPrice;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_LINE_CREATED_TIMESTAMP = "ParentLineCreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_PARENT_LINE_CREATED_TIMESTAMP)
  private OffsetDateTime parentLineCreatedTimestamp;

  public static final String SERIALIZED_NAME_PARENT_ORDER_ID = "ParentOrderId";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER_ID)
  private String parentOrderId;

  public static final String SERIALIZED_NAME_PARENT_ORDER_LINE_ID = "ParentOrderLineId";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER_LINE_ID)
  private String parentOrderLineId;

  public static final String SERIALIZED_NAME_PARENT_ORDER_LINE_TYPE = "ParentOrderLineType";
  @SerializedName(SERIALIZED_NAME_PARENT_ORDER_LINE_TYPE)
  private LineTypeId parentOrderLineType = null;

  public static final String SERIALIZED_NAME_PAYMENT_GROUP_ID = "PaymentGroupId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_GROUP_ID)
  private String paymentGroupId;

  public static final String SERIALIZED_NAME_PHYSICAL_ORIGIN_ID = "PhysicalOriginId";
  @SerializedName(SERIALIZED_NAME_PHYSICAL_ORIGIN_ID)
  private String physicalOriginId;

  public static final String SERIALIZED_NAME_PIPELINE_ID = "PipelineId";
  @SerializedName(SERIALIZED_NAME_PIPELINE_ID)
  private String pipelineId;

  public static final String SERIALIZED_NAME_PRIORITY = "Priority";
  @SerializedName(SERIALIZED_NAME_PRIORITY)
  private Long priority;

  public static final String SERIALIZED_NAME_PRODUCT_CLASS = "ProductClass";
  @SerializedName(SERIALIZED_NAME_PRODUCT_CLASS)
  private String productClass;

  public static final String SERIALIZED_NAME_PROMISED_DELIVERY_DATE = "PromisedDeliveryDate";
  @SerializedName(SERIALIZED_NAME_PROMISED_DELIVERY_DATE)
  private OffsetDateTime promisedDeliveryDate;

  public static final String SERIALIZED_NAME_PROMISED_SHIP_DATE = "PromisedShipDate";
  @SerializedName(SERIALIZED_NAME_PROMISED_SHIP_DATE)
  private OffsetDateTime promisedShipDate;

  public static final String SERIALIZED_NAME_QUANTITY = "Quantity";
  @SerializedName(SERIALIZED_NAME_QUANTITY)
  private Double quantity;

  public static final String SERIALIZED_NAME_QUANTITY_DETAIL = "QuantityDetail";
  @SerializedName(SERIALIZED_NAME_QUANTITY_DETAIL)
  private List<QuantityDetail> quantityDetail = null;

  public static final String SERIALIZED_NAME_REFUND_PRICE = "RefundPrice";
  @SerializedName(SERIALIZED_NAME_REFUND_PRICE)
  private BigDecimal refundPrice;

  public static final String SERIALIZED_NAME_REQUESTED_DELIVERY_DATE = "RequestedDeliveryDate";
  @SerializedName(SERIALIZED_NAME_REQUESTED_DELIVERY_DATE)
  private OffsetDateTime requestedDeliveryDate;

  public static final String SERIALIZED_NAME_RETURN_DETAIL = "ReturnDetail";
  @SerializedName(SERIALIZED_NAME_RETURN_DETAIL)
  private List<ReturnDetail> returnDetail = null;

  public static final String SERIALIZED_NAME_RETURN_LINE_TOTAL_WITHOUT_FEES = "ReturnLineTotalWithoutFees";
  @SerializedName(SERIALIZED_NAME_RETURN_LINE_TOTAL_WITHOUT_FEES)
  private BigDecimal returnLineTotalWithoutFees;

  public static final String SERIALIZED_NAME_RETURN_TYPE = "ReturnType";
  @SerializedName(SERIALIZED_NAME_RETURN_TYPE)
  private ReturnTypeId returnType = null;

  public static final String SERIALIZED_NAME_RETURNABLE_LINE_TOTAL = "ReturnableLineTotal";
  @SerializedName(SERIALIZED_NAME_RETURNABLE_LINE_TOTAL)
  private BigDecimal returnableLineTotal;

  public static final String SERIALIZED_NAME_RETURNABLE_QUANTITY = "ReturnableQuantity";
  @SerializedName(SERIALIZED_NAME_RETURNABLE_QUANTITY)
  private Double returnableQuantity;

  public static final String SERIALIZED_NAME_SELLING_LOCATION_ID = "SellingLocationId";
  @SerializedName(SERIALIZED_NAME_SELLING_LOCATION_ID)
  private String sellingLocationId;

  public static final String SERIALIZED_NAME_SERVICE_LEVEL_CODE = "ServiceLevelCode";
  @SerializedName(SERIALIZED_NAME_SERVICE_LEVEL_CODE)
  private String serviceLevelCode;

  public static final String SERIALIZED_NAME_SHIP_FROM_ADDRESS = "ShipFromAddress";
  @SerializedName(SERIALIZED_NAME_SHIP_FROM_ADDRESS)
  private ShipToAddress shipFromAddress = null;

  public static final String SERIALIZED_NAME_SHIP_FROM_ADDRESS_ID = "ShipFromAddressId";
  @SerializedName(SERIALIZED_NAME_SHIP_FROM_ADDRESS_ID)
  private String shipFromAddressId;

  public static final String SERIALIZED_NAME_SHIP_TO_ADDRESS = "ShipToAddress";
  @SerializedName(SERIALIZED_NAME_SHIP_TO_ADDRESS)
  private ShipToAddress shipToAddress = null;

  public static final String SERIALIZED_NAME_SHIP_TO_LOCATION_ID = "ShipToLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_TO_LOCATION_ID)
  private String shipToLocationId;

  public static final String SERIALIZED_NAME_SHIPPING_METHOD_ID = "ShippingMethodId";
  @SerializedName(SERIALIZED_NAME_SHIPPING_METHOD_ID)
  private String shippingMethodId;

  public static final String SERIALIZED_NAME_SMALL_IMAGE_U_R_I = "SmallImageURI";
  @SerializedName(SERIALIZED_NAME_SMALL_IMAGE_U_R_I)
  private String smallImageURI;

  public static final String SERIALIZED_NAME_STORE_SALE_ENTRY_METHOD = "StoreSaleEntryMethod";
  @SerializedName(SERIALIZED_NAME_STORE_SALE_ENTRY_METHOD)
  private StoreSaleEntryMethodId storeSaleEntryMethod = null;

  public static final String SERIALIZED_NAME_STREET_DATE = "StreetDate";
  @SerializedName(SERIALIZED_NAME_STREET_DATE)
  private OffsetDateTime streetDate;

  public static final String SERIALIZED_NAME_TAX_OVERRIDE_PERC_VALUE = "TaxOverridePercValue";
  @SerializedName(SERIALIZED_NAME_TAX_OVERRIDE_PERC_VALUE)
  private BigDecimal taxOverridePercValue;

  public static final String SERIALIZED_NAME_TAX_OVERRIDE_TYPE = "TaxOverrideType";
  @SerializedName(SERIALIZED_NAME_TAX_OVERRIDE_TYPE)
  private TaxOverrideTypeId taxOverrideType = null;

  public static final String SERIALIZED_NAME_TAX_OVERRIDE_VALUE = "TaxOverrideValue";
  @SerializedName(SERIALIZED_NAME_TAX_OVERRIDE_VALUE)
  private BigDecimal taxOverrideValue;

  public static final String SERIALIZED_NAME_TAX_SHIP_FROM_ADDRESS = "TaxShipFromAddress";
  @SerializedName(SERIALIZED_NAME_TAX_SHIP_FROM_ADDRESS)
  private ShipToAddress taxShipFromAddress = null;

  public static final String SERIALIZED_NAME_TAX_SHIP_FROM_LOCATION_ID = "TaxShipFromLocationId";
  @SerializedName(SERIALIZED_NAME_TAX_SHIP_FROM_LOCATION_ID)
  private String taxShipFromLocationId;

  public static final String SERIALIZED_NAME_TAXABLE_AMOUNT = "TaxableAmount";
  @SerializedName(SERIALIZED_NAME_TAXABLE_AMOUNT)
  private BigDecimal taxableAmount;

  public static final String SERIALIZED_NAME_TOTAL_CHARGES = "TotalCharges";
  @SerializedName(SERIALIZED_NAME_TOTAL_CHARGES)
  private BigDecimal totalCharges;

  public static final String SERIALIZED_NAME_TOTAL_DISCOUNT_ON_ITEM = "TotalDiscountOnItem";
  @SerializedName(SERIALIZED_NAME_TOTAL_DISCOUNT_ON_ITEM)
  private BigDecimal totalDiscountOnItem;

  public static final String SERIALIZED_NAME_TOTAL_DISCOUNTS = "TotalDiscounts";
  @SerializedName(SERIALIZED_NAME_TOTAL_DISCOUNTS)
  private BigDecimal totalDiscounts;

  public static final String SERIALIZED_NAME_TOTAL_INFORMATIONAL_TAXES = "TotalInformationalTaxes";
  @SerializedName(SERIALIZED_NAME_TOTAL_INFORMATIONAL_TAXES)
  private BigDecimal totalInformationalTaxes;

  public static final String SERIALIZED_NAME_TOTAL_TAXES = "TotalTaxes";
  @SerializedName(SERIALIZED_NAME_TOTAL_TAXES)
  private BigDecimal totalTaxes;

  public static final String SERIALIZED_NAME_TRANSACTION_REFERENCE_ID = "TransactionReferenceId";
  @SerializedName(SERIALIZED_NAME_TRANSACTION_REFERENCE_ID)
  private String transactionReferenceId;

  public static final String SERIALIZED_NAME_U_O_M = "UOM";
  @SerializedName(SERIALIZED_NAME_U_O_M)
  private String UOM;

  public static final String SERIALIZED_NAME_UNIT_PRICE = "UnitPrice";
  @SerializedName(SERIALIZED_NAME_UNIT_PRICE)
  private BigDecimal unitPrice;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_VALUE_ENTRY_REQUIRED = "ValueEntryRequired";
  @SerializedName(SERIALIZED_NAME_VALUE_ENTRY_REQUIRED)
  private Boolean valueEntryRequired;

  public static final String SERIALIZED_NAME_VOLUMETRIC_WEIGHT = "VolumetricWeight";
  @SerializedName(SERIALIZED_NAME_VOLUMETRIC_WEIGHT)
  private Double volumetricWeight;

  public static final String SERIALIZED_NAME_VOLUMETRIC_WEIGHT_U_O_M = "VolumetricWeightUOM";
  @SerializedName(SERIALIZED_NAME_VOLUMETRIC_WEIGHT_U_O_M)
  private UOMId volumetricWeightUOM = null;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderLine actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderLine putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

  /**
   * Get actions
   * @return actions
   **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderLine addressId(String addressId) {
    this.addressId = addressId;
    return this;
  }

  /**
   * Identifier of the shipping address, which is stored in the address table. Used if deliveryMethod is Ship to address
   * @return addressId
   **/
  
  public String getAddressId() {
    return addressId;
  }

  public void setAddressId(String addressId) {
    this.addressId = addressId;
  }

  public OrderLine allocation(List<Allocation> allocation) {
    this.allocation = allocation;
    return this;
  }

  public OrderLine addAllocationItem(Allocation allocationItem) {
    if (this.allocation == null) {
      this.allocation = new ArrayList<Allocation>();
    }
    this.allocation.add(allocationItem);
    return this;
  }

  /**
   * Get allocation
   * @return allocation
   **/
  
  public List<Allocation> getAllocation() {
    return allocation;
  }

  public void setAllocation(List<Allocation> allocation) {
    this.allocation = allocation;
  }

  public OrderLine allocationConfigId(String allocationConfigId) {
    this.allocationConfigId = allocationConfigId;
    return this;
  }

  /**
   * Identifies the allocation config which is used to allocate the order line. Used by the reapportionment scheduler to pick order lines for a given config id, when the config scheduler runs.
   * @return allocationConfigId
   **/
  
  public String getAllocationConfigId() {
    return allocationConfigId;
  }

  public void setAllocationConfigId(String allocationConfigId) {
    this.allocationConfigId = allocationConfigId;
  }

  public OrderLine alternateOrderLineId(String alternateOrderLineId) {
    this.alternateOrderLineId = alternateOrderLineId;
    return this;
  }

  /**
   * Unique alternate identifier of order line, as defined by external system
   * @return alternateOrderLineId
   **/
  
  public String getAlternateOrderLineId() {
    return alternateOrderLineId;
  }

  public void setAlternateOrderLineId(String alternateOrderLineId) {
    this.alternateOrderLineId = alternateOrderLineId;
  }

  public OrderLine businessDate(LocalDate businessDate) {
    this.businessDate = businessDate;
    return this;
  }

  /**
   * Store&#39;s Business Date which is set in Back Office. This can be different from the transaction created date if the store is kept open through more than one calendar date.
   * @return businessDate
   **/
  
  public LocalDate getBusinessDate() {
    return businessDate;
  }

  public void setBusinessDate(LocalDate businessDate) {
    this.businessDate = businessDate;
  }

  public OrderLine calculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
    return this;
  }

  /**
   * Get calculatedValues
   * @return calculatedValues
   **/
  
  public Object getCalculatedValues() {
    return calculatedValues;
  }

  public void setCalculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
  }

  public OrderLine cancelComments(String cancelComments) {
    this.cancelComments = cancelComments;
    return this;
  }

  /**
   * If isCancelled is true, these are comments entered for the cancellation
   * @return cancelComments
   **/
  
  public String getCancelComments() {
    return cancelComments;
  }

  public void setCancelComments(String cancelComments) {
    this.cancelComments = cancelComments;
  }

  public OrderLine cancelReason(ReasonId cancelReason) {
    this.cancelReason = cancelReason;
    return this;
  }

  /**
   * Get cancelReason
   * @return cancelReason
   **/
  
  public ReasonId getCancelReason() {
    return cancelReason;
  }

  public void setCancelReason(ReasonId cancelReason) {
    this.cancelReason = cancelReason;
  }

  /**
   * When an order line is cancelled, populate this field with Orderline.OrderLineSubTotal value.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return cancelledOrderLineSubTotal
   **/
  
  public BigDecimal getCancelledOrderLineSubTotal() {
    return cancelledOrderLineSubTotal;
  }

  public OrderLine cancelledTotalDiscounts(BigDecimal cancelledTotalDiscounts) {
    this.cancelledTotalDiscounts = cancelledTotalDiscounts;
    return this;
  }

  /**
   * When an order line is cancelled, populate this field with Orderline.TotalDiscounts value.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return cancelledTotalDiscounts
   **/
  
  public BigDecimal getCancelledTotalDiscounts() {
    return cancelledTotalDiscounts;
  }

  public void setCancelledTotalDiscounts(BigDecimal cancelledTotalDiscounts) {
    this.cancelledTotalDiscounts = cancelledTotalDiscounts;
  }

  public OrderLine carrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
    return this;
  }

  /**
   * Carrier which should be used to fulfill the order line e.g. Fedex, UPS etc
   * @return carrierCode
   **/
  
  public String getCarrierCode() {
    return carrierCode;
  }

  public void setCarrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
  }

  public OrderLine changeLog(ChangeLog changeLog) {
    this.changeLog = changeLog;
    return this;
  }

  /**
   * Get changeLog
   * @return changeLog
   **/
  
  public ChangeLog getChangeLog() {
    return changeLog;
  }

  public void setChangeLog(ChangeLog changeLog) {
    this.changeLog = changeLog;
  }

  public OrderLine createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

  /**
   * Created By
   * @return createdBy
   **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderLine createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

  /**
   * Created Timestamp
   * @return createdTimestamp
   **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderLine deliveryMethod(DeliveryMethodId deliveryMethod) {
    this.deliveryMethod = deliveryMethod;
    return this;
  }

  /**
   * Get deliveryMethod
   * @return deliveryMethod
   **/
  
  public DeliveryMethodId getDeliveryMethod() {
    return deliveryMethod;
  }

  public void setDeliveryMethod(DeliveryMethodId deliveryMethod) {
    this.deliveryMethod = deliveryMethod;
  }

  public OrderLine doNotShipBeforeDate(OffsetDateTime doNotShipBeforeDate) {
    this.doNotShipBeforeDate = doNotShipBeforeDate;
    return this;
  }

  /**
   * Earliest date on which the order line should be shipped; This is read only if IsPackAndHold is true.
   * @return doNotShipBeforeDate
   **/
  
  public OffsetDateTime getDoNotShipBeforeDate() {
    return doNotShipBeforeDate;
  }

  public void setDoNotShipBeforeDate(OffsetDateTime doNotShipBeforeDate) {
    this.doNotShipBeforeDate = doNotShipBeforeDate;
  }

  public OrderLine effectiveRank(String effectiveRank) {
    this.effectiveRank = effectiveRank;
    return this;
  }

  /**
   * Rank of the order line, as calculated by the system during prioritization.
   * @return effectiveRank
   **/
  
  public String getEffectiveRank() {
    return effectiveRank;
  }

  public void setEffectiveRank(String effectiveRank) {
    this.effectiveRank = effectiveRank;
  }

  public OrderLine extended(Object extended) {
    this.extended = extended;
    return this;
  }

  /**
   * Extended Properties
   * @return extended
   **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderLine fulfillmentDetail(List<FulfillmentDetail> fulfillmentDetail) {
    this.fulfillmentDetail = fulfillmentDetail;
    return this;
  }

  public OrderLine addFulfillmentDetailItem(FulfillmentDetail fulfillmentDetailItem) {
    if (this.fulfillmentDetail == null) {
      this.fulfillmentDetail = new ArrayList<FulfillmentDetail>();
    }
    this.fulfillmentDetail.add(fulfillmentDetailItem);
    return this;
  }

  /**
   * Get fulfillmentDetail
   * @return fulfillmentDetail
   **/
  
  public List<FulfillmentDetail> getFulfillmentDetail() {
    return fulfillmentDetail;
  }

  public void setFulfillmentDetail(List<FulfillmentDetail> fulfillmentDetail) {
    this.fulfillmentDetail = fulfillmentDetail;
  }

  public OrderLine fulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
    return this;
  }

  /**
   * ID used to group items which need to be fulfilled together (e.g. warranties, kits, gift with purchase, etc.)
   * @return fulfillmentGroupId
   **/
  
  public String getFulfillmentGroupId() {
    return fulfillmentGroupId;
  }

  public void setFulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
  }

  public OrderLine fulfillmentStatus(String fulfillmentStatus) {
    this.fulfillmentStatus = fulfillmentStatus;
    return this;
  }

  /**
   * Get fulfillmentStatus
   * @return fulfillmentStatus
   **/
  
  public String getFulfillmentStatus() {
    return fulfillmentStatus;
  }

  public void setFulfillmentStatus(String fulfillmentStatus) {
    this.fulfillmentStatus = fulfillmentStatus;
  }

  public OrderLine giftCardValue(BigDecimal giftCardValue) {
    this.giftCardValue = giftCardValue;
    return this;
  }

  /**
   * Activation value, if the item is a gift card
   * minimum: 0
   * maximum: 99999999999999.98
   * @return giftCardValue
   **/
  
  public BigDecimal getGiftCardValue() {
    return giftCardValue;
  }

  public void setGiftCardValue(BigDecimal giftCardValue) {
    this.giftCardValue = giftCardValue;
  }

  /**
   * This flag indicates if the order line has component items defined. This is a calculated field, if there is at least one OrderLineComponents entity for the current line then this flag should set to true.
   * @return hasComponents
   **/
  
  public Boolean getHasComponents() {
    return hasComponents;
  }

  public OrderLine isActivationRequired(Boolean isActivationRequired) {
    this.isActivationRequired = isActivationRequired;
    return this;
  }

  /**
   * Indicates if item is gift card item needs to be activated freshly or if its an reload to an existing gift card
   * @return isActivationRequired
   **/
  
  public Boolean getIsActivationRequired() {
    return isActivationRequired;
  }

  public void setIsActivationRequired(Boolean isActivationRequired) {
    this.isActivationRequired = isActivationRequired;
  }

  public OrderLine isCancelled(Boolean isCancelled) {
    this.isCancelled = isCancelled;
    return this;
  }

  /**
   * IIndicates if the order line is cancelled
   * @return isCancelled
   **/
  
  public Boolean getIsCancelled() {
    return isCancelled;
  }

  public void setIsCancelled(Boolean isCancelled) {
    this.isCancelled = isCancelled;
  }

  public OrderLine isDiscountable(Boolean isDiscountable) {
    this.isDiscountable = isDiscountable;
    return this;
  }

  /**
   * Indicates if an order line can have manual discounts applied against it. E.g. header discounts applied at POS should not be prorated to gift card items. When a header-level charge of type Discount is applied, if the chargeTypeOverride table has skipNonDiscountableItems &#x3D; true for Discounts, then the header discount is only prorated to lines where isDiscountable &#x3D; true. If the chargeTypeOverride config does not have skipNonDiscountableItems &#x3D; true for Discounts, then header discounts are prorated across all order lines, including non-discountable items. Validations for line-level discounts should be handled in order capture systems; there is no validation of line level discounts saved against order lines which are not discountable. This logic applies only to charge type Discount; it does not affect appeasements, coupons, promotions, SnH, VAS, etc.
   * @return isDiscountable
   **/
  
  public Boolean getIsDiscountable() {
    return isDiscountable;
  }

  public void setIsDiscountable(Boolean isDiscountable) {
    this.isDiscountable = isDiscountable;
  }

  public OrderLine isEligibleForOverride(Boolean isEligibleForOverride) {
    this.isEligibleForOverride = isEligibleForOverride;
    return this;
  }

  /**
   * IsEligibleForOverride
   * @return isEligibleForOverride
   **/
  
  public Boolean getIsEligibleForOverride() {
    return isEligibleForOverride;
  }

  public void setIsEligibleForOverride(Boolean isEligibleForOverride) {
    this.isEligibleForOverride = isEligibleForOverride;
  }

  public OrderLine isEvenExchange(Boolean isEvenExchange) {
    this.isEvenExchange = isEvenExchange;
    return this;
  }

  /**
   * Identifies items being exchanged for an item of the same style, but in a different color or size. If isEvenExchange &#x3D; true and rePriceEvenExchanges is disabled, then line price, taxes, and charges are copied from the parent order line. For example, if the original line has unit price $200, $10 tax, and $8 SnH, then an even exchange line would be created with unit price is set to $200, with $10 line tax and $8 SnH. If isEvenExchange &#x3D; true and no parent order line is found or parent order line is null, then an error is thrown. If isEvenExchange &#x3D; true, then isReturn cannot be set to true. If isEvenExchange &#x3D; false or rePriceEvenExchanges is enabled, then the line is sent through pricing, promo, tax, and SnH per the typical sale flow.
   * @return isEvenExchange
   **/
  
  public Boolean getIsEvenExchange() {
    return isEvenExchange;
  }

  public void setIsEvenExchange(Boolean isEvenExchange) {
    this.isEvenExchange = isEvenExchange;
  }

  public OrderLine isExchangeable(Boolean isExchangeable) {
    this.isExchangeable = isExchangeable;
    return this;
  }

  /**
   * Indicates if item is eligible to be exchagned. Can be used by return capture systems, such as the call center UI, POS UI, or any third party return capture system. If isExchangeable &#x3D; false, then the UI should display the item as non-exchangeable but could optionally allow the user to override this and exchange the item anyways. This field is not used by the order component to validate during exchange order creation; any restrictions must be done by the UI.
   * @return isExchangeable
   **/
  
  public Boolean getIsExchangeable() {
    return isExchangeable;
  }

  public void setIsExchangeable(Boolean isExchangeable) {
    this.isExchangeable = isExchangeable;
  }

  public OrderLine isGift(Boolean isGift) {
    this.isGift = isGift;
    return this;
  }

  /**
   * Indicates if item is a gift
   * @return isGift
   **/
  
  public Boolean getIsGift() {
    return isGift;
  }

  public void setIsGift(Boolean isGift) {
    this.isGift = isGift;
  }

  public OrderLine isGiftCard(Boolean isGiftCard) {
    this.isGiftCard = isGiftCard;
    return this;
  }

  /**
   * This flag indicates its a gift card item
   * @return isGiftCard
   **/
  
  public Boolean getIsGiftCard() {
    return isGiftCard;
  }

  public void setIsGiftCard(Boolean isGiftCard) {
    this.isGiftCard = isGiftCard;
  }

  public OrderLine isHazmat(Boolean isHazmat) {
    this.isHazmat = isHazmat;
    return this;
  }

  /**
   * Indicates if the item qualifies as hazmat
   * @return isHazmat
   **/
  
  public Boolean getIsHazmat() {
    return isHazmat;
  }

  public void setIsHazmat(Boolean isHazmat) {
    this.isHazmat = isHazmat;
  }

  public OrderLine isItemNotOnFile(Boolean isItemNotOnFile) {
    this.isItemNotOnFile = isItemNotOnFile;
    return this;
  }

  /**
   * If value is yes, this indicates the item is not stored in the Item master
   * @return isItemNotOnFile
   **/
  
  public Boolean getIsItemNotOnFile() {
    return isItemNotOnFile;
  }

  public void setIsItemNotOnFile(Boolean isItemNotOnFile) {
    this.isItemNotOnFile = isItemNotOnFile;
  }

  public OrderLine isItemTaxExemptable(Boolean isItemTaxExemptable) {
    this.isItemTaxExemptable = isItemTaxExemptable;
    return this;
  }

  /**
   * This flag indicates if tax exempt is applicable on the item. by default all items will have this value set to true.
   * @return isItemTaxExemptable
   **/
  
  public Boolean getIsItemTaxExemptable() {
    return isItemTaxExemptable;
  }

  public void setIsItemTaxExemptable(Boolean isItemTaxExemptable) {
    this.isItemTaxExemptable = isItemTaxExemptable;
  }

  public OrderLine isItemTaxOverridable(Boolean isItemTaxOverridable) {
    this.isItemTaxOverridable = isItemTaxOverridable;
    return this;
  }

  /**
   * This flag indicates if tax exempt is applicable on the item. by default all items have this value set to true.
   * @return isItemTaxOverridable
   **/
  
  public Boolean getIsItemTaxOverridable() {
    return isItemTaxOverridable;
  }

  public void setIsItemTaxOverridable(Boolean isItemTaxOverridable) {
    this.isItemTaxOverridable = isItemTaxOverridable;
  }

  public OrderLine isNonMerchandise(Boolean isNonMerchandise) {
    this.isNonMerchandise = isNonMerchandise;
    return this;
  }

  /**
   * This flag indicates its a non merchandise item
   * @return isNonMerchandise
   **/
  
  public Boolean getIsNonMerchandise() {
    return isNonMerchandise;
  }

  public void setIsNonMerchandise(Boolean isNonMerchandise) {
    this.isNonMerchandise = isNonMerchandise;
  }

  public OrderLine isOnHold(Boolean isOnHold) {
    this.isOnHold = isOnHold;
    return this;
  }

  /**
   * Indicates if the order line is on hold
   * @return isOnHold
   **/
  
  public Boolean getIsOnHold() {
    return isOnHold;
  }

  public void setIsOnHold(Boolean isOnHold) {
    this.isOnHold = isOnHold;
  }

  public OrderLine isPackAndHold(Boolean isPackAndHold) {
    this.isPackAndHold = isPackAndHold;
    return this;
  }

  /**
   * Indicates if the order line should be held until the DoNotShipBeforeDate
   * @return isPackAndHold
   **/
  
  public Boolean getIsPackAndHold() {
    return isPackAndHold;
  }

  public void setIsPackAndHold(Boolean isPackAndHold) {
    this.isPackAndHold = isPackAndHold;
  }

  public OrderLine isPerishable(Boolean isPerishable) {
    this.isPerishable = isPerishable;
    return this;
  }

  /**
   * Indicates if the item is perishable
   * @return isPerishable
   **/
  
  public Boolean getIsPerishable() {
    return isPerishable;
  }

  public void setIsPerishable(Boolean isPerishable) {
    this.isPerishable = isPerishable;
  }

  public OrderLine isPreOrder(Boolean isPreOrder) {
    this.isPreOrder = isPreOrder;
    return this;
  }

  /**
   * Indicates if item is pre-order
   * @return isPreOrder
   **/
  
  public Boolean getIsPreOrder() {
    return isPreOrder;
  }

  public void setIsPreOrder(Boolean isPreOrder) {
    this.isPreOrder = isPreOrder;
  }

  public OrderLine isPreSale(Boolean isPreSale) {
    this.isPreSale = isPreSale;
    return this;
  }

  /**
   * Indicates if item is pre-sale
   * @return isPreSale
   **/
  
  public Boolean getIsPreSale() {
    return isPreSale;
  }

  public void setIsPreSale(Boolean isPreSale) {
    this.isPreSale = isPreSale;
  }

  public OrderLine isPriceOverridden(Boolean isPriceOverridden) {
    this.isPriceOverridden = isPriceOverridden;
    return this;
  }

  /**
   * Indicates if the price has been overridden
   * @return isPriceOverridden
   **/
  
  public Boolean getIsPriceOverridden() {
    return isPriceOverridden;
  }

  public void setIsPriceOverridden(Boolean isPriceOverridden) {
    this.isPriceOverridden = isPriceOverridden;
  }

  public OrderLine isPriceOverrideable(Boolean isPriceOverrideable) {
    this.isPriceOverrideable = isPriceOverrideable;
    return this;
  }

  /**
   * Indicates if price can be overridden for the item. Used by order capture systems such as point of sale to determine whether to allow users to override an item price.
   * @return isPriceOverrideable
   **/
  
  public Boolean getIsPriceOverrideable() {
    return isPriceOverrideable;
  }

  public void setIsPriceOverrideable(Boolean isPriceOverrideable) {
    this.isPriceOverrideable = isPriceOverrideable;
  }

  public OrderLine isReceiptExpected(Boolean isReceiptExpected) {
    this.isReceiptExpected = isReceiptExpected;
    return this;
  }

  /**
   * For return order lines where the delivery method is ship to return center, this flag indicates if receipt of the return item is expected by the retailer. If true, then the customer plans to ship the item back to the retailer&#39;s return center. If false, then the customer does not return any item to the retailer (aka field destroy). If receipt expected is false, then the retailer can decide to approve the return and issue a refund or to reject the return and issue no refund. The return configuration autoApproveReceiptNotExpected indicates whether these order lines are automatically approved on order confirmation or that a CSR needs to manually approve these lines.
   * @return isReceiptExpected
   **/
  
  public Boolean getIsReceiptExpected() {
    return isReceiptExpected;
  }

  public void setIsReceiptExpected(Boolean isReceiptExpected) {
    this.isReceiptExpected = isReceiptExpected;
  }

  /**
   * This flag indicates if a gift card line is added by the system to process any refunds resulting from order updates where the payment was originally made by cash or gift cards. Order updates can include order cancellation, applying appeasement, regular returns resulting in refunds or a gift receipt returns
   * @return isRefundGiftCard
   **/
  
  public Boolean getIsRefundGiftCard() {
    return isRefundGiftCard;
  }

  public OrderLine isReturn(Boolean isReturn) {
    this.isReturn = isReturn;
    return this;
  }

  /**
   * Indicates if the item is being returned. If isReturn &#x3D; true and an order has not yet been priced, then return price, taxes, and charges are pulled from the parent order line. For example, if sale line has unit price $200, $10 tax, and $8 SnH, then when a return line is created against this parent order line, the return line unit price is set to -$200, with -$10 line tax and -$8 SnH. If isReturn &#x3D; true and no parent order line is found, then the current price and tax rates are used to calculate the refund price and tax. If isReturn &#x3D; false, then the line is sent through pricing, promo, tax, and SnH per the typical sale flow, unless the line is an even exchange (see isEvenExchange)
   * @return isReturn
   **/
  
  public Boolean getIsReturn() {
    return isReturn;
  }

  public void setIsReturn(Boolean isReturn) {
    this.isReturn = isReturn;
  }

  public OrderLine isReturnAllowedByAgePolicy(Boolean isReturnAllowedByAgePolicy) {
    this.isReturnAllowedByAgePolicy = isReturnAllowedByAgePolicy;
    return this;
  }

  /**
   * Get isReturnAllowedByAgePolicy
   * @return isReturnAllowedByAgePolicy
   **/
  
  public Boolean getIsReturnAllowedByAgePolicy() {
    return isReturnAllowedByAgePolicy;
  }

  public void setIsReturnAllowedByAgePolicy(Boolean isReturnAllowedByAgePolicy) {
    this.isReturnAllowedByAgePolicy = isReturnAllowedByAgePolicy;
  }

  public OrderLine isReturnable(Boolean isReturnable) {
    this.isReturnable = isReturnable;
    return this;
  }

  /**
   * Indicates if item is returnable. Can be used by return capture systems, such as the call center UI, POS UI, or any third party return capture system. If IsReturanble &#x3D; false, then the UI should display the item as non-returnable but could optionally allow the user to override this and return the item anyways. This field is not used by the order component to validate returnability during return order creation; any restrictions must be done by the UI.
   * @return isReturnable
   **/
  
  public Boolean getIsReturnable() {
    return isReturnable;
  }

  public void setIsReturnable(Boolean isReturnable) {
    this.isReturnable = isReturnable;
  }

  public OrderLine isReturnableAtStore(Boolean isReturnableAtStore) {
    this.isReturnableAtStore = isReturnableAtStore;
    return this;
  }

  /**
   * Indicates if item is returnable at Store
   * @return isReturnableAtStore
   **/
  
  public Boolean getIsReturnableAtStore() {
    return isReturnableAtStore;
  }

  public void setIsReturnableAtStore(Boolean isReturnableAtStore) {
    this.isReturnableAtStore = isReturnableAtStore;
  }

  public OrderLine isTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
    return this;
  }

  /**
   * Indicates if tax is included in the UnitPrice, in case of a Value-Added Tax (VAT) or a return charge.
   * @return isTaxIncluded
   **/
  
  public Boolean getIsTaxIncluded() {
    return isTaxIncluded;
  }

  public void setIsTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
  }

  public OrderLine isTaxOverridden(Boolean isTaxOverridden) {
    this.isTaxOverridden = isTaxOverridden;
    return this;
  }

  /**
   * Indicates if the line tax has been overridden
   * @return isTaxOverridden
   **/
  
  public Boolean getIsTaxOverridden() {
    return isTaxOverridden;
  }

  public void setIsTaxOverridden(Boolean isTaxOverridden) {
    this.isTaxOverridden = isTaxOverridden;
  }

  public OrderLine itemBarcode(String itemBarcode) {
    this.itemBarcode = itemBarcode;
    return this;
  }

  /**
   * This field will capture the X-ref info when we have added item using alternate UPC or X-ref
   * @return itemBarcode
   **/
  
  public String getItemBarcode() {
    return itemBarcode;
  }

  public void setItemBarcode(String itemBarcode) {
    this.itemBarcode = itemBarcode;
  }

  public OrderLine itemBrand(String itemBrand) {
    this.itemBrand = itemBrand;
    return this;
  }

  /**
   * Brand of the ordered item
   * @return itemBrand
   **/
  
  public String getItemBrand() {
    return itemBrand;
  }

  public void setItemBrand(String itemBrand) {
    this.itemBrand = itemBrand;
  }

  public OrderLine itemColorDescription(String itemColorDescription) {
    this.itemColorDescription = itemColorDescription;
    return this;
  }

  /**
   * Color of the ordered item
   * @return itemColorDescription
   **/
  
  public String getItemColorDescription() {
    return itemColorDescription;
  }

  public void setItemColorDescription(String itemColorDescription) {
    this.itemColorDescription = itemColorDescription;
  }

  public OrderLine itemDepartmentName(String itemDepartmentName) {
    this.itemDepartmentName = itemDepartmentName;
    return this;
  }

  /**
   * DepartmentName of the ordered item.
   * @return itemDepartmentName
   **/
  
  public String getItemDepartmentName() {
    return itemDepartmentName;
  }

  public void setItemDepartmentName(String itemDepartmentName) {
    this.itemDepartmentName = itemDepartmentName;
  }

  public OrderLine itemDepartmentNumber(Long itemDepartmentNumber) {
    this.itemDepartmentNumber = itemDepartmentNumber;
    return this;
  }

  /**
   * Department number of the item. Ensure this value is defined as an integer, if this attribute needs to be copied to the order line entity. For example, when an order is created with item A, if item A has department number 678 defined, then this department number is saved on the order line.
   * minimum: 0
   * maximum: -8446744073709551617
   * @return itemDepartmentNumber
   **/
  
  public Long getItemDepartmentNumber() {
    return itemDepartmentNumber;
  }

  public void setItemDepartmentNumber(Long itemDepartmentNumber) {
    this.itemDepartmentNumber = itemDepartmentNumber;
  }

  public OrderLine itemDeptNumber(String itemDeptNumber) {
    this.itemDeptNumber = itemDeptNumber;
    return this;
  }

  /**
   * Department number of the item. The value of this attribute is copied from Item to the order line entity. For example, when an order is created with Item A which belongs to the department number “Softline123”, then this department number is copied and saved in the order line which contains the Item A.
   * @return itemDeptNumber
   **/
  
  public String getItemDeptNumber() {
    return itemDeptNumber;
  }

  public void setItemDeptNumber(String itemDeptNumber) {
    this.itemDeptNumber = itemDeptNumber;
  }

  public OrderLine itemDescription(String itemDescription) {
    this.itemDescription = itemDescription;
    return this;
  }

  /**
   * Description of the ordered item
   * @return itemDescription
   **/
  
  public String getItemDescription() {
    return itemDescription;
  }

  public void setItemDescription(String itemDescription) {
    this.itemDescription = itemDescription;
  }

  public OrderLine itemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

  /**
   * The item Id for the item on this order line.
   * @return itemId
   **/
  
  public String getItemId() {
    return itemId;
  }

  public void setItemId(String itemId) {
    this.itemId = itemId;
  }

  public OrderLine itemMaxDiscountAmount(BigDecimal itemMaxDiscountAmount) {
    this.itemMaxDiscountAmount = itemMaxDiscountAmount;
    return this;
  }

  /**
   * This field indicates the maxBalanceDue amount of discount in money that can be applied to the item in orderline.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return itemMaxDiscountAmount
   **/
  
  public BigDecimal getItemMaxDiscountAmount() {
    return itemMaxDiscountAmount;
  }

  public void setItemMaxDiscountAmount(BigDecimal itemMaxDiscountAmount) {
    this.itemMaxDiscountAmount = itemMaxDiscountAmount;
  }

  public OrderLine itemMaxDiscountPercentage(BigDecimal itemMaxDiscountPercentage) {
    this.itemMaxDiscountPercentage = itemMaxDiscountPercentage;
    return this;
  }

  /**
   * This field indicates the max amount of discount in percentage that can be applied to the item in the orderline.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return itemMaxDiscountPercentage
   **/
  
  public BigDecimal getItemMaxDiscountPercentage() {
    return itemMaxDiscountPercentage;
  }

  public void setItemMaxDiscountPercentage(BigDecimal itemMaxDiscountPercentage) {
    this.itemMaxDiscountPercentage = itemMaxDiscountPercentage;
  }

  public OrderLine itemSeason(String itemSeason) {
    this.itemSeason = itemSeason;
    return this;
  }

  /**
   * Season of the ordered item
   * @return itemSeason
   **/
  
  public String getItemSeason() {
    return itemSeason;
  }

  public void setItemSeason(String itemSeason) {
    this.itemSeason = itemSeason;
  }

  public OrderLine itemShortDescription(String itemShortDescription) {
    this.itemShortDescription = itemShortDescription;
    return this;
  }

  /**
   * Short Description of the ordered item
   * @return itemShortDescription
   **/
  
  public String getItemShortDescription() {
    return itemShortDescription;
  }

  public void setItemShortDescription(String itemShortDescription) {
    this.itemShortDescription = itemShortDescription;
  }

  public OrderLine itemSize(String itemSize) {
    this.itemSize = itemSize;
    return this;
  }

  /**
   * Size of the ordered item
   * @return itemSize
   **/
  
  public String getItemSize() {
    return itemSize;
  }

  public void setItemSize(String itemSize) {
    this.itemSize = itemSize;
  }

  public OrderLine itemStyle(String itemStyle) {
    this.itemStyle = itemStyle;
    return this;
  }

  /**
   * Style of the ordered item
   * @return itemStyle
   **/
  
  public String getItemStyle() {
    return itemStyle;
  }

  public void setItemStyle(String itemStyle) {
    this.itemStyle = itemStyle;
  }

  public OrderLine itemTaxCode(String itemTaxCode) {
    this.itemTaxCode = itemTaxCode;
    return this;
  }

  /**
   * Item Tax Code
   * @return itemTaxCode
   **/
  
  public String getItemTaxCode() {
    return itemTaxCode;
  }

  public void setItemTaxCode(String itemTaxCode) {
    this.itemTaxCode = itemTaxCode;
  }

  public OrderLine latestDeliveryDate(OffsetDateTime latestDeliveryDate) {
    this.latestDeliveryDate = latestDeliveryDate;
    return this;
  }

  /**
   * Latest possible delivery date on which the items should be delivered to the destination
   * @return latestDeliveryDate
   **/
  
  public OffsetDateTime getLatestDeliveryDate() {
    return latestDeliveryDate;
  }

  public void setLatestDeliveryDate(OffsetDateTime latestDeliveryDate) {
    this.latestDeliveryDate = latestDeliveryDate;
  }

  public OrderLine latestFulfilledDate(OffsetDateTime latestFulfilledDate) {
    this.latestFulfilledDate = latestFulfilledDate;
    return this;
  }

  /**
   * Get latestFulfilledDate
   * @return latestFulfilledDate
   **/
  
  public OffsetDateTime getLatestFulfilledDate() {
    return latestFulfilledDate;
  }

  public void setLatestFulfilledDate(OffsetDateTime latestFulfilledDate) {
    this.latestFulfilledDate = latestFulfilledDate;
  }

  public OrderLine lineProcessInfo(LineProcessInfo lineProcessInfo) {
    this.lineProcessInfo = lineProcessInfo;
    return this;
  }

  /**
   * Get lineProcessInfo
   * @return lineProcessInfo
   **/
  
  public LineProcessInfo getLineProcessInfo() {
    return lineProcessInfo;
  }

  public void setLineProcessInfo(LineProcessInfo lineProcessInfo) {
    this.lineProcessInfo = lineProcessInfo;
  }

  public OrderLine lineType(LineTypeId lineType) {
    this.lineType = lineType;
    return this;
  }

  /**
   * Get lineType
   * @return lineType
   **/
  
  public LineTypeId getLineType() {
    return lineType;
  }

  public void setLineType(LineTypeId lineType) {
    this.lineType = lineType;
  }

  public OrderLine localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

  /**
   * Get localizedTo
   * @return localizedTo
   **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderLine maxAppeasementAmount(BigDecimal maxAppeasementAmount) {
    this.maxAppeasementAmount = maxAppeasementAmount;
    return this;
  }

  /**
   * Get maxAppeasementAmount
   * @return maxAppeasementAmount
   **/
  
  public BigDecimal getMaxAppeasementAmount() {
    return maxAppeasementAmount;
  }

  public void setMaxAppeasementAmount(BigDecimal maxAppeasementAmount) {
    this.maxAppeasementAmount = maxAppeasementAmount;
  }

  public OrderLine maxFulfillmentStatus(KeyDTO maxFulfillmentStatus) {
    this.maxFulfillmentStatus = maxFulfillmentStatus;
    return this;
  }

  /**
   * Get maxFulfillmentStatus
   * @return maxFulfillmentStatus
   **/
  
  public KeyDTO getMaxFulfillmentStatus() {
    return maxFulfillmentStatus;
  }

  public void setMaxFulfillmentStatus(KeyDTO maxFulfillmentStatus) {
    this.maxFulfillmentStatus = maxFulfillmentStatus;
  }

  /**
   * Indicates the maximum fulfillment lifecycle status of all units on the order line
   * @return maxFulfillmentStatusId
   **/
  
  public String getMaxFulfillmentStatusId() {
    return maxFulfillmentStatusId;
  }

  public void setMaxFulfillmentStatusId(String maxFulfillmentStatusId) {
    this.maxFulfillmentStatusId = maxFulfillmentStatusId;
  }

  public OrderLine messages(Messages messages) {
    this.messages = messages;
    return this;
  }

  /**
   * Get messages
   * @return messages
   **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderLine minFulfillmentStatus(KeyDTO minFulfillmentStatus) {
    this.minFulfillmentStatus = minFulfillmentStatus;
    return this;
  }

  /**
   * Get minFulfillmentStatus
   * @return minFulfillmentStatus
   **/
  
  public KeyDTO getMinFulfillmentStatus() {
    return minFulfillmentStatus;
  }

  public void setMinFulfillmentStatus(KeyDTO minFulfillmentStatus) {
    this.minFulfillmentStatus = minFulfillmentStatus;
  }

  /**
   * Indicates the minimum fulfillment lifecycle status of all units on the order line
   * @return minFulfillmentStatusId
   **/
  
  public String getMinFulfillmentStatusId() {
    return minFulfillmentStatusId;
  }

  public void setMinFulfillmentStatusId(String minFulfillmentStatusId) {
    this.minFulfillmentStatusId = minFulfillmentStatusId;
  }

  public OrderLine orderId(String orderId) {
    this.orderId = orderId;
    return this;
  }

  /**
   * Unique identifier of the order with which the OrderLine is associated
   * @return orderId
   **/
  
  public String getOrderId() {
    return orderId;
  }

  public void setOrderId(String orderId) {
    this.orderId = orderId;
  }

  public OrderLine orderLineAdditional(OrderLineAdditional orderLineAdditional) {
    this.orderLineAdditional = orderLineAdditional;
    return this;
  }

  /**
   * Get orderLineAdditional
   * @return orderLineAdditional
   **/
  
  public OrderLineAdditional getOrderLineAdditional() {
    return orderLineAdditional;
  }

  public void setOrderLineAdditional(OrderLineAdditional orderLineAdditional) {
    this.orderLineAdditional = orderLineAdditional;
  }

  public OrderLine orderLineAttribute(List<OrderLineAttribute> orderLineAttribute) {
    this.orderLineAttribute = orderLineAttribute;
    return this;
  }

  public OrderLine addOrderLineAttributeItem(OrderLineAttribute orderLineAttributeItem) {
    if (this.orderLineAttribute == null) {
      this.orderLineAttribute = new ArrayList<OrderLineAttribute>();
    }
    this.orderLineAttribute.add(orderLineAttributeItem);
    return this;
  }

  /**
   * Get orderLineAttribute
   * @return orderLineAttribute
   **/
  
  public List<OrderLineAttribute> getOrderLineAttribute() {
    return orderLineAttribute;
  }

  public void setOrderLineAttribute(List<OrderLineAttribute> orderLineAttribute) {
    this.orderLineAttribute = orderLineAttribute;
  }

  public OrderLine orderLineCancelHistory(List<OrderLineCancelHistory> orderLineCancelHistory) {
    this.orderLineCancelHistory = orderLineCancelHistory;
    return this;
  }

  public OrderLine addOrderLineCancelHistoryItem(OrderLineCancelHistory orderLineCancelHistoryItem) {
    if (this.orderLineCancelHistory == null) {
      this.orderLineCancelHistory = new ArrayList<OrderLineCancelHistory>();
    }
    this.orderLineCancelHistory.add(orderLineCancelHistoryItem);
    return this;
  }

  /**
   * Get orderLineCancelHistory
   * @return orderLineCancelHistory
   **/
  
  public List<OrderLineCancelHistory> getOrderLineCancelHistory() {
    return orderLineCancelHistory;
  }

  public void setOrderLineCancelHistory(List<OrderLineCancelHistory> orderLineCancelHistory) {
    this.orderLineCancelHistory = orderLineCancelHistory;
  }

  public OrderLine orderLineChargeDetail(List<OrderLineChargeDetail> orderLineChargeDetail) {
    this.orderLineChargeDetail = orderLineChargeDetail;
    return this;
  }

  public OrderLine addOrderLineChargeDetailItem(OrderLineChargeDetail orderLineChargeDetailItem) {
    if (this.orderLineChargeDetail == null) {
      this.orderLineChargeDetail = new ArrayList<OrderLineChargeDetail>();
    }
    this.orderLineChargeDetail.add(orderLineChargeDetailItem);
    return this;
  }

  /**
   * Get orderLineChargeDetail
   * @return orderLineChargeDetail
   **/
  
  public List<OrderLineChargeDetail> getOrderLineChargeDetail() {
    return orderLineChargeDetail;
  }

  public void setOrderLineChargeDetail(List<OrderLineChargeDetail> orderLineChargeDetail) {
    this.orderLineChargeDetail = orderLineChargeDetail;
  }

  public OrderLine orderLineComponents(List<OrderLineComponents> orderLineComponents) {
    this.orderLineComponents = orderLineComponents;
    return this;
  }

  public OrderLine addOrderLineComponentsItem(OrderLineComponents orderLineComponentsItem) {
    if (this.orderLineComponents == null) {
      this.orderLineComponents = new ArrayList<OrderLineComponents>();
    }
    this.orderLineComponents.add(orderLineComponentsItem);
    return this;
  }

  /**
   * Get orderLineComponents
   * @return orderLineComponents
   **/
  
  public List<OrderLineComponents> getOrderLineComponents() {
    return orderLineComponents;
  }

  public void setOrderLineComponents(List<OrderLineComponents> orderLineComponents) {
    this.orderLineComponents = orderLineComponents;
  }

  public OrderLine orderLineExtension1(OrderLineExtension1 orderLineExtension1) {
    this.orderLineExtension1 = orderLineExtension1;
    return this;
  }

  /**
   * Get orderLineExtension1
   * @return orderLineExtension1
   **/
  
  public OrderLineExtension1 getOrderLineExtension1() {
    return orderLineExtension1;
  }

  public void setOrderLineExtension1(OrderLineExtension1 orderLineExtension1) {
    this.orderLineExtension1 = orderLineExtension1;
  }

  public OrderLine orderLineExtension2(List<OrderLineExtension2> orderLineExtension2) {
    this.orderLineExtension2 = orderLineExtension2;
    return this;
  }

  public OrderLine addOrderLineExtension2Item(OrderLineExtension2 orderLineExtension2Item) {
    if (this.orderLineExtension2 == null) {
      this.orderLineExtension2 = new ArrayList<OrderLineExtension2>();
    }
    this.orderLineExtension2.add(orderLineExtension2Item);
    return this;
  }

  /**
   * Get orderLineExtension2
   * @return orderLineExtension2
   **/
  
  public List<OrderLineExtension2> getOrderLineExtension2() {
    return orderLineExtension2;
  }

  public void setOrderLineExtension2(List<OrderLineExtension2> orderLineExtension2) {
    this.orderLineExtension2 = orderLineExtension2;
  }

  public OrderLine orderLineHold(List<OrderLineHold> orderLineHold) {
    this.orderLineHold = orderLineHold;
    return this;
  }

  public OrderLine addOrderLineHoldItem(OrderLineHold orderLineHoldItem) {
    if (this.orderLineHold == null) {
      this.orderLineHold = new ArrayList<OrderLineHold>();
    }
    this.orderLineHold.add(orderLineHoldItem);
    return this;
  }

  /**
   * Get orderLineHold
   * @return orderLineHold
   **/
  
  public List<OrderLineHold> getOrderLineHold() {
    return orderLineHold;
  }

  public void setOrderLineHold(List<OrderLineHold> orderLineHold) {
    this.orderLineHold = orderLineHold;
  }

  public OrderLine orderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
    return this;
  }

  /**
   * Unique identifier of order line, as defined by external system. If external system does not provide a value, then the system populates this field with a next-up value
   * @return orderLineId
   **/
  
  public String getOrderLineId() {
    return orderLineId;
  }

  public void setOrderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
  }

  public OrderLine orderLineNote(List<OrderLineNote> orderLineNote) {
    this.orderLineNote = orderLineNote;
    return this;
  }

  public OrderLine addOrderLineNoteItem(OrderLineNote orderLineNoteItem) {
    if (this.orderLineNote == null) {
      this.orderLineNote = new ArrayList<OrderLineNote>();
    }
    this.orderLineNote.add(orderLineNoteItem);
    return this;
  }

  /**
   * Get orderLineNote
   * @return orderLineNote
   **/
  
  public List<OrderLineNote> getOrderLineNote() {
    return orderLineNote;
  }

  public void setOrderLineNote(List<OrderLineNote> orderLineNote) {
    this.orderLineNote = orderLineNote;
  }

  public OrderLine orderLinePickupDetail(List<OrderLinePickupDetail> orderLinePickupDetail) {
    this.orderLinePickupDetail = orderLinePickupDetail;
    return this;
  }

  public OrderLine addOrderLinePickupDetailItem(OrderLinePickupDetail orderLinePickupDetailItem) {
    if (this.orderLinePickupDetail == null) {
      this.orderLinePickupDetail = new ArrayList<OrderLinePickupDetail>();
    }
    this.orderLinePickupDetail.add(orderLinePickupDetailItem);
    return this;
  }

  /**
   * Get orderLinePickupDetail
   * @return orderLinePickupDetail
   **/
  
  public List<OrderLinePickupDetail> getOrderLinePickupDetail() {
    return orderLinePickupDetail;
  }

  public void setOrderLinePickupDetail(List<OrderLinePickupDetail> orderLinePickupDetail) {
    this.orderLinePickupDetail = orderLinePickupDetail;
  }

  public OrderLine orderLinePriceOverrideHistory(List<OrderLinePriceOverrideHistory> orderLinePriceOverrideHistory) {
    this.orderLinePriceOverrideHistory = orderLinePriceOverrideHistory;
    return this;
  }

  public OrderLine addOrderLinePriceOverrideHistoryItem(OrderLinePriceOverrideHistory orderLinePriceOverrideHistoryItem) {
    if (this.orderLinePriceOverrideHistory == null) {
      this.orderLinePriceOverrideHistory = new ArrayList<OrderLinePriceOverrideHistory>();
    }
    this.orderLinePriceOverrideHistory.add(orderLinePriceOverrideHistoryItem);
    return this;
  }

  /**
   * Get orderLinePriceOverrideHistory
   * @return orderLinePriceOverrideHistory
   **/
  
  public List<OrderLinePriceOverrideHistory> getOrderLinePriceOverrideHistory() {
    return orderLinePriceOverrideHistory;
  }

  public void setOrderLinePriceOverrideHistory(List<OrderLinePriceOverrideHistory> orderLinePriceOverrideHistory) {
    this.orderLinePriceOverrideHistory = orderLinePriceOverrideHistory;
  }

  public OrderLine orderLinePromisingInfo(OrderLinePromisingInfo orderLinePromisingInfo) {
    this.orderLinePromisingInfo = orderLinePromisingInfo;
    return this;
  }

  /**
   * Get orderLinePromisingInfo
   * @return orderLinePromisingInfo
   **/
  
  public OrderLinePromisingInfo getOrderLinePromisingInfo() {
    return orderLinePromisingInfo;
  }

  public void setOrderLinePromisingInfo(OrderLinePromisingInfo orderLinePromisingInfo) {
    this.orderLinePromisingInfo = orderLinePromisingInfo;
  }

  public OrderLine orderLinePromotionRequest(List<OrderLinePromotionRequest> orderLinePromotionRequest) {
    this.orderLinePromotionRequest = orderLinePromotionRequest;
    return this;
  }

  public OrderLine addOrderLinePromotionRequestItem(OrderLinePromotionRequest orderLinePromotionRequestItem) {
    if (this.orderLinePromotionRequest == null) {
      this.orderLinePromotionRequest = new ArrayList<OrderLinePromotionRequest>();
    }
    this.orderLinePromotionRequest.add(orderLinePromotionRequestItem);
    return this;
  }

  /**
   * Get orderLinePromotionRequest
   * @return orderLinePromotionRequest
   **/
  
  public List<OrderLinePromotionRequest> getOrderLinePromotionRequest() {
    return orderLinePromotionRequest;
  }

  public void setOrderLinePromotionRequest(List<OrderLinePromotionRequest> orderLinePromotionRequest) {
    this.orderLinePromotionRequest = orderLinePromotionRequest;
  }

  public OrderLine orderLineSalesAssociate(List<OrderLineSalesAssociate> orderLineSalesAssociate) {
    this.orderLineSalesAssociate = orderLineSalesAssociate;
    return this;
  }

  public OrderLine addOrderLineSalesAssociateItem(OrderLineSalesAssociate orderLineSalesAssociateItem) {
    if (this.orderLineSalesAssociate == null) {
      this.orderLineSalesAssociate = new ArrayList<OrderLineSalesAssociate>();
    }
    this.orderLineSalesAssociate.add(orderLineSalesAssociateItem);
    return this;
  }

  /**
   * Get orderLineSalesAssociate
   * @return orderLineSalesAssociate
   **/
  
  public List<OrderLineSalesAssociate> getOrderLineSalesAssociate() {
    return orderLineSalesAssociate;
  }

  public void setOrderLineSalesAssociate(List<OrderLineSalesAssociate> orderLineSalesAssociate) {
    this.orderLineSalesAssociate = orderLineSalesAssociate;
  }

  /**
   * Line subtotal; This is calculated as the item unit price times the order line quantity
   * minimum: 0
   * maximum: 99999999999999.98
   * @return orderLineSubTotal
   **/
  
  public BigDecimal getOrderLineSubTotal() {
    return orderLineSubTotal;
  }

  public OrderLine orderLineTagDetail(List<OrderLineTagDetail> orderLineTagDetail) {
    this.orderLineTagDetail = orderLineTagDetail;
    return this;
  }

  public OrderLine addOrderLineTagDetailItem(OrderLineTagDetail orderLineTagDetailItem) {
    if (this.orderLineTagDetail == null) {
      this.orderLineTagDetail = new ArrayList<OrderLineTagDetail>();
    }
    this.orderLineTagDetail.add(orderLineTagDetailItem);
    return this;
  }

  /**
   * Get orderLineTagDetail
   * @return orderLineTagDetail
   **/
  
  public List<OrderLineTagDetail> getOrderLineTagDetail() {
    return orderLineTagDetail;
  }

  public void setOrderLineTagDetail(List<OrderLineTagDetail> orderLineTagDetail) {
    this.orderLineTagDetail = orderLineTagDetail;
  }

  public OrderLine orderLineTaxDetail(List<OrderLineTaxDetail> orderLineTaxDetail) {
    this.orderLineTaxDetail = orderLineTaxDetail;
    return this;
  }

  public OrderLine addOrderLineTaxDetailItem(OrderLineTaxDetail orderLineTaxDetailItem) {
    if (this.orderLineTaxDetail == null) {
      this.orderLineTaxDetail = new ArrayList<OrderLineTaxDetail>();
    }
    this.orderLineTaxDetail.add(orderLineTaxDetailItem);
    return this;
  }

  /**
   * Get orderLineTaxDetail
   * @return orderLineTaxDetail
   **/
  
  public List<OrderLineTaxDetail> getOrderLineTaxDetail() {
    return orderLineTaxDetail;
  }

  public void setOrderLineTaxDetail(List<OrderLineTaxDetail> orderLineTaxDetail) {
    this.orderLineTaxDetail = orderLineTaxDetail;
  }

  /**
   * Sum of items, charges, discounts, and taxes for the order line
   * minimum: 0
   * maximum: 99999999999999.98
   * @return orderLineTotal
   **/
  
  public BigDecimal getOrderLineTotal() {
    return orderLineTotal;
  }

  public OrderLine orderLineVASInstructions(List<OrderLineVASInstructions> orderLineVASInstructions) {
    this.orderLineVASInstructions = orderLineVASInstructions;
    return this;
  }

  public OrderLine addOrderLineVASInstructionsItem(OrderLineVASInstructions orderLineVASInstructionsItem) {
    if (this.orderLineVASInstructions == null) {
      this.orderLineVASInstructions = new ArrayList<OrderLineVASInstructions>();
    }
    this.orderLineVASInstructions.add(orderLineVASInstructionsItem);
    return this;
  }

  /**
   * Get orderLineVASInstructions
   * @return orderLineVASInstructions
   **/
  
  public List<OrderLineVASInstructions> getOrderLineVASInstructions() {
    return orderLineVASInstructions;
  }

  public void setOrderLineVASInstructions(List<OrderLineVASInstructions> orderLineVASInstructions) {
    this.orderLineVASInstructions = orderLineVASInstructions;
  }

  public OrderLine orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

  /**
   * Organization Id
   * @return orgId
   **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderLine originalUnitPrice(BigDecimal originalUnitPrice) {
    this.originalUnitPrice = originalUnitPrice;
    return this;
  }

  /**
   * Original UnitPrice, if the price is overridden
   * minimum: 0
   * maximum: 99999999999999.98
   * @return originalUnitPrice
   **/
  
  public BigDecimal getOriginalUnitPrice() {
    return originalUnitPrice;
  }

  public void setOriginalUnitPrice(BigDecimal originalUnitPrice) {
    this.originalUnitPrice = originalUnitPrice;
  }

  public OrderLine PK(String PK) {
    this.PK = PK;
    return this;
  }

  /**
   * Primary Key
   * @return PK
   **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderLine parentLineCreatedTimestamp(OffsetDateTime parentLineCreatedTimestamp) {
    this.parentLineCreatedTimestamp = parentLineCreatedTimestamp;
    return this;
  }

  /**
   * Created timestamp of the parent order line. Used in return invoice tax requests to pass the original parent order line created date to the third party tax engine, so that tax holidays can be honored.
   * @return parentLineCreatedTimestamp
   **/
  
  public OffsetDateTime getParentLineCreatedTimestamp() {
    return parentLineCreatedTimestamp;
  }

  public void setParentLineCreatedTimestamp(OffsetDateTime parentLineCreatedTimestamp) {
    this.parentLineCreatedTimestamp = parentLineCreatedTimestamp;
  }

  public OrderLine parentOrderId(String parentOrderId) {
    this.parentOrderId = parentOrderId;
    return this;
  }

  /**
   * Identifies the original order in case of returns or even exchanges. Parent order is used in conjunction with parent order line id while retrieving prices, charges, taxes, discounts, etc. from the original order line to be copied on return or exchange lines. Parent order id and parent order line id should only be populated in return or even exchange scenarios, since return credit is pulled from the parent order and applied to the return or exchange order. Parent order id should not be populated for sale lines or uneven exchange lines
   * @return parentOrderId
   **/
  
  public String getParentOrderId() {
    return parentOrderId;
  }

  public void setParentOrderId(String parentOrderId) {
    this.parentOrderId = parentOrderId;
  }

  public OrderLine parentOrderLineId(String parentOrderLineId) {
    this.parentOrderLineId = parentOrderLineId;
    return this;
  }

  /**
   * Identifies the original order line in case of returns or even exchanges. Used in conjunction with parent order id while retrieving prices, charges, taxes, discounts, etc. from the original order line to be copied on return or exchange lines. Parent order id and parent order line id should only be populated in return or even exchange scenarios, since return credit is pulled from the parent order and applied to the return or exchange order. Parent order line id should not be populated for sale lines or uneven exchange lines
   * @return parentOrderLineId
   **/
  
  public String getParentOrderLineId() {
    return parentOrderLineId;
  }

  public void setParentOrderLineId(String parentOrderLineId) {
    this.parentOrderLineId = parentOrderLineId;
  }

  public OrderLine parentOrderLineType(LineTypeId parentOrderLineType) {
    this.parentOrderLineType = parentOrderLineType;
    return this;
  }

  /**
   * Get parentOrderLineType
   * @return parentOrderLineType
   **/
  
  public LineTypeId getParentOrderLineType() {
    return parentOrderLineType;
  }

  public void setParentOrderLineType(LineTypeId parentOrderLineType) {
    this.parentOrderLineType = parentOrderLineType;
  }

  public OrderLine paymentGroupId(String paymentGroupId) {
    this.paymentGroupId = paymentGroupId;
    return this;
  }

  /**
   * ID used to associate a payment with an item or group of items
   * @return paymentGroupId
   **/
  
  public String getPaymentGroupId() {
    return paymentGroupId;
  }

  public void setPaymentGroupId(String paymentGroupId) {
    this.paymentGroupId = paymentGroupId;
  }

  public OrderLine physicalOriginId(String physicalOriginId) {
    this.physicalOriginId = physicalOriginId;
    return this;
  }

  /**
   * Location from where the items are being fulfilled. Populated with the allocation origin location for ship to home items. Populated with the selling location for store sales. Populated with the ship to location for pick up in store and ship to store. Used to send the origin address to the tax engine, which calculates tax rates based on this data
   * @return physicalOriginId
   **/
  
  public String getPhysicalOriginId() {
    return physicalOriginId;
  }

  public void setPhysicalOriginId(String physicalOriginId) {
    this.physicalOriginId = physicalOriginId;
  }

  public OrderLine pipelineId(String pipelineId) {
    this.pipelineId = pipelineId;
    return this;
  }

  /**
   * Id of the state transition pipeline.
   * @return pipelineId
   **/
  
  public String getPipelineId() {
    return pipelineId;
  }

  public void setPipelineId(String pipelineId) {
    this.pipelineId = pipelineId;
  }

  public OrderLine priority(Long priority) {
    this.priority = priority;
    return this;
  }

  /**
   * Priority of order line, used in case of constrained inventory
   * minimum: 0
   * maximum: -8446744073709551617
   * @return priority
   **/
  
  public Long getPriority() {
    return priority;
  }

  public void setPriority(Long priority) {
    this.priority = priority;
  }

  public OrderLine productClass(String productClass) {
    this.productClass = productClass;
    return this;
  }

  /**
   * Product Class of Item.
   * @return productClass
   **/
  
  public String getProductClass() {
    return productClass;
  }

  public void setProductClass(String productClass) {
    this.productClass = productClass;
  }

  public OrderLine promisedDeliveryDate(OffsetDateTime promisedDeliveryDate) {
    this.promisedDeliveryDate = promisedDeliveryDate;
    return this;
  }

  /**
   * Delivery date which was originally promised to the customer
   * @return promisedDeliveryDate
   **/
  
  public OffsetDateTime getPromisedDeliveryDate() {
    return promisedDeliveryDate;
  }

  public void setPromisedDeliveryDate(OffsetDateTime promisedDeliveryDate) {
    this.promisedDeliveryDate = promisedDeliveryDate;
  }

  public OrderLine promisedShipDate(OffsetDateTime promisedShipDate) {
    this.promisedShipDate = promisedShipDate;
    return this;
  }

  /**
   * Ship date which was originally promised to the customer during order creation
   * @return promisedShipDate
   **/
  
  public OffsetDateTime getPromisedShipDate() {
    return promisedShipDate;
  }

  public void setPromisedShipDate(OffsetDateTime promisedShipDate) {
    this.promisedShipDate = promisedShipDate;
  }

  public OrderLine quantity(Double quantity) {
    this.quantity = quantity;
    return this;
  }

  /**
   * Order Line ordered quantity
   * minimum: 0
   * maximum: 999999999999.9999
   * @return quantity
   **/
  
  public Double getQuantity() {
    return quantity;
  }

  public void setQuantity(Double quantity) {
    this.quantity = quantity;
  }

  public OrderLine quantityDetail(List<QuantityDetail> quantityDetail) {
    this.quantityDetail = quantityDetail;
    return this;
  }

  public OrderLine addQuantityDetailItem(QuantityDetail quantityDetailItem) {
    if (this.quantityDetail == null) {
      this.quantityDetail = new ArrayList<QuantityDetail>();
    }
    this.quantityDetail.add(quantityDetailItem);
    return this;
  }

  /**
   * Get quantityDetail
   * @return quantityDetail
   **/
  
  public List<QuantityDetail> getQuantityDetail() {
    return quantityDetail;
  }

  public void setQuantityDetail(List<QuantityDetail> quantityDetail) {
    this.quantityDetail = quantityDetail;
  }

  public OrderLine refundPrice(BigDecimal refundPrice) {
    this.refundPrice = refundPrice;
    return this;
  }

  /**
   * Price to be refunded, if the item is returned
   * minimum: 0
   * maximum: 99999999999999.98
   * @return refundPrice
   **/
  
  public BigDecimal getRefundPrice() {
    return refundPrice;
  }

  public void setRefundPrice(BigDecimal refundPrice) {
    this.refundPrice = refundPrice;
  }

  public OrderLine requestedDeliveryDate(OffsetDateTime requestedDeliveryDate) {
    this.requestedDeliveryDate = requestedDeliveryDate;
    return this;
  }

  /**
   * Requested date on which the items should be delivered to the destination
   * @return requestedDeliveryDate
   **/
  
  public OffsetDateTime getRequestedDeliveryDate() {
    return requestedDeliveryDate;
  }

  public void setRequestedDeliveryDate(OffsetDateTime requestedDeliveryDate) {
    this.requestedDeliveryDate = requestedDeliveryDate;
  }

  public OrderLine returnDetail(List<ReturnDetail> returnDetail) {
    this.returnDetail = returnDetail;
    return this;
  }

  public OrderLine addReturnDetailItem(ReturnDetail returnDetailItem) {
    if (this.returnDetail == null) {
      this.returnDetail = new ArrayList<ReturnDetail>();
    }
    this.returnDetail.add(returnDetailItem);
    return this;
  }

  /**
   * Get returnDetail
   * @return returnDetail
   **/
  
  public List<ReturnDetail> getReturnDetail() {
    return returnDetail;
  }

  public void setReturnDetail(List<ReturnDetail> returnDetail) {
    this.returnDetail = returnDetail;
  }

  public OrderLine returnLineTotalWithoutFees(BigDecimal returnLineTotalWithoutFees) {
    this.returnLineTotalWithoutFees = returnLineTotalWithoutFees;
    return this;
  }

  /**
   * Get returnLineTotalWithoutFees
   * @return returnLineTotalWithoutFees
   **/
  
  public BigDecimal getReturnLineTotalWithoutFees() {
    return returnLineTotalWithoutFees;
  }

  public void setReturnLineTotalWithoutFees(BigDecimal returnLineTotalWithoutFees) {
    this.returnLineTotalWithoutFees = returnLineTotalWithoutFees;
  }

  public OrderLine returnType(ReturnTypeId returnType) {
    this.returnType = returnType;
    return this;
  }

  /**
   * Get returnType
   * @return returnType
   **/
  
  public ReturnTypeId getReturnType() {
    return returnType;
  }

  public void setReturnType(ReturnTypeId returnType) {
    this.returnType = returnType;
  }

  public OrderLine returnableLineTotal(BigDecimal returnableLineTotal) {
    this.returnableLineTotal = returnableLineTotal;
    return this;
  }

  /**
   * Get returnableLineTotal
   * @return returnableLineTotal
   **/
  
  public BigDecimal getReturnableLineTotal() {
    return returnableLineTotal;
  }

  public void setReturnableLineTotal(BigDecimal returnableLineTotal) {
    this.returnableLineTotal = returnableLineTotal;
  }

  public OrderLine returnableQuantity(Double returnableQuantity) {
    this.returnableQuantity = returnableQuantity;
    return this;
  }

  /**
   * Get returnableQuantity
   * @return returnableQuantity
   **/
  
  public Double getReturnableQuantity() {
    return returnableQuantity;
  }

  public void setReturnableQuantity(Double returnableQuantity) {
    this.returnableQuantity = returnableQuantity;
  }

  public OrderLine sellingLocationId(String sellingLocationId) {
    this.sellingLocationId = sellingLocationId;
    return this;
  }

  /**
   * Selling location of order line, in case of multi-company orders or shared carts
   * @return sellingLocationId
   **/
  
  public String getSellingLocationId() {
    return sellingLocationId;
  }

  public void setSellingLocationId(String sellingLocationId) {
    this.sellingLocationId = sellingLocationId;
  }

  public OrderLine serviceLevelCode(String serviceLevelCode) {
    this.serviceLevelCode = serviceLevelCode;
    return this;
  }

  /**
   * Service Level which should be used to fulfill the order line e.g. AIR-1 Day, Road-day
   * @return serviceLevelCode
   **/
  
  public String getServiceLevelCode() {
    return serviceLevelCode;
  }

  public void setServiceLevelCode(String serviceLevelCode) {
    this.serviceLevelCode = serviceLevelCode;
  }

  public OrderLine shipFromAddress(ShipToAddress shipFromAddress) {
    this.shipFromAddress = shipFromAddress;
    return this;
  }

  /**
   * Get shipFromAddress
   * @return shipFromAddress
   **/
  
  public ShipToAddress getShipFromAddress() {
    return shipFromAddress;
  }

  public void setShipFromAddress(ShipToAddress shipFromAddress) {
    this.shipFromAddress = shipFromAddress;
  }

  public OrderLine shipFromAddressId(String shipFromAddressId) {
    this.shipFromAddressId = shipFromAddressId;
    return this;
  }

  /**
   * Ship from address for return order lines being shipped from a customer address to a return center. This address is printed on return labels and used to calculate return taxes. Ship from address can be specified on return order import. If null on import and delivery method is Ship to Return Center, then ship from address is populated with the ship to address from the parent order line. If parent order line ship to address is null (in case of pick up in store or ship to store), then the preferred customer address is retrieved from the customer record. If no preferred customer address exists, then ship from address should be manually entered by a user
   * @return shipFromAddressId
   **/
  
  public String getShipFromAddressId() {
    return shipFromAddressId;
  }

  public void setShipFromAddressId(String shipFromAddressId) {
    this.shipFromAddressId = shipFromAddressId;
  }

  public OrderLine shipToAddress(ShipToAddress shipToAddress) {
    this.shipToAddress = shipToAddress;
    return this;
  }

  /**
   * Get shipToAddress
   * @return shipToAddress
   **/
  
  public ShipToAddress getShipToAddress() {
    return shipToAddress;
  }

  public void setShipToAddress(ShipToAddress shipToAddress) {
    this.shipToAddress = shipToAddress;
  }

  public OrderLine shipToLocationId(String shipToLocationId) {
    this.shipToLocationId = shipToLocationId;
    return this;
  }

  /**
   * Ship-to location ID, in case of ship to store, pick up in store, or any flow where the items are shipping to a destination which is configured as a location in the network
   * @return shipToLocationId
   **/
  
  public String getShipToLocationId() {
    return shipToLocationId;
  }

  public void setShipToLocationId(String shipToLocationId) {
    this.shipToLocationId = shipToLocationId;
  }

  public OrderLine shippingMethodId(String shippingMethodId) {
    this.shippingMethodId = shippingMethodId;
    return this;
  }

  /**
   * Customer shipping preference. When a customer order is placed, it is required to know what is his preference of shipping. For example, it could be Same day delivery / Next Day / Two day or prefers ground shipment
   * @return shippingMethodId
   **/
  
  public String getShippingMethodId() {
    return shippingMethodId;
  }

  public void setShippingMethodId(String shippingMethodId) {
    this.shippingMethodId = shippingMethodId;
  }

  public OrderLine smallImageURI(String smallImageURI) {
    this.smallImageURI = smallImageURI;
    return this;
  }

  /**
   * URI of the small image for the ordered item
   * @return smallImageURI
   **/
  
  public String getSmallImageURI() {
    return smallImageURI;
  }

  public void setSmallImageURI(String smallImageURI) {
    this.smallImageURI = smallImageURI;
  }

  public OrderLine storeSaleEntryMethod(StoreSaleEntryMethodId storeSaleEntryMethod) {
    this.storeSaleEntryMethod = storeSaleEntryMethod;
    return this;
  }

  /**
   * Get storeSaleEntryMethod
   * @return storeSaleEntryMethod
   **/
  
  public StoreSaleEntryMethodId getStoreSaleEntryMethod() {
    return storeSaleEntryMethod;
  }

  public void setStoreSaleEntryMethod(StoreSaleEntryMethodId storeSaleEntryMethod) {
    this.storeSaleEntryMethod = storeSaleEntryMethod;
  }

  public OrderLine streetDate(OffsetDateTime streetDate) {
    this.streetDate = streetDate;
    return this;
  }

  /**
   * Indicates date on which pre-order item can be shipped/sold
   * @return streetDate
   **/
  
  public OffsetDateTime getStreetDate() {
    return streetDate;
  }

  public void setStreetDate(OffsetDateTime streetDate) {
    this.streetDate = streetDate;
  }

  public OrderLine taxOverridePercValue(BigDecimal taxOverridePercValue) {
    this.taxOverridePercValue = taxOverridePercValue;
    return this;
  }

  /**
   * It has the tax override value in percentage. This attribute can have values with a precision upto 4 places of decimal. Hence when the tax override percentage requires higher precision, then this attribute must be used.
   * minimum: 0
   * maximum: 999999999999.9999
   * @return taxOverridePercValue
   **/
  
  public BigDecimal getTaxOverridePercValue() {
    return taxOverridePercValue;
  }

  public void setTaxOverridePercValue(BigDecimal taxOverridePercValue) {
    this.taxOverridePercValue = taxOverridePercValue;
  }

  public OrderLine taxOverrideType(TaxOverrideTypeId taxOverrideType) {
    this.taxOverrideType = taxOverrideType;
    return this;
  }

  /**
   * Get taxOverrideType
   * @return taxOverrideType
   **/
  
  public TaxOverrideTypeId getTaxOverrideType() {
    return taxOverrideType;
  }

  public void setTaxOverrideType(TaxOverrideTypeId taxOverrideType) {
    this.taxOverrideType = taxOverrideType;
  }

  public OrderLine taxOverrideValue(BigDecimal taxOverrideValue) {
    this.taxOverrideValue = taxOverrideValue;
    return this;
  }

  /**
   * It has the tax override value. The value can be in terms of percentage or flat number and the precision is upto 2 places of decimal. When there is a need to provide a percentage for taxOverrideValue which requires higher precision (upto 3 or 4 places of decimal, say 2.3456), then donot use this attribute instead use taxOverridePercValue
   * minimum: 0
   * maximum: 99999999999999.98
   * @return taxOverrideValue
   **/
  
  public BigDecimal getTaxOverrideValue() {
    return taxOverrideValue;
  }

  public void setTaxOverrideValue(BigDecimal taxOverrideValue) {
    this.taxOverrideValue = taxOverrideValue;
  }

  public OrderLine taxShipFromAddress(ShipToAddress taxShipFromAddress) {
    this.taxShipFromAddress = taxShipFromAddress;
    return this;
  }

  /**
   * Get taxShipFromAddress
   * @return taxShipFromAddress
   **/
  
  public ShipToAddress getTaxShipFromAddress() {
    return taxShipFromAddress;
  }

  public void setTaxShipFromAddress(ShipToAddress taxShipFromAddress) {
    this.taxShipFromAddress = taxShipFromAddress;
  }

  public OrderLine taxShipFromLocationId(String taxShipFromLocationId) {
    this.taxShipFromLocationId = taxShipFromLocationId;
    return this;
  }

  /**
   * Get taxShipFromLocationId
   * @return taxShipFromLocationId
   **/
  
  public String getTaxShipFromLocationId() {
    return taxShipFromLocationId;
  }

  public void setTaxShipFromLocationId(String taxShipFromLocationId) {
    this.taxShipFromLocationId = taxShipFromLocationId;
  }

  public OrderLine taxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
    return this;
  }

  /**
   * Get taxableAmount
   * @return taxableAmount
   **/
  
  public BigDecimal getTaxableAmount() {
    return taxableAmount;
  }

  public void setTaxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
  }

  /**
   * Sum of charges for the order line
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalCharges
   **/
  
  public BigDecimal getTotalCharges() {
    return totalCharges;
  }

  /**
   * This field will capture Sum of all the discount against Item, It can be discount applied directly on Item or prorated Order discount or prorated discount on entire line. It can be of any Charge Type, Promotion, Coupon, Appeasement or Discount.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalDiscountOnItem
   **/
  
  public BigDecimal getTotalDiscountOnItem() {
    return totalDiscountOnItem;
  }

  /**
   * Sum of discounts for the order line
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalDiscounts
   **/
  
  public BigDecimal getTotalDiscounts() {
    return totalDiscounts;
  }

  public OrderLine totalInformationalTaxes(BigDecimal totalInformationalTaxes) {
    this.totalInformationalTaxes = totalInformationalTaxes;
    return this;
  }

  /**
   * Get totalInformationalTaxes
   * @return totalInformationalTaxes
   **/
  
  public BigDecimal getTotalInformationalTaxes() {
    return totalInformationalTaxes;
  }

  public void setTotalInformationalTaxes(BigDecimal totalInformationalTaxes) {
    this.totalInformationalTaxes = totalInformationalTaxes;
  }

  /**
   * Sum of taxes for the order line
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalTaxes
   **/
  
  public BigDecimal getTotalTaxes() {
    return totalTaxes;
  }

  public OrderLine transactionReferenceId(String transactionReferenceId) {
    this.transactionReferenceId = transactionReferenceId;
    return this;
  }

  /**
   *  Indicates transaction id under which this orderline is captured. eg Adding more store sale lines in existing order while customer comes to pick up in a store.
   * @return transactionReferenceId
   **/
  
  public String getTransactionReferenceId() {
    return transactionReferenceId;
  }

  public void setTransactionReferenceId(String transactionReferenceId) {
    this.transactionReferenceId = transactionReferenceId;
  }

  public OrderLine UOM(String UOM) {
    this.UOM = UOM;
    return this;
  }

  /**
   * Unit of measure (UOM) of the ordered quantity
   * @return UOM
   **/
  
  public String getUOM() {
    return UOM;
  }

  public void setUOM(String UOM) {
    this.UOM = UOM;
  }

  public OrderLine unitPrice(BigDecimal unitPrice) {
    this.unitPrice = unitPrice;
    return this;
  }

  /**
   * Unit price of the ordered item
   * minimum: 0
   * maximum: 99999999999999.98
   * @return unitPrice
   **/
  
  public BigDecimal getUnitPrice() {
    return unitPrice;
  }

  public void setUnitPrice(BigDecimal unitPrice) {
    this.unitPrice = unitPrice;
  }

  public OrderLine updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

  /**
   * Updated By
   * @return updatedBy
   **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderLine updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

  /**
   * Updated Timestamp
   * @return updatedTimestamp
   **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderLine valueEntryRequired(Boolean valueEntryRequired) {
    this.valueEntryRequired = valueEntryRequired;
    return this;
  }

  /**
   * Indicates if item requires an activation value to be entered (e.g. variable value gift cards). This attribute is used by point of sale to determine whether to prompt a user for item value upon scanning.
   * @return valueEntryRequired
   **/
  
  public Boolean getValueEntryRequired() {
    return valueEntryRequired;
  }

  public void setValueEntryRequired(Boolean valueEntryRequired) {
    this.valueEntryRequired = valueEntryRequired;
  }

  public OrderLine volumetricWeight(Double volumetricWeight) {
    this.volumetricWeight = volumetricWeight;
    return this;
  }

  /**
   * This is the volumetric weight of an item and the same is used to calculate Shipping and Handling charges when the rate basis for Shipping and Handling charges calculation is configured as Volumetric weight
   * minimum: 0
   * maximum: 999999999999.9999
   * @return volumetricWeight
   **/
  
  public Double getVolumetricWeight() {
    return volumetricWeight;
  }

  public void setVolumetricWeight(Double volumetricWeight) {
    this.volumetricWeight = volumetricWeight;
  }

  public OrderLine volumetricWeightUOM(UOMId volumetricWeightUOM) {
    this.volumetricWeightUOM = volumetricWeightUOM;
    return this;
  }

  /**
   * Get volumetricWeightUOM
   * @return volumetricWeightUOM
   **/
  
  public UOMId getVolumetricWeightUOM() {
    return volumetricWeightUOM;
  }

  public void setVolumetricWeightUOM(UOMId volumetricWeightUOM) {
    this.volumetricWeightUOM = volumetricWeightUOM;
  }

  public OrderLine entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

  /**
   * Get entityName
   * @return entityName
   **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderLine localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

  /**
   * Get localize
   * @return localize
   **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderLine orderLine = (OrderLine) o;
    return Objects.equals(this.actions, orderLine.actions) &&
            Objects.equals(this.addressId, orderLine.addressId) &&
            Objects.equals(this.allocation, orderLine.allocation) &&
            Objects.equals(this.allocationConfigId, orderLine.allocationConfigId) &&
            Objects.equals(this.alternateOrderLineId, orderLine.alternateOrderLineId) &&
            Objects.equals(this.businessDate, orderLine.businessDate) &&
            Objects.equals(this.calculatedValues, orderLine.calculatedValues) &&
            Objects.equals(this.cancelComments, orderLine.cancelComments) &&
            Objects.equals(this.cancelReason, orderLine.cancelReason) &&
            Objects.equals(this.cancelledOrderLineSubTotal, orderLine.cancelledOrderLineSubTotal) &&
            Objects.equals(this.cancelledTotalDiscounts, orderLine.cancelledTotalDiscounts) &&
            Objects.equals(this.carrierCode, orderLine.carrierCode) &&
            Objects.equals(this.changeLog, orderLine.changeLog) &&
            Objects.equals(this.createdBy, orderLine.createdBy) &&
            Objects.equals(this.createdTimestamp, orderLine.createdTimestamp) &&
            Objects.equals(this.deliveryMethod, orderLine.deliveryMethod) &&
            Objects.equals(this.doNotShipBeforeDate, orderLine.doNotShipBeforeDate) &&
            Objects.equals(this.effectiveRank, orderLine.effectiveRank) &&
            Objects.equals(this.extended, orderLine.extended) &&
            Objects.equals(this.fulfillmentDetail, orderLine.fulfillmentDetail) &&
            Objects.equals(this.fulfillmentGroupId, orderLine.fulfillmentGroupId) &&
            Objects.equals(this.fulfillmentStatus, orderLine.fulfillmentStatus) &&
            Objects.equals(this.giftCardValue, orderLine.giftCardValue) &&
            Objects.equals(this.hasComponents, orderLine.hasComponents) &&
            Objects.equals(this.isActivationRequired, orderLine.isActivationRequired) &&
            Objects.equals(this.isCancelled, orderLine.isCancelled) &&
            Objects.equals(this.isDiscountable, orderLine.isDiscountable) &&
            Objects.equals(this.isEligibleForOverride, orderLine.isEligibleForOverride) &&
            Objects.equals(this.isEvenExchange, orderLine.isEvenExchange) &&
            Objects.equals(this.isExchangeable, orderLine.isExchangeable) &&
            Objects.equals(this.isGift, orderLine.isGift) &&
            Objects.equals(this.isGiftCard, orderLine.isGiftCard) &&
            Objects.equals(this.isHazmat, orderLine.isHazmat) &&
            Objects.equals(this.isItemNotOnFile, orderLine.isItemNotOnFile) &&
            Objects.equals(this.isItemTaxExemptable, orderLine.isItemTaxExemptable) &&
            Objects.equals(this.isItemTaxOverridable, orderLine.isItemTaxOverridable) &&
            Objects.equals(this.isNonMerchandise, orderLine.isNonMerchandise) &&
            Objects.equals(this.isOnHold, orderLine.isOnHold) &&
            Objects.equals(this.isPackAndHold, orderLine.isPackAndHold) &&
            Objects.equals(this.isPerishable, orderLine.isPerishable) &&
            Objects.equals(this.isPreOrder, orderLine.isPreOrder) &&
            Objects.equals(this.isPreSale, orderLine.isPreSale) &&
            Objects.equals(this.isPriceOverridden, orderLine.isPriceOverridden) &&
            Objects.equals(this.isPriceOverrideable, orderLine.isPriceOverrideable) &&
            Objects.equals(this.isReceiptExpected, orderLine.isReceiptExpected) &&
            Objects.equals(this.isRefundGiftCard, orderLine.isRefundGiftCard) &&
            Objects.equals(this.isReturn, orderLine.isReturn) &&
            Objects.equals(this.isReturnAllowedByAgePolicy, orderLine.isReturnAllowedByAgePolicy) &&
            Objects.equals(this.isReturnable, orderLine.isReturnable) &&
            Objects.equals(this.isReturnableAtStore, orderLine.isReturnableAtStore) &&
            Objects.equals(this.isTaxIncluded, orderLine.isTaxIncluded) &&
            Objects.equals(this.isTaxOverridden, orderLine.isTaxOverridden) &&
            Objects.equals(this.itemBarcode, orderLine.itemBarcode) &&
            Objects.equals(this.itemBrand, orderLine.itemBrand) &&
            Objects.equals(this.itemColorDescription, orderLine.itemColorDescription) &&
            Objects.equals(this.itemDepartmentName, orderLine.itemDepartmentName) &&
            Objects.equals(this.itemDepartmentNumber, orderLine.itemDepartmentNumber) &&
            Objects.equals(this.itemDeptNumber, orderLine.itemDeptNumber) &&
            Objects.equals(this.itemDescription, orderLine.itemDescription) &&
            Objects.equals(this.itemId, orderLine.itemId) &&
            Objects.equals(this.itemMaxDiscountAmount, orderLine.itemMaxDiscountAmount) &&
            Objects.equals(this.itemMaxDiscountPercentage, orderLine.itemMaxDiscountPercentage) &&
            Objects.equals(this.itemSeason, orderLine.itemSeason) &&
            Objects.equals(this.itemShortDescription, orderLine.itemShortDescription) &&
            Objects.equals(this.itemSize, orderLine.itemSize) &&
            Objects.equals(this.itemStyle, orderLine.itemStyle) &&
            Objects.equals(this.itemTaxCode, orderLine.itemTaxCode) &&
            Objects.equals(this.latestDeliveryDate, orderLine.latestDeliveryDate) &&
            Objects.equals(this.latestFulfilledDate, orderLine.latestFulfilledDate) &&
            Objects.equals(this.lineProcessInfo, orderLine.lineProcessInfo) &&
            Objects.equals(this.lineType, orderLine.lineType) &&
            Objects.equals(this.localizedTo, orderLine.localizedTo) &&
            Objects.equals(this.maxAppeasementAmount, orderLine.maxAppeasementAmount) &&
            Objects.equals(this.maxFulfillmentStatus, orderLine.maxFulfillmentStatus) &&
            Objects.equals(this.maxFulfillmentStatusId, orderLine.maxFulfillmentStatusId) &&
            Objects.equals(this.messages, orderLine.messages) &&
            Objects.equals(this.minFulfillmentStatus, orderLine.minFulfillmentStatus) &&
            Objects.equals(this.minFulfillmentStatusId, orderLine.minFulfillmentStatusId) &&
            Objects.equals(this.orderId, orderLine.orderId) &&
            Objects.equals(this.orderLineAdditional, orderLine.orderLineAdditional) &&
            Objects.equals(this.orderLineAttribute, orderLine.orderLineAttribute) &&
            Objects.equals(this.orderLineCancelHistory, orderLine.orderLineCancelHistory) &&
            Objects.equals(this.orderLineChargeDetail, orderLine.orderLineChargeDetail) &&
            Objects.equals(this.orderLineComponents, orderLine.orderLineComponents) &&
            Objects.equals(this.orderLineExtension1, orderLine.orderLineExtension1) &&
            Objects.equals(this.orderLineExtension2, orderLine.orderLineExtension2) &&
            Objects.equals(this.orderLineHold, orderLine.orderLineHold) &&
            Objects.equals(this.orderLineId, orderLine.orderLineId) &&
            Objects.equals(this.orderLineNote, orderLine.orderLineNote) &&
            Objects.equals(this.orderLinePickupDetail, orderLine.orderLinePickupDetail) &&
            Objects.equals(this.orderLinePriceOverrideHistory, orderLine.orderLinePriceOverrideHistory) &&
            Objects.equals(this.orderLinePromisingInfo, orderLine.orderLinePromisingInfo) &&
            Objects.equals(this.orderLinePromotionRequest, orderLine.orderLinePromotionRequest) &&
            Objects.equals(this.orderLineSalesAssociate, orderLine.orderLineSalesAssociate) &&
            Objects.equals(this.orderLineSubTotal, orderLine.orderLineSubTotal) &&
            Objects.equals(this.orderLineTagDetail, orderLine.orderLineTagDetail) &&
            Objects.equals(this.orderLineTaxDetail, orderLine.orderLineTaxDetail) &&
            Objects.equals(this.orderLineTotal, orderLine.orderLineTotal) &&
            Objects.equals(this.orderLineVASInstructions, orderLine.orderLineVASInstructions) &&
            Objects.equals(this.orgId, orderLine.orgId) &&
            Objects.equals(this.originalUnitPrice, orderLine.originalUnitPrice) &&
            Objects.equals(this.PK, orderLine.PK) &&
            Objects.equals(this.parentLineCreatedTimestamp, orderLine.parentLineCreatedTimestamp) &&
            Objects.equals(this.parentOrderId, orderLine.parentOrderId) &&
            Objects.equals(this.parentOrderLineId, orderLine.parentOrderLineId) &&
            Objects.equals(this.parentOrderLineType, orderLine.parentOrderLineType) &&
            Objects.equals(this.paymentGroupId, orderLine.paymentGroupId) &&
            Objects.equals(this.physicalOriginId, orderLine.physicalOriginId) &&
            Objects.equals(this.pipelineId, orderLine.pipelineId) &&
            Objects.equals(this.priority, orderLine.priority) &&
            Objects.equals(this.productClass, orderLine.productClass) &&
            Objects.equals(this.promisedDeliveryDate, orderLine.promisedDeliveryDate) &&
            Objects.equals(this.promisedShipDate, orderLine.promisedShipDate) &&
            Objects.equals(this.quantity, orderLine.quantity) &&
            Objects.equals(this.quantityDetail, orderLine.quantityDetail) &&
            Objects.equals(this.refundPrice, orderLine.refundPrice) &&
            Objects.equals(this.requestedDeliveryDate, orderLine.requestedDeliveryDate) &&
            Objects.equals(this.returnDetail, orderLine.returnDetail) &&
            Objects.equals(this.returnLineTotalWithoutFees, orderLine.returnLineTotalWithoutFees) &&
            Objects.equals(this.returnType, orderLine.returnType) &&
            Objects.equals(this.returnableLineTotal, orderLine.returnableLineTotal) &&
            Objects.equals(this.returnableQuantity, orderLine.returnableQuantity) &&
            Objects.equals(this.sellingLocationId, orderLine.sellingLocationId) &&
            Objects.equals(this.serviceLevelCode, orderLine.serviceLevelCode) &&
            Objects.equals(this.shipFromAddress, orderLine.shipFromAddress) &&
            Objects.equals(this.shipFromAddressId, orderLine.shipFromAddressId) &&
            Objects.equals(this.shipToAddress, orderLine.shipToAddress) &&
            Objects.equals(this.shipToLocationId, orderLine.shipToLocationId) &&
            Objects.equals(this.shippingMethodId, orderLine.shippingMethodId) &&
            Objects.equals(this.smallImageURI, orderLine.smallImageURI) &&
            Objects.equals(this.storeSaleEntryMethod, orderLine.storeSaleEntryMethod) &&
            Objects.equals(this.streetDate, orderLine.streetDate) &&
            Objects.equals(this.taxOverridePercValue, orderLine.taxOverridePercValue) &&
            Objects.equals(this.taxOverrideType, orderLine.taxOverrideType) &&
            Objects.equals(this.taxOverrideValue, orderLine.taxOverrideValue) &&
            Objects.equals(this.taxShipFromAddress, orderLine.taxShipFromAddress) &&
            Objects.equals(this.taxShipFromLocationId, orderLine.taxShipFromLocationId) &&
            Objects.equals(this.taxableAmount, orderLine.taxableAmount) &&
            Objects.equals(this.totalCharges, orderLine.totalCharges) &&
            Objects.equals(this.totalDiscountOnItem, orderLine.totalDiscountOnItem) &&
            Objects.equals(this.totalDiscounts, orderLine.totalDiscounts) &&
            Objects.equals(this.totalInformationalTaxes, orderLine.totalInformationalTaxes) &&
            Objects.equals(this.totalTaxes, orderLine.totalTaxes) &&
            Objects.equals(this.transactionReferenceId, orderLine.transactionReferenceId) &&
            Objects.equals(this.UOM, orderLine.UOM) &&
            Objects.equals(this.unitPrice, orderLine.unitPrice) &&
            Objects.equals(this.updatedBy, orderLine.updatedBy) &&
            Objects.equals(this.updatedTimestamp, orderLine.updatedTimestamp) &&
            Objects.equals(this.valueEntryRequired, orderLine.valueEntryRequired) &&
            Objects.equals(this.volumetricWeight, orderLine.volumetricWeight) &&
            Objects.equals(this.volumetricWeightUOM, orderLine.volumetricWeightUOM) &&
            Objects.equals(this.entityName, orderLine.entityName) &&
            Objects.equals(this.localize, orderLine.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, addressId, allocation, allocationConfigId, alternateOrderLineId, businessDate, calculatedValues, cancelComments, cancelReason, cancelledOrderLineSubTotal, cancelledTotalDiscounts, carrierCode, changeLog, createdBy, createdTimestamp, deliveryMethod, doNotShipBeforeDate, effectiveRank, extended, fulfillmentDetail, fulfillmentGroupId, fulfillmentStatus, giftCardValue, hasComponents, isActivationRequired, isCancelled, isDiscountable, isEligibleForOverride, isEvenExchange, isExchangeable, isGift, isGiftCard, isHazmat, isItemNotOnFile, isItemTaxExemptable, isItemTaxOverridable, isNonMerchandise, isOnHold, isPackAndHold, isPerishable, isPreOrder, isPreSale, isPriceOverridden, isPriceOverrideable, isReceiptExpected, isRefundGiftCard, isReturn, isReturnAllowedByAgePolicy, isReturnable, isReturnableAtStore, isTaxIncluded, isTaxOverridden, itemBarcode, itemBrand, itemColorDescription, itemDepartmentName, itemDepartmentNumber, itemDeptNumber, itemDescription, itemId, itemMaxDiscountAmount, itemMaxDiscountPercentage, itemSeason, itemShortDescription, itemSize, itemStyle, itemTaxCode, latestDeliveryDate, latestFulfilledDate, lineProcessInfo, lineType, localizedTo, maxAppeasementAmount, maxFulfillmentStatus, maxFulfillmentStatusId, messages, minFulfillmentStatus, minFulfillmentStatusId, orderId, orderLineAdditional, orderLineAttribute, orderLineCancelHistory, orderLineChargeDetail, orderLineComponents, orderLineExtension1, orderLineExtension2, orderLineHold, orderLineId, orderLineNote, orderLinePickupDetail, orderLinePriceOverrideHistory, orderLinePromisingInfo, orderLinePromotionRequest, orderLineSalesAssociate, orderLineSubTotal, orderLineTagDetail, orderLineTaxDetail, orderLineTotal, orderLineVASInstructions, orgId, originalUnitPrice, PK, parentLineCreatedTimestamp, parentOrderId, parentOrderLineId, parentOrderLineType, paymentGroupId, physicalOriginId, pipelineId, priority, productClass, promisedDeliveryDate, promisedShipDate, quantity, quantityDetail, refundPrice, requestedDeliveryDate, returnDetail, returnLineTotalWithoutFees, returnType, returnableLineTotal, returnableQuantity, sellingLocationId, serviceLevelCode, shipFromAddress, shipFromAddressId, shipToAddress, shipToLocationId, shippingMethodId, smallImageURI, storeSaleEntryMethod, streetDate, taxOverridePercValue, taxOverrideType, taxOverrideValue, taxShipFromAddress, taxShipFromLocationId, taxableAmount, totalCharges, totalDiscountOnItem, totalDiscounts, totalInformationalTaxes, totalTaxes, transactionReferenceId, UOM, unitPrice, updatedBy, updatedTimestamp, valueEntryRequired, volumetricWeight, volumetricWeightUOM, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderLine {\n");

    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    addressId: ").append(toIndentedString(addressId)).append("\n");
    sb.append("    allocation: ").append(toIndentedString(allocation)).append("\n");
    sb.append("    allocationConfigId: ").append(toIndentedString(allocationConfigId)).append("\n");
    sb.append("    alternateOrderLineId: ").append(toIndentedString(alternateOrderLineId)).append("\n");
    sb.append("    businessDate: ").append(toIndentedString(businessDate)).append("\n");
    sb.append("    calculatedValues: ").append(toIndentedString(calculatedValues)).append("\n");
    sb.append("    cancelComments: ").append(toIndentedString(cancelComments)).append("\n");
    sb.append("    cancelReason: ").append(toIndentedString(cancelReason)).append("\n");
    sb.append("    cancelledOrderLineSubTotal: ").append(toIndentedString(cancelledOrderLineSubTotal)).append("\n");
    sb.append("    cancelledTotalDiscounts: ").append(toIndentedString(cancelledTotalDiscounts)).append("\n");
    sb.append("    carrierCode: ").append(toIndentedString(carrierCode)).append("\n");
    sb.append("    changeLog: ").append(toIndentedString(changeLog)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    deliveryMethod: ").append(toIndentedString(deliveryMethod)).append("\n");
    sb.append("    doNotShipBeforeDate: ").append(toIndentedString(doNotShipBeforeDate)).append("\n");
    sb.append("    effectiveRank: ").append(toIndentedString(effectiveRank)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    fulfillmentDetail: ").append(toIndentedString(fulfillmentDetail)).append("\n");
    sb.append("    fulfillmentGroupId: ").append(toIndentedString(fulfillmentGroupId)).append("\n");
    sb.append("    fulfillmentStatus: ").append(toIndentedString(fulfillmentStatus)).append("\n");
    sb.append("    giftCardValue: ").append(toIndentedString(giftCardValue)).append("\n");
    sb.append("    hasComponents: ").append(toIndentedString(hasComponents)).append("\n");
    sb.append("    isActivationRequired: ").append(toIndentedString(isActivationRequired)).append("\n");
    sb.append("    isCancelled: ").append(toIndentedString(isCancelled)).append("\n");
    sb.append("    isDiscountable: ").append(toIndentedString(isDiscountable)).append("\n");
    sb.append("    isEligibleForOverride: ").append(toIndentedString(isEligibleForOverride)).append("\n");
    sb.append("    isEvenExchange: ").append(toIndentedString(isEvenExchange)).append("\n");
    sb.append("    isExchangeable: ").append(toIndentedString(isExchangeable)).append("\n");
    sb.append("    isGift: ").append(toIndentedString(isGift)).append("\n");
    sb.append("    isGiftCard: ").append(toIndentedString(isGiftCard)).append("\n");
    sb.append("    isHazmat: ").append(toIndentedString(isHazmat)).append("\n");
    sb.append("    isItemNotOnFile: ").append(toIndentedString(isItemNotOnFile)).append("\n");
    sb.append("    isItemTaxExemptable: ").append(toIndentedString(isItemTaxExemptable)).append("\n");
    sb.append("    isItemTaxOverridable: ").append(toIndentedString(isItemTaxOverridable)).append("\n");
    sb.append("    isNonMerchandise: ").append(toIndentedString(isNonMerchandise)).append("\n");
    sb.append("    isOnHold: ").append(toIndentedString(isOnHold)).append("\n");
    sb.append("    isPackAndHold: ").append(toIndentedString(isPackAndHold)).append("\n");
    sb.append("    isPerishable: ").append(toIndentedString(isPerishable)).append("\n");
    sb.append("    isPreOrder: ").append(toIndentedString(isPreOrder)).append("\n");
    sb.append("    isPreSale: ").append(toIndentedString(isPreSale)).append("\n");
    sb.append("    isPriceOverridden: ").append(toIndentedString(isPriceOverridden)).append("\n");
    sb.append("    isPriceOverrideable: ").append(toIndentedString(isPriceOverrideable)).append("\n");
    sb.append("    isReceiptExpected: ").append(toIndentedString(isReceiptExpected)).append("\n");
    sb.append("    isRefundGiftCard: ").append(toIndentedString(isRefundGiftCard)).append("\n");
    sb.append("    isReturn: ").append(toIndentedString(isReturn)).append("\n");
    sb.append("    isReturnAllowedByAgePolicy: ").append(toIndentedString(isReturnAllowedByAgePolicy)).append("\n");
    sb.append("    isReturnable: ").append(toIndentedString(isReturnable)).append("\n");
    sb.append("    isReturnableAtStore: ").append(toIndentedString(isReturnableAtStore)).append("\n");
    sb.append("    isTaxIncluded: ").append(toIndentedString(isTaxIncluded)).append("\n");
    sb.append("    isTaxOverridden: ").append(toIndentedString(isTaxOverridden)).append("\n");
    sb.append("    itemBarcode: ").append(toIndentedString(itemBarcode)).append("\n");
    sb.append("    itemBrand: ").append(toIndentedString(itemBrand)).append("\n");
    sb.append("    itemColorDescription: ").append(toIndentedString(itemColorDescription)).append("\n");
    sb.append("    itemDepartmentName: ").append(toIndentedString(itemDepartmentName)).append("\n");
    sb.append("    itemDepartmentNumber: ").append(toIndentedString(itemDepartmentNumber)).append("\n");
    sb.append("    itemDeptNumber: ").append(toIndentedString(itemDeptNumber)).append("\n");
    sb.append("    itemDescription: ").append(toIndentedString(itemDescription)).append("\n");
    sb.append("    itemId: ").append(toIndentedString(itemId)).append("\n");
    sb.append("    itemMaxDiscountAmount: ").append(toIndentedString(itemMaxDiscountAmount)).append("\n");
    sb.append("    itemMaxDiscountPercentage: ").append(toIndentedString(itemMaxDiscountPercentage)).append("\n");
    sb.append("    itemSeason: ").append(toIndentedString(itemSeason)).append("\n");
    sb.append("    itemShortDescription: ").append(toIndentedString(itemShortDescription)).append("\n");
    sb.append("    itemSize: ").append(toIndentedString(itemSize)).append("\n");
    sb.append("    itemStyle: ").append(toIndentedString(itemStyle)).append("\n");
    sb.append("    itemTaxCode: ").append(toIndentedString(itemTaxCode)).append("\n");
    sb.append("    latestDeliveryDate: ").append(toIndentedString(latestDeliveryDate)).append("\n");
    sb.append("    latestFulfilledDate: ").append(toIndentedString(latestFulfilledDate)).append("\n");
    sb.append("    lineProcessInfo: ").append(toIndentedString(lineProcessInfo)).append("\n");
    sb.append("    lineType: ").append(toIndentedString(lineType)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    maxAppeasementAmount: ").append(toIndentedString(maxAppeasementAmount)).append("\n");
    sb.append("    maxFulfillmentStatus: ").append(toIndentedString(maxFulfillmentStatus)).append("\n");
    sb.append("    maxFulfillmentStatusId: ").append(toIndentedString(maxFulfillmentStatusId)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    minFulfillmentStatus: ").append(toIndentedString(minFulfillmentStatus)).append("\n");
    sb.append("    minFulfillmentStatusId: ").append(toIndentedString(minFulfillmentStatusId)).append("\n");
    sb.append("    orderId: ").append(toIndentedString(orderId)).append("\n");
    sb.append("    orderLineAdditional: ").append(toIndentedString(orderLineAdditional)).append("\n");
    sb.append("    orderLineAttribute: ").append(toIndentedString(orderLineAttribute)).append("\n");
    sb.append("    orderLineCancelHistory: ").append(toIndentedString(orderLineCancelHistory)).append("\n");
    sb.append("    orderLineChargeDetail: ").append(toIndentedString(orderLineChargeDetail)).append("\n");
    sb.append("    orderLineComponents: ").append(toIndentedString(orderLineComponents)).append("\n");
    sb.append("    orderLineExtension1: ").append(toIndentedString(orderLineExtension1)).append("\n");
    sb.append("    orderLineExtension2: ").append(toIndentedString(orderLineExtension2)).append("\n");
    sb.append("    orderLineHold: ").append(toIndentedString(orderLineHold)).append("\n");
    sb.append("    orderLineId: ").append(toIndentedString(orderLineId)).append("\n");
    sb.append("    orderLineNote: ").append(toIndentedString(orderLineNote)).append("\n");
    sb.append("    orderLinePickupDetail: ").append(toIndentedString(orderLinePickupDetail)).append("\n");
    sb.append("    orderLinePriceOverrideHistory: ").append(toIndentedString(orderLinePriceOverrideHistory)).append("\n");
    sb.append("    orderLinePromisingInfo: ").append(toIndentedString(orderLinePromisingInfo)).append("\n");
    sb.append("    orderLinePromotionRequest: ").append(toIndentedString(orderLinePromotionRequest)).append("\n");
    sb.append("    orderLineSalesAssociate: ").append(toIndentedString(orderLineSalesAssociate)).append("\n");
    sb.append("    orderLineSubTotal: ").append(toIndentedString(orderLineSubTotal)).append("\n");
    sb.append("    orderLineTagDetail: ").append(toIndentedString(orderLineTagDetail)).append("\n");
    sb.append("    orderLineTaxDetail: ").append(toIndentedString(orderLineTaxDetail)).append("\n");
    sb.append("    orderLineTotal: ").append(toIndentedString(orderLineTotal)).append("\n");
    sb.append("    orderLineVASInstructions: ").append(toIndentedString(orderLineVASInstructions)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    originalUnitPrice: ").append(toIndentedString(originalUnitPrice)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentLineCreatedTimestamp: ").append(toIndentedString(parentLineCreatedTimestamp)).append("\n");
    sb.append("    parentOrderId: ").append(toIndentedString(parentOrderId)).append("\n");
    sb.append("    parentOrderLineId: ").append(toIndentedString(parentOrderLineId)).append("\n");
    sb.append("    parentOrderLineType: ").append(toIndentedString(parentOrderLineType)).append("\n");
    sb.append("    paymentGroupId: ").append(toIndentedString(paymentGroupId)).append("\n");
    sb.append("    physicalOriginId: ").append(toIndentedString(physicalOriginId)).append("\n");
    sb.append("    pipelineId: ").append(toIndentedString(pipelineId)).append("\n");
    sb.append("    priority: ").append(toIndentedString(priority)).append("\n");
    sb.append("    productClass: ").append(toIndentedString(productClass)).append("\n");
    sb.append("    promisedDeliveryDate: ").append(toIndentedString(promisedDeliveryDate)).append("\n");
    sb.append("    promisedShipDate: ").append(toIndentedString(promisedShipDate)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    quantityDetail: ").append(toIndentedString(quantityDetail)).append("\n");
    sb.append("    refundPrice: ").append(toIndentedString(refundPrice)).append("\n");
    sb.append("    requestedDeliveryDate: ").append(toIndentedString(requestedDeliveryDate)).append("\n");
    sb.append("    returnDetail: ").append(toIndentedString(returnDetail)).append("\n");
    sb.append("    returnLineTotalWithoutFees: ").append(toIndentedString(returnLineTotalWithoutFees)).append("\n");
    sb.append("    returnType: ").append(toIndentedString(returnType)).append("\n");
    sb.append("    returnableLineTotal: ").append(toIndentedString(returnableLineTotal)).append("\n");
    sb.append("    returnableQuantity: ").append(toIndentedString(returnableQuantity)).append("\n");
    sb.append("    sellingLocationId: ").append(toIndentedString(sellingLocationId)).append("\n");
    sb.append("    serviceLevelCode: ").append(toIndentedString(serviceLevelCode)).append("\n");
    sb.append("    shipFromAddress: ").append(toIndentedString(shipFromAddress)).append("\n");
    sb.append("    shipFromAddressId: ").append(toIndentedString(shipFromAddressId)).append("\n");
    sb.append("    shipToAddress: ").append(toIndentedString(shipToAddress)).append("\n");
    sb.append("    shipToLocationId: ").append(toIndentedString(shipToLocationId)).append("\n");
    sb.append("    shippingMethodId: ").append(toIndentedString(shippingMethodId)).append("\n");
    sb.append("    smallImageURI: ").append(toIndentedString(smallImageURI)).append("\n");
    sb.append("    storeSaleEntryMethod: ").append(toIndentedString(storeSaleEntryMethod)).append("\n");
    sb.append("    streetDate: ").append(toIndentedString(streetDate)).append("\n");
    sb.append("    taxOverridePercValue: ").append(toIndentedString(taxOverridePercValue)).append("\n");
    sb.append("    taxOverrideType: ").append(toIndentedString(taxOverrideType)).append("\n");
    sb.append("    taxOverrideValue: ").append(toIndentedString(taxOverrideValue)).append("\n");
    sb.append("    taxShipFromAddress: ").append(toIndentedString(taxShipFromAddress)).append("\n");
    sb.append("    taxShipFromLocationId: ").append(toIndentedString(taxShipFromLocationId)).append("\n");
    sb.append("    taxableAmount: ").append(toIndentedString(taxableAmount)).append("\n");
    sb.append("    totalCharges: ").append(toIndentedString(totalCharges)).append("\n");
    sb.append("    totalDiscountOnItem: ").append(toIndentedString(totalDiscountOnItem)).append("\n");
    sb.append("    totalDiscounts: ").append(toIndentedString(totalDiscounts)).append("\n");
    sb.append("    totalInformationalTaxes: ").append(toIndentedString(totalInformationalTaxes)).append("\n");
    sb.append("    totalTaxes: ").append(toIndentedString(totalTaxes)).append("\n");
    sb.append("    transactionReferenceId: ").append(toIndentedString(transactionReferenceId)).append("\n");
    sb.append("    UOM: ").append(toIndentedString(UOM)).append("\n");
    sb.append("    unitPrice: ").append(toIndentedString(unitPrice)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    valueEntryRequired: ").append(toIndentedString(valueEntryRequired)).append("\n");
    sb.append("    volumetricWeight: ").append(toIndentedString(volumetricWeight)).append("\n");
    sb.append("    volumetricWeightUOM: ").append(toIndentedString(volumetricWeightUOM)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

