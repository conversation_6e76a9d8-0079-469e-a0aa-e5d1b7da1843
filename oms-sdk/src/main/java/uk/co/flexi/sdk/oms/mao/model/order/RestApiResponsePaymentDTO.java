/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.33.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Object returned by all RESTful endpoints.
 */
public class RestApiResponsePaymentDTO {
  public static final String SERIALIZED_NAME_CLOUD_COMPONENT = "cloudComponent";
  @SerializedName(SERIALIZED_NAME_CLOUD_COMPONENT)
  private String cloudComponent;

  public static final String SERIALIZED_NAME_CLOUD_COMPONENT_HOST_NAME = "cloudComponentHostName";
  @SerializedName(SERIALIZED_NAME_CLOUD_COMPONENT_HOST_NAME)
  private String cloudComponentHostName;

  public static final String SERIALIZED_NAME_DATA = "data";
  @SerializedName(SERIALIZED_NAME_DATA)
  private List<PaymentHeaderDTO> data = null;

  public static final String SERIALIZED_NAME_ERRORS = "errors";
  @SerializedName(SERIALIZED_NAME_ERRORS)
  private List<RestApiError> errors = null;

  public static final String SERIALIZED_NAME_EXCEPTIONS = "exceptions";
  @SerializedName(SERIALIZED_NAME_EXCEPTIONS)
  private List<RestApiError> exceptions = null;

  public static final String SERIALIZED_NAME_HEADER = "header";
  @SerializedName(SERIALIZED_NAME_HEADER)
  private RestApiHeader header = null;

  public static final String SERIALIZED_NAME_MESSAGE = "message";
  @SerializedName(SERIALIZED_NAME_MESSAGE)
  private String message;

  public static final String SERIALIZED_NAME_MESSAGE_KEY = "messageKey";
  @SerializedName(SERIALIZED_NAME_MESSAGE_KEY)
  private String messageKey;

  public static final String SERIALIZED_NAME_MESSAGES = "messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_REQUEST_URI = "requestUri";
  @SerializedName(SERIALIZED_NAME_REQUEST_URI)
  private String requestUri;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  /**
   * Gets or Sets statusCode
   */
  @JsonAdapter(StatusCodeEnum.Adapter.class)
  public enum StatusCodeEnum {
    _100("100"),
    
    _101("101"),
    
    _102("102"),
    
    _103("103"),
    
    _200("200"),
    
    _201("201"),
    
    _202("202"),
    
    _203("203"),
    
    _204("204"),
    
    _205("205"),
    
    _206("206"),
    
    _207("207"),
    
    _208("208"),
    
    _226("226"),
    
    _300("300"),
    
    _301("301"),
    
    _302("302"),
    
    _303("303"),
    
    _304("304"),
    
    _305("305"),
    
    _307("307"),
    
    _308("308"),
    
    _400("400"),
    
    _401("401"),
    
    _402("402"),
    
    _403("403"),
    
    _404("404"),
    
    _405("405"),
    
    _406("406"),
    
    _407("407"),
    
    _408("408"),
    
    _409("409"),
    
    _410("410"),
    
    _411("411"),
    
    _412("412"),
    
    _413("413"),
    
    _414("414"),
    
    _415("415"),
    
    _416("416"),
    
    _417("417"),
    
    _418("418"),
    
    _419("419"),
    
    _420("420"),
    
    _421("421"),
    
    _422("422"),
    
    _423("423"),
    
    _424("424"),
    
    _426("426"),
    
    _428("428"),
    
    _429("429"),
    
    _431("431"),
    
    _451("451"),
    
    _500("500"),
    
    _501("501"),
    
    _502("502"),
    
    _503("503"),
    
    _504("504"),
    
    _505("505"),
    
    _506("506"),
    
    _507("507"),
    
    _508("508"),
    
    _509("509"),
    
    _510("510"),
    
    _511("511"),

    _OK("OK");

    private String value;

    StatusCodeEnum(String value) {
      this.value = value;
    }

    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    public static StatusCodeEnum fromValue(String text) {
      for (StatusCodeEnum b : StatusCodeEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + text + "'");
    }

    public static class Adapter extends TypeAdapter<StatusCodeEnum> {
      @Override
      public void write(final JsonWriter jsonWriter, final StatusCodeEnum enumeration) throws IOException {
        jsonWriter.value(enumeration.getValue());
      }

      @Override
      public StatusCodeEnum read(final JsonReader jsonReader) throws IOException {
        String value = jsonReader.nextString();
        return StatusCodeEnum.fromValue(String.valueOf(value));
      }
    }
  }

  public static final String SERIALIZED_NAME_STATUS_CODE = "statusCode";
  @SerializedName(SERIALIZED_NAME_STATUS_CODE)
  private StatusCodeEnum statusCode;

  public static final String SERIALIZED_NAME_SUCCESS = "success";
  @SerializedName(SERIALIZED_NAME_SUCCESS)
  private Boolean success;

  public RestApiResponsePaymentDTO cloudComponent(String cloudComponent) {
    this.cloudComponent = cloudComponent;
    return this;
  }

   /**
   * The name of the cloud component that created this object
   * @return cloudComponent
  **/
  
  public String getCloudComponent() {
    return cloudComponent;
  }

  public void setCloudComponent(String cloudComponent) {
    this.cloudComponent = cloudComponent;
  }

  public RestApiResponsePaymentDTO cloudComponentHostName(String cloudComponentHostName) {
    this.cloudComponentHostName = cloudComponentHostName;
    return this;
  }

   /**
   * The host name of where this cloud component lives
   * @return cloudComponentHostName
  **/
  
  public String getCloudComponentHostName() {
    return cloudComponentHostName;
  }

  public void setCloudComponentHostName(String cloudComponentHostName) {
    this.cloudComponentHostName = cloudComponentHostName;
  }

  public RestApiResponsePaymentDTO data(List<PaymentHeaderDTO> data) {
    this.data = data;
    return this;
  }

   /**
   * Get data
   * @return data
  **/
  
  public List<PaymentHeaderDTO> getData() {
    return data;
  }

  public void setData(List<PaymentHeaderDTO> data) {
    this.data = data;
  }

  public RestApiResponsePaymentDTO errors(List<RestApiError> errors) {
    this.errors = errors;
    return this;
  }

  public RestApiResponsePaymentDTO addErrorsItem(RestApiError errorsItem) {
    if (this.errors == null) {
      this.errors = new ArrayList<RestApiError>();
    }
    this.errors.add(errorsItem);
    return this;
  }

   /**
   * List of errors encountered (i.e. binding errors)
   * @return errors
  **/
  
  public List<RestApiError> getErrors() {
    return errors;
  }

  public void setErrors(List<RestApiError> errors) {
    this.errors = errors;
  }

  public RestApiResponsePaymentDTO exceptions(List<RestApiError> exceptions) {
    this.exceptions = exceptions;
    return this;
  }

  public RestApiResponsePaymentDTO addExceptionsItem(RestApiError exceptionsItem) {
    if (this.exceptions == null) {
      this.exceptions = new ArrayList<RestApiError>();
    }
    this.exceptions.add(exceptionsItem);
    return this;
  }

   /**
   * List of exceptions encountered
   * @return exceptions
  **/
  
  public List<RestApiError> getExceptions() {
    return exceptions;
  }

  public void setExceptions(List<RestApiError> exceptions) {
    this.exceptions = exceptions;
  }

  public RestApiResponsePaymentDTO header(RestApiHeader header) {
    this.header = header;
    return this;
  }

   /**
   * Get header
   * @return header
  **/
  
  public RestApiHeader getHeader() {
    return header;
  }

  public void setHeader(RestApiHeader header) {
    this.header = header;
  }

  public RestApiResponsePaymentDTO message(String message) {
    this.message = message;
    return this;
  }

   /**
   * The message resolved from the I18N messages
   * @return message
  **/
  
  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public RestApiResponsePaymentDTO messageKey(String messageKey) {
    this.messageKey = messageKey;
    return this;
  }

   /**
   * The message key used to resolve I18N messages
   * @return messageKey
  **/
  
  public String getMessageKey() {
    return messageKey;
  }

  public void setMessageKey(String messageKey) {
    this.messageKey = messageKey;
  }

  public RestApiResponsePaymentDTO messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public RestApiResponsePaymentDTO requestUri(String requestUri) {
    this.requestUri = requestUri;
    return this;
  }

   /**
   * The origninal requested uri for which the response is
   * @return requestUri
  **/
  
  public String getRequestUri() {
    return requestUri;
  }

  public void setRequestUri(String requestUri) {
    this.requestUri = requestUri;
  }

  public RestApiResponsePaymentDTO rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * The root cause of a problem (if any)
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }

  public RestApiResponsePaymentDTO statusCode(StatusCodeEnum statusCode) {
    this.statusCode = statusCode;
    return this;
  }

   /**
   * Get statusCode
   * @return statusCode
  **/
  
  public StatusCodeEnum getStatusCode() {
    return statusCode;
  }

  public void setStatusCode(StatusCodeEnum statusCode) {
    this.statusCode = statusCode;
  }

  public RestApiResponsePaymentDTO success(Boolean success) {
    this.success = success;
    return this;
  }

   /**
   * Whether the response represents a success or an error
   * @return success
  **/
  
  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RestApiResponsePromisingResponseDTO {\n");
    
    sb.append("    cloudComponent: ").append(toIndentedString(cloudComponent)).append("\n");
    sb.append("    cloudComponentHostName: ").append(toIndentedString(cloudComponentHostName)).append("\n");
    sb.append("    data: ").append(toIndentedString(data)).append("\n");
    sb.append("    errors: ").append(toIndentedString(errors)).append("\n");
    sb.append("    exceptions: ").append(toIndentedString(exceptions)).append("\n");
    sb.append("    header: ").append(toIndentedString(header)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    messageKey: ").append(toIndentedString(messageKey)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    requestUri: ").append(toIndentedString(requestUri)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("    statusCode: ").append(toIndentedString(statusCode)).append("\n");
    sb.append("    success: ").append(toIndentedString(success)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

