package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * HandlingAttributes
 */
public class HandlingAttributes {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_HAZMAT_CODE = "HazmatCode";
  @SerializedName(SERIALIZED_NAME_HAZMAT_CODE)
  private String hazmatCode;

  public static final String SERIALIZED_NAME_IS_AIR_SHIPPING_ALLOWED = "IsAirShippingAllowed";
  @SerializedName(SERIALIZED_NAME_IS_AIR_SHIPPING_ALLOWED)
  private Boolean isAirShippingAllowed;

  public static final String SERIALIZED_NAME_IS_FROZEN = "IsFrozen";
  @SerializedName(SERIALIZED_NAME_IS_FROZEN)
  private Boolean isFrozen;

  public static final String SERIALIZED_NAME_IS_HAZMAT = "IsHazmat";
  @SerializedName(SERIALIZED_NAME_IS_HAZMAT)
  private Boolean isHazmat;

  public static final String SERIALIZED_NAME_IS_PARCEL_SHIPPING_ALLOWED = "IsParcelShippingAllowed";
  @SerializedName(SERIALIZED_NAME_IS_PARCEL_SHIPPING_ALLOWED)
  private Boolean isParcelShippingAllowed;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public HandlingAttributes actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public HandlingAttributes putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public HandlingAttributes createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public HandlingAttributes createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public HandlingAttributes extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public HandlingAttributes hazmatCode(String hazmatCode) {
    this.hazmatCode = hazmatCode;
    return this;
  }

   /**
   * Hazardous Material code of the item
   * @return hazmatCode
  **/
  public String getHazmatCode() {
    return hazmatCode;
  }

  public void setHazmatCode(String hazmatCode) {
    this.hazmatCode = hazmatCode;
  }

  public HandlingAttributes isAirShippingAllowed(Boolean isAirShippingAllowed) {
    this.isAirShippingAllowed = isAirShippingAllowed;
    return this;
  }

   /**
   * Indicates if the item can be air shipped or not
   * @return isAirShippingAllowed
  **/
  public Boolean getIsAirShippingAllowed() {
    return isAirShippingAllowed;
  }

  public void setIsAirShippingAllowed(Boolean isAirShippingAllowed) {
    this.isAirShippingAllowed = isAirShippingAllowed;
  }

  public HandlingAttributes isFrozen(Boolean isFrozen) {
    this.isFrozen = isFrozen;
    return this;
  }

   /**
   * Indicates if the item is frozen item or not
   * @return isFrozen
  **/
  public Boolean getIsFrozen() {
    return isFrozen;
  }

  public void setIsFrozen(Boolean isFrozen) {
    this.isFrozen = isFrozen;
  }

  public HandlingAttributes isHazmat(Boolean isHazmat) {
    this.isHazmat = isHazmat;
    return this;
  }

   /**
   * Indicates if the Item is Hazardous or not
   * @return isHazmat
  **/
  public Boolean getIsHazmat() {
    return isHazmat;
  }

  public void setIsHazmat(Boolean isHazmat) {
    this.isHazmat = isHazmat;
  }

  public HandlingAttributes isParcelShippingAllowed(Boolean isParcelShippingAllowed) {
    this.isParcelShippingAllowed = isParcelShippingAllowed;
    return this;
  }

   /**
   * Indicates if the item is parcel or non parcel item
   * @return isParcelShippingAllowed
  **/
  public Boolean getIsParcelShippingAllowed() {
    return isParcelShippingAllowed;
  }

  public void setIsParcelShippingAllowed(Boolean isParcelShippingAllowed) {
    this.isParcelShippingAllowed = isParcelShippingAllowed;
  }

  public HandlingAttributes localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public HandlingAttributes messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public HandlingAttributes PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public HandlingAttributes profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public HandlingAttributes updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public HandlingAttributes updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public HandlingAttributes entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public HandlingAttributes localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HandlingAttributes handlingAttributes = (HandlingAttributes) o;
    return Objects.equals(this.actions, handlingAttributes.actions) &&
        Objects.equals(this.createdBy, handlingAttributes.createdBy) &&
        Objects.equals(this.createdTimestamp, handlingAttributes.createdTimestamp) &&
        Objects.equals(this.extended, handlingAttributes.extended) &&
        Objects.equals(this.hazmatCode, handlingAttributes.hazmatCode) &&
        Objects.equals(this.isAirShippingAllowed, handlingAttributes.isAirShippingAllowed) &&
        Objects.equals(this.isFrozen, handlingAttributes.isFrozen) &&
        Objects.equals(this.isHazmat, handlingAttributes.isHazmat) &&
        Objects.equals(this.isParcelShippingAllowed, handlingAttributes.isParcelShippingAllowed) &&
        Objects.equals(this.localizedTo, handlingAttributes.localizedTo) &&
        Objects.equals(this.messages, handlingAttributes.messages) &&
        Objects.equals(this.PK, handlingAttributes.PK) &&
        Objects.equals(this.profileId, handlingAttributes.profileId) &&
        Objects.equals(this.updatedBy, handlingAttributes.updatedBy) &&
        Objects.equals(this.updatedTimestamp, handlingAttributes.updatedTimestamp) &&
        Objects.equals(this.entityName, handlingAttributes.entityName) &&
        Objects.equals(this.localize, handlingAttributes.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, extended, hazmatCode, isAirShippingAllowed, isFrozen, isHazmat, isParcelShippingAllowed, localizedTo, messages, PK, profileId, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HandlingAttributes {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    hazmatCode: ").append(toIndentedString(hazmatCode)).append("\n");
    sb.append("    isAirShippingAllowed: ").append(toIndentedString(isAirShippingAllowed)).append("\n");
    sb.append("    isFrozen: ").append(toIndentedString(isFrozen)).append("\n");
    sb.append("    isHazmat: ").append(toIndentedString(isHazmat)).append("\n");
    sb.append("    isParcelShippingAllowed: ").append(toIndentedString(isParcelShippingAllowed)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

