/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * ManufacturingAttribute
 */
public class ManufacturingAttribute {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_COUNTRYOF_ORIGIN = "CountryofOrigin";
  @SerializedName(SERIALIZED_NAME_COUNTRYOF_ORIGIN)
  private String countryofOrigin;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_ITEM = "ParentItem";
  @SerializedName(SERIALIZED_NAME_PARENT_ITEM)
  private PrimaryKey parentItem = null;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_SCAN_QUANTITY = "ScanQuantity";
  @SerializedName(SERIALIZED_NAME_SCAN_QUANTITY)
  private Double scanQuantity;

  public static final String SERIALIZED_NAME_UPC = "Upc";
  @SerializedName(SERIALIZED_NAME_UPC)
  private String upc;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_VENDOR_LOCATION_ID = "VendorLocationId";
  @SerializedName(SERIALIZED_NAME_VENDOR_LOCATION_ID)
  private String vendorLocationId;

  public static final String SERIALIZED_NAME_VENDOR_STYLE_NUMBER = "VendorStyleNumber";
  @SerializedName(SERIALIZED_NAME_VENDOR_STYLE_NUMBER)
  private String vendorStyleNumber;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public ManufacturingAttribute actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ManufacturingAttribute putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ManufacturingAttribute countryofOrigin(String countryofOrigin) {
    this.countryofOrigin = countryofOrigin;
    return this;
  }

   /**
   * Country of origin where the item is manufactured
   * @return countryofOrigin
  **/
  public String getCountryofOrigin() {
    return countryofOrigin;
  }

  public void setCountryofOrigin(String countryofOrigin) {
    this.countryofOrigin = countryofOrigin;
  }

  public ManufacturingAttribute createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ManufacturingAttribute createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ManufacturingAttribute extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ManufacturingAttribute localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public ManufacturingAttribute messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ManufacturingAttribute PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ManufacturingAttribute parentItem(PrimaryKey parentItem) {
    this.parentItem = parentItem;
    return this;
  }

   /**
   * Get parentItem
   * @return parentItem
  **/
  public PrimaryKey getParentItem() {
    return parentItem;
  }

  public void setParentItem(PrimaryKey parentItem) {
    this.parentItem = parentItem;
  }

  public ManufacturingAttribute profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public ManufacturingAttribute scanQuantity(Double scanQuantity) {
    this.scanQuantity = scanQuantity;
    return this;
  }

   /**
   * Quantity included in scan of specified barcode
   * minimum: 0
   * maximum: 999999999999.9999
   * @return scanQuantity
  **/
  public Double getScanQuantity() {
    return scanQuantity;
  }

  public void setScanQuantity(Double scanQuantity) {
    this.scanQuantity = scanQuantity;
  }

  public ManufacturingAttribute upc(String upc) {
    this.upc = upc;
    return this;
  }

   /**
   * Unique identifier of the item, for the given vendor or country of origin
   * @return upc
  **/
  public String getUpc() {
    return upc;
  }

  public void setUpc(String upc) {
    this.upc = upc;
  }

  public ManufacturingAttribute updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ManufacturingAttribute updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ManufacturingAttribute vendorLocationId(String vendorLocationId) {
    this.vendorLocationId = vendorLocationId;
    return this;
  }

   /**
   * Vendor location which uses the specified UPC
   * @return vendorLocationId
  **/
  public String getVendorLocationId() {
    return vendorLocationId;
  }

  public void setVendorLocationId(String vendorLocationId) {
    this.vendorLocationId = vendorLocationId;
  }

  public ManufacturingAttribute vendorStyleNumber(String vendorStyleNumber) {
    this.vendorStyleNumber = vendorStyleNumber;
    return this;
  }

   /**
   * Style number given by the vendor
   * @return vendorStyleNumber
  **/
  public String getVendorStyleNumber() {
    return vendorStyleNumber;
  }

  public void setVendorStyleNumber(String vendorStyleNumber) {
    this.vendorStyleNumber = vendorStyleNumber;
  }

  public ManufacturingAttribute entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public ManufacturingAttribute localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ManufacturingAttribute manufacturingAttribute = (ManufacturingAttribute) o;
    return Objects.equals(this.actions, manufacturingAttribute.actions) &&
        Objects.equals(this.countryofOrigin, manufacturingAttribute.countryofOrigin) &&
        Objects.equals(this.createdBy, manufacturingAttribute.createdBy) &&
        Objects.equals(this.createdTimestamp, manufacturingAttribute.createdTimestamp) &&
        Objects.equals(this.extended, manufacturingAttribute.extended) &&
        Objects.equals(this.localizedTo, manufacturingAttribute.localizedTo) &&
        Objects.equals(this.messages, manufacturingAttribute.messages) &&
        Objects.equals(this.PK, manufacturingAttribute.PK) &&
        Objects.equals(this.parentItem, manufacturingAttribute.parentItem) &&
        Objects.equals(this.profileId, manufacturingAttribute.profileId) &&
        Objects.equals(this.scanQuantity, manufacturingAttribute.scanQuantity) &&
        Objects.equals(this.upc, manufacturingAttribute.upc) &&
        Objects.equals(this.updatedBy, manufacturingAttribute.updatedBy) &&
        Objects.equals(this.updatedTimestamp, manufacturingAttribute.updatedTimestamp) &&
        Objects.equals(this.vendorLocationId, manufacturingAttribute.vendorLocationId) &&
        Objects.equals(this.vendorStyleNumber, manufacturingAttribute.vendorStyleNumber) &&
        Objects.equals(this.entityName, manufacturingAttribute.entityName) &&
        Objects.equals(this.localize, manufacturingAttribute.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, countryofOrigin, createdBy, createdTimestamp, extended, localizedTo, messages, PK, parentItem, profileId, scanQuantity, upc, updatedBy, updatedTimestamp, vendorLocationId, vendorStyleNumber, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ManufacturingAttribute {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    countryofOrigin: ").append(toIndentedString(countryofOrigin)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentItem: ").append(toIndentedString(parentItem)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    scanQuantity: ").append(toIndentedString(scanQuantity)).append("\n");
    sb.append("    upc: ").append(toIndentedString(upc)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    vendorLocationId: ").append(toIndentedString(vendorLocationId)).append("\n");
    sb.append("    vendorStyleNumber: ").append(toIndentedString(vendorStyleNumber)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

