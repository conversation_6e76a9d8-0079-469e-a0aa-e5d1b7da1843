/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Object describing a single error situation.
 */
public class RestApiError {
  public static final String SERIALIZED_NAME_MESSAGE = "message";
  @SerializedName(SERIALIZED_NAME_MESSAGE)
  private String message;

  public static final String SERIALIZED_NAME_MESSAGE_DATA = "messageData";
  @SerializedName(SERIALIZED_NAME_MESSAGE_DATA)
  private Map<String, String> messageData = null;

  public static final String SERIALIZED_NAME_MESSAGE_KEY = "messageKey";
  @SerializedName(SERIALIZED_NAME_MESSAGE_KEY)
  private String messageKey;

  public static final String SERIALIZED_NAME_RESOURCE = "resource";
  @SerializedName(SERIALIZED_NAME_RESOURCE)
  private String resource;

  public static final String SERIALIZED_NAME_STACK_TRACE = "stackTrace";
  @SerializedName(SERIALIZED_NAME_STACK_TRACE)
  private String stackTrace;

  public RestApiError message(String message) {
    this.message = message;
    return this;
  }

   /**
   * The I18N message
   * @return message
  **/
  
  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public RestApiError messageData(Map<String, String> messageData) {
    this.messageData = messageData;
    return this;
  }

  public RestApiError putMessageDataItem(String key, String messageDataItem) {
    if (this.messageData == null) {
      this.messageData = new HashMap<String, String>();
    }
    this.messageData.put(key, messageDataItem);
    return this;
  }

   /**
   * Map containing key/value pair to be used for interpolation of the I18N message
   * @return messageData
  **/
  
  public Map<String, String> getMessageData() {
    return messageData;
  }

  public void setMessageData(Map<String, String> messageData) {
    this.messageData = messageData;
  }

  public RestApiError messageKey(String messageKey) {
    this.messageKey = messageKey;
    return this;
  }

   /**
   * The message key to be used for I18N support
   * @return messageKey
  **/
  
  public String getMessageKey() {
    return messageKey;
  }

  public void setMessageKey(String messageKey) {
    this.messageKey = messageKey;
  }

  public RestApiError resource(String resource) {
    this.resource = resource;
    return this;
  }

   /**
   * Simple name of the underlying class responsible for this error
   * @return resource
  **/
  
  public String getResource() {
    return resource;
  }

  public void setResource(String resource) {
    this.resource = resource;
  }

  public RestApiError stackTrace(String stackTrace) {
    this.stackTrace = stackTrace;
    return this;
  }

   /**
   * The stack trace belonging to this error
   * @return stackTrace
  **/
  
  public String getStackTrace() {
    return stackTrace;
  }

  public void setStackTrace(String stackTrace) {
    this.stackTrace = stackTrace;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RestApiError restApiError = (RestApiError) o;
    return Objects.equals(this.message, restApiError.message) &&
        Objects.equals(this.messageData, restApiError.messageData) &&
        Objects.equals(this.messageKey, restApiError.messageKey) &&
        Objects.equals(this.resource, restApiError.resource) &&
        Objects.equals(this.stackTrace, restApiError.stackTrace);
  }

  @Override
  public int hashCode() {
    return Objects.hash(message, messageData, messageKey, resource, stackTrace);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RestApiError {\n");
    
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    messageData: ").append(toIndentedString(messageData)).append("\n");
    sb.append("    messageKey: ").append(toIndentedString(messageKey)).append("\n");
    sb.append("    resource: ").append(toIndentedString(resource)).append("\n");
    sb.append("    stackTrace: ").append(toIndentedString(stackTrace)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

