/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * OrderFulfillmentGroupDTO
 */
public class OrderFulfillmentGroupDTO {
  public static final String SERIALIZED_NAME_GROUP_ID = "GroupId";
  @SerializedName(SERIALIZED_NAME_GROUP_ID)
  private String groupId;

  public static final String SERIALIZED_NAME_SUBTOTAL = "Subtotal";
  @SerializedName(SERIALIZED_NAME_SUBTOTAL)
  private BigDecimal subtotal;

  public static final String SERIALIZED_NAME_TOTAL = "Total";
  @SerializedName(SERIALIZED_NAME_TOTAL)
  private BigDecimal total;

  public static final String SERIALIZED_NAME_TOTAL_CHARGES_RETURN_FEE = "TotalChargesReturnFee";
  @SerializedName(SERIALIZED_NAME_TOTAL_CHARGES_RETURN_FEE)
  private BigDecimal totalChargesReturnFee;

  public static final String SERIALIZED_NAME_TOTAL_CHARGES_RETURN_SHIPPING = "TotalChargesReturnShipping";
  @SerializedName(SERIALIZED_NAME_TOTAL_CHARGES_RETURN_SHIPPING)
  private BigDecimal totalChargesReturnShipping;

  public static final String SERIALIZED_NAME_TOTAL_CHARGES_SHIPPING = "TotalChargesShipping";
  @SerializedName(SERIALIZED_NAME_TOTAL_CHARGES_SHIPPING)
  private BigDecimal totalChargesShipping;

  public static final String SERIALIZED_NAME_TOTAL_CHARGES_V_A_S = "TotalChargesVAS";
  @SerializedName(SERIALIZED_NAME_TOTAL_CHARGES_V_A_S)
  private BigDecimal totalChargesVAS;

  public static final String SERIALIZED_NAME_TOTAL_DISCOUNT_ON_ITEM = "TotalDiscountOnItem";
  @SerializedName(SERIALIZED_NAME_TOTAL_DISCOUNT_ON_ITEM)
  private BigDecimal totalDiscountOnItem;

  public static final String SERIALIZED_NAME_TOTAL_DISCOUNT_SHIPPING = "TotalDiscountShipping";
  @SerializedName(SERIALIZED_NAME_TOTAL_DISCOUNT_SHIPPING)
  private BigDecimal totalDiscountShipping;

  public static final String SERIALIZED_NAME_TOTAL_TAXES = "TotalTaxes";
  @SerializedName(SERIALIZED_NAME_TOTAL_TAXES)
  private BigDecimal totalTaxes;

  public OrderFulfillmentGroupDTO groupId(String groupId) {
    this.groupId = groupId;
    return this;
  }

   /**
   * Get groupId
   * @return groupId
  **/
  
  public String getGroupId() {
    return groupId;
  }

  public void setGroupId(String groupId) {
    this.groupId = groupId;
  }

  public OrderFulfillmentGroupDTO subtotal(BigDecimal subtotal) {
    this.subtotal = subtotal;
    return this;
  }

   /**
   * Get subtotal
   * @return subtotal
  **/
  
  public BigDecimal getSubtotal() {
    return subtotal;
  }

  public void setSubtotal(BigDecimal subtotal) {
    this.subtotal = subtotal;
  }

  public OrderFulfillmentGroupDTO total(BigDecimal total) {
    this.total = total;
    return this;
  }

   /**
   * Get total
   * @return total
  **/
  
  public BigDecimal getTotal() {
    return total;
  }

  public void setTotal(BigDecimal total) {
    this.total = total;
  }

  public OrderFulfillmentGroupDTO totalChargesReturnFee(BigDecimal totalChargesReturnFee) {
    this.totalChargesReturnFee = totalChargesReturnFee;
    return this;
  }

   /**
   * Get totalChargesReturnFee
   * @return totalChargesReturnFee
  **/
  
  public BigDecimal getTotalChargesReturnFee() {
    return totalChargesReturnFee;
  }

  public void setTotalChargesReturnFee(BigDecimal totalChargesReturnFee) {
    this.totalChargesReturnFee = totalChargesReturnFee;
  }

  public OrderFulfillmentGroupDTO totalChargesReturnShipping(BigDecimal totalChargesReturnShipping) {
    this.totalChargesReturnShipping = totalChargesReturnShipping;
    return this;
  }

   /**
   * Get totalChargesReturnShipping
   * @return totalChargesReturnShipping
  **/
  
  public BigDecimal getTotalChargesReturnShipping() {
    return totalChargesReturnShipping;
  }

  public void setTotalChargesReturnShipping(BigDecimal totalChargesReturnShipping) {
    this.totalChargesReturnShipping = totalChargesReturnShipping;
  }

  public OrderFulfillmentGroupDTO totalChargesShipping(BigDecimal totalChargesShipping) {
    this.totalChargesShipping = totalChargesShipping;
    return this;
  }

   /**
   * Get totalChargesShipping
   * @return totalChargesShipping
  **/
  
  public BigDecimal getTotalChargesShipping() {
    return totalChargesShipping;
  }

  public void setTotalChargesShipping(BigDecimal totalChargesShipping) {
    this.totalChargesShipping = totalChargesShipping;
  }

  public OrderFulfillmentGroupDTO totalChargesVAS(BigDecimal totalChargesVAS) {
    this.totalChargesVAS = totalChargesVAS;
    return this;
  }

   /**
   * Get totalChargesVAS
   * @return totalChargesVAS
  **/
  
  public BigDecimal getTotalChargesVAS() {
    return totalChargesVAS;
  }

  public void setTotalChargesVAS(BigDecimal totalChargesVAS) {
    this.totalChargesVAS = totalChargesVAS;
  }

  public OrderFulfillmentGroupDTO totalDiscountOnItem(BigDecimal totalDiscountOnItem) {
    this.totalDiscountOnItem = totalDiscountOnItem;
    return this;
  }

   /**
   * Get totalDiscountOnItem
   * @return totalDiscountOnItem
  **/
  
  public BigDecimal getTotalDiscountOnItem() {
    return totalDiscountOnItem;
  }

  public void setTotalDiscountOnItem(BigDecimal totalDiscountOnItem) {
    this.totalDiscountOnItem = totalDiscountOnItem;
  }

  public OrderFulfillmentGroupDTO totalDiscountShipping(BigDecimal totalDiscountShipping) {
    this.totalDiscountShipping = totalDiscountShipping;
    return this;
  }

   /**
   * Get totalDiscountShipping
   * @return totalDiscountShipping
  **/
  
  public BigDecimal getTotalDiscountShipping() {
    return totalDiscountShipping;
  }

  public void setTotalDiscountShipping(BigDecimal totalDiscountShipping) {
    this.totalDiscountShipping = totalDiscountShipping;
  }

  public OrderFulfillmentGroupDTO totalTaxes(BigDecimal totalTaxes) {
    this.totalTaxes = totalTaxes;
    return this;
  }

   /**
   * Get totalTaxes
   * @return totalTaxes
  **/
  
  public BigDecimal getTotalTaxes() {
    return totalTaxes;
  }

  public void setTotalTaxes(BigDecimal totalTaxes) {
    this.totalTaxes = totalTaxes;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderFulfillmentGroupDTO orderFulfillmentGroupDTO = (OrderFulfillmentGroupDTO) o;
    return Objects.equals(this.groupId, orderFulfillmentGroupDTO.groupId) &&
        Objects.equals(this.subtotal, orderFulfillmentGroupDTO.subtotal) &&
        Objects.equals(this.total, orderFulfillmentGroupDTO.total) &&
        Objects.equals(this.totalChargesReturnFee, orderFulfillmentGroupDTO.totalChargesReturnFee) &&
        Objects.equals(this.totalChargesReturnShipping, orderFulfillmentGroupDTO.totalChargesReturnShipping) &&
        Objects.equals(this.totalChargesShipping, orderFulfillmentGroupDTO.totalChargesShipping) &&
        Objects.equals(this.totalChargesVAS, orderFulfillmentGroupDTO.totalChargesVAS) &&
        Objects.equals(this.totalDiscountOnItem, orderFulfillmentGroupDTO.totalDiscountOnItem) &&
        Objects.equals(this.totalDiscountShipping, orderFulfillmentGroupDTO.totalDiscountShipping) &&
        Objects.equals(this.totalTaxes, orderFulfillmentGroupDTO.totalTaxes);
  }

  @Override
  public int hashCode() {
    return Objects.hash(groupId, subtotal, total, totalChargesReturnFee, totalChargesReturnShipping, totalChargesShipping, totalChargesVAS, totalDiscountOnItem, totalDiscountShipping, totalTaxes);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderFulfillmentGroupDTO {\n");
    
    sb.append("    groupId: ").append(toIndentedString(groupId)).append("\n");
    sb.append("    subtotal: ").append(toIndentedString(subtotal)).append("\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("    totalChargesReturnFee: ").append(toIndentedString(totalChargesReturnFee)).append("\n");
    sb.append("    totalChargesReturnShipping: ").append(toIndentedString(totalChargesReturnShipping)).append("\n");
    sb.append("    totalChargesShipping: ").append(toIndentedString(totalChargesShipping)).append("\n");
    sb.append("    totalChargesVAS: ").append(toIndentedString(totalChargesVAS)).append("\n");
    sb.append("    totalDiscountOnItem: ").append(toIndentedString(totalDiscountOnItem)).append("\n");
    sb.append("    totalDiscountShipping: ").append(toIndentedString(totalDiscountShipping)).append("\n");
    sb.append("    totalTaxes: ").append(toIndentedString(totalTaxes)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

