/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * OrderLines
 */
public class OrderLines {
  public static final String SERIALIZED_NAME_ORDER_LINE_IDS = "OrderLineIds";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_IDS)
  private List<OrderLineIdDTO> orderLineIds = null;

  public OrderLines orderLineIds(List<OrderLineIdDTO> orderLineIds) {
    this.orderLineIds = orderLineIds;
    return this;
  }

  public OrderLines addOrderLineIdsItem(OrderLineIdDTO orderLineIdsItem) {
    if (this.orderLineIds == null) {
      this.orderLineIds = new ArrayList<OrderLineIdDTO>();
    }
    this.orderLineIds.add(orderLineIdsItem);
    return this;
  }

   /**
   * Get orderLineIds
   * @return orderLineIds
  **/
  
  public List<OrderLineIdDTO> getOrderLineIds() {
    return orderLineIds;
  }

  public void setOrderLineIds(List<OrderLineIdDTO> orderLineIds) {
    this.orderLineIds = orderLineIds;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderLines orderLines = (OrderLines) o;
    return Objects.equals(this.orderLineIds, orderLines.orderLineIds);
  }

  @Override
  public int hashCode() {
    return Objects.hash(orderLineIds);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderLines {\n");
    
    sb.append("    orderLineIds: ").append(toIndentedString(orderLineIds)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

