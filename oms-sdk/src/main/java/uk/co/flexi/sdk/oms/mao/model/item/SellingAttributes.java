package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.LocalDate;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * SellingAttributes
 */
public class SellingAttributes {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ACTIVATION_REQUIRED = "ActivationRequired";
  @SerializedName(SERIALIZED_NAME_ACTIVATION_REQUIRED)
  private Boolean activationRequired;

  public static final String SERIALIZED_NAME_AVAILABLE_TO_SELL_DATE_TIME = "AvailableToSellDateTime";
  @SerializedName(SERIALIZED_NAME_AVAILABLE_TO_SELL_DATE_TIME)
  private OffsetDateTime availableToSellDateTime;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DIGITAL_GOODS = "DigitalGoods";
  @SerializedName(SERIALIZED_NAME_DIGITAL_GOODS)
  private Boolean digitalGoods;

  public static final String SERIALIZED_NAME_DISPOSITION_ID = "DispositionId";
  @SerializedName(SERIALIZED_NAME_DISPOSITION_ID)
  private String dispositionId;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IS_DISCOUNTABLE = "IsDiscountable";
  @SerializedName(SERIALIZED_NAME_IS_DISCOUNTABLE)
  private Boolean isDiscountable;

  public static final String SERIALIZED_NAME_IS_EXCHANGEABLE = "IsExchangeable";
  @SerializedName(SERIALIZED_NAME_IS_EXCHANGEABLE)
  private Boolean isExchangeable;

  public static final String SERIALIZED_NAME_IS_PRICE_OVERRIDEABLE = "IsPriceOverrideable";
  @SerializedName(SERIALIZED_NAME_IS_PRICE_OVERRIDEABLE)
  private Boolean isPriceOverrideable;

  public static final String SERIALIZED_NAME_IS_RETURNABLE_AT_D_C = "IsReturnableAtDC";
  @SerializedName(SERIALIZED_NAME_IS_RETURNABLE_AT_D_C)
  private Boolean isReturnableAtDC;

  public static final String SERIALIZED_NAME_IS_RETURNABLE_AT_STORE = "IsReturnableAtStore";
  @SerializedName(SERIALIZED_NAME_IS_RETURNABLE_AT_STORE)
  private Boolean isReturnableAtStore;

  public static final String SERIALIZED_NAME_IS_TAX_EXEMPTABLE = "IsTaxExemptable";
  @SerializedName(SERIALIZED_NAME_IS_TAX_EXEMPTABLE)
  private Boolean isTaxExemptable;

  public static final String SERIALIZED_NAME_IS_TAX_OVERRIDEABLE = "IsTaxOverrideable";
  @SerializedName(SERIALIZED_NAME_IS_TAX_OVERRIDEABLE)
  private Boolean isTaxOverrideable;

  public static final String SERIALIZED_NAME_IS_TAXABLE = "IsTaxable";
  @SerializedName(SERIALIZED_NAME_IS_TAXABLE)
  private Boolean isTaxable;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MAX_DISCOUNT = "MaxDiscount";
  @SerializedName(SERIALIZED_NAME_MAX_DISCOUNT)
  private BigDecimal maxDiscount;

  public static final String SERIALIZED_NAME_MAX_DISCOUNT_AMOUNT = "MaxDiscountAmount";
  @SerializedName(SERIALIZED_NAME_MAX_DISCOUNT_AMOUNT)
  private BigDecimal maxDiscountAmount;

  public static final String SERIALIZED_NAME_MAX_DISCOUNT_PERCENTAGE = "MaxDiscountPercentage";
  @SerializedName(SERIALIZED_NAME_MAX_DISCOUNT_PERCENTAGE)
  private BigDecimal maxDiscountPercentage;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PICK_UP_IN_STORE = "PickUpInStore";
  @SerializedName(SERIALIZED_NAME_PICK_UP_IN_STORE)
  private Boolean pickUpInStore;

  public static final String SERIALIZED_NAME_PRICE_STATUS_ID = "PriceStatusId";
  @SerializedName(SERIALIZED_NAME_PRICE_STATUS_ID)
  private String priceStatusId;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_SELL_DATE = "SellDate";
  @SerializedName(SERIALIZED_NAME_SELL_DATE)
  private LocalDate sellDate;

  public static final String SERIALIZED_NAME_SELL_THROUGH = "SellThrough";
  @SerializedName(SERIALIZED_NAME_SELL_THROUGH)
  private Double sellThrough;

  public static final String SERIALIZED_NAME_SHIP_TO_ADDRESS = "ShipToAddress";
  @SerializedName(SERIALIZED_NAME_SHIP_TO_ADDRESS)
  private Boolean shipToAddress;

  public static final String SERIALIZED_NAME_SHIP_TO_STORE = "ShipToStore";
  @SerializedName(SERIALIZED_NAME_SHIP_TO_STORE)
  private Boolean shipToStore;

  public static final String SERIALIZED_NAME_SOLD_IN_STORES = "SoldInStores";
  @SerializedName(SERIALIZED_NAME_SOLD_IN_STORES)
  private Boolean soldInStores;

  public static final String SERIALIZED_NAME_SOLD_ONLINE = "SoldOnline";
  @SerializedName(SERIALIZED_NAME_SOLD_ONLINE)
  private Boolean soldOnline;

  public static final String SERIALIZED_NAME_TAX_CODE = "TaxCode";
  @SerializedName(SERIALIZED_NAME_TAX_CODE)
  private String taxCode;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_VALUE_ENTRY_REQUIRED = "ValueEntryRequired";
  @SerializedName(SERIALIZED_NAME_VALUE_ENTRY_REQUIRED)
  private Boolean valueEntryRequired;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public SellingAttributes actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public SellingAttributes putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public SellingAttributes activationRequired(Boolean activationRequired) {
    this.activationRequired = activationRequired;
    return this;
  }

   /**
   * Indicates if item requires activation (e.g. gift cards)
   * @return activationRequired
  **/
  public Boolean getActivationRequired() {
    return activationRequired;
  }

  public void setActivationRequired(Boolean activationRequired) {
    this.activationRequired = activationRequired;
  }

  public SellingAttributes availableToSellDateTime(OffsetDateTime availableToSellDateTime) {
    this.availableToSellDateTime = availableToSellDateTime;
    return this;
  }

   /**
   * Indicates the date,time from when an item can be sold in a store or online
   * @return availableToSellDateTime
  **/
  public OffsetDateTime getAvailableToSellDateTime() {
    return availableToSellDateTime;
  }

  public void setAvailableToSellDateTime(OffsetDateTime availableToSellDateTime) {
    this.availableToSellDateTime = availableToSellDateTime;
  }

  public SellingAttributes createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public SellingAttributes createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public SellingAttributes digitalGoods(Boolean digitalGoods) {
    this.digitalGoods = digitalGoods;
    return this;
  }

   /**
   * Item which are digital goods are identified with this flag. Digital goods does not need any store or warehouse fulfillment process instead are fulfilled via email confirmation.  For example, e- gift card. 
   * @return digitalGoods
  **/
  public Boolean getDigitalGoods() {
    return digitalGoods;
  }

  public void setDigitalGoods(Boolean digitalGoods) {
    this.digitalGoods = digitalGoods;
  }

  public SellingAttributes dispositionId(String dispositionId) {
    this.dispositionId = dispositionId;
    return this;
  }

   /**
   * Disposition of the item (e.g. Recalled, Active, Inactive)
   * @return dispositionId
  **/
  public String getDispositionId() {
    return dispositionId;
  }

  public void setDispositionId(String dispositionId) {
    this.dispositionId = dispositionId;
  }

  public SellingAttributes extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public SellingAttributes isDiscountable(Boolean isDiscountable) {
    this.isDiscountable = isDiscountable;
    return this;
  }

   /**
   * Indicates if item is discountable
   * @return isDiscountable
  **/
  public Boolean getIsDiscountable() {
    return isDiscountable;
  }

  public void setIsDiscountable(Boolean isDiscountable) {
    this.isDiscountable = isDiscountable;
  }

  public SellingAttributes isExchangeable(Boolean isExchangeable) {
    this.isExchangeable = isExchangeable;
    return this;
  }

   /**
   * Indicates if item can be exchanged
   * @return isExchangeable
  **/
  public Boolean getIsExchangeable() {
    return isExchangeable;
  }

  public void setIsExchangeable(Boolean isExchangeable) {
    this.isExchangeable = isExchangeable;
  }

  public SellingAttributes isPriceOverrideable(Boolean isPriceOverrideable) {
    this.isPriceOverrideable = isPriceOverrideable;
    return this;
  }

   /**
   * Indicates if price can be overridden for the item
   * @return isPriceOverrideable
  **/
  public Boolean getIsPriceOverrideable() {
    return isPriceOverrideable;
  }

  public void setIsPriceOverrideable(Boolean isPriceOverrideable) {
    this.isPriceOverrideable = isPriceOverrideable;
  }

  public SellingAttributes isReturnableAtDC(Boolean isReturnableAtDC) {
    this.isReturnableAtDC = isReturnableAtDC;
    return this;
  }

   /**
   * Indicates if item is returnable at DC locations
   * @return isReturnableAtDC
  **/
  public Boolean getIsReturnableAtDC() {
    return isReturnableAtDC;
  }

  public void setIsReturnableAtDC(Boolean isReturnableAtDC) {
    this.isReturnableAtDC = isReturnableAtDC;
  }

  public SellingAttributes isReturnableAtStore(Boolean isReturnableAtStore) {
    this.isReturnableAtStore = isReturnableAtStore;
    return this;
  }

   /**
   * Indicates if item is returnable at store locations
   * @return isReturnableAtStore
  **/
  public Boolean getIsReturnableAtStore() {
    return isReturnableAtStore;
  }

  public void setIsReturnableAtStore(Boolean isReturnableAtStore) {
    this.isReturnableAtStore = isReturnableAtStore;
  }

  public SellingAttributes isTaxExemptable(Boolean isTaxExemptable) {
    this.isTaxExemptable = isTaxExemptable;
    return this;
  }

   /**
   * Indicates if tax can be exempted for the item
   * @return isTaxExemptable
  **/
  public Boolean getIsTaxExemptable() {
    return isTaxExemptable;
  }

  public void setIsTaxExemptable(Boolean isTaxExemptable) {
    this.isTaxExemptable = isTaxExemptable;
  }

  public SellingAttributes isTaxOverrideable(Boolean isTaxOverrideable) {
    this.isTaxOverrideable = isTaxOverrideable;
    return this;
  }

   /**
   * Not active for transaction level override. Indicates if tax can be overridden for the item
   * @return isTaxOverrideable
  **/
  public Boolean getIsTaxOverrideable() {
    return isTaxOverrideable;
  }

  public void setIsTaxOverrideable(Boolean isTaxOverrideable) {
    this.isTaxOverrideable = isTaxOverrideable;
  }

  public SellingAttributes isTaxable(Boolean isTaxable) {
    this.isTaxable = isTaxable;
    return this;
  }

   /**
   * Indicates if item is taxable. Not used for any taxability calculations for order lines. Can be used for custom calculations via user exit.
   * @return isTaxable
  **/
  public Boolean getIsTaxable() {
    return isTaxable;
  }

  public void setIsTaxable(Boolean isTaxable) {
    this.isTaxable = isTaxable;
  }

  public SellingAttributes localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public SellingAttributes maxDiscount(BigDecimal maxDiscount) {
    this.maxDiscount = maxDiscount;
    return this;
  }

   /**
   * Indicates max amount for item
   * minimum: 0
   * maximum: 99999999999999.98
   * @return maxDiscount
  **/
  public BigDecimal getMaxDiscount() {
    return maxDiscount;
  }

  public void setMaxDiscount(BigDecimal maxDiscount) {
    this.maxDiscount = maxDiscount;
  }

  public SellingAttributes maxDiscountAmount(BigDecimal maxDiscountAmount) {
    this.maxDiscountAmount = maxDiscountAmount;
    return this;
  }

   /**
   * Not active. Indicates max amount of discount for item
   * minimum: 0
   * maximum: 99999999999999.98
   * @return maxDiscountAmount
  **/
  public BigDecimal getMaxDiscountAmount() {
    return maxDiscountAmount;
  }

  public void setMaxDiscountAmount(BigDecimal maxDiscountAmount) {
    this.maxDiscountAmount = maxDiscountAmount;
  }

  public SellingAttributes maxDiscountPercentage(BigDecimal maxDiscountPercentage) {
    this.maxDiscountPercentage = maxDiscountPercentage;
    return this;
  }

   /**
   * Indicates max percentage for item
   * minimum: 0
   * maximum: 99999999999999.98
   * @return maxDiscountPercentage
  **/
  public BigDecimal getMaxDiscountPercentage() {
    return maxDiscountPercentage;
  }

  public void setMaxDiscountPercentage(BigDecimal maxDiscountPercentage) {
    this.maxDiscountPercentage = maxDiscountPercentage;
  }

  public SellingAttributes messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public SellingAttributes PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public SellingAttributes pickUpInStore(Boolean pickUpInStore) {
    this.pickUpInStore = pickUpInStore;
    return this;
  }

   /**
   * Indicates if item can be picked up in a store
   * @return pickUpInStore
  **/
  public Boolean getPickUpInStore() {
    return pickUpInStore;
  }

  public void setPickUpInStore(Boolean pickUpInStore) {
    this.pickUpInStore = pickUpInStore;
  }

  public SellingAttributes priceStatusId(String priceStatusId) {
    this.priceStatusId = priceStatusId;
    return this;
  }

   /**
   * Price status of the item
   * @return priceStatusId
  **/
  public String getPriceStatusId() {
    return priceStatusId;
  }

  public void setPriceStatusId(String priceStatusId) {
    this.priceStatusId = priceStatusId;
  }

  public SellingAttributes profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public SellingAttributes sellDate(LocalDate sellDate) {
    this.sellDate = sellDate;
    return this;
  }

   /**
   * Date after which item can be sold (street date)
   * @return sellDate
  **/
  public LocalDate getSellDate() {
    return sellDate;
  }

  public void setSellDate(LocalDate sellDate) {
    this.sellDate = sellDate;
  }

  public SellingAttributes sellThrough(Double sellThrough) {
    this.sellThrough = sellThrough;
    return this;
  }

   /**
   * Sell through level of the item
   * minimum: 0
   * maximum: 999999999999.9999
   * @return sellThrough
  **/
  public Double getSellThrough() {
    return sellThrough;
  }

  public void setSellThrough(Double sellThrough) {
    this.sellThrough = sellThrough;
  }

  public SellingAttributes shipToAddress(Boolean shipToAddress) {
    this.shipToAddress = shipToAddress;
    return this;
  }

   /**
   * Indicates if item can be shipped to an address
   * @return shipToAddress
  **/
  public Boolean getShipToAddress() {
    return shipToAddress;
  }

  public void setShipToAddress(Boolean shipToAddress) {
    this.shipToAddress = shipToAddress;
  }

  public SellingAttributes shipToStore(Boolean shipToStore) {
    this.shipToStore = shipToStore;
    return this;
  }

   /**
   * Indicates if item can be shipped to a store for customer pick up
   * @return shipToStore
  **/
  public Boolean getShipToStore() {
    return shipToStore;
  }

  public void setShipToStore(Boolean shipToStore) {
    this.shipToStore = shipToStore;
  }

  public SellingAttributes soldInStores(Boolean soldInStores) {
    this.soldInStores = soldInStores;
    return this;
  }

   /**
   * Not active. Indicates if item is sold in stores
   * @return soldInStores
  **/
  public Boolean getSoldInStores() {
    return soldInStores;
  }

  public void setSoldInStores(Boolean soldInStores) {
    this.soldInStores = soldInStores;
  }

  public SellingAttributes soldOnline(Boolean soldOnline) {
    this.soldOnline = soldOnline;
    return this;
  }

   /**
   * Indicates if item is sold online
   * @return soldOnline
  **/
  public Boolean getSoldOnline() {
    return soldOnline;
  }

  public void setSoldOnline(Boolean soldOnline) {
    this.soldOnline = soldOnline;
  }

  public SellingAttributes taxCode(String taxCode) {
    this.taxCode = taxCode;
    return this;
  }

   /**
   * Tax code of item
   * @return taxCode
  **/
  public String getTaxCode() {
    return taxCode;
  }

  public void setTaxCode(String taxCode) {
    this.taxCode = taxCode;
  }

  public SellingAttributes updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public SellingAttributes updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public SellingAttributes valueEntryRequired(Boolean valueEntryRequired) {
    this.valueEntryRequired = valueEntryRequired;
    return this;
  }

   /**
   * Indicates if item requires an activation value to be entered (e.g. variable value gift cards)
   * @return valueEntryRequired
  **/
  public Boolean getValueEntryRequired() {
    return valueEntryRequired;
  }

  public void setValueEntryRequired(Boolean valueEntryRequired) {
    this.valueEntryRequired = valueEntryRequired;
  }

  public SellingAttributes entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public SellingAttributes localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SellingAttributes sellingAttributes = (SellingAttributes) o;
    return Objects.equals(this.actions, sellingAttributes.actions) &&
        Objects.equals(this.activationRequired, sellingAttributes.activationRequired) &&
        Objects.equals(this.availableToSellDateTime, sellingAttributes.availableToSellDateTime) &&
        Objects.equals(this.createdBy, sellingAttributes.createdBy) &&
        Objects.equals(this.createdTimestamp, sellingAttributes.createdTimestamp) &&
        Objects.equals(this.digitalGoods, sellingAttributes.digitalGoods) &&
        Objects.equals(this.dispositionId, sellingAttributes.dispositionId) &&
        Objects.equals(this.extended, sellingAttributes.extended) &&
        Objects.equals(this.isDiscountable, sellingAttributes.isDiscountable) &&
        Objects.equals(this.isExchangeable, sellingAttributes.isExchangeable) &&
        Objects.equals(this.isPriceOverrideable, sellingAttributes.isPriceOverrideable) &&
        Objects.equals(this.isReturnableAtDC, sellingAttributes.isReturnableAtDC) &&
        Objects.equals(this.isReturnableAtStore, sellingAttributes.isReturnableAtStore) &&
        Objects.equals(this.isTaxExemptable, sellingAttributes.isTaxExemptable) &&
        Objects.equals(this.isTaxOverrideable, sellingAttributes.isTaxOverrideable) &&
        Objects.equals(this.isTaxable, sellingAttributes.isTaxable) &&
        Objects.equals(this.localizedTo, sellingAttributes.localizedTo) &&
        Objects.equals(this.maxDiscount, sellingAttributes.maxDiscount) &&
        Objects.equals(this.maxDiscountAmount, sellingAttributes.maxDiscountAmount) &&
        Objects.equals(this.maxDiscountPercentage, sellingAttributes.maxDiscountPercentage) &&
        Objects.equals(this.messages, sellingAttributes.messages) &&
        Objects.equals(this.PK, sellingAttributes.PK) &&
        Objects.equals(this.pickUpInStore, sellingAttributes.pickUpInStore) &&
        Objects.equals(this.priceStatusId, sellingAttributes.priceStatusId) &&
        Objects.equals(this.profileId, sellingAttributes.profileId) &&
        Objects.equals(this.sellDate, sellingAttributes.sellDate) &&
        Objects.equals(this.sellThrough, sellingAttributes.sellThrough) &&
        Objects.equals(this.shipToAddress, sellingAttributes.shipToAddress) &&
        Objects.equals(this.shipToStore, sellingAttributes.shipToStore) &&
        Objects.equals(this.soldInStores, sellingAttributes.soldInStores) &&
        Objects.equals(this.soldOnline, sellingAttributes.soldOnline) &&
        Objects.equals(this.taxCode, sellingAttributes.taxCode) &&
        Objects.equals(this.updatedBy, sellingAttributes.updatedBy) &&
        Objects.equals(this.updatedTimestamp, sellingAttributes.updatedTimestamp) &&
        Objects.equals(this.valueEntryRequired, sellingAttributes.valueEntryRequired) &&
        Objects.equals(this.entityName, sellingAttributes.entityName) &&
        Objects.equals(this.localize, sellingAttributes.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, activationRequired, availableToSellDateTime, createdBy, createdTimestamp, digitalGoods, dispositionId, extended, isDiscountable, isExchangeable, isPriceOverrideable, isReturnableAtDC, isReturnableAtStore, isTaxExemptable, isTaxOverrideable, isTaxable, localizedTo, maxDiscount, maxDiscountAmount, maxDiscountPercentage, messages, PK, pickUpInStore, priceStatusId, profileId, sellDate, sellThrough, shipToAddress, shipToStore, soldInStores, soldOnline, taxCode, updatedBy, updatedTimestamp, valueEntryRequired, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SellingAttributes {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    activationRequired: ").append(toIndentedString(activationRequired)).append("\n");
    sb.append("    availableToSellDateTime: ").append(toIndentedString(availableToSellDateTime)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    digitalGoods: ").append(toIndentedString(digitalGoods)).append("\n");
    sb.append("    dispositionId: ").append(toIndentedString(dispositionId)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    isDiscountable: ").append(toIndentedString(isDiscountable)).append("\n");
    sb.append("    isExchangeable: ").append(toIndentedString(isExchangeable)).append("\n");
    sb.append("    isPriceOverrideable: ").append(toIndentedString(isPriceOverrideable)).append("\n");
    sb.append("    isReturnableAtDC: ").append(toIndentedString(isReturnableAtDC)).append("\n");
    sb.append("    isReturnableAtStore: ").append(toIndentedString(isReturnableAtStore)).append("\n");
    sb.append("    isTaxExemptable: ").append(toIndentedString(isTaxExemptable)).append("\n");
    sb.append("    isTaxOverrideable: ").append(toIndentedString(isTaxOverrideable)).append("\n");
    sb.append("    isTaxable: ").append(toIndentedString(isTaxable)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    maxDiscount: ").append(toIndentedString(maxDiscount)).append("\n");
    sb.append("    maxDiscountAmount: ").append(toIndentedString(maxDiscountAmount)).append("\n");
    sb.append("    maxDiscountPercentage: ").append(toIndentedString(maxDiscountPercentage)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    pickUpInStore: ").append(toIndentedString(pickUpInStore)).append("\n");
    sb.append("    priceStatusId: ").append(toIndentedString(priceStatusId)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    sellDate: ").append(toIndentedString(sellDate)).append("\n");
    sb.append("    sellThrough: ").append(toIndentedString(sellThrough)).append("\n");
    sb.append("    shipToAddress: ").append(toIndentedString(shipToAddress)).append("\n");
    sb.append("    shipToStore: ").append(toIndentedString(shipToStore)).append("\n");
    sb.append("    soldInStores: ").append(toIndentedString(soldInStores)).append("\n");
    sb.append("    soldOnline: ").append(toIndentedString(soldOnline)).append("\n");
    sb.append("    taxCode: ").append(toIndentedString(taxCode)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    valueEntryRequired: ").append(toIndentedString(valueEntryRequired)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

