/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * SuggestedPromo
 */
public class SuggestedPromo {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_BANNER_U_R_L = "BannerURL";
  @SerializedName(SERIALIZED_NAME_BANNER_U_R_L)
  private String bannerURL;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EFFECTIVE_END_DATE = "EffectiveEndDate";
  @SerializedName(SERIALIZED_NAME_EFFECTIVE_END_DATE)
  private OffsetDateTime effectiveEndDate;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROMO_ID = "PromoId";
  @SerializedName(SERIALIZED_NAME_PROMO_ID)
  private String promoId;

  public static final String SERIALIZED_NAME_PROMPT_TEXT = "PromptText";
  @SerializedName(SERIALIZED_NAME_PROMPT_TEXT)
  private String promptText;

  public static final String SERIALIZED_NAME_SELLING_CHANNEL = "SellingChannel";
  @SerializedName(SERIALIZED_NAME_SELLING_CHANNEL)
  private String sellingChannel;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public SuggestedPromo actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public SuggestedPromo putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public SuggestedPromo bannerURL(String bannerURL) {
    this.bannerURL = bannerURL;
    return this;
  }

   /**
   * URL pointing to the promotion banner image.
   * @return bannerURL
  **/
  
  public String getBannerURL() {
    return bannerURL;
  }

  public void setBannerURL(String bannerURL) {
    this.bannerURL = bannerURL;
  }

  public SuggestedPromo createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public SuggestedPromo createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public SuggestedPromo effectiveEndDate(OffsetDateTime effectiveEndDate) {
    this.effectiveEndDate = effectiveEndDate;
    return this;
  }

   /**
   * Date after which this Promotion will expire
   * @return effectiveEndDate
  **/
  
  public OffsetDateTime getEffectiveEndDate() {
    return effectiveEndDate;
  }

  public void setEffectiveEndDate(OffsetDateTime effectiveEndDate) {
    this.effectiveEndDate = effectiveEndDate;
  }

  public SuggestedPromo extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public SuggestedPromo localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public SuggestedPromo messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public SuggestedPromo orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public SuggestedPromo PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public SuggestedPromo promoId(String promoId) {
    this.promoId = promoId;
    return this;
  }

   /**
   * Unique name for the promotion within a profile.
   * @return promoId
  **/
  
  public String getPromoId() {
    return promoId;
  }

  public void setPromoId(String promoId) {
    this.promoId = promoId;
  }

  public SuggestedPromo promptText(String promptText) {
    this.promptText = promptText;
    return this;
  }

   /**
   * Text to be displayed while prompting the deal as a suggested one.
   * @return promptText
  **/
  
  public String getPromptText() {
    return promptText;
  }

  public void setPromptText(String promptText) {
    this.promptText = promptText;
  }

  public SuggestedPromo sellingChannel(String sellingChannel) {
    this.sellingChannel = sellingChannel;
    return this;
  }

   /**
   * Selling Channel to which the prompt text and bannerURL are applicable - Store or Web or Call center
   * @return sellingChannel
  **/
  
  public String getSellingChannel() {
    return sellingChannel;
  }

  public void setSellingChannel(String sellingChannel) {
    this.sellingChannel = sellingChannel;
  }

  public SuggestedPromo updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public SuggestedPromo updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public SuggestedPromo entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public SuggestedPromo localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SuggestedPromo suggestedPromo = (SuggestedPromo) o;
    return Objects.equals(this.actions, suggestedPromo.actions) &&
        Objects.equals(this.bannerURL, suggestedPromo.bannerURL) &&
        Objects.equals(this.createdBy, suggestedPromo.createdBy) &&
        Objects.equals(this.createdTimestamp, suggestedPromo.createdTimestamp) &&
        Objects.equals(this.effectiveEndDate, suggestedPromo.effectiveEndDate) &&
        Objects.equals(this.extended, suggestedPromo.extended) &&
        Objects.equals(this.localizedTo, suggestedPromo.localizedTo) &&
        Objects.equals(this.messages, suggestedPromo.messages) &&
        Objects.equals(this.orgId, suggestedPromo.orgId) &&
        Objects.equals(this.PK, suggestedPromo.PK) &&
        Objects.equals(this.promoId, suggestedPromo.promoId) &&
        Objects.equals(this.promptText, suggestedPromo.promptText) &&
        Objects.equals(this.sellingChannel, suggestedPromo.sellingChannel) &&
        Objects.equals(this.updatedBy, suggestedPromo.updatedBy) &&
        Objects.equals(this.updatedTimestamp, suggestedPromo.updatedTimestamp) &&
        Objects.equals(this.entityName, suggestedPromo.entityName) &&
        Objects.equals(this.localize, suggestedPromo.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, bannerURL, createdBy, createdTimestamp, effectiveEndDate, extended, localizedTo, messages, orgId, PK, promoId, promptText, sellingChannel, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SuggestedPromo {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    bannerURL: ").append(toIndentedString(bannerURL)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    effectiveEndDate: ").append(toIndentedString(effectiveEndDate)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    promoId: ").append(toIndentedString(promoId)).append("\n");
    sb.append("    promptText: ").append(toIndentedString(promptText)).append("\n");
    sb.append("    sellingChannel: ").append(toIndentedString(sellingChannel)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

