/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.*;

/**
 * ModTypeConfig
 */
public class ModTypeConfig {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CALCULATE_EFFECTIVE_RANK = "CalculateEffectiveRank";
  @SerializedName(SERIALIZED_NAME_CALCULATE_EFFECTIVE_RANK)
  private Boolean calculateEffectiveRank;

  public static final String SERIALIZED_NAME_CALCULATE_PRICE = "CalculatePrice";
  @SerializedName(SERIALIZED_NAME_CALCULATE_PRICE)
  private Boolean calculatePrice;

  public static final String SERIALIZED_NAME_CALCULATE_RETURN_FEE = "CalculateReturnFee";
  @SerializedName(SERIALIZED_NAME_CALCULATE_RETURN_FEE)
  private Boolean calculateReturnFee;

  public static final String SERIALIZED_NAME_CALCULATE_SNH = "CalculateSnh";
  @SerializedName(SERIALIZED_NAME_CALCULATE_SNH)
  private Boolean calculateSnh;

  public static final String SERIALIZED_NAME_CALCULATE_TAX = "CalculateTax";
  @SerializedName(SERIALIZED_NAME_CALCULATE_TAX)
  private Boolean calculateTax;

  public static final String SERIALIZED_NAME_CANCEL_RESERVATION = "CancelReservation";
  @SerializedName(SERIALIZED_NAME_CANCEL_RESERVATION)
  private Boolean cancelReservation;

  public static final String SERIALIZED_NAME_CHANGE_STATUS = "ChangeStatus";
  @SerializedName(SERIALIZED_NAME_CHANGE_STATUS)
  private String changeStatus;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_CUSTOM_SERVICE_CONFIG = "CustomServiceConfig";
  @SerializedName(SERIALIZED_NAME_CUSTOM_SERVICE_CONFIG)
  private List<CustomServiceConfig> customServiceConfig = null;

  public static final String SERIALIZED_NAME_EVALUATE_PROMOTION = "EvaluatePromotion";
  @SerializedName(SERIALIZED_NAME_EVALUATE_PROMOTION)
  private Boolean evaluatePromotion;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_MOD_TYPE = "ModType";
  @SerializedName(SERIALIZED_NAME_MOD_TYPE)
  private ModType modType = null;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_PUBLISH_ORDER = "PublishOrder";
  @SerializedName(SERIALIZED_NAME_PUBLISH_ORDER)
  private Boolean publishOrder;

  public static final String SERIALIZED_NAME_PUBLISH_ORDER_LINE = "PublishOrderLine";
  @SerializedName(SERIALIZED_NAME_PUBLISH_ORDER_LINE)
  private Boolean publishOrderLine;

  public static final String SERIALIZED_NAME_REEVALUATE_PIPELINE = "ReevaluatePipeline";
  @SerializedName(SERIALIZED_NAME_REEVALUATE_PIPELINE)
  private Boolean reevaluatePipeline;

  public static final String SERIALIZED_NAME_RESTRICT_FROM_RETURN_STATUS = "RestrictFromReturnStatus";
  @SerializedName(SERIALIZED_NAME_RESTRICT_FROM_RETURN_STATUS)
  private String restrictFromReturnStatus;

  public static final String SERIALIZED_NAME_RESTRICT_FROM_STATUS = "RestrictFromStatus";
  @SerializedName(SERIALIZED_NAME_RESTRICT_FROM_STATUS)
  private String restrictFromStatus;

  public static final String SERIALIZED_NAME_RESTRICT_POST_CONFIRMATION = "RestrictPostConfirmation";
  @SerializedName(SERIALIZED_NAME_RESTRICT_POST_CONFIRMATION)
  private Boolean restrictPostConfirmation;

  public static final String SERIALIZED_NAME_RESUBMIT_PIPELINE = "ResubmitPipeline";
  @SerializedName(SERIALIZED_NAME_RESUBMIT_PIPELINE)
  private Boolean resubmitPipeline;

  public static final String SERIALIZED_NAME_STATUS = "Status";
  @SerializedName(SERIALIZED_NAME_STATUS)
  private String status;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ROOT_CAUSE = "rootCause";
  @SerializedName(SERIALIZED_NAME_ROOT_CAUSE)
  private String rootCause;

  public ModTypeConfig actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public ModTypeConfig putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public ModTypeConfig calculateEffectiveRank(Boolean calculateEffectiveRank) {
    this.calculateEffectiveRank = calculateEffectiveRank;
    return this;
  }

   /**
   * Flag to indicate  whether to calculate effective rank or not
   * @return calculateEffectiveRank
  **/
  
  public Boolean getCalculateEffectiveRank() {
    return calculateEffectiveRank;
  }

  public void setCalculateEffectiveRank(Boolean calculateEffectiveRank) {
    this.calculateEffectiveRank = calculateEffectiveRank;
  }

  public ModTypeConfig calculatePrice(Boolean calculatePrice) {
    this.calculatePrice = calculatePrice;
    return this;
  }

   /**
   * Flag to indicate that item pricing service will be called for specific modofication type 
   * @return calculatePrice
  **/
  
  public Boolean getCalculatePrice() {
    return calculatePrice;
  }

  public void setCalculatePrice(Boolean calculatePrice) {
    this.calculatePrice = calculatePrice;
  }

  public ModTypeConfig calculateReturnFee(Boolean calculateReturnFee) {
    this.calculateReturnFee = calculateReturnFee;
    return this;
  }

   /**
   * Flag to indicate  whether to calculate return fee or not
   * @return calculateReturnFee
  **/
  
  public Boolean getCalculateReturnFee() {
    return calculateReturnFee;
  }

  public void setCalculateReturnFee(Boolean calculateReturnFee) {
    this.calculateReturnFee = calculateReturnFee;
  }

  public ModTypeConfig calculateSnh(Boolean calculateSnh) {
    this.calculateSnh = calculateSnh;
    return this;
  }

   /**
   * Flag to indicate  SnH service will be enabled for specific modification type 
   * @return calculateSnh
  **/
  
  public Boolean getCalculateSnh() {
    return calculateSnh;
  }

  public void setCalculateSnh(Boolean calculateSnh) {
    this.calculateSnh = calculateSnh;
  }

  public ModTypeConfig calculateTax(Boolean calculateTax) {
    this.calculateTax = calculateTax;
    return this;
  }

   /**
   * Flag to indicate  Tax serivice will be enabled for specific modification type 
   * @return calculateTax
  **/
  
  public Boolean getCalculateTax() {
    return calculateTax;
  }

  public void setCalculateTax(Boolean calculateTax) {
    this.calculateTax = calculateTax;
  }

  public ModTypeConfig cancelReservation(Boolean cancelReservation) {
    this.cancelReservation = cancelReservation;
    return this;
  }

   /**
   * Flag to indicate whether to cancel reservation of allocated inventories for the specified modification type
   * @return cancelReservation
  **/
  
  public Boolean getCancelReservation() {
    return cancelReservation;
  }

  public void setCancelReservation(Boolean cancelReservation) {
    this.cancelReservation = cancelReservation;
  }

  public ModTypeConfig changeStatus(String changeStatus) {
    this.changeStatus = changeStatus;
    return this;
  }

   /**
   * Status the line will be changed to when modification happens line level
   * @return changeStatus
  **/
  
  public String getChangeStatus() {
    return changeStatus;
  }

  public void setChangeStatus(String changeStatus) {
    this.changeStatus = changeStatus;
  }

  public ModTypeConfig createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public ModTypeConfig createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public ModTypeConfig customServiceConfig(List<CustomServiceConfig> customServiceConfig) {
    this.customServiceConfig = customServiceConfig;
    return this;
  }

  public ModTypeConfig addCustomServiceConfigItem(CustomServiceConfig customServiceConfigItem) {
    if (this.customServiceConfig == null) {
      this.customServiceConfig = new ArrayList<CustomServiceConfig>();
    }
    this.customServiceConfig.add(customServiceConfigItem);
    return this;
  }

   /**
   * Get customServiceConfig
   * @return customServiceConfig
  **/
  
  public List<CustomServiceConfig> getCustomServiceConfig() {
    return customServiceConfig;
  }

  public void setCustomServiceConfig(List<CustomServiceConfig> customServiceConfig) {
    this.customServiceConfig = customServiceConfig;
  }

  public ModTypeConfig evaluatePromotion(Boolean evaluatePromotion) {
    this.evaluatePromotion = evaluatePromotion;
    return this;
  }

   /**
   * Flag to indicate that item pricing service will be called for specific modofication type 
   * @return evaluatePromotion
  **/
  
  public Boolean getEvaluatePromotion() {
    return evaluatePromotion;
  }

  public void setEvaluatePromotion(Boolean evaluatePromotion) {
    this.evaluatePromotion = evaluatePromotion;
  }

  public ModTypeConfig extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public ModTypeConfig localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public ModTypeConfig messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public ModTypeConfig modType(ModType modType) {
    this.modType = modType;
    return this;
  }

   /**
   * Get modType
   * @return modType
  **/
  
  public ModType getModType() {
    return modType;
  }

  public void setModType(ModType modType) {
    this.modType = modType;
  }

  public ModTypeConfig PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public ModTypeConfig profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

   /**
   * Profile Id
   * @return profileId
  **/
  
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public ModTypeConfig publishOrder(Boolean publishOrder) {
    this.publishOrder = publishOrder;
    return this;
  }

   /**
   * Flag to indicate  publish Order serivice will be enabled for specific modification type 
   * @return publishOrder
  **/
  
  public Boolean getPublishOrder() {
    return publishOrder;
  }

  public void setPublishOrder(Boolean publishOrder) {
    this.publishOrder = publishOrder;
  }

  public ModTypeConfig publishOrderLine(Boolean publishOrderLine) {
    this.publishOrderLine = publishOrderLine;
    return this;
  }

   /**
   * Flag to indicate  publish OrderLine serivice will be enabled for specific modification type 
   * @return publishOrderLine
  **/
  
  public Boolean getPublishOrderLine() {
    return publishOrderLine;
  }

  public void setPublishOrderLine(Boolean publishOrderLine) {
    this.publishOrderLine = publishOrderLine;
  }

  public ModTypeConfig reevaluatePipeline(Boolean reevaluatePipeline) {
    this.reevaluatePipeline = reevaluatePipeline;
    return this;
  }

   /**
   * Flag to indicate whether to re-evaluate pipeline Id for the specified modification type
   * @return reevaluatePipeline
  **/
  
  public Boolean getReevaluatePipeline() {
    return reevaluatePipeline;
  }

  public void setReevaluatePipeline(Boolean reevaluatePipeline) {
    this.reevaluatePipeline = reevaluatePipeline;
  }

  public ModTypeConfig restrictFromReturnStatus(String restrictFromReturnStatus) {
    this.restrictFromReturnStatus = restrictFromReturnStatus;
    return this;
  }

   /**
   * Maximum status that is allowed for this modType is lessthan restrictFromReturnStatus.This property is applicable only for Return ModTypes
   * @return restrictFromReturnStatus
  **/
  
  public String getRestrictFromReturnStatus() {
    return restrictFromReturnStatus;
  }

  public void setRestrictFromReturnStatus(String restrictFromReturnStatus) {
    this.restrictFromReturnStatus = restrictFromReturnStatus;
  }

  public ModTypeConfig restrictFromStatus(String restrictFromStatus) {
    this.restrictFromStatus = restrictFromStatus;
    return this;
  }

   /**
   * Maximum status that is allowed for this modType is lessthan restrictFromStatus
   * @return restrictFromStatus
  **/
  
  public String getRestrictFromStatus() {
    return restrictFromStatus;
  }

  public void setRestrictFromStatus(String restrictFromStatus) {
    this.restrictFromStatus = restrictFromStatus;
  }

  public ModTypeConfig restrictPostConfirmation(Boolean restrictPostConfirmation) {
    this.restrictPostConfirmation = restrictPostConfirmation;
    return this;
  }

   /**
   * Flag to indicate wether current modification is allowed once order is confirmed
   * @return restrictPostConfirmation
  **/
  
  public Boolean getRestrictPostConfirmation() {
    return restrictPostConfirmation;
  }

  public void setRestrictPostConfirmation(Boolean restrictPostConfirmation) {
    this.restrictPostConfirmation = restrictPostConfirmation;
  }

  public ModTypeConfig resubmitPipeline(Boolean resubmitPipeline) {
    this.resubmitPipeline = resubmitPipeline;
    return this;
  }

   /**
   * Flag to indicate whether to re-submit the line to pipeline for the specified modification type
   * @return resubmitPipeline
  **/
  
  public Boolean getResubmitPipeline() {
    return resubmitPipeline;
  }

  public void setResubmitPipeline(Boolean resubmitPipeline) {
    this.resubmitPipeline = resubmitPipeline;
  }

  public ModTypeConfig status(String status) {
    this.status = status;
    return this;
  }

   /**
   * Maximum status that is allowed for this modType is lessthan restrictFromStatus
   * @return status
  **/
  
  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }

  public ModTypeConfig updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public ModTypeConfig updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public ModTypeConfig rootCause(String rootCause) {
    this.rootCause = rootCause;
    return this;
  }

   /**
   * Get rootCause
   * @return rootCause
  **/
  
  public String getRootCause() {
    return rootCause;
  }

  public void setRootCause(String rootCause) {
    this.rootCause = rootCause;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ModTypeConfig modTypeConfig = (ModTypeConfig) o;
    return Objects.equals(this.actions, modTypeConfig.actions) &&
        Objects.equals(this.calculateEffectiveRank, modTypeConfig.calculateEffectiveRank) &&
        Objects.equals(this.calculatePrice, modTypeConfig.calculatePrice) &&
        Objects.equals(this.calculateReturnFee, modTypeConfig.calculateReturnFee) &&
        Objects.equals(this.calculateSnh, modTypeConfig.calculateSnh) &&
        Objects.equals(this.calculateTax, modTypeConfig.calculateTax) &&
        Objects.equals(this.cancelReservation, modTypeConfig.cancelReservation) &&
        Objects.equals(this.changeStatus, modTypeConfig.changeStatus) &&
        Objects.equals(this.createdBy, modTypeConfig.createdBy) &&
        Objects.equals(this.createdTimestamp, modTypeConfig.createdTimestamp) &&
        Objects.equals(this.customServiceConfig, modTypeConfig.customServiceConfig) &&
        Objects.equals(this.evaluatePromotion, modTypeConfig.evaluatePromotion) &&
        Objects.equals(this.extended, modTypeConfig.extended) &&
        Objects.equals(this.localizedTo, modTypeConfig.localizedTo) &&
        Objects.equals(this.messages, modTypeConfig.messages) &&
        Objects.equals(this.modType, modTypeConfig.modType) &&
        Objects.equals(this.PK, modTypeConfig.PK) &&
        Objects.equals(this.profileId, modTypeConfig.profileId) &&
        Objects.equals(this.publishOrder, modTypeConfig.publishOrder) &&
        Objects.equals(this.publishOrderLine, modTypeConfig.publishOrderLine) &&
        Objects.equals(this.reevaluatePipeline, modTypeConfig.reevaluatePipeline) &&
        Objects.equals(this.restrictFromReturnStatus, modTypeConfig.restrictFromReturnStatus) &&
        Objects.equals(this.restrictFromStatus, modTypeConfig.restrictFromStatus) &&
        Objects.equals(this.restrictPostConfirmation, modTypeConfig.restrictPostConfirmation) &&
        Objects.equals(this.resubmitPipeline, modTypeConfig.resubmitPipeline) &&
        Objects.equals(this.status, modTypeConfig.status) &&
        Objects.equals(this.updatedBy, modTypeConfig.updatedBy) &&
        Objects.equals(this.updatedTimestamp, modTypeConfig.updatedTimestamp) &&
        Objects.equals(this.rootCause, modTypeConfig.rootCause);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, calculateEffectiveRank, calculatePrice, calculateReturnFee, calculateSnh, calculateTax, cancelReservation, changeStatus, createdBy, createdTimestamp, customServiceConfig, evaluatePromotion, extended, localizedTo, messages, modType, PK, profileId, publishOrder, publishOrderLine, reevaluatePipeline, restrictFromReturnStatus, restrictFromStatus, restrictPostConfirmation, resubmitPipeline, status, updatedBy, updatedTimestamp, rootCause);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ModTypeConfig {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    calculateEffectiveRank: ").append(toIndentedString(calculateEffectiveRank)).append("\n");
    sb.append("    calculatePrice: ").append(toIndentedString(calculatePrice)).append("\n");
    sb.append("    calculateReturnFee: ").append(toIndentedString(calculateReturnFee)).append("\n");
    sb.append("    calculateSnh: ").append(toIndentedString(calculateSnh)).append("\n");
    sb.append("    calculateTax: ").append(toIndentedString(calculateTax)).append("\n");
    sb.append("    cancelReservation: ").append(toIndentedString(cancelReservation)).append("\n");
    sb.append("    changeStatus: ").append(toIndentedString(changeStatus)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    customServiceConfig: ").append(toIndentedString(customServiceConfig)).append("\n");
    sb.append("    evaluatePromotion: ").append(toIndentedString(evaluatePromotion)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    modType: ").append(toIndentedString(modType)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    publishOrder: ").append(toIndentedString(publishOrder)).append("\n");
    sb.append("    publishOrderLine: ").append(toIndentedString(publishOrderLine)).append("\n");
    sb.append("    reevaluatePipeline: ").append(toIndentedString(reevaluatePipeline)).append("\n");
    sb.append("    restrictFromReturnStatus: ").append(toIndentedString(restrictFromReturnStatus)).append("\n");
    sb.append("    restrictFromStatus: ").append(toIndentedString(restrictFromStatus)).append("\n");
    sb.append("    restrictPostConfirmation: ").append(toIndentedString(restrictPostConfirmation)).append("\n");
    sb.append("    resubmitPipeline: ").append(toIndentedString(resubmitPipeline)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    rootCause: ").append(toIndentedString(rootCause)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

