/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * PaymentTransactionEMVTags
 */
public class PaymentTransactionEMVTags {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CARD_ENTRY_MODE = "CardEntryMode";
  @SerializedName(SERIALIZED_NAME_CARD_ENTRY_MODE)
  private String cardEntryMode;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EMV4_F = "Emv4F";
  @SerializedName(SERIALIZED_NAME_EMV4_F)
  private String emv4F;

  public static final String SERIALIZED_NAME_EMV50 = "Emv50";
  @SerializedName(SERIALIZED_NAME_EMV50)
  private String emv50;

  public static final String SERIALIZED_NAME_EMV5_F2_A = "Emv5F2A";
  @SerializedName(SERIALIZED_NAME_EMV5_F2_A)
  private String emv5F2A;

  public static final String SERIALIZED_NAME_EMV5_F34 = "Emv5F34";
  @SerializedName(SERIALIZED_NAME_EMV5_F34)
  private String emv5F34;

  public static final String SERIALIZED_NAME_EMV82 = "Emv82";
  @SerializedName(SERIALIZED_NAME_EMV82)
  private String emv82;

  public static final String SERIALIZED_NAME_EMV84 = "Emv84";
  @SerializedName(SERIALIZED_NAME_EMV84)
  private String emv84;

  public static final String SERIALIZED_NAME_EMV8_A = "Emv8A";
  @SerializedName(SERIALIZED_NAME_EMV8_A)
  private String emv8A;

  public static final String SERIALIZED_NAME_EMV95 = "Emv95";
  @SerializedName(SERIALIZED_NAME_EMV95)
  private String emv95;

  public static final String SERIALIZED_NAME_EMV9_A = "Emv9A";
  @SerializedName(SERIALIZED_NAME_EMV9_A)
  private String emv9A;

  public static final String SERIALIZED_NAME_EMV9_B = "Emv9B";
  @SerializedName(SERIALIZED_NAME_EMV9_B)
  private String emv9B;

  public static final String SERIALIZED_NAME_EMV9_C = "Emv9C";
  @SerializedName(SERIALIZED_NAME_EMV9_C)
  private String emv9C;

  public static final String SERIALIZED_NAME_EMV9_F02 = "Emv9F02";
  @SerializedName(SERIALIZED_NAME_EMV9_F02)
  private String emv9F02;

  public static final String SERIALIZED_NAME_EMV9_F03 = "Emv9F03";
  @SerializedName(SERIALIZED_NAME_EMV9_F03)
  private String emv9F03;

  public static final String SERIALIZED_NAME_EMV9_F06 = "Emv9F06";
  @SerializedName(SERIALIZED_NAME_EMV9_F06)
  private String emv9F06;

  public static final String SERIALIZED_NAME_EMV9_F07 = "Emv9F07";
  @SerializedName(SERIALIZED_NAME_EMV9_F07)
  private String emv9F07;

  public static final String SERIALIZED_NAME_EMV9_F08 = "Emv9F08";
  @SerializedName(SERIALIZED_NAME_EMV9_F08)
  private String emv9F08;

  public static final String SERIALIZED_NAME_EMV9_F09 = "Emv9F09";
  @SerializedName(SERIALIZED_NAME_EMV9_F09)
  private String emv9F09;

  public static final String SERIALIZED_NAME_EMV9_F0_D = "Emv9F0D";
  @SerializedName(SERIALIZED_NAME_EMV9_F0_D)
  private String emv9F0D;

  public static final String SERIALIZED_NAME_EMV9_F0_E = "Emv9F0E";
  @SerializedName(SERIALIZED_NAME_EMV9_F0_E)
  private String emv9F0E;

  public static final String SERIALIZED_NAME_EMV9_F0_F = "Emv9F0F";
  @SerializedName(SERIALIZED_NAME_EMV9_F0_F)
  private String emv9F0F;

  public static final String SERIALIZED_NAME_EMV9_F10 = "Emv9F10";
  @SerializedName(SERIALIZED_NAME_EMV9_F10)
  private String emv9F10;

  public static final String SERIALIZED_NAME_EMV9_F11 = "Emv9F11";
  @SerializedName(SERIALIZED_NAME_EMV9_F11)
  private String emv9F11;

  public static final String SERIALIZED_NAME_EMV9_F12 = "Emv9F12";
  @SerializedName(SERIALIZED_NAME_EMV9_F12)
  private String emv9F12;

  public static final String SERIALIZED_NAME_EMV9_F1_A = "Emv9F1A";
  @SerializedName(SERIALIZED_NAME_EMV9_F1_A)
  private String emv9F1A;

  public static final String SERIALIZED_NAME_EMV9_F1_E = "Emv9F1E";
  @SerializedName(SERIALIZED_NAME_EMV9_F1_E)
  private String emv9F1E;

  public static final String SERIALIZED_NAME_EMV9_F21 = "Emv9F21";
  @SerializedName(SERIALIZED_NAME_EMV9_F21)
  private String emv9F21;

  public static final String SERIALIZED_NAME_EMV9_F26 = "Emv9F26";
  @SerializedName(SERIALIZED_NAME_EMV9_F26)
  private String emv9F26;

  public static final String SERIALIZED_NAME_EMV9_F27 = "Emv9F27";
  @SerializedName(SERIALIZED_NAME_EMV9_F27)
  private String emv9F27;

  public static final String SERIALIZED_NAME_EMV9_F33 = "Emv9F33";
  @SerializedName(SERIALIZED_NAME_EMV9_F33)
  private String emv9F33;

  public static final String SERIALIZED_NAME_EMV9_F34 = "Emv9F34";
  @SerializedName(SERIALIZED_NAME_EMV9_F34)
  private String emv9F34;

  public static final String SERIALIZED_NAME_EMV9_F35 = "Emv9F35";
  @SerializedName(SERIALIZED_NAME_EMV9_F35)
  private String emv9F35;

  public static final String SERIALIZED_NAME_EMV9_F36 = "Emv9F36";
  @SerializedName(SERIALIZED_NAME_EMV9_F36)
  private String emv9F36;

  public static final String SERIALIZED_NAME_EMV9_F37 = "Emv9F37";
  @SerializedName(SERIALIZED_NAME_EMV9_F37)
  private String emv9F37;

  public static final String SERIALIZED_NAME_EMV9_F39 = "Emv9F39";
  @SerializedName(SERIALIZED_NAME_EMV9_F39)
  private String emv9F39;

  public static final String SERIALIZED_NAME_EMV9_F41 = "Emv9F41";
  @SerializedName(SERIALIZED_NAME_EMV9_F41)
  private String emv9F41;

  public static final String SERIALIZED_NAME_EMV_CHIP_INDICATOR = "EmvChipIndicator";
  @SerializedName(SERIALIZED_NAME_EMV_CHIP_INDICATOR)
  private String emvChipIndicator;

  public static final String SERIALIZED_NAME_EMV_CVM = "EmvCvm";
  @SerializedName(SERIALIZED_NAME_EMV_CVM)
  private String emvCvm;

  public static final String SERIALIZED_NAME_EMV_MODE = "EmvMode";
  @SerializedName(SERIALIZED_NAME_EMV_MODE)
  private String emvMode;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_ISSUER_SCRIPT_RESULTS = "IssuerScriptResults";
  @SerializedName(SERIALIZED_NAME_ISSUER_SCRIPT_RESULTS)
  private String issuerScriptResults;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MERCH_ID = "MerchId";
  @SerializedName(SERIALIZED_NAME_MERCH_ID)
  private String merchId;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_REF_NUMBER = "RefNumber";
  @SerializedName(SERIALIZED_NAME_REF_NUMBER)
  private String refNumber;

  public static final String SERIALIZED_NAME_TAC_DEFAULT = "TacDefault";
  @SerializedName(SERIALIZED_NAME_TAC_DEFAULT)
  private String tacDefault;

  public static final String SERIALIZED_NAME_TAC_DENIAL = "TacDenial";
  @SerializedName(SERIALIZED_NAME_TAC_DENIAL)
  private String tacDenial;

  public static final String SERIALIZED_NAME_TAC_ONLINE = "TacOnline";
  @SerializedName(SERIALIZED_NAME_TAC_ONLINE)
  private String tacOnline;

  public static final String SERIALIZED_NAME_TERM_ID = "TermId";
  @SerializedName(SERIALIZED_NAME_TERM_ID)
  private String termId;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_VALIDATION_CODE = "ValidationCode";
  @SerializedName(SERIALIZED_NAME_VALIDATION_CODE)
  private String validationCode;

  public static final String SERIALIZED_NAME_VISA_IDENTIFIER = "VisaIdentifier";
  @SerializedName(SERIALIZED_NAME_VISA_IDENTIFIER)
  private String visaIdentifier;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public PaymentTransactionEMVTags actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public PaymentTransactionEMVTags putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public PaymentTransactionEMVTags cardEntryMode(String cardEntryMode) {
    this.cardEntryMode = cardEntryMode;
    return this;
  }

   /**
   * Indicates the method used to enter the card. For example: Chip Read, Contactless, FSwipe (Fallback), Keyed, or Swiped (Insert not attempted).
   * @return cardEntryMode
  **/
  
  public String getCardEntryMode() {
    return cardEntryMode;
  }

  public void setCardEntryMode(String cardEntryMode) {
    this.cardEntryMode = cardEntryMode;
  }

  public PaymentTransactionEMVTags createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public PaymentTransactionEMVTags createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public PaymentTransactionEMVTags emv4F(String emv4F) {
    this.emv4F = emv4F;
    return this;
  }

   /**
   * Identifies the application as described in ISO/IEC 7816-5
   * @return emv4F
  **/
  
  public String getEmv4F() {
    return emv4F;
  }

  public void setEmv4F(String emv4F) {
    this.emv4F = emv4F;
  }

  public PaymentTransactionEMVTags emv50(String emv50) {
    this.emv50 = emv50;
    return this;
  }

   /**
   * Mnemonic associated with the AID according to ISO/IEC 7816-5
   * @return emv50
  **/
  
  public String getEmv50() {
    return emv50;
  }

  public void setEmv50(String emv50) {
    this.emv50 = emv50;
  }

  public PaymentTransactionEMVTags emv5F2A(String emv5F2A) {
    this.emv5F2A = emv5F2A;
    return this;
  }

   /**
   * Indicates the currency code of the transaction according to ISO 4217
   * @return emv5F2A
  **/
  
  public String getEmv5F2A() {
    return emv5F2A;
  }

  public void setEmv5F2A(String emv5F2A) {
    this.emv5F2A = emv5F2A;
  }

  public PaymentTransactionEMVTags emv5F34(String emv5F34) {
    this.emv5F34 = emv5F34;
    return this;
  }

   /**
   * Identifies and differentiates cards with the same PAN
   * @return emv5F34
  **/
  
  public String getEmv5F34() {
    return emv5F34;
  }

  public void setEmv5F34(String emv5F34) {
    this.emv5F34 = emv5F34;
  }

  public PaymentTransactionEMVTags emv82(String emv82) {
    this.emv82 = emv82;
    return this;
  }

   /**
   * Indicates the capabilities of the card to support specific functions in the application
   * @return emv82
  **/
  
  public String getEmv82() {
    return emv82;
  }

  public void setEmv82(String emv82) {
    this.emv82 = emv82;
  }

  public PaymentTransactionEMVTags emv84(String emv84) {
    this.emv84 = emv84;
    return this;
  }

   /**
   * Identifies the name of the DF as described in ISO/IEC 7816-4
   * @return emv84
  **/
  
  public String getEmv84() {
    return emv84;
  }

  public void setEmv84(String emv84) {
    this.emv84 = emv84;
  }

  public PaymentTransactionEMVTags emv8A(String emv8A) {
    this.emv8A = emv8A;
    return this;
  }

   /**
   * Code that defines the disposition of a message
   * @return emv8A
  **/
  
  public String getEmv8A() {
    return emv8A;
  }

  public void setEmv8A(String emv8A) {
    this.emv8A = emv8A;
  }

  public PaymentTransactionEMVTags emv95(String emv95) {
    this.emv95 = emv95;
    return this;
  }

   /**
   * Status of the different functions as seen from the terminal
   * @return emv95
  **/
  
  public String getEmv95() {
    return emv95;
  }

  public void setEmv95(String emv95) {
    this.emv95 = emv95;
  }

  public PaymentTransactionEMVTags emv9A(String emv9A) {
    this.emv9A = emv9A;
    return this;
  }

   /**
   * Local date that the transaction was authorised
   * @return emv9A
  **/
  
  public String getEmv9A() {
    return emv9A;
  }

  public void setEmv9A(String emv9A) {
    this.emv9A = emv9A;
  }

  public PaymentTransactionEMVTags emv9B(String emv9B) {
    this.emv9B = emv9B;
    return this;
  }

   /**
   * Indicates the functions performed in a transaction
   * @return emv9B
  **/
  
  public String getEmv9B() {
    return emv9B;
  }

  public void setEmv9B(String emv9B) {
    this.emv9B = emv9B;
  }

  public PaymentTransactionEMVTags emv9C(String emv9C) {
    this.emv9C = emv9C;
    return this;
  }

   /**
   * Indicates the type of financial transaction, represented by the first two digits of ISO 8583:1987 Processing Code
   * @return emv9C
  **/
  
  public String getEmv9C() {
    return emv9C;
  }

  public void setEmv9C(String emv9C) {
    this.emv9C = emv9C;
  }

  public PaymentTransactionEMVTags emv9F02(String emv9F02) {
    this.emv9F02 = emv9F02;
    return this;
  }

   /**
   * Authorised amount of the transaction (excluding adjustments)
   * @return emv9F02
  **/
  
  public String getEmv9F02() {
    return emv9F02;
  }

  public void setEmv9F02(String emv9F02) {
    this.emv9F02 = emv9F02;
  }

  public PaymentTransactionEMVTags emv9F03(String emv9F03) {
    this.emv9F03 = emv9F03;
    return this;
  }

   /**
   * Secondary amount associated with the transaction representing a cashback amount
   * @return emv9F03
  **/
  
  public String getEmv9F03() {
    return emv9F03;
  }

  public void setEmv9F03(String emv9F03) {
    this.emv9F03 = emv9F03;
  }

  public PaymentTransactionEMVTags emv9F06(String emv9F06) {
    this.emv9F06 = emv9F06;
    return this;
  }

   /**
   * Identifies the application as described in ISO/IEC 7816-5
   * @return emv9F06
  **/
  
  public String getEmv9F06() {
    return emv9F06;
  }

  public void setEmv9F06(String emv9F06) {
    this.emv9F06 = emv9F06;
  }

  public PaymentTransactionEMVTags emv9F07(String emv9F07) {
    this.emv9F07 = emv9F07;
    return this;
  }

   /**
   * Indicates issuer’s specified restrictions on the geographic usage and services allowed for the application
   * @return emv9F07
  **/
  
  public String getEmv9F07() {
    return emv9F07;
  }

  public void setEmv9F07(String emv9F07) {
    this.emv9F07 = emv9F07;
  }

  public PaymentTransactionEMVTags emv9F08(String emv9F08) {
    this.emv9F08 = emv9F08;
    return this;
  }

   /**
   * Version number assigned by the payment system for the application
   * @return emv9F08
  **/
  
  public String getEmv9F08() {
    return emv9F08;
  }

  public void setEmv9F08(String emv9F08) {
    this.emv9F08 = emv9F08;
  }

  public PaymentTransactionEMVTags emv9F09(String emv9F09) {
    this.emv9F09 = emv9F09;
    return this;
  }

   /**
   * Version number assigned by the payment system for the application
   * @return emv9F09
  **/
  
  public String getEmv9F09() {
    return emv9F09;
  }

  public void setEmv9F09(String emv9F09) {
    this.emv9F09 = emv9F09;
  }

  public PaymentTransactionEMVTags emv9F0D(String emv9F0D) {
    this.emv9F0D = emv9F0D;
    return this;
  }

   /**
   * Specifies the issuer’s conditions that cause a transaction to be rejected if it might have been approved online, but the terminal is unable to process the transaction online
   * @return emv9F0D
  **/
  
  public String getEmv9F0D() {
    return emv9F0D;
  }

  public void setEmv9F0D(String emv9F0D) {
    this.emv9F0D = emv9F0D;
  }

  public PaymentTransactionEMVTags emv9F0E(String emv9F0E) {
    this.emv9F0E = emv9F0E;
    return this;
  }

   /**
   * Specifies the issuer’s conditions that cause the denial of a transaction without attempt to go online
   * @return emv9F0E
  **/
  
  public String getEmv9F0E() {
    return emv9F0E;
  }

  public void setEmv9F0E(String emv9F0E) {
    this.emv9F0E = emv9F0E;
  }

  public PaymentTransactionEMVTags emv9F0F(String emv9F0F) {
    this.emv9F0F = emv9F0F;
    return this;
  }

   /**
   * Specifies the issuer’s conditions that cause a transaction to be transmitted online
   * @return emv9F0F
  **/
  
  public String getEmv9F0F() {
    return emv9F0F;
  }

  public void setEmv9F0F(String emv9F0F) {
    this.emv9F0F = emv9F0F;
  }

  public PaymentTransactionEMVTags emv9F10(String emv9F10) {
    this.emv9F10 = emv9F10;
    return this;
  }

   /**
   * Contains proprietary application data for transmission to the issuer in an online transaction
   * @return emv9F10
  **/
  
  public String getEmv9F10() {
    return emv9F10;
  }

  public void setEmv9F10(String emv9F10) {
    this.emv9F10 = emv9F10;
  }

  public PaymentTransactionEMVTags emv9F11(String emv9F11) {
    this.emv9F11 = emv9F11;
    return this;
  }

   /**
   * Indicates the code table according to ISO/IEC 8859 for displaying the Application Preferred Name
   * @return emv9F11
  **/
  
  public String getEmv9F11() {
    return emv9F11;
  }

  public void setEmv9F11(String emv9F11) {
    this.emv9F11 = emv9F11;
  }

  public PaymentTransactionEMVTags emv9F12(String emv9F12) {
    this.emv9F12 = emv9F12;
    return this;
  }

   /**
   * Preferred mnemonic associated with the AID
   * @return emv9F12
  **/
  
  public String getEmv9F12() {
    return emv9F12;
  }

  public void setEmv9F12(String emv9F12) {
    this.emv9F12 = emv9F12;
  }

  public PaymentTransactionEMVTags emv9F1A(String emv9F1A) {
    this.emv9F1A = emv9F1A;
    return this;
  }

   /**
   * Indicates the country of the terminal, represented according to ISO 3166
   * @return emv9F1A
  **/
  
  public String getEmv9F1A() {
    return emv9F1A;
  }

  public void setEmv9F1A(String emv9F1A) {
    this.emv9F1A = emv9F1A;
  }

  public PaymentTransactionEMVTags emv9F1E(String emv9F1E) {
    this.emv9F1E = emv9F1E;
    return this;
  }

   /**
   * Unique and permanent serial number assigned to the IFD by the manufacturer
   * @return emv9F1E
  **/
  
  public String getEmv9F1E() {
    return emv9F1E;
  }

  public void setEmv9F1E(String emv9F1E) {
    this.emv9F1E = emv9F1E;
  }

  public PaymentTransactionEMVTags emv9F21(String emv9F21) {
    this.emv9F21 = emv9F21;
    return this;
  }

   /**
   * Local time that the transaction was authorised
   * @return emv9F21
  **/
  
  public String getEmv9F21() {
    return emv9F21;
  }

  public void setEmv9F21(String emv9F21) {
    this.emv9F21 = emv9F21;
  }

  public PaymentTransactionEMVTags emv9F26(String emv9F26) {
    this.emv9F26 = emv9F26;
    return this;
  }

   /**
   * Cryptogram returned by the ICC in response of the GENERATE AC command
   * @return emv9F26
  **/
  
  public String getEmv9F26() {
    return emv9F26;
  }

  public void setEmv9F26(String emv9F26) {
    this.emv9F26 = emv9F26;
  }

  public PaymentTransactionEMVTags emv9F27(String emv9F27) {
    this.emv9F27 = emv9F27;
    return this;
  }

   /**
   * Indicates the type of cryptogram and the actions to be performed by the terminal
   * @return emv9F27
  **/
  
  public String getEmv9F27() {
    return emv9F27;
  }

  public void setEmv9F27(String emv9F27) {
    this.emv9F27 = emv9F27;
  }

  public PaymentTransactionEMVTags emv9F33(String emv9F33) {
    this.emv9F33 = emv9F33;
    return this;
  }

   /**
   * Indicates the card data input, CVM, and security capabilities of the terminal
   * @return emv9F33
  **/
  
  public String getEmv9F33() {
    return emv9F33;
  }

  public void setEmv9F33(String emv9F33) {
    this.emv9F33 = emv9F33;
  }

  public PaymentTransactionEMVTags emv9F34(String emv9F34) {
    this.emv9F34 = emv9F34;
    return this;
  }

   /**
   * Indicates the results of the last CVM performed
   * @return emv9F34
  **/
  
  public String getEmv9F34() {
    return emv9F34;
  }

  public void setEmv9F34(String emv9F34) {
    this.emv9F34 = emv9F34;
  }

  public PaymentTransactionEMVTags emv9F35(String emv9F35) {
    this.emv9F35 = emv9F35;
    return this;
  }

   /**
   * Indicates the environment of the terminal, its communications capability, and its operational control
   * @return emv9F35
  **/
  
  public String getEmv9F35() {
    return emv9F35;
  }

  public void setEmv9F35(String emv9F35) {
    this.emv9F35 = emv9F35;
  }

  public PaymentTransactionEMVTags emv9F36(String emv9F36) {
    this.emv9F36 = emv9F36;
    return this;
  }

   /**
   * Counter maintained by the application in the ICC (incrementing the ATC is managed by the ICC)
   * @return emv9F36
  **/
  
  public String getEmv9F36() {
    return emv9F36;
  }

  public void setEmv9F36(String emv9F36) {
    this.emv9F36 = emv9F36;
  }

  public PaymentTransactionEMVTags emv9F37(String emv9F37) {
    this.emv9F37 = emv9F37;
    return this;
  }

   /**
   * Value to provide variability and uniqueness to the generation of a cryptogram
   * @return emv9F37
  **/
  
  public String getEmv9F37() {
    return emv9F37;
  }

  public void setEmv9F37(String emv9F37) {
    this.emv9F37 = emv9F37;
  }

  public PaymentTransactionEMVTags emv9F39(String emv9F39) {
    this.emv9F39 = emv9F39;
    return this;
  }

   /**
   * Indicates the method by which the PAN was entered, according to the first two digits of the ISO 8583:1987 POS Entry Mode
   * @return emv9F39
  **/
  
  public String getEmv9F39() {
    return emv9F39;
  }

  public void setEmv9F39(String emv9F39) {
    this.emv9F39 = emv9F39;
  }

  public PaymentTransactionEMVTags emv9F41(String emv9F41) {
    this.emv9F41 = emv9F41;
    return this;
  }

   /**
   * Counter maintained by the terminal that is incremented by one for each transaction
   * @return emv9F41
  **/
  
  public String getEmv9F41() {
    return emv9F41;
  }

  public void setEmv9F41(String emv9F41) {
    this.emv9F41 = emv9F41;
  }

  public PaymentTransactionEMVTags emvChipIndicator(String emvChipIndicator) {
    this.emvChipIndicator = emvChipIndicator;
    return this;
  }

   /**
   * Emv entry mode. For example, contact, contactless, keyed, fallback swipe
   * @return emvChipIndicator
  **/
  
  public String getEmvChipIndicator() {
    return emvChipIndicator;
  }

  public void setEmvChipIndicator(String emvChipIndicator) {
    this.emvChipIndicator = emvChipIndicator;
  }

  public PaymentTransactionEMVTags emvCvm(String emvCvm) {
    this.emvCvm = emvCvm;
    return this;
  }

   /**
   * Cardholder verification method. For example, signature, PIN, none
   * @return emvCvm
  **/
  
  public String getEmvCvm() {
    return emvCvm;
  }

  public void setEmvCvm(String emvCvm) {
    this.emvCvm = emvCvm;
  }

  public PaymentTransactionEMVTags emvMode(String emvMode) {
    this.emvMode = emvMode;
    return this;
  }

   /**
   * Indicates if run in online or offline mode. For example, issuer or card
   * @return emvMode
  **/
  
  public String getEmvMode() {
    return emvMode;
  }

  public void setEmvMode(String emvMode) {
    this.emvMode = emvMode;
  }

  public PaymentTransactionEMVTags extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public PaymentTransactionEMVTags issuerScriptResults(String issuerScriptResults) {
    this.issuerScriptResults = issuerScriptResults;
    return this;
  }

   /**
   * Result of issuer script update
   * @return issuerScriptResults
  **/
  
  public String getIssuerScriptResults() {
    return issuerScriptResults;
  }

  public void setIssuerScriptResults(String issuerScriptResults) {
    this.issuerScriptResults = issuerScriptResults;
  }

  public PaymentTransactionEMVTags localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public PaymentTransactionEMVTags merchId(String merchId) {
    this.merchId = merchId;
    return this;
  }

   /**
   * Merchant Id, as printed when sent from the SSI host
   * @return merchId
  **/
  
  public String getMerchId() {
    return merchId;
  }

  public void setMerchId(String merchId) {
    this.merchId = merchId;
  }

  public PaymentTransactionEMVTags messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public PaymentTransactionEMVTags orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public PaymentTransactionEMVTags PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public PaymentTransactionEMVTags refNumber(String refNumber) {
    this.refNumber = refNumber;
    return this;
  }

   /**
   * Transaction reference number
   * @return refNumber
  **/
  
  public String getRefNumber() {
    return refNumber;
  }

  public void setRefNumber(String refNumber) {
    this.refNumber = refNumber;
  }

  public PaymentTransactionEMVTags tacDefault(String tacDefault) {
    this.tacDefault = tacDefault;
    return this;
  }

   /**
   * Terminal Action Code - default
   * @return tacDefault
  **/
  
  public String getTacDefault() {
    return tacDefault;
  }

  public void setTacDefault(String tacDefault) {
    this.tacDefault = tacDefault;
  }

  public PaymentTransactionEMVTags tacDenial(String tacDenial) {
    this.tacDenial = tacDenial;
    return this;
  }

   /**
   * Terminal Action Code - Denial
   * @return tacDenial
  **/
  
  public String getTacDenial() {
    return tacDenial;
  }

  public void setTacDenial(String tacDenial) {
    this.tacDenial = tacDenial;
  }

  public PaymentTransactionEMVTags tacOnline(String tacOnline) {
    this.tacOnline = tacOnline;
    return this;
  }

   /**
   * Terminal Action Code - Online
   * @return tacOnline
  **/
  
  public String getTacOnline() {
    return tacOnline;
  }

  public void setTacOnline(String tacOnline) {
    this.tacOnline = tacOnline;
  }

  public PaymentTransactionEMVTags termId(String termId) {
    this.termId = termId;
    return this;
  }

   /**
   * Terminal Id, as printed when sent from the SSI host
   * @return termId
  **/
  
  public String getTermId() {
    return termId;
  }

  public void setTermId(String termId) {
    this.termId = termId;
  }

  public PaymentTransactionEMVTags updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public PaymentTransactionEMVTags updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public PaymentTransactionEMVTags validationCode(String validationCode) {
    this.validationCode = validationCode;
    return this;
  }

   /**
   * Validation code
   * @return validationCode
  **/
  
  public String getValidationCode() {
    return validationCode;
  }

  public void setValidationCode(String validationCode) {
    this.validationCode = validationCode;
  }

  public PaymentTransactionEMVTags visaIdentifier(String visaIdentifier) {
    this.visaIdentifier = visaIdentifier;
    return this;
  }

   /**
   * Visa Rewards Program Identifier
   * @return visaIdentifier
  **/
  
  public String getVisaIdentifier() {
    return visaIdentifier;
  }

  public void setVisaIdentifier(String visaIdentifier) {
    this.visaIdentifier = visaIdentifier;
  }

  public PaymentTransactionEMVTags entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public PaymentTransactionEMVTags localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentTransactionEMVTags paymentTransactionEMVTags = (PaymentTransactionEMVTags) o;
    return Objects.equals(this.actions, paymentTransactionEMVTags.actions) &&
        Objects.equals(this.cardEntryMode, paymentTransactionEMVTags.cardEntryMode) &&
        Objects.equals(this.createdBy, paymentTransactionEMVTags.createdBy) &&
        Objects.equals(this.createdTimestamp, paymentTransactionEMVTags.createdTimestamp) &&
        Objects.equals(this.emv4F, paymentTransactionEMVTags.emv4F) &&
        Objects.equals(this.emv50, paymentTransactionEMVTags.emv50) &&
        Objects.equals(this.emv5F2A, paymentTransactionEMVTags.emv5F2A) &&
        Objects.equals(this.emv5F34, paymentTransactionEMVTags.emv5F34) &&
        Objects.equals(this.emv82, paymentTransactionEMVTags.emv82) &&
        Objects.equals(this.emv84, paymentTransactionEMVTags.emv84) &&
        Objects.equals(this.emv8A, paymentTransactionEMVTags.emv8A) &&
        Objects.equals(this.emv95, paymentTransactionEMVTags.emv95) &&
        Objects.equals(this.emv9A, paymentTransactionEMVTags.emv9A) &&
        Objects.equals(this.emv9B, paymentTransactionEMVTags.emv9B) &&
        Objects.equals(this.emv9C, paymentTransactionEMVTags.emv9C) &&
        Objects.equals(this.emv9F02, paymentTransactionEMVTags.emv9F02) &&
        Objects.equals(this.emv9F03, paymentTransactionEMVTags.emv9F03) &&
        Objects.equals(this.emv9F06, paymentTransactionEMVTags.emv9F06) &&
        Objects.equals(this.emv9F07, paymentTransactionEMVTags.emv9F07) &&
        Objects.equals(this.emv9F08, paymentTransactionEMVTags.emv9F08) &&
        Objects.equals(this.emv9F09, paymentTransactionEMVTags.emv9F09) &&
        Objects.equals(this.emv9F0D, paymentTransactionEMVTags.emv9F0D) &&
        Objects.equals(this.emv9F0E, paymentTransactionEMVTags.emv9F0E) &&
        Objects.equals(this.emv9F0F, paymentTransactionEMVTags.emv9F0F) &&
        Objects.equals(this.emv9F10, paymentTransactionEMVTags.emv9F10) &&
        Objects.equals(this.emv9F11, paymentTransactionEMVTags.emv9F11) &&
        Objects.equals(this.emv9F12, paymentTransactionEMVTags.emv9F12) &&
        Objects.equals(this.emv9F1A, paymentTransactionEMVTags.emv9F1A) &&
        Objects.equals(this.emv9F1E, paymentTransactionEMVTags.emv9F1E) &&
        Objects.equals(this.emv9F21, paymentTransactionEMVTags.emv9F21) &&
        Objects.equals(this.emv9F26, paymentTransactionEMVTags.emv9F26) &&
        Objects.equals(this.emv9F27, paymentTransactionEMVTags.emv9F27) &&
        Objects.equals(this.emv9F33, paymentTransactionEMVTags.emv9F33) &&
        Objects.equals(this.emv9F34, paymentTransactionEMVTags.emv9F34) &&
        Objects.equals(this.emv9F35, paymentTransactionEMVTags.emv9F35) &&
        Objects.equals(this.emv9F36, paymentTransactionEMVTags.emv9F36) &&
        Objects.equals(this.emv9F37, paymentTransactionEMVTags.emv9F37) &&
        Objects.equals(this.emv9F39, paymentTransactionEMVTags.emv9F39) &&
        Objects.equals(this.emv9F41, paymentTransactionEMVTags.emv9F41) &&
        Objects.equals(this.emvChipIndicator, paymentTransactionEMVTags.emvChipIndicator) &&
        Objects.equals(this.emvCvm, paymentTransactionEMVTags.emvCvm) &&
        Objects.equals(this.emvMode, paymentTransactionEMVTags.emvMode) &&
        Objects.equals(this.extended, paymentTransactionEMVTags.extended) &&
        Objects.equals(this.issuerScriptResults, paymentTransactionEMVTags.issuerScriptResults) &&
        Objects.equals(this.localizedTo, paymentTransactionEMVTags.localizedTo) &&
        Objects.equals(this.merchId, paymentTransactionEMVTags.merchId) &&
        Objects.equals(this.messages, paymentTransactionEMVTags.messages) &&
        Objects.equals(this.orgId, paymentTransactionEMVTags.orgId) &&
        Objects.equals(this.PK, paymentTransactionEMVTags.PK) &&
        Objects.equals(this.refNumber, paymentTransactionEMVTags.refNumber) &&
        Objects.equals(this.tacDefault, paymentTransactionEMVTags.tacDefault) &&
        Objects.equals(this.tacDenial, paymentTransactionEMVTags.tacDenial) &&
        Objects.equals(this.tacOnline, paymentTransactionEMVTags.tacOnline) &&
        Objects.equals(this.termId, paymentTransactionEMVTags.termId) &&
        Objects.equals(this.updatedBy, paymentTransactionEMVTags.updatedBy) &&
        Objects.equals(this.updatedTimestamp, paymentTransactionEMVTags.updatedTimestamp) &&
        Objects.equals(this.validationCode, paymentTransactionEMVTags.validationCode) &&
        Objects.equals(this.visaIdentifier, paymentTransactionEMVTags.visaIdentifier) &&
        Objects.equals(this.entityName, paymentTransactionEMVTags.entityName) &&
        Objects.equals(this.localize, paymentTransactionEMVTags.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, cardEntryMode, createdBy, createdTimestamp, emv4F, emv50, emv5F2A, emv5F34, emv82, emv84, emv8A, emv95, emv9A, emv9B, emv9C, emv9F02, emv9F03, emv9F06, emv9F07, emv9F08, emv9F09, emv9F0D, emv9F0E, emv9F0F, emv9F10, emv9F11, emv9F12, emv9F1A, emv9F1E, emv9F21, emv9F26, emv9F27, emv9F33, emv9F34, emv9F35, emv9F36, emv9F37, emv9F39, emv9F41, emvChipIndicator, emvCvm, emvMode, extended, issuerScriptResults, localizedTo, merchId, messages, orgId, PK, refNumber, tacDefault, tacDenial, tacOnline, termId, updatedBy, updatedTimestamp, validationCode, visaIdentifier, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentTransactionEMVTags {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    cardEntryMode: ").append(toIndentedString(cardEntryMode)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    emv4F: ").append(toIndentedString(emv4F)).append("\n");
    sb.append("    emv50: ").append(toIndentedString(emv50)).append("\n");
    sb.append("    emv5F2A: ").append(toIndentedString(emv5F2A)).append("\n");
    sb.append("    emv5F34: ").append(toIndentedString(emv5F34)).append("\n");
    sb.append("    emv82: ").append(toIndentedString(emv82)).append("\n");
    sb.append("    emv84: ").append(toIndentedString(emv84)).append("\n");
    sb.append("    emv8A: ").append(toIndentedString(emv8A)).append("\n");
    sb.append("    emv95: ").append(toIndentedString(emv95)).append("\n");
    sb.append("    emv9A: ").append(toIndentedString(emv9A)).append("\n");
    sb.append("    emv9B: ").append(toIndentedString(emv9B)).append("\n");
    sb.append("    emv9C: ").append(toIndentedString(emv9C)).append("\n");
    sb.append("    emv9F02: ").append(toIndentedString(emv9F02)).append("\n");
    sb.append("    emv9F03: ").append(toIndentedString(emv9F03)).append("\n");
    sb.append("    emv9F06: ").append(toIndentedString(emv9F06)).append("\n");
    sb.append("    emv9F07: ").append(toIndentedString(emv9F07)).append("\n");
    sb.append("    emv9F08: ").append(toIndentedString(emv9F08)).append("\n");
    sb.append("    emv9F09: ").append(toIndentedString(emv9F09)).append("\n");
    sb.append("    emv9F0D: ").append(toIndentedString(emv9F0D)).append("\n");
    sb.append("    emv9F0E: ").append(toIndentedString(emv9F0E)).append("\n");
    sb.append("    emv9F0F: ").append(toIndentedString(emv9F0F)).append("\n");
    sb.append("    emv9F10: ").append(toIndentedString(emv9F10)).append("\n");
    sb.append("    emv9F11: ").append(toIndentedString(emv9F11)).append("\n");
    sb.append("    emv9F12: ").append(toIndentedString(emv9F12)).append("\n");
    sb.append("    emv9F1A: ").append(toIndentedString(emv9F1A)).append("\n");
    sb.append("    emv9F1E: ").append(toIndentedString(emv9F1E)).append("\n");
    sb.append("    emv9F21: ").append(toIndentedString(emv9F21)).append("\n");
    sb.append("    emv9F26: ").append(toIndentedString(emv9F26)).append("\n");
    sb.append("    emv9F27: ").append(toIndentedString(emv9F27)).append("\n");
    sb.append("    emv9F33: ").append(toIndentedString(emv9F33)).append("\n");
    sb.append("    emv9F34: ").append(toIndentedString(emv9F34)).append("\n");
    sb.append("    emv9F35: ").append(toIndentedString(emv9F35)).append("\n");
    sb.append("    emv9F36: ").append(toIndentedString(emv9F36)).append("\n");
    sb.append("    emv9F37: ").append(toIndentedString(emv9F37)).append("\n");
    sb.append("    emv9F39: ").append(toIndentedString(emv9F39)).append("\n");
    sb.append("    emv9F41: ").append(toIndentedString(emv9F41)).append("\n");
    sb.append("    emvChipIndicator: ").append(toIndentedString(emvChipIndicator)).append("\n");
    sb.append("    emvCvm: ").append(toIndentedString(emvCvm)).append("\n");
    sb.append("    emvMode: ").append(toIndentedString(emvMode)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    issuerScriptResults: ").append(toIndentedString(issuerScriptResults)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    merchId: ").append(toIndentedString(merchId)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    refNumber: ").append(toIndentedString(refNumber)).append("\n");
    sb.append("    tacDefault: ").append(toIndentedString(tacDefault)).append("\n");
    sb.append("    tacDenial: ").append(toIndentedString(tacDenial)).append("\n");
    sb.append("    tacOnline: ").append(toIndentedString(tacOnline)).append("\n");
    sb.append("    termId: ").append(toIndentedString(termId)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    validationCode: ").append(toIndentedString(validationCode)).append("\n");
    sb.append("    visaIdentifier: ").append(toIndentedString(visaIdentifier)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

