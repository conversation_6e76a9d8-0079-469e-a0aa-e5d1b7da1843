/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ChangeSet
 */
public class ChangeSet {
  public static final String SERIALIZED_NAME_MOD_TYPE = "ModType";
  @SerializedName(SERIALIZED_NAME_MOD_TYPE)
  private String modType;

  public static final String SERIALIZED_NAME_PROPERTIES = "Properties";
  @SerializedName(SERIALIZED_NAME_PROPERTIES)
  private List<Property> properties = null;

  public ChangeSet modType(String modType) {
    this.modType = modType;
    return this;
  }

   /**
   * Get modType
   * @return modType
  **/
  
  public String getModType() {
    return modType;
  }

  public void setModType(String modType) {
    this.modType = modType;
  }

  public ChangeSet properties(List<Property> properties) {
    this.properties = properties;
    return this;
  }

  public ChangeSet addPropertiesItem(Property propertiesItem) {
    if (this.properties == null) {
      this.properties = new ArrayList<Property>();
    }
    this.properties.add(propertiesItem);
    return this;
  }

   /**
   * Get properties
   * @return properties
  **/
  
  public List<Property> getProperties() {
    return properties;
  }

  public void setProperties(List<Property> properties) {
    this.properties = properties;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChangeSet changeSet = (ChangeSet) o;
    return Objects.equals(this.modType, changeSet.modType) &&
        Objects.equals(this.properties, changeSet.properties);
  }

  @Override
  public int hashCode() {
    return Objects.hash(modType, properties);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChangeSet {\n");
    
    sb.append("    modType: ").append(toIndentedString(modType)).append("\n");
    sb.append("    properties: ").append(toIndentedString(properties)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

