/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderLineNote
 */
public class OrderLineNote {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DISPLAY_SEQUENCE = "DisplaySequence";
  @SerializedName(SERIALIZED_NAME_DISPLAY_SEQUENCE)
  private Integer displaySequence;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_IS_VISIBLE = "IsVisible";
  @SerializedName(SERIALIZED_NAME_IS_VISIBLE)
  private Boolean isVisible;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_NOTE_CATEGORY = "NoteCategory";
  @SerializedName(SERIALIZED_NAME_NOTE_CATEGORY)
  private NoteCategoryId noteCategory = null;

  public static final String SERIALIZED_NAME_NOTE_ID = "NoteId";
  @SerializedName(SERIALIZED_NAME_NOTE_ID)
  private String noteId;

  public static final String SERIALIZED_NAME_NOTE_TEXT = "NoteText";
  @SerializedName(SERIALIZED_NAME_NOTE_TEXT)
  private String noteText;

  public static final String SERIALIZED_NAME_NOTE_TYPE = "NoteType";
  @SerializedName(SERIALIZED_NAME_NOTE_TYPE)
  private NoteTypeId noteType = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public OrderLineNote actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderLineNote putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderLineNote createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderLineNote createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderLineNote displaySequence(Integer displaySequence) {
    this.displaySequence = displaySequence;
    return this;
  }

   /**
   * Seaquenc in which it should be display on UI.
   * minimum: 0
   * maximum: 99999
   * @return displaySequence
  **/
  
  public Integer getDisplaySequence() {
    return displaySequence;
  }

  public void setDisplaySequence(Integer displaySequence) {
    this.displaySequence = displaySequence;
  }

  public OrderLineNote extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderLineNote isVisible(Boolean isVisible) {
    this.isVisible = isVisible;
    return this;
  }

   /**
   * Visible on UI
   * @return isVisible
  **/
  
  public Boolean getIsVisible() {
    return isVisible;
  }

  public void setIsVisible(Boolean isVisible) {
    this.isVisible = isVisible;
  }

  public OrderLineNote localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public OrderLineNote messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderLineNote noteCategory(NoteCategoryId noteCategory) {
    this.noteCategory = noteCategory;
    return this;
  }

   /**
   * Get noteCategory
   * @return noteCategory
  **/
  
  public NoteCategoryId getNoteCategory() {
    return noteCategory;
  }

  public void setNoteCategory(NoteCategoryId noteCategory) {
    this.noteCategory = noteCategory;
  }

  public OrderLineNote noteId(String noteId) {
    this.noteId = noteId;
    return this;
  }

   /**
   * Note id, If its not passed CP would generate one.
   * @return noteId
  **/
  
  public String getNoteId() {
    return noteId;
  }

  public void setNoteId(String noteId) {
    this.noteId = noteId;
  }

  public OrderLineNote noteText(String noteText) {
    this.noteText = noteText;
    return this;
  }

   /**
   * Note text
   * @return noteText
  **/
  
  public String getNoteText() {
    return noteText;
  }

  public void setNoteText(String noteText) {
    this.noteText = noteText;
  }

  public OrderLineNote noteType(NoteTypeId noteType) {
    this.noteType = noteType;
    return this;
  }

   /**
   * Get noteType
   * @return noteType
  **/
  
  public NoteTypeId getNoteType() {
    return noteType;
  }

  public void setNoteType(NoteTypeId noteType) {
    this.noteType = noteType;
  }

  public OrderLineNote orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderLineNote PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderLineNote updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderLineNote updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderLineNote entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public OrderLineNote localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderLineNote orderLineNote = (OrderLineNote) o;
    return Objects.equals(this.actions, orderLineNote.actions) &&
        Objects.equals(this.createdBy, orderLineNote.createdBy) &&
        Objects.equals(this.createdTimestamp, orderLineNote.createdTimestamp) &&
        Objects.equals(this.displaySequence, orderLineNote.displaySequence) &&
        Objects.equals(this.extended, orderLineNote.extended) &&
        Objects.equals(this.isVisible, orderLineNote.isVisible) &&
        Objects.equals(this.localizedTo, orderLineNote.localizedTo) &&
        Objects.equals(this.messages, orderLineNote.messages) &&
        Objects.equals(this.noteCategory, orderLineNote.noteCategory) &&
        Objects.equals(this.noteId, orderLineNote.noteId) &&
        Objects.equals(this.noteText, orderLineNote.noteText) &&
        Objects.equals(this.noteType, orderLineNote.noteType) &&
        Objects.equals(this.orgId, orderLineNote.orgId) &&
        Objects.equals(this.PK, orderLineNote.PK) &&
        Objects.equals(this.updatedBy, orderLineNote.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderLineNote.updatedTimestamp) &&
        Objects.equals(this.entityName, orderLineNote.entityName) &&
        Objects.equals(this.localize, orderLineNote.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, displaySequence, extended, isVisible, localizedTo, messages, noteCategory, noteId, noteText, noteType, orgId, PK, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderLineNote {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    displaySequence: ").append(toIndentedString(displaySequence)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    isVisible: ").append(toIndentedString(isVisible)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    noteCategory: ").append(toIndentedString(noteCategory)).append("\n");
    sb.append("    noteId: ").append(toIndentedString(noteId)).append("\n");
    sb.append("    noteText: ").append(toIndentedString(noteText)).append("\n");
    sb.append("    noteType: ").append(toIndentedString(noteType)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

