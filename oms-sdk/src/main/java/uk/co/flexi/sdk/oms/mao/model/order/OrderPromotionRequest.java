/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * OrderPromotionRequest
 */
public class OrderPromotionRequest {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_INVALID_REASON = "InvalidReason";
  @SerializedName(SERIALIZED_NAME_INVALID_REASON)
  private ReasonId invalidReason = null;

  public static final String SERIALIZED_NAME_IS_INVALID = "IsInvalid";
  @SerializedName(SERIALIZED_NAME_IS_INVALID)
  private Boolean isInvalid;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PROMOTION_ID = "PromotionId";
  @SerializedName(SERIALIZED_NAME_PROMOTION_ID)
  private String promotionId;

  public static final String SERIALIZED_NAME_PROMOTION_REQUEST_ID = "PromotionRequestId";
  @SerializedName(SERIALIZED_NAME_PROMOTION_REQUEST_ID)
  private String promotionRequestId;

  public static final String SERIALIZED_NAME_TRANSLATIONS = "Translations";
  @SerializedName(SERIALIZED_NAME_TRANSLATIONS)
  private Map<String, Map<String, String>> translations = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public OrderPromotionRequest actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public OrderPromotionRequest putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public OrderPromotionRequest createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public OrderPromotionRequest createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public OrderPromotionRequest extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public OrderPromotionRequest invalidReason(ReasonId invalidReason) {
    this.invalidReason = invalidReason;
    return this;
  }

   /**
   * Get invalidReason
   * @return invalidReason
  **/
  
  public ReasonId getInvalidReason() {
    return invalidReason;
  }

  public void setInvalidReason(ReasonId invalidReason) {
    this.invalidReason = invalidReason;
  }

  public OrderPromotionRequest isInvalid(Boolean isInvalid) {
    this.isInvalid = isInvalid;
    return this;
  }

   /**
   * If a promotion or coupon is invalid the value would be set to true.
   * @return isInvalid
  **/
  
  public Boolean getIsInvalid() {
    return isInvalid;
  }

  public void setIsInvalid(Boolean isInvalid) {
    this.isInvalid = isInvalid;
  }

  public OrderPromotionRequest messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public OrderPromotionRequest orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public OrderPromotionRequest PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public OrderPromotionRequest promotionId(String promotionId) {
    this.promotionId = promotionId;
    return this;
  }

   /**
   * Unique identifier of the Promotion Id. It will be used for Capture promotion or coupon id manually provided by Customer or CSR..
   * @return promotionId
  **/
  
  public String getPromotionId() {
    return promotionId;
  }

  public void setPromotionId(String promotionId) {
    this.promotionId = promotionId;
  }

  public OrderPromotionRequest promotionRequestId(String promotionRequestId) {
    this.promotionRequestId = promotionRequestId;
    return this;
  }

   /**
   * Unique business key of the Promotion Request Entity.
   * @return promotionRequestId
  **/
  
  public String getPromotionRequestId() {
    return promotionRequestId;
  }

  public void setPromotionRequestId(String promotionRequestId) {
    this.promotionRequestId = promotionRequestId;
  }

  public OrderPromotionRequest translations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
    return this;
  }

  public OrderPromotionRequest putTranslationsItem(String key, Map<String, String> translationsItem) {
    if (this.translations == null) {
      this.translations = new HashMap<String, Map<String, String>>();
    }
    this.translations.put(key, translationsItem);
    return this;
  }

   /**
   * Get translations
   * @return translations
  **/
  
  public Map<String, Map<String, String>> getTranslations() {
    return translations;
  }

  public void setTranslations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
  }

  public OrderPromotionRequest updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public OrderPromotionRequest updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public OrderPromotionRequest entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderPromotionRequest orderPromotionRequest = (OrderPromotionRequest) o;
    return Objects.equals(this.actions, orderPromotionRequest.actions) &&
        Objects.equals(this.createdBy, orderPromotionRequest.createdBy) &&
        Objects.equals(this.createdTimestamp, orderPromotionRequest.createdTimestamp) &&
        Objects.equals(this.extended, orderPromotionRequest.extended) &&
        Objects.equals(this.invalidReason, orderPromotionRequest.invalidReason) &&
        Objects.equals(this.isInvalid, orderPromotionRequest.isInvalid) &&
        Objects.equals(this.messages, orderPromotionRequest.messages) &&
        Objects.equals(this.orgId, orderPromotionRequest.orgId) &&
        Objects.equals(this.PK, orderPromotionRequest.PK) &&
        Objects.equals(this.promotionId, orderPromotionRequest.promotionId) &&
        Objects.equals(this.promotionRequestId, orderPromotionRequest.promotionRequestId) &&
        Objects.equals(this.translations, orderPromotionRequest.translations) &&
        Objects.equals(this.updatedBy, orderPromotionRequest.updatedBy) &&
        Objects.equals(this.updatedTimestamp, orderPromotionRequest.updatedTimestamp) &&
        Objects.equals(this.entityName, orderPromotionRequest.entityName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, extended, invalidReason, isInvalid, messages, orgId, PK, promotionId, promotionRequestId, translations, updatedBy, updatedTimestamp, entityName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderPromotionRequest {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    invalidReason: ").append(toIndentedString(invalidReason)).append("\n");
    sb.append("    isInvalid: ").append(toIndentedString(isInvalid)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    promotionId: ").append(toIndentedString(promotionId)).append("\n");
    sb.append("    promotionRequestId: ").append(toIndentedString(promotionRequestId)).append("\n");
    sb.append("    translations: ").append(toIndentedString(translations)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

