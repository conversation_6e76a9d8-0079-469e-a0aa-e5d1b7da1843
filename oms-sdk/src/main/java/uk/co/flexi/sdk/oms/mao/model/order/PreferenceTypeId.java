/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * PreferenceTypeId
 */
public class PreferenceTypeId {
  public static final String SERIALIZED_NAME_PREFERENCE_TYPE_ID = "PreferenceTypeId";
  @SerializedName(SERIALIZED_NAME_PREFERENCE_TYPE_ID)
  private String preferenceTypeId;

  public PreferenceTypeId preferenceTypeId(String preferenceTypeId) {
    this.preferenceTypeId = preferenceTypeId;
    return this;
  }

   /**
   * Unique identifier of the Preference Type Id
   * @return preferenceTypeId
  **/
  
  public String getPreferenceTypeId() {
    return preferenceTypeId;
  }

  public void setPreferenceTypeId(String preferenceTypeId) {
    this.preferenceTypeId = preferenceTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PreferenceTypeId preferenceTypeId = (PreferenceTypeId) o;
    return Objects.equals(this.preferenceTypeId, preferenceTypeId.preferenceTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(preferenceTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PreferenceTypeId {\n");
    
    sb.append("    preferenceTypeId: ").append(toIndentedString(preferenceTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

