package uk.co.flexi.sdk.oms.model;

public class OrderLineData {

    private String orderLineId;
    private String returnReason;
    private String itemCondition;

    private String country;

    private String region;

    private String city;

    private OrderLineItemData item;

    public String getOrderLineId() {
        return orderLineId;
    }

    public OrderLineData setOrderLineId(String orderLineId) {
        this.orderLineId = orderLineId;
        return this;
    }

    public OrderLineItemData getItem() {
        return item;
    }

    public OrderLineData setItem(OrderLineItemData item) {
        this.item = item;
        return this;
    }

    public String getCountry() {
        return country;
    }

    public OrderLineData setCountry(String country) {
        this.country = country;
        return this;
    }

    public String getRegion() {
        return region;
    }

    public OrderLineData setRegion(String region) {
        this.region = region;
        return this;
    }

    public String getCity() {
        return city;
    }

    public OrderLineData setCity(String city) {
        this.city = city;
        return this;
    }

    public String getReturnReason() {
        return returnReason;
    }

    public OrderLineData setReturnReason(String returnReason) {
        this.returnReason = returnReason;
        return this;
    }

    public String getItemCondition() {
        return itemCondition;
    }

    public OrderLineData setItemCondition(String itemCondition) {
        this.itemCondition = itemCondition;
        return this;
    }

    @Override
    public String toString() {
        return "OrderLineData{" +
                "orderLineId='" + orderLineId + '\'' +
                ", returnReason='" + returnReason + '\'' +
                ", itemCondition='" + itemCondition + '\'' +
                ", country='" + country + '\'' +
                ", region='" + region + '\'' +
                ", city='" + city + '\'' +
                ", item=" + item +
                '}';
    }
}