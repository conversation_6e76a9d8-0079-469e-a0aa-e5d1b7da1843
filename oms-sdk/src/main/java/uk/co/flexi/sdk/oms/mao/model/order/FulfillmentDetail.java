/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * FulfillmentDetail
 */
public class FulfillmentDetail {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CALCULATED_VALUES = "CalculatedValues";
  @SerializedName(SERIALIZED_NAME_CALCULATED_VALUES)
  private Object calculatedValues = null;

  public static final String SERIALIZED_NAME_CARRIER_CODE = "CarrierCode";
  @SerializedName(SERIALIZED_NAME_CARRIER_CODE)
  private String carrierCode;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_ETA = "Eta";
  @SerializedName(SERIALIZED_NAME_ETA)
  private OffsetDateTime eta;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FULFILLMENT_DATE = "FulfillmentDate";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_DATE)
  private OffsetDateTime fulfillmentDate;

  public static final String SERIALIZED_NAME_FULFILLMENT_DETAIL_ID = "FulfillmentDetailId";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_DETAIL_ID)
  private String fulfillmentDetailId;

  public static final String SERIALIZED_NAME_FULFILLMENT_ID = "FulfillmentId";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_ID)
  private String fulfillmentId;

  public static final String SERIALIZED_NAME_GC_NUMBER = "GcNumber";
  @SerializedName(SERIALIZED_NAME_GC_NUMBER)
  private String gcNumber;

  public static final String SERIALIZED_NAME_GC_P_I_N = "GcPIN";
  @SerializedName(SERIALIZED_NAME_GC_P_I_N)
  private String gcPIN;

  public static final String SERIALIZED_NAME_GIFT_CARD_NUMBER = "GiftCardNumber";
  @SerializedName(SERIALIZED_NAME_GIFT_CARD_NUMBER)
  private String giftCardNumber;

  public static final String SERIALIZED_NAME_GIFT_CARD_P_I_N = "GiftCardPIN";
  @SerializedName(SERIALIZED_NAME_GIFT_CARD_P_I_N)
  private String giftCardPIN;

  public static final String SERIALIZED_NAME_ITEM_ID = "ItemId";
  @SerializedName(SERIALIZED_NAME_ITEM_ID)
  private String itemId;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PACKAGE_DETAIL_ID = "PackageDetailId";
  @SerializedName(SERIALIZED_NAME_PACKAGE_DETAIL_ID)
  private String packageDetailId;

  public static final String SERIALIZED_NAME_PACKAGE_ID = "PackageId";
  @SerializedName(SERIALIZED_NAME_PACKAGE_ID)
  private String packageId;

  public static final String SERIALIZED_NAME_QUANTITY = "Quantity";
  @SerializedName(SERIALIZED_NAME_QUANTITY)
  private Double quantity;

  public static final String SERIALIZED_NAME_RELEASE_ID = "ReleaseId";
  @SerializedName(SERIALIZED_NAME_RELEASE_ID)
  private String releaseId;

  public static final String SERIALIZED_NAME_RELEASE_LINE_ID = "ReleaseLineId";
  @SerializedName(SERIALIZED_NAME_RELEASE_LINE_ID)
  private String releaseLineId;

  public static final String SERIALIZED_NAME_RETURN_TRACKING_NUMBER = "ReturnTrackingNumber";
  @SerializedName(SERIALIZED_NAME_RETURN_TRACKING_NUMBER)
  private String returnTrackingNumber;

  public static final String SERIALIZED_NAME_SERIAL_NUMBER = "SerialNumber";
  @SerializedName(SERIALIZED_NAME_SERIAL_NUMBER)
  private String serialNumber;

  public static final String SERIALIZED_NAME_SERVICE_LEVEL_CODE = "ServiceLevelCode";
  @SerializedName(SERIALIZED_NAME_SERVICE_LEVEL_CODE)
  private String serviceLevelCode;

  public static final String SERIALIZED_NAME_SHIP_VIA_ID = "ShipViaId";
  @SerializedName(SERIALIZED_NAME_SHIP_VIA_ID)
  private String shipViaId;

  public static final String SERIALIZED_NAME_SHIPMENT_ID = "ShipmentId";
  @SerializedName(SERIALIZED_NAME_SHIPMENT_ID)
  private String shipmentId;

  public static final String SERIALIZED_NAME_STATUS_ID = "StatusId";
  @SerializedName(SERIALIZED_NAME_STATUS_ID)
  private String statusId;

  public static final String SERIALIZED_NAME_TRACKING_NUMBER = "TrackingNumber";
  @SerializedName(SERIALIZED_NAME_TRACKING_NUMBER)
  private String trackingNumber;

  public static final String SERIALIZED_NAME_TRACKING_U_R_L = "TrackingURL";
  @SerializedName(SERIALIZED_NAME_TRACKING_U_R_L)
  private String trackingURL;

  public static final String SERIALIZED_NAME_U_O_M = "UOM";
  @SerializedName(SERIALIZED_NAME_U_O_M)
  private String UOM;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public FulfillmentDetail actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public FulfillmentDetail putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public FulfillmentDetail calculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
    return this;
  }

   /**
   * Get calculatedValues
   * @return calculatedValues
  **/
  
  public Object getCalculatedValues() {
    return calculatedValues;
  }

  public void setCalculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
  }

  public FulfillmentDetail carrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
    return this;
  }

   /**
   * Carrier used for shipping
   * @return carrierCode
  **/
  
  public String getCarrierCode() {
    return carrierCode;
  }

  public void setCarrierCode(String carrierCode) {
    this.carrierCode = carrierCode;
  }

  public FulfillmentDetail createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public FulfillmentDetail createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public FulfillmentDetail eta(OffsetDateTime eta) {
    this.eta = eta;
    return this;
  }

   /**
   * ETA of the ASN
   * @return eta
  **/
  
  public OffsetDateTime getEta() {
    return eta;
  }

  public void setEta(OffsetDateTime eta) {
    this.eta = eta;
  }

  public FulfillmentDetail extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public FulfillmentDetail fulfillmentDate(OffsetDateTime fulfillmentDate) {
    this.fulfillmentDate = fulfillmentDate;
    return this;
  }

   /**
   * Date when the items are fulfilled
   * @return fulfillmentDate
  **/
  
  public OffsetDateTime getFulfillmentDate() {
    return fulfillmentDate;
  }

  public void setFulfillmentDate(OffsetDateTime fulfillmentDate) {
    this.fulfillmentDate = fulfillmentDate;
  }

  public FulfillmentDetail fulfillmentDetailId(String fulfillmentDetailId) {
    this.fulfillmentDetailId = fulfillmentDetailId;
    return this;
  }

   /**
   * Unique identifier of the fulfillment detail
   * @return fulfillmentDetailId
  **/
  
  public String getFulfillmentDetailId() {
    return fulfillmentDetailId;
  }

  public void setFulfillmentDetailId(String fulfillmentDetailId) {
    this.fulfillmentDetailId = fulfillmentDetailId;
  }

  public FulfillmentDetail fulfillmentId(String fulfillmentId) {
    this.fulfillmentId = fulfillmentId;
    return this;
  }

   /**
   * Fulfillment with which the detail is associated
   * @return fulfillmentId
  **/
  
  public String getFulfillmentId() {
    return fulfillmentId;
  }

  public void setFulfillmentId(String fulfillmentId) {
    this.fulfillmentId = fulfillmentId;
  }

  public FulfillmentDetail gcNumber(String gcNumber) {
    this.gcNumber = gcNumber;
    return this;
  }

   /**
   * Gift card number
   * @return gcNumber
  **/
  
  public String getGcNumber() {
    return gcNumber;
  }

  public void setGcNumber(String gcNumber) {
    this.gcNumber = gcNumber;
  }

  public FulfillmentDetail gcPIN(String gcPIN) {
    this.gcPIN = gcPIN;
    return this;
  }

   /**
   * Gift card PIN
   * @return gcPIN
  **/
  
  public String getGcPIN() {
    return gcPIN;
  }

  public void setGcPIN(String gcPIN) {
    this.gcPIN = gcPIN;
  }

  public FulfillmentDetail giftCardNumber(String giftCardNumber) {
    this.giftCardNumber = giftCardNumber;
    return this;
  }

   /**
   * Gift card number
   * @return giftCardNumber
  **/
  
  public String getGiftCardNumber() {
    return giftCardNumber;
  }

  public void setGiftCardNumber(String giftCardNumber) {
    this.giftCardNumber = giftCardNumber;
  }

  public FulfillmentDetail giftCardPIN(String giftCardPIN) {
    this.giftCardPIN = giftCardPIN;
    return this;
  }

   /**
   * Gift card PIN
   * @return giftCardPIN
  **/
  
  public String getGiftCardPIN() {
    return giftCardPIN;
  }

  public void setGiftCardPIN(String giftCardPIN) {
    this.giftCardPIN = giftCardPIN;
  }

  public FulfillmentDetail itemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

   /**
   * Identifier of the item
   * @return itemId
  **/
  
  public String getItemId() {
    return itemId;
  }

  public void setItemId(String itemId) {
    this.itemId = itemId;
  }

  public FulfillmentDetail localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public FulfillmentDetail messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public FulfillmentDetail orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public FulfillmentDetail PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public FulfillmentDetail packageDetailId(String packageDetailId) {
    this.packageDetailId = packageDetailId;
    return this;
  }

   /**
   * Identifier of the package detail in which the item is fulfilled
   * @return packageDetailId
  **/
  
  public String getPackageDetailId() {
    return packageDetailId;
  }

  public void setPackageDetailId(String packageDetailId) {
    this.packageDetailId = packageDetailId;
  }

  public FulfillmentDetail packageId(String packageId) {
    this.packageId = packageId;
    return this;
  }

   /**
   * Identifier of the package in which the item is fulfilled
   * @return packageId
  **/
  
  public String getPackageId() {
    return packageId;
  }

  public void setPackageId(String packageId) {
    this.packageId = packageId;
  }

  public FulfillmentDetail quantity(Double quantity) {
    this.quantity = quantity;
    return this;
  }

   /**
   * Quantity which is fulfilled
   * minimum: 0
   * maximum: 999999999999.9999
   * @return quantity
  **/
  
  public Double getQuantity() {
    return quantity;
  }

  public void setQuantity(Double quantity) {
    this.quantity = quantity;
  }

  public FulfillmentDetail releaseId(String releaseId) {
    this.releaseId = releaseId;
    return this;
  }

   /**
   * Unique identifier of the release
   * @return releaseId
  **/
  
  public String getReleaseId() {
    return releaseId;
  }

  public void setReleaseId(String releaseId) {
    this.releaseId = releaseId;
  }

  public FulfillmentDetail releaseLineId(String releaseLineId) {
    this.releaseLineId = releaseLineId;
    return this;
  }

   /**
   * Unique identifier of the release line
   * @return releaseLineId
  **/
  
  public String getReleaseLineId() {
    return releaseLineId;
  }

  public void setReleaseLineId(String releaseLineId) {
    this.releaseLineId = releaseLineId;
  }

  public FulfillmentDetail returnTrackingNumber(String returnTrackingNumber) {
    this.returnTrackingNumber = returnTrackingNumber;
    return this;
  }

   /**
   * Tracking number of the return shipment, if a return shipping label is included in the package. If the customer returns the item, this attribute is used when the carrier scans the package to identify the original order. The carrier feed event contains RTN, then the fulfillment details are searched to identify the original order and refund the customer. This number should be generated by the carrier such as FedEx or UPS.
   * @return returnTrackingNumber
  **/
  
  public String getReturnTrackingNumber() {
    return returnTrackingNumber;
  }

  public void setReturnTrackingNumber(String returnTrackingNumber) {
    this.returnTrackingNumber = returnTrackingNumber;
  }

  public FulfillmentDetail serialNumber(String serialNumber) {
    this.serialNumber = serialNumber;
    return this;
  }

   /**
   * Unique serial number of the unit
   * @return serialNumber
  **/
  
  public String getSerialNumber() {
    return serialNumber;
  }

  public void setSerialNumber(String serialNumber) {
    this.serialNumber = serialNumber;
  }

  public FulfillmentDetail serviceLevelCode(String serviceLevelCode) {
    this.serviceLevelCode = serviceLevelCode;
    return this;
  }

   /**
   * Service level used for shipping
   * @return serviceLevelCode
  **/
  
  public String getServiceLevelCode() {
    return serviceLevelCode;
  }

  public void setServiceLevelCode(String serviceLevelCode) {
    this.serviceLevelCode = serviceLevelCode;
  }

  public FulfillmentDetail shipViaId(String shipViaId) {
    this.shipViaId = shipViaId;
    return this;
  }

   /**
   * Shipping method used for package
   * @return shipViaId
  **/
  
  public String getShipViaId() {
    return shipViaId;
  }

  public void setShipViaId(String shipViaId) {
    this.shipViaId = shipViaId;
  }

  public FulfillmentDetail shipmentId(String shipmentId) {
    this.shipmentId = shipmentId;
    return this;
  }

   /**
   * Identifier of the shipment on which the item is fulfilled
   * @return shipmentId
  **/
  
  public String getShipmentId() {
    return shipmentId;
  }

  public void setShipmentId(String shipmentId) {
    this.shipmentId = shipmentId;
  }

  public FulfillmentDetail statusId(String statusId) {
    this.statusId = statusId;
    return this;
  }

   /**
   * Status of the fulfilled quantity
   * @return statusId
  **/
  
  public String getStatusId() {
    return statusId;
  }

  public void setStatusId(String statusId) {
    this.statusId = statusId;
  }

  public FulfillmentDetail trackingNumber(String trackingNumber) {
    this.trackingNumber = trackingNumber;
    return this;
  }

   /**
   * Tracking number of the package, if shipped
   * @return trackingNumber
  **/
  
  public String getTrackingNumber() {
    return trackingNumber;
  }

  public void setTrackingNumber(String trackingNumber) {
    this.trackingNumber = trackingNumber;
  }

  public FulfillmentDetail trackingURL(String trackingURL) {
    this.trackingURL = trackingURL;
    return this;
  }

   /**
   * Get trackingURL
   * @return trackingURL
  **/
  
  public String getTrackingURL() {
    return trackingURL;
  }

  public void setTrackingURL(String trackingURL) {
    this.trackingURL = trackingURL;
  }

  public FulfillmentDetail UOM(String UOM) {
    this.UOM = UOM;
    return this;
  }

   /**
   * Unit of measure (UOM) of the quantity
   * @return UOM
  **/
  
  public String getUOM() {
    return UOM;
  }

  public void setUOM(String UOM) {
    this.UOM = UOM;
  }

  public FulfillmentDetail updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public FulfillmentDetail updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public FulfillmentDetail entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public FulfillmentDetail localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FulfillmentDetail fulfillmentDetail = (FulfillmentDetail) o;
    return Objects.equals(this.actions, fulfillmentDetail.actions) &&
        Objects.equals(this.calculatedValues, fulfillmentDetail.calculatedValues) &&
        Objects.equals(this.carrierCode, fulfillmentDetail.carrierCode) &&
        Objects.equals(this.createdBy, fulfillmentDetail.createdBy) &&
        Objects.equals(this.createdTimestamp, fulfillmentDetail.createdTimestamp) &&
        Objects.equals(this.eta, fulfillmentDetail.eta) &&
        Objects.equals(this.extended, fulfillmentDetail.extended) &&
        Objects.equals(this.fulfillmentDate, fulfillmentDetail.fulfillmentDate) &&
        Objects.equals(this.fulfillmentDetailId, fulfillmentDetail.fulfillmentDetailId) &&
        Objects.equals(this.fulfillmentId, fulfillmentDetail.fulfillmentId) &&
        Objects.equals(this.gcNumber, fulfillmentDetail.gcNumber) &&
        Objects.equals(this.gcPIN, fulfillmentDetail.gcPIN) &&
        Objects.equals(this.giftCardNumber, fulfillmentDetail.giftCardNumber) &&
        Objects.equals(this.giftCardPIN, fulfillmentDetail.giftCardPIN) &&
        Objects.equals(this.itemId, fulfillmentDetail.itemId) &&
        Objects.equals(this.localizedTo, fulfillmentDetail.localizedTo) &&
        Objects.equals(this.messages, fulfillmentDetail.messages) &&
        Objects.equals(this.orgId, fulfillmentDetail.orgId) &&
        Objects.equals(this.PK, fulfillmentDetail.PK) &&
        Objects.equals(this.packageDetailId, fulfillmentDetail.packageDetailId) &&
        Objects.equals(this.packageId, fulfillmentDetail.packageId) &&
        Objects.equals(this.quantity, fulfillmentDetail.quantity) &&
        Objects.equals(this.releaseId, fulfillmentDetail.releaseId) &&
        Objects.equals(this.releaseLineId, fulfillmentDetail.releaseLineId) &&
        Objects.equals(this.returnTrackingNumber, fulfillmentDetail.returnTrackingNumber) &&
        Objects.equals(this.serialNumber, fulfillmentDetail.serialNumber) &&
        Objects.equals(this.serviceLevelCode, fulfillmentDetail.serviceLevelCode) &&
        Objects.equals(this.shipViaId, fulfillmentDetail.shipViaId) &&
        Objects.equals(this.shipmentId, fulfillmentDetail.shipmentId) &&
        Objects.equals(this.statusId, fulfillmentDetail.statusId) &&
        Objects.equals(this.trackingNumber, fulfillmentDetail.trackingNumber) &&
        Objects.equals(this.trackingURL, fulfillmentDetail.trackingURL) &&
        Objects.equals(this.UOM, fulfillmentDetail.UOM) &&
        Objects.equals(this.updatedBy, fulfillmentDetail.updatedBy) &&
        Objects.equals(this.updatedTimestamp, fulfillmentDetail.updatedTimestamp) &&
        Objects.equals(this.entityName, fulfillmentDetail.entityName) &&
        Objects.equals(this.localize, fulfillmentDetail.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, calculatedValues, carrierCode, createdBy, createdTimestamp, eta, extended, fulfillmentDate, fulfillmentDetailId, fulfillmentId, gcNumber, gcPIN, giftCardNumber, giftCardPIN, itemId, localizedTo, messages, orgId, PK, packageDetailId, packageId, quantity, releaseId, releaseLineId, returnTrackingNumber, serialNumber, serviceLevelCode, shipViaId, shipmentId, statusId, trackingNumber, trackingURL, UOM, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FulfillmentDetail {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    calculatedValues: ").append(toIndentedString(calculatedValues)).append("\n");
    sb.append("    carrierCode: ").append(toIndentedString(carrierCode)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    eta: ").append(toIndentedString(eta)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    fulfillmentDate: ").append(toIndentedString(fulfillmentDate)).append("\n");
    sb.append("    fulfillmentDetailId: ").append(toIndentedString(fulfillmentDetailId)).append("\n");
    sb.append("    fulfillmentId: ").append(toIndentedString(fulfillmentId)).append("\n");
    sb.append("    gcNumber: ").append(toIndentedString(gcNumber)).append("\n");
    sb.append("    gcPIN: ").append(toIndentedString(gcPIN)).append("\n");
    sb.append("    giftCardNumber: ").append(toIndentedString(giftCardNumber)).append("\n");
    sb.append("    giftCardPIN: ").append(toIndentedString(giftCardPIN)).append("\n");
    sb.append("    itemId: ").append(toIndentedString(itemId)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    packageDetailId: ").append(toIndentedString(packageDetailId)).append("\n");
    sb.append("    packageId: ").append(toIndentedString(packageId)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    releaseId: ").append(toIndentedString(releaseId)).append("\n");
    sb.append("    releaseLineId: ").append(toIndentedString(releaseLineId)).append("\n");
    sb.append("    returnTrackingNumber: ").append(toIndentedString(returnTrackingNumber)).append("\n");
    sb.append("    serialNumber: ").append(toIndentedString(serialNumber)).append("\n");
    sb.append("    serviceLevelCode: ").append(toIndentedString(serviceLevelCode)).append("\n");
    sb.append("    shipViaId: ").append(toIndentedString(shipViaId)).append("\n");
    sb.append("    shipmentId: ").append(toIndentedString(shipmentId)).append("\n");
    sb.append("    statusId: ").append(toIndentedString(statusId)).append("\n");
    sb.append("    trackingNumber: ").append(toIndentedString(trackingNumber)).append("\n");
    sb.append("    trackingURL: ").append(toIndentedString(trackingURL)).append("\n");
    sb.append("    UOM: ").append(toIndentedString(UOM)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

