package uk.co.flexi.sdk.oms.mao.client.interceptor;



import uk.co.flexi.sdk.oms.mao.client.MaoAuthClient;
import uk.co.flexi.sdk.oms.mao.model.auth.AuthResponse;

import java.util.HashMap;
import java.util.Map;

public class MaoAccessTokenRepository {

    private final MaoAuthClient apiAuth;

    private final Map<String, String> accessTokens = new HashMap<>();

    public MaoAccessTokenRepository(MaoAuthClient apiAuth) {
        this.apiAuth = apiAuth;
    }
    String getAccessToken() {
        String key = apiAuth.uniqueAuthKey();
        String accessToken = accessTokens.get(key);
        return accessToken == null ? generateNewAccessToken() : accessToken;
    }

    String generateNewAccessToken() {
        AuthResponse tokenResponse = apiAuth.authorize();
        String accessToken = tokenResponse.getAccessToken();
        accessTokens.put(apiAuth.uniqueAuthKey(), accessToken);
        return accessToken;
    }

    String refreshAccessToken() {
        return generateNewAccessToken();
    }
}
