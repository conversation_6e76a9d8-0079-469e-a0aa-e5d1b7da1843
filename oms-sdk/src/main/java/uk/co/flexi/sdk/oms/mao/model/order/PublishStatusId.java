/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * PublishStatusId
 */
public class PublishStatusId {
  public static final String SERIALIZED_NAME_PUBLISH_STATUS_ID = "PublishStatusId";
  @SerializedName(SERIALIZED_NAME_PUBLISH_STATUS_ID)
  private String publishStatusId;

  public PublishStatusId publishStatusId(String publishStatusId) {
    this.publishStatusId = publishStatusId;
    return this;
  }

   /**
   * Unique identifier of publish Status Id
   * @return publishStatusId
  **/
  
  public String getPublishStatusId() {
    return publishStatusId;
  }

  public void setPublishStatusId(String publishStatusId) {
    this.publishStatusId = publishStatusId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PublishStatusId publishStatusId = (PublishStatusId) o;
    return Objects.equals(this.publishStatusId, publishStatusId.publishStatusId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(publishStatusId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PublishStatusId {\n");
    
    sb.append("    publishStatusId: ").append(toIndentedString(publishStatusId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

