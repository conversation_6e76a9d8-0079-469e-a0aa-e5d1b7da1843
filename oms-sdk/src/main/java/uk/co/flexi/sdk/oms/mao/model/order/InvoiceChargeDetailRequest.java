/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * InvoiceChargeDetailRequest
 */
public class InvoiceChargeDetailRequest {
  public static final String SERIALIZED_NAME_CHARGE_DETAIL_ID = "chargeDetailId";
  @SerializedName(SERIALIZED_NAME_CHARGE_DETAIL_ID)
  private String chargeDetailId;

  public static final String SERIALIZED_NAME_CHARGE_PERCENT = "chargePercent";
  @SerializedName(SERIALIZED_NAME_CHARGE_PERCENT)
  private Double chargePercent;

  public static final String SERIALIZED_NAME_CHARGE_REFERENCE_ID = "chargeReferenceId";
  @SerializedName(SERIALIZED_NAME_CHARGE_REFERENCE_ID)
  private String chargeReferenceId;

  public static final String SERIALIZED_NAME_CHARGE_SEQUENCE = "chargeSequence";
  @SerializedName(SERIALIZED_NAME_CHARGE_SEQUENCE)
  private Long chargeSequence;

  public static final String SERIALIZED_NAME_CHARGE_TOTAL = "chargeTotal";
  @SerializedName(SERIALIZED_NAME_CHARGE_TOTAL)
  private BigDecimal chargeTotal;

  public static final String SERIALIZED_NAME_COMMENTS = "comments";
  @SerializedName(SERIALIZED_NAME_COMMENTS)
  private String comments;

  public static final String SERIALIZED_NAME_EXTENDED = "extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FULFILLMENT_GROUP_ID = "fulfillmentGroupId";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_GROUP_ID)
  private String fulfillmentGroupId;

  public static final String SERIALIZED_NAME_IS_INFORMATIONAL = "isInformational";
  @SerializedName(SERIALIZED_NAME_IS_INFORMATIONAL)
  private Boolean isInformational;

  public static final String SERIALIZED_NAME_IS_PRORATED_AT_SAME_LEVEL = "isProratedAtSameLevel";
  @SerializedName(SERIALIZED_NAME_IS_PRORATED_AT_SAME_LEVEL)
  private Boolean isProratedAtSameLevel;

  public static final String SERIALIZED_NAME_IS_RETURN_CHARGE = "isReturnCharge";
  @SerializedName(SERIALIZED_NAME_IS_RETURN_CHARGE)
  private Boolean isReturnCharge;

  public static final String SERIALIZED_NAME_IS_TAX_INCLUDED = "isTaxIncluded";
  @SerializedName(SERIALIZED_NAME_IS_TAX_INCLUDED)
  private Boolean isTaxIncluded;

  public static final String SERIALIZED_NAME_PARENT_CHARGE_DETAIL_ID = "parentChargeDetailId";
  @SerializedName(SERIALIZED_NAME_PARENT_CHARGE_DETAIL_ID)
  private String parentChargeDetailId;

  public static final String SERIALIZED_NAME_RELATED_CHARGE_DETAIL_ID = "relatedChargeDetailId";
  @SerializedName(SERIALIZED_NAME_RELATED_CHARGE_DETAIL_ID)
  private String relatedChargeDetailId;

  public static final String SERIALIZED_NAME_RELATED_CHARGE_TYPE = "relatedChargeType";
  @SerializedName(SERIALIZED_NAME_RELATED_CHARGE_TYPE)
  private String relatedChargeType;

  public static final String SERIALIZED_NAME_REQUESTED_AMOUNT = "requestedAmount";
  @SerializedName(SERIALIZED_NAME_REQUESTED_AMOUNT)
  private BigDecimal requestedAmount;

  public static final String SERIALIZED_NAME_TAX_CODE = "taxCode";
  @SerializedName(SERIALIZED_NAME_TAX_CODE)
  private String taxCode;

  public InvoiceChargeDetailRequest chargeDetailId(String chargeDetailId) {
    this.chargeDetailId = chargeDetailId;
    return this;
  }

   /**
   * Get chargeDetailId
   * @return chargeDetailId
  **/
  
  public String getChargeDetailId() {
    return chargeDetailId;
  }

  public void setChargeDetailId(String chargeDetailId) {
    this.chargeDetailId = chargeDetailId;
  }

  public InvoiceChargeDetailRequest chargePercent(Double chargePercent) {
    this.chargePercent = chargePercent;
    return this;
  }

   /**
   * Get chargePercent
   * @return chargePercent
  **/
  
  public Double getChargePercent() {
    return chargePercent;
  }

  public void setChargePercent(Double chargePercent) {
    this.chargePercent = chargePercent;
  }

  public InvoiceChargeDetailRequest chargeReferenceId(String chargeReferenceId) {
    this.chargeReferenceId = chargeReferenceId;
    return this;
  }

   /**
   * Get chargeReferenceId
   * @return chargeReferenceId
  **/
  
  public String getChargeReferenceId() {
    return chargeReferenceId;
  }

  public void setChargeReferenceId(String chargeReferenceId) {
    this.chargeReferenceId = chargeReferenceId;
  }

  public InvoiceChargeDetailRequest chargeSequence(Long chargeSequence) {
    this.chargeSequence = chargeSequence;
    return this;
  }

   /**
   * Get chargeSequence
   * @return chargeSequence
  **/
  
  public Long getChargeSequence() {
    return chargeSequence;
  }

  public void setChargeSequence(Long chargeSequence) {
    this.chargeSequence = chargeSequence;
  }

  public InvoiceChargeDetailRequest chargeTotal(BigDecimal chargeTotal) {
    this.chargeTotal = chargeTotal;
    return this;
  }

   /**
   * Get chargeTotal
   * @return chargeTotal
  **/
  
  public BigDecimal getChargeTotal() {
    return chargeTotal;
  }

  public void setChargeTotal(BigDecimal chargeTotal) {
    this.chargeTotal = chargeTotal;
  }

  public InvoiceChargeDetailRequest comments(String comments) {
    this.comments = comments;
    return this;
  }

   /**
   * Get comments
   * @return comments
  **/
  
  public String getComments() {
    return comments;
  }

  public void setComments(String comments) {
    this.comments = comments;
  }

  public InvoiceChargeDetailRequest extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Get extended
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public InvoiceChargeDetailRequest fulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
    return this;
  }

   /**
   * Get fulfillmentGroupId
   * @return fulfillmentGroupId
  **/
  
  public String getFulfillmentGroupId() {
    return fulfillmentGroupId;
  }

  public void setFulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
  }

  public InvoiceChargeDetailRequest isInformational(Boolean isInformational) {
    this.isInformational = isInformational;
    return this;
  }

   /**
   * Get isInformational
   * @return isInformational
  **/
  
  public Boolean getIsInformational() {
    return isInformational;
  }

  public void setIsInformational(Boolean isInformational) {
    this.isInformational = isInformational;
  }

  public InvoiceChargeDetailRequest isProratedAtSameLevel(Boolean isProratedAtSameLevel) {
    this.isProratedAtSameLevel = isProratedAtSameLevel;
    return this;
  }

   /**
   * Get isProratedAtSameLevel
   * @return isProratedAtSameLevel
  **/
  
  public Boolean getIsProratedAtSameLevel() {
    return isProratedAtSameLevel;
  }

  public void setIsProratedAtSameLevel(Boolean isProratedAtSameLevel) {
    this.isProratedAtSameLevel = isProratedAtSameLevel;
  }

  public InvoiceChargeDetailRequest isReturnCharge(Boolean isReturnCharge) {
    this.isReturnCharge = isReturnCharge;
    return this;
  }

   /**
   * Get isReturnCharge
   * @return isReturnCharge
  **/
  
  public Boolean getIsReturnCharge() {
    return isReturnCharge;
  }

  public void setIsReturnCharge(Boolean isReturnCharge) {
    this.isReturnCharge = isReturnCharge;
  }

  public InvoiceChargeDetailRequest isTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
    return this;
  }

   /**
   * Get isTaxIncluded
   * @return isTaxIncluded
  **/
  
  public Boolean getIsTaxIncluded() {
    return isTaxIncluded;
  }

  public void setIsTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
  }

  public InvoiceChargeDetailRequest parentChargeDetailId(String parentChargeDetailId) {
    this.parentChargeDetailId = parentChargeDetailId;
    return this;
  }

   /**
   * Get parentChargeDetailId
   * @return parentChargeDetailId
  **/
  
  public String getParentChargeDetailId() {
    return parentChargeDetailId;
  }

  public void setParentChargeDetailId(String parentChargeDetailId) {
    this.parentChargeDetailId = parentChargeDetailId;
  }

  public InvoiceChargeDetailRequest relatedChargeDetailId(String relatedChargeDetailId) {
    this.relatedChargeDetailId = relatedChargeDetailId;
    return this;
  }

   /**
   * Get relatedChargeDetailId
   * @return relatedChargeDetailId
  **/
  
  public String getRelatedChargeDetailId() {
    return relatedChargeDetailId;
  }

  public void setRelatedChargeDetailId(String relatedChargeDetailId) {
    this.relatedChargeDetailId = relatedChargeDetailId;
  }

  public InvoiceChargeDetailRequest relatedChargeType(String relatedChargeType) {
    this.relatedChargeType = relatedChargeType;
    return this;
  }

   /**
   * Get relatedChargeType
   * @return relatedChargeType
  **/
  
  public String getRelatedChargeType() {
    return relatedChargeType;
  }

  public void setRelatedChargeType(String relatedChargeType) {
    this.relatedChargeType = relatedChargeType;
  }

  public InvoiceChargeDetailRequest requestedAmount(BigDecimal requestedAmount) {
    this.requestedAmount = requestedAmount;
    return this;
  }

   /**
   * Get requestedAmount
   * @return requestedAmount
  **/
  
  public BigDecimal getRequestedAmount() {
    return requestedAmount;
  }

  public void setRequestedAmount(BigDecimal requestedAmount) {
    this.requestedAmount = requestedAmount;
  }

  public InvoiceChargeDetailRequest taxCode(String taxCode) {
    this.taxCode = taxCode;
    return this;
  }

   /**
   * Get taxCode
   * @return taxCode
  **/
  
  public String getTaxCode() {
    return taxCode;
  }

  public void setTaxCode(String taxCode) {
    this.taxCode = taxCode;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvoiceChargeDetailRequest invoiceChargeDetailRequest = (InvoiceChargeDetailRequest) o;
    return Objects.equals(this.chargeDetailId, invoiceChargeDetailRequest.chargeDetailId) &&
        Objects.equals(this.chargePercent, invoiceChargeDetailRequest.chargePercent) &&
        Objects.equals(this.chargeReferenceId, invoiceChargeDetailRequest.chargeReferenceId) &&
        Objects.equals(this.chargeSequence, invoiceChargeDetailRequest.chargeSequence) &&
        Objects.equals(this.chargeTotal, invoiceChargeDetailRequest.chargeTotal) &&
        Objects.equals(this.comments, invoiceChargeDetailRequest.comments) &&
        Objects.equals(this.extended, invoiceChargeDetailRequest.extended) &&
        Objects.equals(this.fulfillmentGroupId, invoiceChargeDetailRequest.fulfillmentGroupId) &&
        Objects.equals(this.isInformational, invoiceChargeDetailRequest.isInformational) &&
        Objects.equals(this.isProratedAtSameLevel, invoiceChargeDetailRequest.isProratedAtSameLevel) &&
        Objects.equals(this.isReturnCharge, invoiceChargeDetailRequest.isReturnCharge) &&
        Objects.equals(this.isTaxIncluded, invoiceChargeDetailRequest.isTaxIncluded) &&
        Objects.equals(this.parentChargeDetailId, invoiceChargeDetailRequest.parentChargeDetailId) &&
        Objects.equals(this.relatedChargeDetailId, invoiceChargeDetailRequest.relatedChargeDetailId) &&
        Objects.equals(this.relatedChargeType, invoiceChargeDetailRequest.relatedChargeType) &&
        Objects.equals(this.requestedAmount, invoiceChargeDetailRequest.requestedAmount) &&
        Objects.equals(this.taxCode, invoiceChargeDetailRequest.taxCode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(chargeDetailId, chargePercent, chargeReferenceId, chargeSequence, chargeTotal, comments, extended, fulfillmentGroupId, isInformational, isProratedAtSameLevel, isReturnCharge, isTaxIncluded, parentChargeDetailId, relatedChargeDetailId, relatedChargeType, requestedAmount, taxCode);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvoiceChargeDetailRequest {\n");
    
    sb.append("    chargeDetailId: ").append(toIndentedString(chargeDetailId)).append("\n");
    sb.append("    chargePercent: ").append(toIndentedString(chargePercent)).append("\n");
    sb.append("    chargeReferenceId: ").append(toIndentedString(chargeReferenceId)).append("\n");
    sb.append("    chargeSequence: ").append(toIndentedString(chargeSequence)).append("\n");
    sb.append("    chargeTotal: ").append(toIndentedString(chargeTotal)).append("\n");
    sb.append("    comments: ").append(toIndentedString(comments)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    fulfillmentGroupId: ").append(toIndentedString(fulfillmentGroupId)).append("\n");
    sb.append("    isInformational: ").append(toIndentedString(isInformational)).append("\n");
    sb.append("    isProratedAtSameLevel: ").append(toIndentedString(isProratedAtSameLevel)).append("\n");
    sb.append("    isReturnCharge: ").append(toIndentedString(isReturnCharge)).append("\n");
    sb.append("    isTaxIncluded: ").append(toIndentedString(isTaxIncluded)).append("\n");
    sb.append("    parentChargeDetailId: ").append(toIndentedString(parentChargeDetailId)).append("\n");
    sb.append("    relatedChargeDetailId: ").append(toIndentedString(relatedChargeDetailId)).append("\n");
    sb.append("    relatedChargeType: ").append(toIndentedString(relatedChargeType)).append("\n");
    sb.append("    requestedAmount: ").append(toIndentedString(requestedAmount)).append("\n");
    sb.append("    taxCode: ").append(toIndentedString(taxCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

