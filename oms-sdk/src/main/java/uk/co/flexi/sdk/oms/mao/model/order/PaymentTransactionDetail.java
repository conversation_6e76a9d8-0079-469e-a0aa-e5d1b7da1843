/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * PaymentTransactionDetail
 */
public class PaymentTransactionDetail {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_AMOUNT = "Amount";
  @SerializedName(SERIALIZED_NAME_AMOUNT)
  private BigDecimal amount;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PAYMENT_TRANSACTION_DETAIL_ID = "PaymentTransactionDetailId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TRANSACTION_DETAIL_ID)
  private String paymentTransactionDetailId;

  public static final String SERIALIZED_NAME_REFERENCE_ID = "ReferenceId";
  @SerializedName(SERIALIZED_NAME_REFERENCE_ID)
  private String referenceId;

  public static final String SERIALIZED_NAME_REFERENCE_TYPE = "ReferenceType";
  @SerializedName(SERIALIZED_NAME_REFERENCE_TYPE)
  private TransactionReferenceTypeId referenceType = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public PaymentTransactionDetail actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public PaymentTransactionDetail putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public PaymentTransactionDetail amount(BigDecimal amount) {
    this.amount = amount;
    return this;
  }

   /**
   * Amount associated with the reference. Used to determine the amount from each invoice which is processed against a payment transaction. For instance, if a $100 shipment invoiceId ABC has $40 settled on credit card 1 and $60 settled on credit card 2, then the credit card 1 settlement has a record in this table with invoiceId ABC, amount $40, and the credit card 2 settlement record has a record in this table with invoiceId ABC, amount $60.
   * minimum: 0
   * maximum: 99999999999999.98
   * @return amount
  **/
  
  public BigDecimal getAmount() {
    return amount;
  }

  public void setAmount(BigDecimal amount) {
    this.amount = amount;
  }

  public PaymentTransactionDetail createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public PaymentTransactionDetail createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public PaymentTransactionDetail extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public PaymentTransactionDetail localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public PaymentTransactionDetail messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public PaymentTransactionDetail orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public PaymentTransactionDetail PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public PaymentTransactionDetail paymentTransactionDetailId(String paymentTransactionDetailId) {
    this.paymentTransactionDetailId = paymentTransactionDetailId;
    return this;
  }

   /**
   * Unique identifier of the payment transaction detail
   * @return paymentTransactionDetailId
  **/
  
  public String getPaymentTransactionDetailId() {
    return paymentTransactionDetailId;
  }

  public void setPaymentTransactionDetailId(String paymentTransactionDetailId) {
    this.paymentTransactionDetailId = paymentTransactionDetailId;
  }

  public PaymentTransactionDetail referenceId(String referenceId) {
    this.referenceId = referenceId;
    return this;
  }

   /**
   * Reference identifier. If referenceTypeId is Invoice, then the referenceId is the invoiceId.
   * @return referenceId
  **/
  
  public String getReferenceId() {
    return referenceId;
  }

  public void setReferenceId(String referenceId) {
    this.referenceId = referenceId;
  }

  public PaymentTransactionDetail referenceType(TransactionReferenceTypeId referenceType) {
    this.referenceType = referenceType;
    return this;
  }

   /**
   * Get referenceType
   * @return referenceType
  **/
  
  public TransactionReferenceTypeId getReferenceType() {
    return referenceType;
  }

  public void setReferenceType(TransactionReferenceTypeId referenceType) {
    this.referenceType = referenceType;
  }

  public PaymentTransactionDetail updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public PaymentTransactionDetail updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public PaymentTransactionDetail entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public PaymentTransactionDetail localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentTransactionDetail paymentTransactionDetail = (PaymentTransactionDetail) o;
    return Objects.equals(this.actions, paymentTransactionDetail.actions) &&
        Objects.equals(this.amount, paymentTransactionDetail.amount) &&
        Objects.equals(this.createdBy, paymentTransactionDetail.createdBy) &&
        Objects.equals(this.createdTimestamp, paymentTransactionDetail.createdTimestamp) &&
        Objects.equals(this.extended, paymentTransactionDetail.extended) &&
        Objects.equals(this.localizedTo, paymentTransactionDetail.localizedTo) &&
        Objects.equals(this.messages, paymentTransactionDetail.messages) &&
        Objects.equals(this.orgId, paymentTransactionDetail.orgId) &&
        Objects.equals(this.PK, paymentTransactionDetail.PK) &&
        Objects.equals(this.paymentTransactionDetailId, paymentTransactionDetail.paymentTransactionDetailId) &&
        Objects.equals(this.referenceId, paymentTransactionDetail.referenceId) &&
        Objects.equals(this.referenceType, paymentTransactionDetail.referenceType) &&
        Objects.equals(this.updatedBy, paymentTransactionDetail.updatedBy) &&
        Objects.equals(this.updatedTimestamp, paymentTransactionDetail.updatedTimestamp) &&
        Objects.equals(this.entityName, paymentTransactionDetail.entityName) &&
        Objects.equals(this.localize, paymentTransactionDetail.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, amount, createdBy, createdTimestamp, extended, localizedTo, messages, orgId, PK, paymentTransactionDetailId, referenceId, referenceType, updatedBy, updatedTimestamp, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentTransactionDetail {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    amount: ").append(toIndentedString(amount)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    paymentTransactionDetailId: ").append(toIndentedString(paymentTransactionDetailId)).append("\n");
    sb.append("    referenceId: ").append(toIndentedString(referenceId)).append("\n");
    sb.append("    referenceType: ").append(toIndentedString(referenceType)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

