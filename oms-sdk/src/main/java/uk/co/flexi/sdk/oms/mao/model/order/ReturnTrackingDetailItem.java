package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import lombok.Data;


@Data
public class ReturnTrackingDetailItem {
    @SerializedName("CarrierCode")
    private String carrierCode;

    @SerializedName("ReturnTrackingNumber")
    private String returnTrackingNumber;

    @SerializedName("PackageStatus")
    private PackageStatus packageStatus;

    @SerializedName("Extended")
    private ReturnTrackingDetailExtended extended;


    @Data
    public static class PackageStatus {
        @SerializedName("PackageStatusId")
        private String packageStatusId;
    }

    public String getReturnTrackingNumber() {
        return returnTrackingNumber;
    }

    public PackageStatus getPackageStatus() {
        return packageStatus;
    }

    public ReturnTrackingDetailExtended getExtended() {
        return extended;
    }

    public String getCarrierCode() {
        return carrierCode;
    }
}
