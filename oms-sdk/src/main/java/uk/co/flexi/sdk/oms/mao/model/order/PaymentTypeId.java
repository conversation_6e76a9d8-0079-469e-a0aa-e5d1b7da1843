/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * PaymentTypeId
 */
public class PaymentTypeId {
  public static final String SERIALIZED_NAME_PAYMENT_TYPE_ID = "PaymentTypeId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_TYPE_ID)
  private String paymentTypeId;

  public PaymentTypeId paymentTypeId(String paymentTypeId) {
    this.paymentTypeId = paymentTypeId;
    return this;
  }

   /**
   * Unique identifier of the payment type. Used to identify the payment type in the payment methods, payment rule, and payment gateway response mapping.
   * @return paymentTypeId
  **/
  
  public String getPaymentTypeId() {
    return paymentTypeId;
  }

  public void setPaymentTypeId(String paymentTypeId) {
    this.paymentTypeId = paymentTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentTypeId paymentTypeId = (PaymentTypeId) o;
    return Objects.equals(this.paymentTypeId, paymentTypeId.paymentTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(paymentTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentTypeId {\n");
    
    sb.append("    paymentTypeId: ").append(toIndentedString(paymentTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

