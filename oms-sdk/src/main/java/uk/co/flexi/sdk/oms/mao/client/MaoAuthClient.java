package uk.co.flexi.sdk.oms.mao.client;

import com.google.gson.Gson;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Request.Builder;
import okhttp3.RequestBody;
import uk.co.flexi.sdk.oms.mao.client.interceptor.LoggingInterceptor;
import uk.co.flexi.sdk.oms.mao.model.auth.AuthResponse;

import java.util.Base64;

public class MaoAuthClient {

    private final String authUrl;
    private final String username;
    private final String password;

    private final String basicAuthUser;
    private final String basicAuthPassword;

    public MaoAuthClient(String authUrl, String basicAuthUser, String basicAuthPassword, String username, String password) {
        this.authUrl = authUrl;
        this.username = username;
        this.password = password;
        this.basicAuthUser = basicAuthUser;
        this.basicAuthPassword = basicAuthPassword;
    }

    public String uniqueAuthKey() {
        return authUrl + username;
    }

    public AuthResponse authorize()
    {
        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .addInterceptor(new LoggingInterceptor()).build();
            RequestBody formBody = new FormBody.Builder()
                    .add("username", username)
                    .add("password", password)
                    .add("grant_type", "password")
                    .build();
            Request request = new Builder()
                    .url(authUrl + "/oauth/token")
                    .addHeader("Authorization", "Basic " +
                            Base64.getEncoder().encodeToString(
                                    (basicAuthUser + ":" + basicAuthPassword).getBytes()))
                    .post(formBody)
                    .build();

            okhttp3.Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                return new Gson().fromJson(response.body().string(), AuthResponse.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
