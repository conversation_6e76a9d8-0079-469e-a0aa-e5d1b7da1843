package uk.co.flexi.sdk.oms.mao.model.order;


import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.Getter;

@Getter
@Data
public class FulfillmentDetailItem {

    @SerializedName("UOM")
    private String uOM;

    @SerializedName("TrackingNumber")
    private String trackingNumber;

    @SerializedName("Extended")
    private Extended extended;

    @SerializedName("Quantity")
    private Double quantity;

    @SerializedName("CarrierCode")
    private String carrierCode;

    @SerializedName("ReleaseId")
    private String releaseId;

    @SerializedName("PK")
    private String pK;

    @SerializedName("ItemId")
    private String itemId;

    @SerializedName("ReleaseLineId")
    private String releaseLineId;


}