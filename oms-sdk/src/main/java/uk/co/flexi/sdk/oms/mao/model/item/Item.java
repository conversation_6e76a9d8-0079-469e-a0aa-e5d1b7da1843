package uk.co.flexi.sdk.oms.mao.model.item;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.*;

/**
 * Item
 */
public class Item {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_BASE_U_O_M = "BaseUOM";
  @SerializedName(SERIALIZED_NAME_BASE_U_O_M)
  private String baseUOM;

  public static final String SERIALIZED_NAME_BRAND = "Brand";
  @SerializedName(SERIALIZED_NAME_BRAND)
  private String brand;

  public static final String SERIALIZED_NAME_COLOR = "Color";
  @SerializedName(SERIALIZED_NAME_COLOR)
  private String color;

  public static final String SERIALIZED_NAME_COLOR_IMAGE_U_R_I = "ColorImageURI";
  @SerializedName(SERIALIZED_NAME_COLOR_IMAGE_U_R_I)
  private String colorImageURI;

  public static final String SERIALIZED_NAME_COLOR_SORT_SEQUENCE = "ColorSortSequence";
  @SerializedName(SERIALIZED_NAME_COLOR_SORT_SEQUENCE)
  private Long colorSortSequence;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_DEPARTMENT_NAME = "DepartmentName";
  @SerializedName(SERIALIZED_NAME_DEPARTMENT_NAME)
  private String departmentName;

  public static final String SERIALIZED_NAME_DEPARTMENT_NUMBER = "DepartmentNumber";
  @SerializedName(SERIALIZED_NAME_DEPARTMENT_NUMBER)
  private String departmentNumber;

  public static final String SERIALIZED_NAME_DESCRIPTION = "Description";
  @SerializedName(SERIALIZED_NAME_DESCRIPTION)
  private String description;

  public static final String SERIALIZED_NAME_DIMENSION_U_O_M = "DimensionUOM";
  @SerializedName(SERIALIZED_NAME_DIMENSION_U_O_M)
  private String dimensionUOM;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_HANDLING_ATTRIBUTES = "HandlingAttributes";
  @SerializedName(SERIALIZED_NAME_HANDLING_ATTRIBUTES)
  private HandlingAttributes handlingAttributes = null;

  public static final String SERIALIZED_NAME_HEIGHT = "Height";
  @SerializedName(SERIALIZED_NAME_HEIGHT)
  private Double height;

  public static final String SERIALIZED_NAME_INVENTORY_PROTECTION = "InventoryProtection";
  @SerializedName(SERIALIZED_NAME_INVENTORY_PROTECTION)
  private InventoryProtection inventoryProtection = null;

  public static final String SERIALIZED_NAME_IS_DISCONTINUED = "IsDiscontinued";
  @SerializedName(SERIALIZED_NAME_IS_DISCONTINUED)
  private Boolean isDiscontinued;

  public static final String SERIALIZED_NAME_IS_GIFT_CARD = "IsGiftCard";
  @SerializedName(SERIALIZED_NAME_IS_GIFT_CARD)
  private Boolean isGiftCard;

  public static final String SERIALIZED_NAME_IS_GIFTWITH_PURCHASE = "IsGiftwithPurchase";
  @SerializedName(SERIALIZED_NAME_IS_GIFTWITH_PURCHASE)
  private Boolean isGiftwithPurchase;

  public static final String SERIALIZED_NAME_IS_NON_MERCHANDISE = "IsNonMerchandise";
  @SerializedName(SERIALIZED_NAME_IS_NON_MERCHANDISE)
  private Boolean isNonMerchandise;

  public static final String SERIALIZED_NAME_IS_RECALLED = "IsRecalled";
  @SerializedName(SERIALIZED_NAME_IS_RECALLED)
  private Boolean isRecalled;

  public static final String SERIALIZED_NAME_IS_SCAN_ONLY = "IsScanOnly";
  @SerializedName(SERIALIZED_NAME_IS_SCAN_ONLY)
  private Boolean isScanOnly;

  public static final String SERIALIZED_NAME_ITEM_ATTRIBUTE = "ItemAttribute";
  @SerializedName(SERIALIZED_NAME_ITEM_ATTRIBUTE)
  private List<ItemAttribute> itemAttribute = null;

  public static final String SERIALIZED_NAME_ITEM_ATTRIBUTES = "ItemAttributes";
  @SerializedName(SERIALIZED_NAME_ITEM_ATTRIBUTES)
  private Object itemAttributes = null;

  public static final String SERIALIZED_NAME_ITEM_CODE = "ItemCode";
  @SerializedName(SERIALIZED_NAME_ITEM_CODE)
  private List<ItemCode> itemCode = null;

  public static final String SERIALIZED_NAME_ITEM_EXTENSION1 = "ItemExtension1";
  @SerializedName(SERIALIZED_NAME_ITEM_EXTENSION1)
  private List<ItemExtension1> itemExtension1 = null;

  public static final String SERIALIZED_NAME_ITEM_ID = "ItemId";
  @SerializedName(SERIALIZED_NAME_ITEM_ID)
  private String itemId;

  public static final String SERIALIZED_NAME_ITEM_INSTRUCTION = "ItemInstruction";
  @SerializedName(SERIALIZED_NAME_ITEM_INSTRUCTION)
  private List<ItemInstruction> itemInstruction = null;

  public static final String SERIALIZED_NAME_ITEM_STATUS_ID = "ItemStatusId";
  @SerializedName(SERIALIZED_NAME_ITEM_STATUS_ID)
  private String itemStatusId;

  public static final String SERIALIZED_NAME_LARGE_IMAGE_U_R_I = "LargeImageURI";
  @SerializedName(SERIALIZED_NAME_LARGE_IMAGE_U_R_I)
  private String largeImageURI;

  public static final String SERIALIZED_NAME_LENGTH = "Length";
  @SerializedName(SERIALIZED_NAME_LENGTH)
  private Double length;

  public static final String SERIALIZED_NAME_LOCALIZED_DATA = "LocalizedData";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_DATA)
  private List<ItemTranslation> localizedData = null;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MANUFACTURING_ATTRIBUTE = "ManufacturingAttribute";
  @SerializedName(SERIALIZED_NAME_MANUFACTURING_ATTRIBUTE)
  private List<ManufacturingAttribute> manufacturingAttribute = null;

  public static final String SERIALIZED_NAME_MEDIA = "Media";
  @SerializedName(SERIALIZED_NAME_MEDIA)
  private List<Media> media = null;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PRODUCT_CLASS = "ProductClass";
  @SerializedName(SERIALIZED_NAME_PRODUCT_CLASS)
  private String productClass;

  public static final String SERIALIZED_NAME_PRODUCT_SUB_CLASS = "ProductSubClass";
  @SerializedName(SERIALIZED_NAME_PRODUCT_SUB_CLASS)
  private String productSubClass;

  public static final String SERIALIZED_NAME_PROFILE_ID = "ProfileId";
  @SerializedName(SERIALIZED_NAME_PROFILE_ID)
  private String profileId;

  public static final String SERIALIZED_NAME_REASON_ID = "ReasonId";
  @SerializedName(SERIALIZED_NAME_REASON_ID)
  private String reasonId;

  public static final String SERIALIZED_NAME_RECOMMENDED_PRODUCT = "RecommendedProduct";
  @SerializedName(SERIALIZED_NAME_RECOMMENDED_PRODUCT)
  private List<RecommendedProduct> recommendedProduct = null;

  public static final String SERIALIZED_NAME_SEASON = "Season";
  @SerializedName(SERIALIZED_NAME_SEASON)
  private String season;

  public static final String SERIALIZED_NAME_SEASON_YEAR = "SeasonYear";
  @SerializedName(SERIALIZED_NAME_SEASON_YEAR)
  private Integer seasonYear;

  public static final String SERIALIZED_NAME_SELLING_ATTRIBUTES = "SellingAttributes";
  @SerializedName(SERIALIZED_NAME_SELLING_ATTRIBUTES)
  private SellingAttributes sellingAttributes = null;

  public static final String SERIALIZED_NAME_SHORT_DESCRIPTION = "ShortDescription";
  @SerializedName(SERIALIZED_NAME_SHORT_DESCRIPTION)
  private String shortDescription;

  public static final String SERIALIZED_NAME_SIZE = "Size";
  @SerializedName(SERIALIZED_NAME_SIZE)
  private String size;

  public static final String SERIALIZED_NAME_SIZE_SORT_SEQUENCE = "SizeSortSequence";
  @SerializedName(SERIALIZED_NAME_SIZE_SORT_SEQUENCE)
  private Long sizeSortSequence;

  public static final String SERIALIZED_NAME_SMALL_IMAGE_U_R_I = "SmallImageURI";
  @SerializedName(SERIALIZED_NAME_SMALL_IMAGE_U_R_I)
  private String smallImageURI;

  public static final String SERIALIZED_NAME_STORE_DEPARTMENT = "StoreDepartment";
  @SerializedName(SERIALIZED_NAME_STORE_DEPARTMENT)
  private String storeDepartment;

  public static final String SERIALIZED_NAME_STYLE = "Style";
  @SerializedName(SERIALIZED_NAME_STYLE)
  private String style;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_VALUE_ADDED_SERVICES = "ValueAddedServices";
  @SerializedName(SERIALIZED_NAME_VALUE_ADDED_SERVICES)
  private List<ValueAddedServices> valueAddedServices = null;

  public static final String SERIALIZED_NAME_VOLUME = "Volume";
  @SerializedName(SERIALIZED_NAME_VOLUME)
  private Double volume;

  public static final String SERIALIZED_NAME_VOLUME_U_O_M = "VolumeUOM";
  @SerializedName(SERIALIZED_NAME_VOLUME_U_O_M)
  private String volumeUOM;

  public static final String SERIALIZED_NAME_VOLUMETRIC_WEIGHT = "VolumetricWeight";
  @SerializedName(SERIALIZED_NAME_VOLUMETRIC_WEIGHT)
  private Double volumetricWeight;

  public static final String SERIALIZED_NAME_VOLUMETRIC_WEIGHT_U_O_M = "VolumetricWeightUOM";
  @SerializedName(SERIALIZED_NAME_VOLUMETRIC_WEIGHT_U_O_M)
  private UOMId volumetricWeightUOM = null;

  public static final String SERIALIZED_NAME_WEB_U_R_L = "WebURL";
  @SerializedName(SERIALIZED_NAME_WEB_U_R_L)
  private String webURL;

  public static final String SERIALIZED_NAME_WEIGHT = "Weight";
  @SerializedName(SERIALIZED_NAME_WEIGHT)
  private Double weight;

  public static final String SERIALIZED_NAME_WEIGHT_U_O_M = "WeightUOM";
  @SerializedName(SERIALIZED_NAME_WEIGHT_U_O_M)
  private String weightUOM;

  public static final String SERIALIZED_NAME_WIDTH = "Width";
  @SerializedName(SERIALIZED_NAME_WIDTH)
  private Double width;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public Item actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public Item putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

  /**
   * Get actions
   * @return actions
   **/
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public Item baseUOM(String baseUOM) {
    this.baseUOM = baseUOM;
    return this;
  }

  /**
   * Localized. Base unit of measure (UOM) of item
   * @return baseUOM
   **/
  public String getBaseUOM() {
    return baseUOM;
  }

  public void setBaseUOM(String baseUOM) {
    this.baseUOM = baseUOM;
  }

  public Item brand(String brand) {
    this.brand = brand;
    return this;
  }

  /**
   * Localized. Item brand
   * @return brand
   **/
  public String getBrand() {
    return brand;
  }

  public void setBrand(String brand) {
    this.brand = brand;
  }

  public Item color(String color) {
    this.color = color;
    return this;
  }

  /**
   * Localized. Item color
   * @return color
   **/
  public String getColor() {
    return color;
  }

  public void setColor(String color) {
    this.color = color;
  }

  public Item colorImageURI(String colorImageURI) {
    this.colorImageURI = colorImageURI;
    return this;
  }

  /**
   * Localized. Color image URI of the item.
   * @return colorImageURI
   **/
  public String getColorImageURI() {
    return colorImageURI;
  }

  public void setColorImageURI(String colorImageURI) {
    this.colorImageURI = colorImageURI;
  }

  public Item colorSortSequence(Long colorSortSequence) {
    this.colorSortSequence = colorSortSequence;
    return this;
  }

  /**
   * Sequence in which color is displayed
   * minimum: 0
   * maximum: -8446744073709551617
   * @return colorSortSequence
   **/
  public Long getColorSortSequence() {
    return colorSortSequence;
  }

  public void setColorSortSequence(Long colorSortSequence) {
    this.colorSortSequence = colorSortSequence;
  }

  public Item createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

  /**
   * Created By
   * @return createdBy
   **/
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public Item createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

  /**
   * Created Timestamp
   * @return createdTimestamp
   **/
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public Item departmentName(String departmentName) {
    this.departmentName = departmentName;
    return this;
  }

  /**
   * Localized. Item&#39;s department name.
   * @return departmentName
   **/
  public String getDepartmentName() {
    return departmentName;
  }

  public void setDepartmentName(String departmentName) {
    this.departmentName = departmentName;
  }

  public Item departmentNumber(String departmentNumber) {
    this.departmentNumber = departmentNumber;
    return this;
  }

  /**
   * Item&#39;s department number.
   * @return departmentNumber
   **/
  public String getDepartmentNumber() {
    return departmentNumber;
  }

  public void setDepartmentNumber(String departmentNumber) {
    this.departmentNumber = departmentNumber;
  }

  public Item description(String description) {
    this.description = description;
    return this;
  }

  /**
   * Localized. Item description
   * @return description
   **/
  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public Item dimensionUOM(String dimensionUOM) {
    this.dimensionUOM = dimensionUOM;
    return this;
  }

  /**
   * Localized. Required if Length, Width, or Height is not null
   * @return dimensionUOM
   **/
  public String getDimensionUOM() {
    return dimensionUOM;
  }

  public void setDimensionUOM(String dimensionUOM) {
    this.dimensionUOM = dimensionUOM;
  }

  public Item extended(Object extended) {
    this.extended = extended;
    return this;
  }

  /**
   * Extended Properties
   * @return extended
   **/
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public Item handlingAttributes(HandlingAttributes handlingAttributes) {
    this.handlingAttributes = handlingAttributes;
    return this;
  }

  /**
   * Get handlingAttributes
   * @return handlingAttributes
   **/
  public HandlingAttributes getHandlingAttributes() {
    return handlingAttributes;
  }

  public void setHandlingAttributes(HandlingAttributes handlingAttributes) {
    this.handlingAttributes = handlingAttributes;
  }

  public Item height(Double height) {
    this.height = height;
    return this;
  }

  /**
   * Indicates raw item dimension (unpackaged)
   * minimum: 0
   * maximum: 999999999999.9999
   * @return height
   **/
  public Double getHeight() {
    return height;
  }

  public void setHeight(Double height) {
    this.height = height;
  }

  public Item inventoryProtection(InventoryProtection inventoryProtection) {
    this.inventoryProtection = inventoryProtection;
    return this;
  }

  /**
   * Get inventoryProtection
   * @return inventoryProtection
   **/
  public InventoryProtection getInventoryProtection() {
    return inventoryProtection;
  }

  public void setInventoryProtection(InventoryProtection inventoryProtection) {
    this.inventoryProtection = inventoryProtection;
  }

  public Item isDiscontinued(Boolean isDiscontinued) {
    this.isDiscontinued = isDiscontinued;
    return this;
  }

  /**
   * Indicates if item should be put in the queue or not
   * @return isDiscontinued
   **/
  public Boolean getIsDiscontinued() {
    return isDiscontinued;
  }

  public void setIsDiscontinued(Boolean isDiscontinued) {
    this.isDiscontinued = isDiscontinued;
  }

  public Item isGiftCard(Boolean isGiftCard) {
    this.isGiftCard = isGiftCard;
    return this;
  }

  /**
   * Indicates whether the item is a gift card or not
   * @return isGiftCard
   **/
  public Boolean getIsGiftCard() {
    return isGiftCard;
  }

  public void setIsGiftCard(Boolean isGiftCard) {
    this.isGiftCard = isGiftCard;
  }

  public Item isGiftwithPurchase(Boolean isGiftwithPurchase) {
    this.isGiftwithPurchase = isGiftwithPurchase;
    return this;
  }

  /**
   * Indicates if item is a gift with purchase
   * @return isGiftwithPurchase
   **/
  public Boolean getIsGiftwithPurchase() {
    return isGiftwithPurchase;
  }

  public void setIsGiftwithPurchase(Boolean isGiftwithPurchase) {
    this.isGiftwithPurchase = isGiftwithPurchase;
  }

  public Item isNonMerchandise(Boolean isNonMerchandise) {
    this.isNonMerchandise = isNonMerchandise;
    return this;
  }

  /**
   * Indicates if item is non-merchandise
   * @return isNonMerchandise
   **/
  public Boolean getIsNonMerchandise() {
    return isNonMerchandise;
  }

  public void setIsNonMerchandise(Boolean isNonMerchandise) {
    this.isNonMerchandise = isNonMerchandise;
  }

  public Item isRecalled(Boolean isRecalled) {
    this.isRecalled = isRecalled;
    return this;
  }

  /**
   * Indicates if the item has been recalled
   * @return isRecalled
   **/
  public Boolean getIsRecalled() {
    return isRecalled;
  }

  public void setIsRecalled(Boolean isRecalled) {
    this.isRecalled = isRecalled;
  }

  public Item isScanOnly(Boolean isScanOnly) {
    this.isScanOnly = isScanOnly;
    return this;
  }

  /**
   * Indicates if item can be scanned
   * @return isScanOnly
   **/
  public Boolean getIsScanOnly() {
    return isScanOnly;
  }

  public void setIsScanOnly(Boolean isScanOnly) {
    this.isScanOnly = isScanOnly;
  }

  public Item itemAttribute(List<ItemAttribute> itemAttribute) {
    this.itemAttribute = itemAttribute;
    return this;
  }

  public Item addItemAttributeItem(ItemAttribute itemAttributeItem) {
    if (this.itemAttribute == null) {
      this.itemAttribute = new ArrayList<ItemAttribute>();
    }
    this.itemAttribute.add(itemAttributeItem);
    return this;
  }

  /**
   * Get itemAttribute
   * @return itemAttribute
   **/
  public List<ItemAttribute> getItemAttribute() {
    return itemAttribute;
  }

  public void setItemAttribute(List<ItemAttribute> itemAttribute) {
    this.itemAttribute = itemAttribute;
  }

  public Item itemAttributes(Object itemAttributes) {
    this.itemAttributes = itemAttributes;
    return this;
  }

  /**
   * ItemAttribute custom structure
   * @return itemAttributes
   **/
  public Object getItemAttributes() {
    return itemAttributes;
  }

  public void setItemAttributes(Object itemAttributes) {
    this.itemAttributes = itemAttributes;
  }

  public Item itemCode(List<ItemCode> itemCode) {
    this.itemCode = itemCode;
    return this;
  }

  public Item addItemCodeItem(ItemCode itemCodeItem) {
    if (this.itemCode == null) {
      this.itemCode = new ArrayList<ItemCode>();
    }
    this.itemCode.add(itemCodeItem);
    return this;
  }

  /**
   * Get itemCode
   * @return itemCode
   **/
  public List<ItemCode> getItemCode() {
    return itemCode;
  }

  public void setItemCode(List<ItemCode> itemCode) {
    this.itemCode = itemCode;
  }

  public Item itemExtension1(List<ItemExtension1> itemExtension1) {
    this.itemExtension1 = itemExtension1;
    return this;
  }

  public Item addItemExtension1Item(ItemExtension1 itemExtension1Item) {
    if (this.itemExtension1 == null) {
      this.itemExtension1 = new ArrayList<ItemExtension1>();
    }
    this.itemExtension1.add(itemExtension1Item);
    return this;
  }

  /**
   * Get itemExtension1
   * @return itemExtension1
   **/
  public List<ItemExtension1> getItemExtension1() {
    return itemExtension1;
  }

  public void setItemExtension1(List<ItemExtension1> itemExtension1) {
    this.itemExtension1 = itemExtension1;
  }

  public Item itemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

  /**
   * Unique identifier of the item.
   * @return itemId
   **/
  public String getItemId() {
    return itemId;
  }

  public void setItemId(String itemId) {
    this.itemId = itemId;
  }

  public Item itemInstruction(List<ItemInstruction> itemInstruction) {
    this.itemInstruction = itemInstruction;
    return this;
  }

  public Item addItemInstructionItem(ItemInstruction itemInstructionItem) {
    if (this.itemInstruction == null) {
      this.itemInstruction = new ArrayList<ItemInstruction>();
    }
    this.itemInstruction.add(itemInstructionItem);
    return this;
  }

  /**
   * Get itemInstruction
   * @return itemInstruction
   **/
  public List<ItemInstruction> getItemInstruction() {
    return itemInstruction;
  }

  public void setItemInstruction(List<ItemInstruction> itemInstruction) {
    this.itemInstruction = itemInstruction;
  }

  public Item itemStatusId(String itemStatusId) {
    this.itemStatusId = itemStatusId;
    return this;
  }

  /**
   * Indicates the item status of an item
   * @return itemStatusId
   **/
  public String getItemStatusId() {
    return itemStatusId;
  }

  public void setItemStatusId(String itemStatusId) {
    this.itemStatusId = itemStatusId;
  }

  public Item largeImageURI(String largeImageURI) {
    this.largeImageURI = largeImageURI;
    return this;
  }

  /**
   * Localized. URI of the large image for the item
   * @return largeImageURI
   **/
  public String getLargeImageURI() {
    return largeImageURI;
  }

  public void setLargeImageURI(String largeImageURI) {
    this.largeImageURI = largeImageURI;
  }

  public Item length(Double length) {
    this.length = length;
    return this;
  }

  /**
   * Indicates raw item dimension (unpackaged)
   * minimum: 0
   * maximum: 999999999999.9999
   * @return length
   **/
  public Double getLength() {
    return length;
  }

  public void setLength(Double length) {
    this.length = length;
  }

  public Item localizedData(List<ItemTranslation> localizedData) {
    this.localizedData = localizedData;
    return this;
  }

  public Item addLocalizedDataItem(ItemTranslation localizedDataItem) {
    if (this.localizedData == null) {
      this.localizedData = new ArrayList<ItemTranslation>();
    }
    this.localizedData.add(localizedDataItem);
    return this;
  }

  /**
   * Get localizedData
   * @return localizedData
   **/
  public List<ItemTranslation> getLocalizedData() {
    return localizedData;
  }

  public void setLocalizedData(List<ItemTranslation> localizedData) {
    this.localizedData = localizedData;
  }

  public Item localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

  /**
   * Get localizedTo
   * @return localizedTo
   **/
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public Item manufacturingAttribute(List<ManufacturingAttribute> manufacturingAttribute) {
    this.manufacturingAttribute = manufacturingAttribute;
    return this;
  }

  public Item addManufacturingAttributeItem(ManufacturingAttribute manufacturingAttributeItem) {
    if (this.manufacturingAttribute == null) {
      this.manufacturingAttribute = new ArrayList<ManufacturingAttribute>();
    }
    this.manufacturingAttribute.add(manufacturingAttributeItem);
    return this;
  }

  /**
   * Get manufacturingAttribute
   * @return manufacturingAttribute
   **/
  public List<ManufacturingAttribute> getManufacturingAttribute() {
    return manufacturingAttribute;
  }

  public void setManufacturingAttribute(List<ManufacturingAttribute> manufacturingAttribute) {
    this.manufacturingAttribute = manufacturingAttribute;
  }

  public Item media(List<Media> media) {
    this.media = media;
    return this;
  }

  public Item addMediaItem(Media mediaItem) {
    if (this.media == null) {
      this.media = new ArrayList<Media>();
    }
    this.media.add(mediaItem);
    return this;
  }

  /**
   * Get media
   * @return media
   **/
  public List<Media> getMedia() {
    return media;
  }

  public void setMedia(List<Media> media) {
    this.media = media;
  }

  public Item messages(Messages messages) {
    this.messages = messages;
    return this;
  }

  /**
   * Get messages
   * @return messages
   **/
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public Item PK(String PK) {
    this.PK = PK;
    return this;
  }

  /**
   * Primary Key
   * @return PK
   **/
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public Item productClass(String productClass) {
    this.productClass = productClass;
    return this;
  }

  /**
   * Localized. Used to categorize a group of item
   * @return productClass
   **/
  public String getProductClass() {
    return productClass;
  }

  public void setProductClass(String productClass) {
    this.productClass = productClass;
  }

  public Item productSubClass(String productSubClass) {
    this.productSubClass = productSubClass;
    return this;
  }

  /**
   * Localized. The sub class to which it belongs
   * @return productSubClass
   **/
  public String getProductSubClass() {
    return productSubClass;
  }

  public void setProductSubClass(String productSubClass) {
    this.productSubClass = productSubClass;
  }

  public Item profileId(String profileId) {
    this.profileId = profileId;
    return this;
  }

  /**
   * Profile Id
   * @return profileId
   **/
  public String getProfileId() {
    return profileId;
  }

  public void setProfileId(String profileId) {
    this.profileId = profileId;
  }

  public Item reasonId(String reasonId) {
    this.reasonId = reasonId;
    return this;
  }

  /**
   * Reason for the item recall
   * @return reasonId
   **/
  public String getReasonId() {
    return reasonId;
  }

  public void setReasonId(String reasonId) {
    this.reasonId = reasonId;
  }

  public Item recommendedProduct(List<RecommendedProduct> recommendedProduct) {
    this.recommendedProduct = recommendedProduct;
    return this;
  }

  public Item addRecommendedProductItem(RecommendedProduct recommendedProductItem) {
    if (this.recommendedProduct == null) {
      this.recommendedProduct = new ArrayList<RecommendedProduct>();
    }
    this.recommendedProduct.add(recommendedProductItem);
    return this;
  }

  /**
   * Get recommendedProduct
   * @return recommendedProduct
   **/
  public List<RecommendedProduct> getRecommendedProduct() {
    return recommendedProduct;
  }

  public void setRecommendedProduct(List<RecommendedProduct> recommendedProduct) {
    this.recommendedProduct = recommendedProduct;
  }

  public Item season(String season) {
    this.season = season;
    return this;
  }

  /**
   * Localized. Item season
   * @return season
   **/
  public String getSeason() {
    return season;
  }

  public void setSeason(String season) {
    this.season = season;
  }

  public Item seasonYear(Integer seasonYear) {
    this.seasonYear = seasonYear;
    return this;
  }

  /**
   * Season year
   * minimum: 0
   * maximum: 99999
   * @return seasonYear
   **/
  public Integer getSeasonYear() {
    return seasonYear;
  }

  public void setSeasonYear(Integer seasonYear) {
    this.seasonYear = seasonYear;
  }

  public Item sellingAttributes(SellingAttributes sellingAttributes) {
    this.sellingAttributes = sellingAttributes;
    return this;
  }

  /**
   * Get sellingAttributes
   * @return sellingAttributes
   **/
  public SellingAttributes getSellingAttributes() {
    return sellingAttributes;
  }

  public void setSellingAttributes(SellingAttributes sellingAttributes) {
    this.sellingAttributes = sellingAttributes;
  }

  public Item shortDescription(String shortDescription) {
    this.shortDescription = shortDescription;
    return this;
  }

  /**
   * Localized. Short description
   * @return shortDescription
   **/
  public String getShortDescription() {
    return shortDescription;
  }

  public void setShortDescription(String shortDescription) {
    this.shortDescription = shortDescription;
  }

  public Item size(String size) {
    this.size = size;
    return this;
  }

  /**
   * Localized. Item size
   * @return size
   **/
  public String getSize() {
    return size;
  }

  public void setSize(String size) {
    this.size = size;
  }

  public Item sizeSortSequence(Long sizeSortSequence) {
    this.sizeSortSequence = sizeSortSequence;
    return this;
  }

  /**
   * Sequence in which size is displayed
   * minimum: 0
   * maximum: -8446744073709551617
   * @return sizeSortSequence
   **/
  public Long getSizeSortSequence() {
    return sizeSortSequence;
  }

  public void setSizeSortSequence(Long sizeSortSequence) {
    this.sizeSortSequence = sizeSortSequence;
  }

  public Item smallImageURI(String smallImageURI) {
    this.smallImageURI = smallImageURI;
    return this;
  }

  /**
   * Localized. URI of the small image for the item
   * @return smallImageURI
   **/
  public String getSmallImageURI() {
    return smallImageURI;
  }

  public void setSmallImageURI(String smallImageURI) {
    this.smallImageURI = smallImageURI;
  }

  public Item storeDepartment(String storeDepartment) {
    this.storeDepartment = storeDepartment;
    return this;
  }

  /**
   * Localized. Item department
   * @return storeDepartment
   **/
  public String getStoreDepartment() {
    return storeDepartment;
  }

  public void setStoreDepartment(String storeDepartment) {
    this.storeDepartment = storeDepartment;
  }

  public Item style(String style) {
    this.style = style;
    return this;
  }

  /**
   * Localized. Item style
   * @return style
   **/
  public String getStyle() {
    return style;
  }

  public void setStyle(String style) {
    this.style = style;
  }

  public Item updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

  /**
   * Updated By
   * @return updatedBy
   **/
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public Item updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

  /**
   * Updated Timestamp
   * @return updatedTimestamp
   **/
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public Item valueAddedServices(List<ValueAddedServices> valueAddedServices) {
    this.valueAddedServices = valueAddedServices;
    return this;
  }

  public Item addValueAddedServicesItem(ValueAddedServices valueAddedServicesItem) {
    if (this.valueAddedServices == null) {
      this.valueAddedServices = new ArrayList<ValueAddedServices>();
    }
    this.valueAddedServices.add(valueAddedServicesItem);
    return this;
  }

  /**
   * Get valueAddedServices
   * @return valueAddedServices
   **/
  public List<ValueAddedServices> getValueAddedServices() {
    return valueAddedServices;
  }

  public void setValueAddedServices(List<ValueAddedServices> valueAddedServices) {
    this.valueAddedServices = valueAddedServices;
  }

  public Item volume(Double volume) {
    this.volume = volume;
    return this;
  }

  /**
   * Indicates raw item volume (unpackaged)
   * minimum: 0
   * maximum: 999999999999.9999
   * @return volume
   **/
  public Double getVolume() {
    return volume;
  }

  public void setVolume(Double volume) {
    this.volume = volume;
  }

  public Item volumeUOM(String volumeUOM) {
    this.volumeUOM = volumeUOM;
    return this;
  }

  /**
   * Localized. Required if Volume is not null
   * @return volumeUOM
   **/
  public String getVolumeUOM() {
    return volumeUOM;
  }

  public void setVolumeUOM(String volumeUOM) {
    this.volumeUOM = volumeUOM;
  }

  public Item volumetricWeight(Double volumetricWeight) {
    this.volumetricWeight = volumetricWeight;
    return this;
  }

  /**
   * It is used to calculate Shipping and Handling charges when the rate basis for Shipping and Handling charges calculation is configured as Volumetric weight
   * minimum: 0
   * maximum: 999999999999.9999
   * @return volumetricWeight
   **/
  public Double getVolumetricWeight() {
    return volumetricWeight;
  }

  public void setVolumetricWeight(Double volumetricWeight) {
    this.volumetricWeight = volumetricWeight;
  }

  public Item volumetricWeightUOM(UOMId volumetricWeightUOM) {
    this.volumetricWeightUOM = volumetricWeightUOM;
    return this;
  }

  /**
   * Get volumetricWeightUOM
   * @return volumetricWeightUOM
   **/
  public UOMId getVolumetricWeightUOM() {
    return volumetricWeightUOM;
  }

  public void setVolumetricWeightUOM(UOMId volumetricWeightUOM) {
    this.volumetricWeightUOM = volumetricWeightUOM;
  }

  public Item webURL(String webURL) {
    this.webURL = webURL;
    return this;
  }

  /**
   * Localized. URL of the web page for the item
   * @return webURL
   **/
  public String getWebURL() {
    return webURL;
  }

  public void setWebURL(String webURL) {
    this.webURL = webURL;
  }

  public Item weight(Double weight) {
    this.weight = weight;
    return this;
  }

  /**
   * Indicates raw item weight (unpackaged)
   * minimum: 0
   * maximum: 999999999999.9999
   * @return weight
   **/
  public Double getWeight() {
    return weight;
  }

  public void setWeight(Double weight) {
    this.weight = weight;
  }

  public Item weightUOM(String weightUOM) {
    this.weightUOM = weightUOM;
    return this;
  }

  /**
   * Localized. Required if Weight is not null
   * @return weightUOM
   **/
  public String getWeightUOM() {
    return weightUOM;
  }

  public void setWeightUOM(String weightUOM) {
    this.weightUOM = weightUOM;
  }

  public Item width(Double width) {
    this.width = width;
    return this;
  }

  /**
   * Indicates raw item dimension (unpackaged)
   * minimum: 0
   * maximum: 999999999999.9999
   * @return width
   **/
  public Double getWidth() {
    return width;
  }

  public void setWidth(Double width) {
    this.width = width;
  }

  public Item entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

  /**
   * Get entityName
   * @return entityName
   **/
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public Item localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

  /**
   * Get localize
   * @return localize
   **/
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Item item = (Item) o;
    return Objects.equals(this.actions, item.actions) &&
        Objects.equals(this.baseUOM, item.baseUOM) &&
        Objects.equals(this.brand, item.brand) &&
        Objects.equals(this.color, item.color) &&
        Objects.equals(this.colorImageURI, item.colorImageURI) &&
        Objects.equals(this.colorSortSequence, item.colorSortSequence) &&
        Objects.equals(this.createdBy, item.createdBy) &&
        Objects.equals(this.createdTimestamp, item.createdTimestamp) &&
        Objects.equals(this.departmentName, item.departmentName) &&
        Objects.equals(this.departmentNumber, item.departmentNumber) &&
        Objects.equals(this.description, item.description) &&
        Objects.equals(this.dimensionUOM, item.dimensionUOM) &&
        Objects.equals(this.extended, item.extended) &&
        Objects.equals(this.handlingAttributes, item.handlingAttributes) &&
        Objects.equals(this.height, item.height) &&
        Objects.equals(this.inventoryProtection, item.inventoryProtection) &&
        Objects.equals(this.isDiscontinued, item.isDiscontinued) &&
        Objects.equals(this.isGiftCard, item.isGiftCard) &&
        Objects.equals(this.isGiftwithPurchase, item.isGiftwithPurchase) &&
        Objects.equals(this.isNonMerchandise, item.isNonMerchandise) &&
        Objects.equals(this.isRecalled, item.isRecalled) &&
        Objects.equals(this.isScanOnly, item.isScanOnly) &&
        Objects.equals(this.itemAttribute, item.itemAttribute) &&
        Objects.equals(this.itemAttributes, item.itemAttributes) &&
        Objects.equals(this.itemCode, item.itemCode) &&
        Objects.equals(this.itemExtension1, item.itemExtension1) &&
        Objects.equals(this.itemId, item.itemId) &&
        Objects.equals(this.itemInstruction, item.itemInstruction) &&
        Objects.equals(this.itemStatusId, item.itemStatusId) &&
        Objects.equals(this.largeImageURI, item.largeImageURI) &&
        Objects.equals(this.length, item.length) &&
        Objects.equals(this.localizedData, item.localizedData) &&
        Objects.equals(this.localizedTo, item.localizedTo) &&
        Objects.equals(this.manufacturingAttribute, item.manufacturingAttribute) &&
        Objects.equals(this.media, item.media) &&
        Objects.equals(this.messages, item.messages) &&
        Objects.equals(this.PK, item.PK) &&
        Objects.equals(this.productClass, item.productClass) &&
        Objects.equals(this.productSubClass, item.productSubClass) &&
        Objects.equals(this.profileId, item.profileId) &&
        Objects.equals(this.reasonId, item.reasonId) &&
        Objects.equals(this.recommendedProduct, item.recommendedProduct) &&
        Objects.equals(this.season, item.season) &&
        Objects.equals(this.seasonYear, item.seasonYear) &&
        Objects.equals(this.sellingAttributes, item.sellingAttributes) &&
        Objects.equals(this.shortDescription, item.shortDescription) &&
        Objects.equals(this.size, item.size) &&
        Objects.equals(this.sizeSortSequence, item.sizeSortSequence) &&
        Objects.equals(this.smallImageURI, item.smallImageURI) &&
        Objects.equals(this.storeDepartment, item.storeDepartment) &&
        Objects.equals(this.style, item.style) &&
        Objects.equals(this.updatedBy, item.updatedBy) &&
        Objects.equals(this.updatedTimestamp, item.updatedTimestamp) &&
        Objects.equals(this.valueAddedServices, item.valueAddedServices) &&
        Objects.equals(this.volume, item.volume) &&
        Objects.equals(this.volumeUOM, item.volumeUOM) &&
        Objects.equals(this.volumetricWeight, item.volumetricWeight) &&
        Objects.equals(this.volumetricWeightUOM, item.volumetricWeightUOM) &&
        Objects.equals(this.webURL, item.webURL) &&
        Objects.equals(this.weight, item.weight) &&
        Objects.equals(this.weightUOM, item.weightUOM) &&
        Objects.equals(this.width, item.width) &&
        Objects.equals(this.entityName, item.entityName) &&
        Objects.equals(this.localize, item.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, baseUOM, brand, color, colorImageURI, colorSortSequence, createdBy, createdTimestamp, departmentName, departmentNumber, description, dimensionUOM, extended, handlingAttributes, height, inventoryProtection, isDiscontinued, isGiftCard, isGiftwithPurchase, isNonMerchandise, isRecalled, isScanOnly, itemAttribute, itemAttributes, itemCode, itemExtension1, itemId, itemInstruction, itemStatusId, largeImageURI, length, localizedData, localizedTo, manufacturingAttribute, media, messages, PK, productClass, productSubClass, profileId, reasonId, recommendedProduct, season, seasonYear, sellingAttributes, shortDescription, size, sizeSortSequence, smallImageURI, storeDepartment, style, updatedBy, updatedTimestamp, valueAddedServices, volume, volumeUOM, volumetricWeight, volumetricWeightUOM, webURL, weight, weightUOM, width, entityName, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Item {\n");

    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    baseUOM: ").append(toIndentedString(baseUOM)).append("\n");
    sb.append("    brand: ").append(toIndentedString(brand)).append("\n");
    sb.append("    color: ").append(toIndentedString(color)).append("\n");
    sb.append("    colorImageURI: ").append(toIndentedString(colorImageURI)).append("\n");
    sb.append("    colorSortSequence: ").append(toIndentedString(colorSortSequence)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    departmentName: ").append(toIndentedString(departmentName)).append("\n");
    sb.append("    departmentNumber: ").append(toIndentedString(departmentNumber)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    dimensionUOM: ").append(toIndentedString(dimensionUOM)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    handlingAttributes: ").append(toIndentedString(handlingAttributes)).append("\n");
    sb.append("    height: ").append(toIndentedString(height)).append("\n");
    sb.append("    inventoryProtection: ").append(toIndentedString(inventoryProtection)).append("\n");
    sb.append("    isDiscontinued: ").append(toIndentedString(isDiscontinued)).append("\n");
    sb.append("    isGiftCard: ").append(toIndentedString(isGiftCard)).append("\n");
    sb.append("    isGiftwithPurchase: ").append(toIndentedString(isGiftwithPurchase)).append("\n");
    sb.append("    isNonMerchandise: ").append(toIndentedString(isNonMerchandise)).append("\n");
    sb.append("    isRecalled: ").append(toIndentedString(isRecalled)).append("\n");
    sb.append("    isScanOnly: ").append(toIndentedString(isScanOnly)).append("\n");
    sb.append("    itemAttribute: ").append(toIndentedString(itemAttribute)).append("\n");
    sb.append("    itemAttributes: ").append(toIndentedString(itemAttributes)).append("\n");
    sb.append("    itemCode: ").append(toIndentedString(itemCode)).append("\n");
    sb.append("    itemExtension1: ").append(toIndentedString(itemExtension1)).append("\n");
    sb.append("    itemId: ").append(toIndentedString(itemId)).append("\n");
    sb.append("    itemInstruction: ").append(toIndentedString(itemInstruction)).append("\n");
    sb.append("    itemStatusId: ").append(toIndentedString(itemStatusId)).append("\n");
    sb.append("    largeImageURI: ").append(toIndentedString(largeImageURI)).append("\n");
    sb.append("    length: ").append(toIndentedString(length)).append("\n");
    sb.append("    localizedData: ").append(toIndentedString(localizedData)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    manufacturingAttribute: ").append(toIndentedString(manufacturingAttribute)).append("\n");
    sb.append("    media: ").append(toIndentedString(media)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    productClass: ").append(toIndentedString(productClass)).append("\n");
    sb.append("    productSubClass: ").append(toIndentedString(productSubClass)).append("\n");
    sb.append("    profileId: ").append(toIndentedString(profileId)).append("\n");
    sb.append("    reasonId: ").append(toIndentedString(reasonId)).append("\n");
    sb.append("    recommendedProduct: ").append(toIndentedString(recommendedProduct)).append("\n");
    sb.append("    season: ").append(toIndentedString(season)).append("\n");
    sb.append("    seasonYear: ").append(toIndentedString(seasonYear)).append("\n");
    sb.append("    sellingAttributes: ").append(toIndentedString(sellingAttributes)).append("\n");
    sb.append("    shortDescription: ").append(toIndentedString(shortDescription)).append("\n");
    sb.append("    size: ").append(toIndentedString(size)).append("\n");
    sb.append("    sizeSortSequence: ").append(toIndentedString(sizeSortSequence)).append("\n");
    sb.append("    smallImageURI: ").append(toIndentedString(smallImageURI)).append("\n");
    sb.append("    storeDepartment: ").append(toIndentedString(storeDepartment)).append("\n");
    sb.append("    style: ").append(toIndentedString(style)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    valueAddedServices: ").append(toIndentedString(valueAddedServices)).append("\n");
    sb.append("    volume: ").append(toIndentedString(volume)).append("\n");
    sb.append("    volumeUOM: ").append(toIndentedString(volumeUOM)).append("\n");
    sb.append("    volumetricWeight: ").append(toIndentedString(volumetricWeight)).append("\n");
    sb.append("    volumetricWeightUOM: ").append(toIndentedString(volumetricWeightUOM)).append("\n");
    sb.append("    webURL: ").append(toIndentedString(webURL)).append("\n");
    sb.append("    weight: ").append(toIndentedString(weight)).append("\n");
    sb.append("    weightUOM: ").append(toIndentedString(weightUOM)).append("\n");
    sb.append("    width: ").append(toIndentedString(width)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

