/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * OrderLineEligibleRefundChargesDTO
 */
public class OrderLineEligibleRefundChargesDTO {
  public static final String SERIALIZED_NAME_ELIGIBLE_REFUND_CHARGES = "EligibleRefundCharges";
  @SerializedName(SERIALIZED_NAME_ELIGIBLE_REFUND_CHARGES)
  private List<OrderLineChargeDetail> eligibleRefundCharges = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_ID = "OrderLineId";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_ID)
  private String orderLineId;

  public OrderLineEligibleRefundChargesDTO eligibleRefundCharges(List<OrderLineChargeDetail> eligibleRefundCharges) {
    this.eligibleRefundCharges = eligibleRefundCharges;
    return this;
  }

  public OrderLineEligibleRefundChargesDTO addEligibleRefundChargesItem(OrderLineChargeDetail eligibleRefundChargesItem) {
    if (this.eligibleRefundCharges == null) {
      this.eligibleRefundCharges = new ArrayList<OrderLineChargeDetail>();
    }
    this.eligibleRefundCharges.add(eligibleRefundChargesItem);
    return this;
  }

   /**
   * Get eligibleRefundCharges
   * @return eligibleRefundCharges
  **/
  
  public List<OrderLineChargeDetail> getEligibleRefundCharges() {
    return eligibleRefundCharges;
  }

  public void setEligibleRefundCharges(List<OrderLineChargeDetail> eligibleRefundCharges) {
    this.eligibleRefundCharges = eligibleRefundCharges;
  }

  public OrderLineEligibleRefundChargesDTO orderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
    return this;
  }

   /**
   * Get orderLineId
   * @return orderLineId
  **/
  
  public String getOrderLineId() {
    return orderLineId;
  }

  public void setOrderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderLineEligibleRefundChargesDTO orderLineEligibleRefundChargesDTO = (OrderLineEligibleRefundChargesDTO) o;
    return Objects.equals(this.eligibleRefundCharges, orderLineEligibleRefundChargesDTO.eligibleRefundCharges) &&
        Objects.equals(this.orderLineId, orderLineEligibleRefundChargesDTO.orderLineId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(eligibleRefundCharges, orderLineId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderLineEligibleRefundChargesDTO {\n");
    
    sb.append("    eligibleRefundCharges: ").append(toIndentedString(eligibleRefundCharges)).append("\n");
    sb.append("    orderLineId: ").append(toIndentedString(orderLineId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

