/*
 * API Authorization
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.auth;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * APIAuthorization400Response
 */
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2024-12-05T21:09:24.954804Z[Europe/London]")
public class APIAuthorization400Response {
  public static final String SERIALIZED_NAME_ERRORS = "errors";
  @SerializedName(SERIALIZED_NAME_ERRORS)
  private String errors;

  public static final String SERIALIZED_NAME_ERROR_DESCRIPTION = "error_description";
  @SerializedName(SERIALIZED_NAME_ERROR_DESCRIPTION)
  private String errorDescription;

  public APIAuthorization400Response() {
  }

  public APIAuthorization400Response errors(String errors) {
    
    this.errors = errors;
    return this;
  }

   /**
   * Error type.
   * @return errors
  **/
  @jakarta.annotation.Nullable
  public String getErrors() {
    return errors;
  }


  public void setErrors(String errors) {
    this.errors = errors;
  }


  public APIAuthorization400Response errorDescription(String errorDescription) {
    
    this.errorDescription = errorDescription;
    return this;
  }

   /**
   * Description of the error.
   * @return errorDescription
  **/
  @jakarta.annotation.Nullable
  public String getErrorDescription() {
    return errorDescription;
  }


  public void setErrorDescription(String errorDescription) {
    this.errorDescription = errorDescription;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    APIAuthorization400Response apIAuthorization400Response = (APIAuthorization400Response) o;
    return Objects.equals(this.errors, apIAuthorization400Response.errors) &&
        Objects.equals(this.errorDescription, apIAuthorization400Response.errorDescription);
  }

  @Override
  public int hashCode() {
    return Objects.hash(errors, errorDescription);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class APIAuthorization400Response {\n");
    sb.append("    errors: ").append(toIndentedString(errors)).append("\n");
    sb.append("    errorDescription: ").append(toIndentedString(errorDescription)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
      if (o == null) {
          return "null";
      }
      return o.toString().replace("\n", "\n    ");
  }
}

