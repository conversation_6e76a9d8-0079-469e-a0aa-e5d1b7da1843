/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.math.BigDecimal;
import java.util.*;

/**
 * InvoiceLine
 */
public class InvoiceLine {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_ADDRESS_ID = "AddressId";
  @SerializedName(SERIALIZED_NAME_ADDRESS_ID)
  private String addressId;

  public static final String SERIALIZED_NAME_CALCULATED_VALUES = "CalculatedValues";
  @SerializedName(SERIALIZED_NAME_CALCULATED_VALUES)
  private Object calculatedValues = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_FULFILLMENT_DATE = "FulfillmentDate";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_DATE)
  private OffsetDateTime fulfillmentDate;

  public static final String SERIALIZED_NAME_FULFILLMENT_GROUP_ID = "FulfillmentGroupId";
  @SerializedName(SERIALIZED_NAME_FULFILLMENT_GROUP_ID)
  private String fulfillmentGroupId;

  public static final String SERIALIZED_NAME_GIFT_CARD_VALUE = "GiftCardValue";
  @SerializedName(SERIALIZED_NAME_GIFT_CARD_VALUE)
  private BigDecimal giftCardValue;

  public static final String SERIALIZED_NAME_INVOICE_LINE_CHARGE_DETAIL = "InvoiceLineChargeDetail";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE_CHARGE_DETAIL)
  private List<InvoiceLineChargeDetail> invoiceLineChargeDetail = null;

  public static final String SERIALIZED_NAME_INVOICE_LINE_ID = "InvoiceLineId";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE_ID)
  private String invoiceLineId;

  public static final String SERIALIZED_NAME_INVOICE_LINE_SUB_TOTAL = "InvoiceLineSubTotal";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE_SUB_TOTAL)
  private BigDecimal invoiceLineSubTotal;

  public static final String SERIALIZED_NAME_INVOICE_LINE_TAX_DETAIL = "InvoiceLineTaxDetail";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE_TAX_DETAIL)
  private List<InvoiceLineTaxDetail> invoiceLineTaxDetail = null;

  public static final String SERIALIZED_NAME_INVOICE_LINE_TOTAL = "InvoiceLineTotal";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE_TOTAL)
  private BigDecimal invoiceLineTotal;

  public static final String SERIALIZED_NAME_IS_REFUND_GIFT_CARD = "IsRefundGiftCard";
  @SerializedName(SERIALIZED_NAME_IS_REFUND_GIFT_CARD)
  private Boolean isRefundGiftCard;

  public static final String SERIALIZED_NAME_IS_TAX_INCLUDED = "IsTaxIncluded";
  @SerializedName(SERIALIZED_NAME_IS_TAX_INCLUDED)
  private Boolean isTaxIncluded;

  public static final String SERIALIZED_NAME_ITEM_ID = "ItemId";
  @SerializedName(SERIALIZED_NAME_ITEM_ID)
  private String itemId;

  public static final String SERIALIZED_NAME_LOCALIZED_TO = "LocalizedTo";
  @SerializedName(SERIALIZED_NAME_LOCALIZED_TO)
  private String localizedTo;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_ORDER_LINE_CREATED_TIMESTAMP = "OrderLineCreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_CREATED_TIMESTAMP)
  private OffsetDateTime orderLineCreatedTimestamp;

  public static final String SERIALIZED_NAME_ORDER_LINE_ID = "OrderLineId";
  @SerializedName(SERIALIZED_NAME_ORDER_LINE_ID)
  private String orderLineId;

  public static final String SERIALIZED_NAME_ORDERED_ITEM_ID = "OrderedItemId";
  @SerializedName(SERIALIZED_NAME_ORDERED_ITEM_ID)
  private String orderedItemId;

  public static final String SERIALIZED_NAME_ORDERED_QUANTITY = "OrderedQuantity";
  @SerializedName(SERIALIZED_NAME_ORDERED_QUANTITY)
  private Double orderedQuantity;

  public static final String SERIALIZED_NAME_ORDERED_U_O_M = "OrderedUOM";
  @SerializedName(SERIALIZED_NAME_ORDERED_U_O_M)
  private String orderedUOM;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PACKAGE_DETAIL = "PackageDetail";
  @SerializedName(SERIALIZED_NAME_PACKAGE_DETAIL)
  private List<PackageDetail> packageDetail = null;

  public static final String SERIALIZED_NAME_PACKAGE_DETAIL_ID = "PackageDetailId";
  @SerializedName(SERIALIZED_NAME_PACKAGE_DETAIL_ID)
  private String packageDetailId;

  public static final String SERIALIZED_NAME_PARENT_LINE_CREATED_TIMESTAMP = "ParentLineCreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_PARENT_LINE_CREATED_TIMESTAMP)
  private OffsetDateTime parentLineCreatedTimestamp;

  public static final String SERIALIZED_NAME_PAYMENT_GROUP_ID = "PaymentGroupId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_GROUP_ID)
  private String paymentGroupId;

  public static final String SERIALIZED_NAME_PHYSICAL_ORIGIN_ID = "PhysicalOriginId";
  @SerializedName(SERIALIZED_NAME_PHYSICAL_ORIGIN_ID)
  private String physicalOriginId;

  public static final String SERIALIZED_NAME_PRODUCT_CLASS = "ProductClass";
  @SerializedName(SERIALIZED_NAME_PRODUCT_CLASS)
  private String productClass;

  public static final String SERIALIZED_NAME_QUANTITY = "Quantity";
  @SerializedName(SERIALIZED_NAME_QUANTITY)
  private Double quantity;

  public static final String SERIALIZED_NAME_SHIP_FROM_ADDRESS = "ShipFromAddress";
  @SerializedName(SERIALIZED_NAME_SHIP_FROM_ADDRESS)
  private ShipToAddress shipFromAddress = null;

  public static final String SERIALIZED_NAME_SHIP_FROM_ADDRESS_ID = "ShipFromAddressId";
  @SerializedName(SERIALIZED_NAME_SHIP_FROM_ADDRESS_ID)
  private String shipFromAddressId;

  public static final String SERIALIZED_NAME_SHIP_FROM_LOCATION_ID = "ShipFromLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_FROM_LOCATION_ID)
  private String shipFromLocationId;

  public static final String SERIALIZED_NAME_SHIP_TO_ADDRESS = "ShipToAddress";
  @SerializedName(SERIALIZED_NAME_SHIP_TO_ADDRESS)
  private ShipToAddress shipToAddress = null;

  public static final String SERIALIZED_NAME_SHIP_TO_LOCATION_ID = "ShipToLocationId";
  @SerializedName(SERIALIZED_NAME_SHIP_TO_LOCATION_ID)
  private String shipToLocationId;

  public static final String SERIALIZED_NAME_TAX_SHIP_FROM_ADDRESS = "TaxShipFromAddress";
  @SerializedName(SERIALIZED_NAME_TAX_SHIP_FROM_ADDRESS)
  private ShipToAddress taxShipFromAddress = null;

  public static final String SERIALIZED_NAME_TAX_SHIP_FROM_LOCATION_ID = "TaxShipFromLocationId";
  @SerializedName(SERIALIZED_NAME_TAX_SHIP_FROM_LOCATION_ID)
  private String taxShipFromLocationId;

  public static final String SERIALIZED_NAME_TAXABLE_AMOUNT = "TaxableAmount";
  @SerializedName(SERIALIZED_NAME_TAXABLE_AMOUNT)
  private BigDecimal taxableAmount;

  public static final String SERIALIZED_NAME_TOTAL_CHARGES = "TotalCharges";
  @SerializedName(SERIALIZED_NAME_TOTAL_CHARGES)
  private BigDecimal totalCharges;

  public static final String SERIALIZED_NAME_TOTAL_DISCOUNTS = "TotalDiscounts";
  @SerializedName(SERIALIZED_NAME_TOTAL_DISCOUNTS)
  private BigDecimal totalDiscounts;

  public static final String SERIALIZED_NAME_TOTAL_INFORMATIONAL_TAXES = "TotalInformationalTaxes";
  @SerializedName(SERIALIZED_NAME_TOTAL_INFORMATIONAL_TAXES)
  private BigDecimal totalInformationalTaxes;

  public static final String SERIALIZED_NAME_TOTAL_TAXES = "TotalTaxes";
  @SerializedName(SERIALIZED_NAME_TOTAL_TAXES)
  private BigDecimal totalTaxes;

  public static final String SERIALIZED_NAME_U_O_M = "UOM";
  @SerializedName(SERIALIZED_NAME_U_O_M)
  private String UOM;

  public static final String SERIALIZED_NAME_UNIT_PRICE = "UnitPrice";
  @SerializedName(SERIALIZED_NAME_UNIT_PRICE)
  private BigDecimal unitPrice;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public static final String SERIALIZED_NAME_INVOICE_LINE_CHARGE_DETAIL_REQUEST_LIST = "invoiceLineChargeDetailRequestList";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE_CHARGE_DETAIL_REQUEST_LIST)
  private List<InvoiceLineChargeDetailRequest> invoiceLineChargeDetailRequestList = null;

  public static final String SERIALIZED_NAME_INVOICE_LINE_TAX_DETAIL_REQUEST_LIST = "invoiceLineTaxDetailRequestList";
  @SerializedName(SERIALIZED_NAME_INVOICE_LINE_TAX_DETAIL_REQUEST_LIST)
  private List<InvoiceLineTaxDetailRequest> invoiceLineTaxDetailRequestList = null;

  public static final String SERIALIZED_NAME_LOCALIZE = "localize";
  @SerializedName(SERIALIZED_NAME_LOCALIZE)
  private Boolean localize;

  public InvoiceLine actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public InvoiceLine putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public InvoiceLine addressId(String addressId) {
    this.addressId = addressId;
    return this;
  }

   /**
   * Unique business key of the address. It is HashValue generated within System.
   * @return addressId
  **/
  
  public String getAddressId() {
    return addressId;
  }

  public void setAddressId(String addressId) {
    this.addressId = addressId;
  }

  public InvoiceLine calculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
    return this;
  }

   /**
   * Get calculatedValues
   * @return calculatedValues
  **/
  
  public Object getCalculatedValues() {
    return calculatedValues;
  }

  public void setCalculatedValues(Object calculatedValues) {
    this.calculatedValues = calculatedValues;
  }

  public InvoiceLine createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public InvoiceLine createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public InvoiceLine extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public InvoiceLine fulfillmentDate(OffsetDateTime fulfillmentDate) {
    this.fulfillmentDate = fulfillmentDate;
    return this;
  }

   /**
   * For return invoices, date when the items were originally fulfilled. Populated with the value of the shipment invoice fulfillment date, or created date if fulfillment date is null. If multiple shipment invoices exist, then fulfillment date is set to the earliest of all fulfillment detail dates.
   * @return fulfillmentDate
  **/
  
  public OffsetDateTime getFulfillmentDate() {
    return fulfillmentDate;
  }

  public void setFulfillmentDate(OffsetDateTime fulfillmentDate) {
    this.fulfillmentDate = fulfillmentDate;
  }

  public InvoiceLine fulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
    return this;
  }

   /**
   * ID used to group items which need to be fulfilled together (e.g. warranties, kits, gift with purchase, etc.)
   * @return fulfillmentGroupId
  **/
  
  public String getFulfillmentGroupId() {
    return fulfillmentGroupId;
  }

  public void setFulfillmentGroupId(String fulfillmentGroupId) {
    this.fulfillmentGroupId = fulfillmentGroupId;
  }

  public InvoiceLine giftCardValue(BigDecimal giftCardValue) {
    this.giftCardValue = giftCardValue;
    return this;
  }

   /**
   * Activation value, if the item is a gift card
   * minimum: 0
   * maximum: 99999999999999.98
   * @return giftCardValue
  **/
  
  public BigDecimal getGiftCardValue() {
    return giftCardValue;
  }

  public void setGiftCardValue(BigDecimal giftCardValue) {
    this.giftCardValue = giftCardValue;
  }

  public InvoiceLine invoiceLineChargeDetail(List<InvoiceLineChargeDetail> invoiceLineChargeDetail) {
    this.invoiceLineChargeDetail = invoiceLineChargeDetail;
    return this;
  }

  public InvoiceLine addInvoiceLineChargeDetailItem(InvoiceLineChargeDetail invoiceLineChargeDetailItem) {
    if (this.invoiceLineChargeDetail == null) {
      this.invoiceLineChargeDetail = new ArrayList<InvoiceLineChargeDetail>();
    }
    this.invoiceLineChargeDetail.add(invoiceLineChargeDetailItem);
    return this;
  }

   /**
   * Get invoiceLineChargeDetail
   * @return invoiceLineChargeDetail
  **/
  
  public List<InvoiceLineChargeDetail> getInvoiceLineChargeDetail() {
    return invoiceLineChargeDetail;
  }

  public void setInvoiceLineChargeDetail(List<InvoiceLineChargeDetail> invoiceLineChargeDetail) {
    this.invoiceLineChargeDetail = invoiceLineChargeDetail;
  }

  public InvoiceLine invoiceLineId(String invoiceLineId) {
    this.invoiceLineId = invoiceLineId;
    return this;
  }

   /**
   * Unique identifier of the invoice line, as defined by external system. If invoiceId is not provided on import, the system generates a value.
   * @return invoiceLineId
  **/
  
  public String getInvoiceLineId() {
    return invoiceLineId;
  }

  public void setInvoiceLineId(String invoiceLineId) {
    this.invoiceLineId = invoiceLineId;
  }

  public InvoiceLine invoiceLineSubTotal(BigDecimal invoiceLineSubTotal) {
    this.invoiceLineSubTotal = invoiceLineSubTotal;
    return this;
  }

   /**
   * Unit price times quantity; total value of items excluding charges, taxes,and discounts
   * minimum: 0
   * maximum: 99999999999999.98
   * @return invoiceLineSubTotal
  **/
  
  public BigDecimal getInvoiceLineSubTotal() {
    return invoiceLineSubTotal;
  }

  public void setInvoiceLineSubTotal(BigDecimal invoiceLineSubTotal) {
    this.invoiceLineSubTotal = invoiceLineSubTotal;
  }

  public InvoiceLine invoiceLineTaxDetail(List<InvoiceLineTaxDetail> invoiceLineTaxDetail) {
    this.invoiceLineTaxDetail = invoiceLineTaxDetail;
    return this;
  }

  public InvoiceLine addInvoiceLineTaxDetailItem(InvoiceLineTaxDetail invoiceLineTaxDetailItem) {
    if (this.invoiceLineTaxDetail == null) {
      this.invoiceLineTaxDetail = new ArrayList<InvoiceLineTaxDetail>();
    }
    this.invoiceLineTaxDetail.add(invoiceLineTaxDetailItem);
    return this;
  }

   /**
   * Get invoiceLineTaxDetail
   * @return invoiceLineTaxDetail
  **/
  
  public List<InvoiceLineTaxDetail> getInvoiceLineTaxDetail() {
    return invoiceLineTaxDetail;
  }

  public void setInvoiceLineTaxDetail(List<InvoiceLineTaxDetail> invoiceLineTaxDetail) {
    this.invoiceLineTaxDetail = invoiceLineTaxDetail;
  }

  public InvoiceLine invoiceLineTotal(BigDecimal invoiceLineTotal) {
    this.invoiceLineTotal = invoiceLineTotal;
    return this;
  }

   /**
   * Sum of invoice lines discounts, charges, and taxes
   * minimum: 0
   * maximum: 99999999999999.98
   * @return invoiceLineTotal
  **/
  
  public BigDecimal getInvoiceLineTotal() {
    return invoiceLineTotal;
  }

  public void setInvoiceLineTotal(BigDecimal invoiceLineTotal) {
    this.invoiceLineTotal = invoiceLineTotal;
  }

  public InvoiceLine isRefundGiftCard(Boolean isRefundGiftCard) {
    this.isRefundGiftCard = isRefundGiftCard;
    return this;
  }

   /**
   * This flag indicates if a gift card line is added by the system to process any refunds resulting from order updates where the payment was originally made by cash or gift cards. Order updates can include order cancellation, applying appeasement, regular returns resulting in refunds or a gift receipt returns
   * @return isRefundGiftCard
  **/
  
  public Boolean getIsRefundGiftCard() {
    return isRefundGiftCard;
  }

  public void setIsRefundGiftCard(Boolean isRefundGiftCard) {
    this.isRefundGiftCard = isRefundGiftCard;
  }

  public InvoiceLine isTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
    return this;
  }

   /**
   * Indicates if tax is included in the invoice totals
   * @return isTaxIncluded
  **/
  
  public Boolean getIsTaxIncluded() {
    return isTaxIncluded;
  }

  public void setIsTaxIncluded(Boolean isTaxIncluded) {
    this.isTaxIncluded = isTaxIncluded;
  }

  public InvoiceLine itemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

   /**
   * Identifier of the item which was fulfilled
   * @return itemId
  **/
  
  public String getItemId() {
    return itemId;
  }

  public void setItemId(String itemId) {
    this.itemId = itemId;
  }

  public InvoiceLine localizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
    return this;
  }

   /**
   * Get localizedTo
   * @return localizedTo
  **/
  
  public String getLocalizedTo() {
    return localizedTo;
  }

  public void setLocalizedTo(String localizedTo) {
    this.localizedTo = localizedTo;
  }

  public InvoiceLine messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public InvoiceLine orderLineCreatedTimestamp(OffsetDateTime orderLineCreatedTimestamp) {
    this.orderLineCreatedTimestamp = orderLineCreatedTimestamp;
    return this;
  }

   /**
   * Created timestamp of the order line. Used in invoice tax requests to pass the original line created date to the third party tax engine, so that tax holidays can be honored.
   * @return orderLineCreatedTimestamp
  **/
  
  public OffsetDateTime getOrderLineCreatedTimestamp() {
    return orderLineCreatedTimestamp;
  }

  public void setOrderLineCreatedTimestamp(OffsetDateTime orderLineCreatedTimestamp) {
    this.orderLineCreatedTimestamp = orderLineCreatedTimestamp;
  }

  public InvoiceLine orderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
    return this;
  }

   /**
   * Identifier of the order line
   * @return orderLineId
  **/
  
  public String getOrderLineId() {
    return orderLineId;
  }

  public void setOrderLineId(String orderLineId) {
    this.orderLineId = orderLineId;
  }

  public InvoiceLine orderedItemId(String orderedItemId) {
    this.orderedItemId = orderedItemId;
    return this;
  }

   /**
   * Identifier of the item which was ordered
   * @return orderedItemId
  **/
  
  public String getOrderedItemId() {
    return orderedItemId;
  }

  public void setOrderedItemId(String orderedItemId) {
    this.orderedItemId = orderedItemId;
  }

  public InvoiceLine orderedQuantity(Double orderedQuantity) {
    this.orderedQuantity = orderedQuantity;
    return this;
  }

   /**
   * Quantity ordered
   * minimum: 0
   * maximum: 999999999999.9999
   * @return orderedQuantity
  **/
  
  public Double getOrderedQuantity() {
    return orderedQuantity;
  }

  public void setOrderedQuantity(Double orderedQuantity) {
    this.orderedQuantity = orderedQuantity;
  }

  public InvoiceLine orderedUOM(String orderedUOM) {
    this.orderedUOM = orderedUOM;
    return this;
  }

   /**
   * UOM of the ordered item
   * @return orderedUOM
  **/
  
  public String getOrderedUOM() {
    return orderedUOM;
  }

  public void setOrderedUOM(String orderedUOM) {
    this.orderedUOM = orderedUOM;
  }

  public InvoiceLine orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public InvoiceLine PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public InvoiceLine packageDetail(List<PackageDetail> packageDetail) {
    this.packageDetail = packageDetail;
    return this;
  }

  public InvoiceLine addPackageDetailItem(PackageDetail packageDetailItem) {
    if (this.packageDetail == null) {
      this.packageDetail = new ArrayList<PackageDetail>();
    }
    this.packageDetail.add(packageDetailItem);
    return this;
  }

   /**
   * Get packageDetail
   * @return packageDetail
  **/
  
  public List<PackageDetail> getPackageDetail() {
    return packageDetail;
  }

  public void setPackageDetail(List<PackageDetail> packageDetail) {
    this.packageDetail = packageDetail;
  }

  public InvoiceLine packageDetailId(String packageDetailId) {
    this.packageDetailId = packageDetailId;
    return this;
  }

   /**
   * Identifier of the package detail
   * @return packageDetailId
  **/
  
  public String getPackageDetailId() {
    return packageDetailId;
  }

  public void setPackageDetailId(String packageDetailId) {
    this.packageDetailId = packageDetailId;
  }

  public InvoiceLine parentLineCreatedTimestamp(OffsetDateTime parentLineCreatedTimestamp) {
    this.parentLineCreatedTimestamp = parentLineCreatedTimestamp;
    return this;
  }

   /**
   * Created timestamp of the parent order line. Used in return invoice tax requests to pass the original parent order line created date to the third party tax engine, so that tax holidays can be honored.
   * @return parentLineCreatedTimestamp
  **/
  
  public OffsetDateTime getParentLineCreatedTimestamp() {
    return parentLineCreatedTimestamp;
  }

  public void setParentLineCreatedTimestamp(OffsetDateTime parentLineCreatedTimestamp) {
    this.parentLineCreatedTimestamp = parentLineCreatedTimestamp;
  }

  public InvoiceLine paymentGroupId(String paymentGroupId) {
    this.paymentGroupId = paymentGroupId;
    return this;
  }

   /**
   * ID used to associate a payment with an item or group of items
   * @return paymentGroupId
  **/
  
  public String getPaymentGroupId() {
    return paymentGroupId;
  }

  public void setPaymentGroupId(String paymentGroupId) {
    this.paymentGroupId = paymentGroupId;
  }

  public InvoiceLine physicalOriginId(String physicalOriginId) {
    this.physicalOriginId = physicalOriginId;
    return this;
  }

   /**
   * Location from where the items are being fulfilled. Populated with the allocation origin location for ship to home items. Populated with the selling location for store sales. Populated with the ship to location for pick up in store and ship to store. Used to send the origin address to the tax engine, which calculates tax rates based on this data.
   * @return physicalOriginId
  **/
  
  public String getPhysicalOriginId() {
    return physicalOriginId;
  }

  public void setPhysicalOriginId(String physicalOriginId) {
    this.physicalOriginId = physicalOriginId;
  }

  public InvoiceLine productClass(String productClass) {
    this.productClass = productClass;
    return this;
  }

   /**
   * Product Class of Item.
   * @return productClass
  **/
  
  public String getProductClass() {
    return productClass;
  }

  public void setProductClass(String productClass) {
    this.productClass = productClass;
  }

  public InvoiceLine quantity(Double quantity) {
    this.quantity = quantity;
    return this;
  }

   /**
   * Quantity of the shipped item which was fulfilled
   * minimum: 0
   * maximum: 999999999999.9999
   * @return quantity
  **/
  
  public Double getQuantity() {
    return quantity;
  }

  public void setQuantity(Double quantity) {
    this.quantity = quantity;
  }

  public InvoiceLine shipFromAddress(ShipToAddress shipFromAddress) {
    this.shipFromAddress = shipFromAddress;
    return this;
  }

   /**
   * Get shipFromAddress
   * @return shipFromAddress
  **/
  
  public ShipToAddress getShipFromAddress() {
    return shipFromAddress;
  }

  public void setShipFromAddress(ShipToAddress shipFromAddress) {
    this.shipFromAddress = shipFromAddress;
  }

  public InvoiceLine shipFromAddressId(String shipFromAddressId) {
    this.shipFromAddressId = shipFromAddressId;
    return this;
  }

   /**
   * Ship from address for return order lines being shipped from a customer address to a return center.
   * @return shipFromAddressId
  **/
  
  public String getShipFromAddressId() {
    return shipFromAddressId;
  }

  public void setShipFromAddressId(String shipFromAddressId) {
    this.shipFromAddressId = shipFromAddressId;
  }

  public InvoiceLine shipFromLocationId(String shipFromLocationId) {
    this.shipFromLocationId = shipFromLocationId;
    return this;
  }

   /**
   * Preferred Store Location for Allocation
   * @return shipFromLocationId
  **/
  
  public String getShipFromLocationId() {
    return shipFromLocationId;
  }

  public void setShipFromLocationId(String shipFromLocationId) {
    this.shipFromLocationId = shipFromLocationId;
  }

  public InvoiceLine shipToAddress(ShipToAddress shipToAddress) {
    this.shipToAddress = shipToAddress;
    return this;
  }

   /**
   * Get shipToAddress
   * @return shipToAddress
  **/
  
  public ShipToAddress getShipToAddress() {
    return shipToAddress;
  }

  public void setShipToAddress(ShipToAddress shipToAddress) {
    this.shipToAddress = shipToAddress;
  }

  public InvoiceLine shipToLocationId(String shipToLocationId) {
    this.shipToLocationId = shipToLocationId;
    return this;
  }

   /**
   * Ship-to location ID, in case of ship to store, pick up in store, or any flow where the items are shipping to a destination which is configured as a location in the network
   * @return shipToLocationId
  **/
  
  public String getShipToLocationId() {
    return shipToLocationId;
  }

  public void setShipToLocationId(String shipToLocationId) {
    this.shipToLocationId = shipToLocationId;
  }

  public InvoiceLine taxShipFromAddress(ShipToAddress taxShipFromAddress) {
    this.taxShipFromAddress = taxShipFromAddress;
    return this;
  }

   /**
   * Get taxShipFromAddress
   * @return taxShipFromAddress
  **/
  
  public ShipToAddress getTaxShipFromAddress() {
    return taxShipFromAddress;
  }

  public void setTaxShipFromAddress(ShipToAddress taxShipFromAddress) {
    this.taxShipFromAddress = taxShipFromAddress;
  }

  public InvoiceLine taxShipFromLocationId(String taxShipFromLocationId) {
    this.taxShipFromLocationId = taxShipFromLocationId;
    return this;
  }

   /**
   * Get taxShipFromLocationId
   * @return taxShipFromLocationId
  **/
  
  public String getTaxShipFromLocationId() {
    return taxShipFromLocationId;
  }

  public void setTaxShipFromLocationId(String taxShipFromLocationId) {
    this.taxShipFromLocationId = taxShipFromLocationId;
  }

  public InvoiceLine taxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
    return this;
  }

   /**
   * Get taxableAmount
   * @return taxableAmount
  **/
  
  public BigDecimal getTaxableAmount() {
    return taxableAmount;
  }

  public void setTaxableAmount(BigDecimal taxableAmount) {
    this.taxableAmount = taxableAmount;
  }

  public InvoiceLine totalCharges(BigDecimal totalCharges) {
    this.totalCharges = totalCharges;
    return this;
  }

   /**
   * Sum of charges for the invoice line
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalCharges
  **/
  
  public BigDecimal getTotalCharges() {
    return totalCharges;
  }

  public void setTotalCharges(BigDecimal totalCharges) {
    this.totalCharges = totalCharges;
  }

  public InvoiceLine totalDiscounts(BigDecimal totalDiscounts) {
    this.totalDiscounts = totalDiscounts;
    return this;
  }

   /**
   * Sum of discounts for the invoice line
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalDiscounts
  **/
  
  public BigDecimal getTotalDiscounts() {
    return totalDiscounts;
  }

  public void setTotalDiscounts(BigDecimal totalDiscounts) {
    this.totalDiscounts = totalDiscounts;
  }

  public InvoiceLine totalInformationalTaxes(BigDecimal totalInformationalTaxes) {
    this.totalInformationalTaxes = totalInformationalTaxes;
    return this;
  }

   /**
   * Get totalInformationalTaxes
   * @return totalInformationalTaxes
  **/
  
  public BigDecimal getTotalInformationalTaxes() {
    return totalInformationalTaxes;
  }

  public void setTotalInformationalTaxes(BigDecimal totalInformationalTaxes) {
    this.totalInformationalTaxes = totalInformationalTaxes;
  }

  public InvoiceLine totalTaxes(BigDecimal totalTaxes) {
    this.totalTaxes = totalTaxes;
    return this;
  }

   /**
   * Sum of taxes for the invoice line
   * minimum: 0
   * maximum: 99999999999999.98
   * @return totalTaxes
  **/
  
  public BigDecimal getTotalTaxes() {
    return totalTaxes;
  }

  public void setTotalTaxes(BigDecimal totalTaxes) {
    this.totalTaxes = totalTaxes;
  }

  public InvoiceLine UOM(String UOM) {
    this.UOM = UOM;
    return this;
  }

   /**
   * UOM of the shipped item
   * @return UOM
  **/
  
  public String getUOM() {
    return UOM;
  }

  public void setUOM(String UOM) {
    this.UOM = UOM;
  }

  public InvoiceLine unitPrice(BigDecimal unitPrice) {
    this.unitPrice = unitPrice;
    return this;
  }

   /**
   * Unit price of the invoice line item
   * minimum: 0
   * maximum: 99999999999999.98
   * @return unitPrice
  **/
  
  public BigDecimal getUnitPrice() {
    return unitPrice;
  }

  public void setUnitPrice(BigDecimal unitPrice) {
    this.unitPrice = unitPrice;
  }

  public InvoiceLine updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public InvoiceLine updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public InvoiceLine entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }

  public InvoiceLine invoiceLineChargeDetailRequestList(List<InvoiceLineChargeDetailRequest> invoiceLineChargeDetailRequestList) {
    this.invoiceLineChargeDetailRequestList = invoiceLineChargeDetailRequestList;
    return this;
  }

  public InvoiceLine addInvoiceLineChargeDetailRequestListItem(InvoiceLineChargeDetailRequest invoiceLineChargeDetailRequestListItem) {
    if (this.invoiceLineChargeDetailRequestList == null) {
      this.invoiceLineChargeDetailRequestList = new ArrayList<InvoiceLineChargeDetailRequest>();
    }
    this.invoiceLineChargeDetailRequestList.add(invoiceLineChargeDetailRequestListItem);
    return this;
  }

   /**
   * Get invoiceLineChargeDetailRequestList
   * @return invoiceLineChargeDetailRequestList
  **/
  
  public List<InvoiceLineChargeDetailRequest> getInvoiceLineChargeDetailRequestList() {
    return invoiceLineChargeDetailRequestList;
  }

  public void setInvoiceLineChargeDetailRequestList(List<InvoiceLineChargeDetailRequest> invoiceLineChargeDetailRequestList) {
    this.invoiceLineChargeDetailRequestList = invoiceLineChargeDetailRequestList;
  }

  public InvoiceLine invoiceLineTaxDetailRequestList(List<InvoiceLineTaxDetailRequest> invoiceLineTaxDetailRequestList) {
    this.invoiceLineTaxDetailRequestList = invoiceLineTaxDetailRequestList;
    return this;
  }

  public InvoiceLine addInvoiceLineTaxDetailRequestListItem(InvoiceLineTaxDetailRequest invoiceLineTaxDetailRequestListItem) {
    if (this.invoiceLineTaxDetailRequestList == null) {
      this.invoiceLineTaxDetailRequestList = new ArrayList<InvoiceLineTaxDetailRequest>();
    }
    this.invoiceLineTaxDetailRequestList.add(invoiceLineTaxDetailRequestListItem);
    return this;
  }

   /**
   * Get invoiceLineTaxDetailRequestList
   * @return invoiceLineTaxDetailRequestList
  **/
  
  public List<InvoiceLineTaxDetailRequest> getInvoiceLineTaxDetailRequestList() {
    return invoiceLineTaxDetailRequestList;
  }

  public void setInvoiceLineTaxDetailRequestList(List<InvoiceLineTaxDetailRequest> invoiceLineTaxDetailRequestList) {
    this.invoiceLineTaxDetailRequestList = invoiceLineTaxDetailRequestList;
  }

  public InvoiceLine localize(Boolean localize) {
    this.localize = localize;
    return this;
  }

   /**
   * Get localize
   * @return localize
  **/
  
  public Boolean getLocalize() {
    return localize;
  }

  public void setLocalize(Boolean localize) {
    this.localize = localize;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvoiceLine invoiceLine = (InvoiceLine) o;
    return Objects.equals(this.actions, invoiceLine.actions) &&
        Objects.equals(this.addressId, invoiceLine.addressId) &&
        Objects.equals(this.calculatedValues, invoiceLine.calculatedValues) &&
        Objects.equals(this.createdBy, invoiceLine.createdBy) &&
        Objects.equals(this.createdTimestamp, invoiceLine.createdTimestamp) &&
        Objects.equals(this.extended, invoiceLine.extended) &&
        Objects.equals(this.fulfillmentDate, invoiceLine.fulfillmentDate) &&
        Objects.equals(this.fulfillmentGroupId, invoiceLine.fulfillmentGroupId) &&
        Objects.equals(this.giftCardValue, invoiceLine.giftCardValue) &&
        Objects.equals(this.invoiceLineChargeDetail, invoiceLine.invoiceLineChargeDetail) &&
        Objects.equals(this.invoiceLineId, invoiceLine.invoiceLineId) &&
        Objects.equals(this.invoiceLineSubTotal, invoiceLine.invoiceLineSubTotal) &&
        Objects.equals(this.invoiceLineTaxDetail, invoiceLine.invoiceLineTaxDetail) &&
        Objects.equals(this.invoiceLineTotal, invoiceLine.invoiceLineTotal) &&
        Objects.equals(this.isRefundGiftCard, invoiceLine.isRefundGiftCard) &&
        Objects.equals(this.isTaxIncluded, invoiceLine.isTaxIncluded) &&
        Objects.equals(this.itemId, invoiceLine.itemId) &&
        Objects.equals(this.localizedTo, invoiceLine.localizedTo) &&
        Objects.equals(this.messages, invoiceLine.messages) &&
        Objects.equals(this.orderLineCreatedTimestamp, invoiceLine.orderLineCreatedTimestamp) &&
        Objects.equals(this.orderLineId, invoiceLine.orderLineId) &&
        Objects.equals(this.orderedItemId, invoiceLine.orderedItemId) &&
        Objects.equals(this.orderedQuantity, invoiceLine.orderedQuantity) &&
        Objects.equals(this.orderedUOM, invoiceLine.orderedUOM) &&
        Objects.equals(this.orgId, invoiceLine.orgId) &&
        Objects.equals(this.PK, invoiceLine.PK) &&
        Objects.equals(this.packageDetail, invoiceLine.packageDetail) &&
        Objects.equals(this.packageDetailId, invoiceLine.packageDetailId) &&
        Objects.equals(this.parentLineCreatedTimestamp, invoiceLine.parentLineCreatedTimestamp) &&
        Objects.equals(this.paymentGroupId, invoiceLine.paymentGroupId) &&
        Objects.equals(this.physicalOriginId, invoiceLine.physicalOriginId) &&
        Objects.equals(this.productClass, invoiceLine.productClass) &&
        Objects.equals(this.quantity, invoiceLine.quantity) &&
        Objects.equals(this.shipFromAddress, invoiceLine.shipFromAddress) &&
        Objects.equals(this.shipFromAddressId, invoiceLine.shipFromAddressId) &&
        Objects.equals(this.shipFromLocationId, invoiceLine.shipFromLocationId) &&
        Objects.equals(this.shipToAddress, invoiceLine.shipToAddress) &&
        Objects.equals(this.shipToLocationId, invoiceLine.shipToLocationId) &&
        Objects.equals(this.taxShipFromAddress, invoiceLine.taxShipFromAddress) &&
        Objects.equals(this.taxShipFromLocationId, invoiceLine.taxShipFromLocationId) &&
        Objects.equals(this.taxableAmount, invoiceLine.taxableAmount) &&
        Objects.equals(this.totalCharges, invoiceLine.totalCharges) &&
        Objects.equals(this.totalDiscounts, invoiceLine.totalDiscounts) &&
        Objects.equals(this.totalInformationalTaxes, invoiceLine.totalInformationalTaxes) &&
        Objects.equals(this.totalTaxes, invoiceLine.totalTaxes) &&
        Objects.equals(this.UOM, invoiceLine.UOM) &&
        Objects.equals(this.unitPrice, invoiceLine.unitPrice) &&
        Objects.equals(this.updatedBy, invoiceLine.updatedBy) &&
        Objects.equals(this.updatedTimestamp, invoiceLine.updatedTimestamp) &&
        Objects.equals(this.entityName, invoiceLine.entityName) &&
        Objects.equals(this.invoiceLineChargeDetailRequestList, invoiceLine.invoiceLineChargeDetailRequestList) &&
        Objects.equals(this.invoiceLineTaxDetailRequestList, invoiceLine.invoiceLineTaxDetailRequestList) &&
        Objects.equals(this.localize, invoiceLine.localize);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, addressId, calculatedValues, createdBy, createdTimestamp, extended, fulfillmentDate, fulfillmentGroupId, giftCardValue, invoiceLineChargeDetail, invoiceLineId, invoiceLineSubTotal, invoiceLineTaxDetail, invoiceLineTotal, isRefundGiftCard, isTaxIncluded, itemId, localizedTo, messages, orderLineCreatedTimestamp, orderLineId, orderedItemId, orderedQuantity, orderedUOM, orgId, PK, packageDetail, packageDetailId, parentLineCreatedTimestamp, paymentGroupId, physicalOriginId, productClass, quantity, shipFromAddress, shipFromAddressId, shipFromLocationId, shipToAddress, shipToLocationId, taxShipFromAddress, taxShipFromLocationId, taxableAmount, totalCharges, totalDiscounts, totalInformationalTaxes, totalTaxes, UOM, unitPrice, updatedBy, updatedTimestamp, entityName, invoiceLineChargeDetailRequestList, invoiceLineTaxDetailRequestList, localize);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvoiceLine {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    addressId: ").append(toIndentedString(addressId)).append("\n");
    sb.append("    calculatedValues: ").append(toIndentedString(calculatedValues)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    fulfillmentDate: ").append(toIndentedString(fulfillmentDate)).append("\n");
    sb.append("    fulfillmentGroupId: ").append(toIndentedString(fulfillmentGroupId)).append("\n");
    sb.append("    giftCardValue: ").append(toIndentedString(giftCardValue)).append("\n");
    sb.append("    invoiceLineChargeDetail: ").append(toIndentedString(invoiceLineChargeDetail)).append("\n");
    sb.append("    invoiceLineId: ").append(toIndentedString(invoiceLineId)).append("\n");
    sb.append("    invoiceLineSubTotal: ").append(toIndentedString(invoiceLineSubTotal)).append("\n");
    sb.append("    invoiceLineTaxDetail: ").append(toIndentedString(invoiceLineTaxDetail)).append("\n");
    sb.append("    invoiceLineTotal: ").append(toIndentedString(invoiceLineTotal)).append("\n");
    sb.append("    isRefundGiftCard: ").append(toIndentedString(isRefundGiftCard)).append("\n");
    sb.append("    isTaxIncluded: ").append(toIndentedString(isTaxIncluded)).append("\n");
    sb.append("    itemId: ").append(toIndentedString(itemId)).append("\n");
    sb.append("    localizedTo: ").append(toIndentedString(localizedTo)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    orderLineCreatedTimestamp: ").append(toIndentedString(orderLineCreatedTimestamp)).append("\n");
    sb.append("    orderLineId: ").append(toIndentedString(orderLineId)).append("\n");
    sb.append("    orderedItemId: ").append(toIndentedString(orderedItemId)).append("\n");
    sb.append("    orderedQuantity: ").append(toIndentedString(orderedQuantity)).append("\n");
    sb.append("    orderedUOM: ").append(toIndentedString(orderedUOM)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    packageDetail: ").append(toIndentedString(packageDetail)).append("\n");
    sb.append("    packageDetailId: ").append(toIndentedString(packageDetailId)).append("\n");
    sb.append("    parentLineCreatedTimestamp: ").append(toIndentedString(parentLineCreatedTimestamp)).append("\n");
    sb.append("    paymentGroupId: ").append(toIndentedString(paymentGroupId)).append("\n");
    sb.append("    physicalOriginId: ").append(toIndentedString(physicalOriginId)).append("\n");
    sb.append("    productClass: ").append(toIndentedString(productClass)).append("\n");
    sb.append("    quantity: ").append(toIndentedString(quantity)).append("\n");
    sb.append("    shipFromAddress: ").append(toIndentedString(shipFromAddress)).append("\n");
    sb.append("    shipFromAddressId: ").append(toIndentedString(shipFromAddressId)).append("\n");
    sb.append("    shipFromLocationId: ").append(toIndentedString(shipFromLocationId)).append("\n");
    sb.append("    shipToAddress: ").append(toIndentedString(shipToAddress)).append("\n");
    sb.append("    shipToLocationId: ").append(toIndentedString(shipToLocationId)).append("\n");
    sb.append("    taxShipFromAddress: ").append(toIndentedString(taxShipFromAddress)).append("\n");
    sb.append("    taxShipFromLocationId: ").append(toIndentedString(taxShipFromLocationId)).append("\n");
    sb.append("    taxableAmount: ").append(toIndentedString(taxableAmount)).append("\n");
    sb.append("    totalCharges: ").append(toIndentedString(totalCharges)).append("\n");
    sb.append("    totalDiscounts: ").append(toIndentedString(totalDiscounts)).append("\n");
    sb.append("    totalInformationalTaxes: ").append(toIndentedString(totalInformationalTaxes)).append("\n");
    sb.append("    totalTaxes: ").append(toIndentedString(totalTaxes)).append("\n");
    sb.append("    UOM: ").append(toIndentedString(UOM)).append("\n");
    sb.append("    unitPrice: ").append(toIndentedString(unitPrice)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("    invoiceLineChargeDetailRequestList: ").append(toIndentedString(invoiceLineChargeDetailRequestList)).append("\n");
    sb.append("    invoiceLineTaxDetailRequestList: ").append(toIndentedString(invoiceLineTaxDetailRequestList)).append("\n");
    sb.append("    localize: ").append(toIndentedString(localize)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

