/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;
import org.threeten.bp.OffsetDateTime;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * PaymentTransAttribute
 */
public class PaymentTransAttribute {
  public static final String SERIALIZED_NAME_ACTIONS = "Actions";
  @SerializedName(SERIALIZED_NAME_ACTIONS)
  private Map<String, String> actions = null;

  public static final String SERIALIZED_NAME_CREATED_BY = "CreatedBy";
  @SerializedName(SERIALIZED_NAME_CREATED_BY)
  private String createdBy;

  public static final String SERIALIZED_NAME_CREATED_TIMESTAMP = "CreatedTimestamp";
  @SerializedName(SERIALIZED_NAME_CREATED_TIMESTAMP)
  private OffsetDateTime createdTimestamp;

  public static final String SERIALIZED_NAME_EXTENDED = "Extended";
  @SerializedName(SERIALIZED_NAME_EXTENDED)
  private Object extended = null;

  public static final String SERIALIZED_NAME_MESSAGES = "Messages";
  @SerializedName(SERIALIZED_NAME_MESSAGES)
  private Messages messages = null;

  public static final String SERIALIZED_NAME_NAME = "Name";
  @SerializedName(SERIALIZED_NAME_NAME)
  private String name;

  public static final String SERIALIZED_NAME_ORG_ID = "OrgId";
  @SerializedName(SERIALIZED_NAME_ORG_ID)
  private String orgId;

  public static final String SERIALIZED_NAME_P_K = "PK";
  @SerializedName(SERIALIZED_NAME_P_K)
  private String PK;

  public static final String SERIALIZED_NAME_PARENT_PAYMENT_TRANSACTION = "ParentPaymentTransaction";
  @SerializedName(SERIALIZED_NAME_PARENT_PAYMENT_TRANSACTION)
  private PrimaryKey parentPaymentTransaction = null;

  public static final String SERIALIZED_NAME_TRANSLATIONS = "Translations";
  @SerializedName(SERIALIZED_NAME_TRANSLATIONS)
  private Map<String, Map<String, String>> translations = null;

  public static final String SERIALIZED_NAME_UPDATED_BY = "UpdatedBy";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY)
  private String updatedBy;

  public static final String SERIALIZED_NAME_UPDATED_TIMESTAMP = "UpdatedTimestamp";
  @SerializedName(SERIALIZED_NAME_UPDATED_TIMESTAMP)
  private OffsetDateTime updatedTimestamp;

  public static final String SERIALIZED_NAME_VALUE = "Value";
  @SerializedName(SERIALIZED_NAME_VALUE)
  private String value;

  public static final String SERIALIZED_NAME_ENTITY_NAME = "entityName";
  @SerializedName(SERIALIZED_NAME_ENTITY_NAME)
  private String entityName;

  public PaymentTransAttribute actions(Map<String, String> actions) {
    this.actions = actions;
    return this;
  }

  public PaymentTransAttribute putActionsItem(String key, String actionsItem) {
    if (this.actions == null) {
      this.actions = new HashMap<String, String>();
    }
    this.actions.put(key, actionsItem);
    return this;
  }

   /**
   * Get actions
   * @return actions
  **/
  
  public Map<String, String> getActions() {
    return actions;
  }

  public void setActions(Map<String, String> actions) {
    this.actions = actions;
  }

  public PaymentTransAttribute createdBy(String createdBy) {
    this.createdBy = createdBy;
    return this;
  }

   /**
   * Created By
   * @return createdBy
  **/
  
  public String getCreatedBy() {
    return createdBy;
  }

  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public PaymentTransAttribute createdTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
    return this;
  }

   /**
   * Created Timestamp
   * @return createdTimestamp
  **/
  
  public OffsetDateTime getCreatedTimestamp() {
    return createdTimestamp;
  }

  public void setCreatedTimestamp(OffsetDateTime createdTimestamp) {
    this.createdTimestamp = createdTimestamp;
  }

  public PaymentTransAttribute extended(Object extended) {
    this.extended = extended;
    return this;
  }

   /**
   * Extended Properties
   * @return extended
  **/
  
  public Object getExtended() {
    return extended;
  }

  public void setExtended(Object extended) {
    this.extended = extended;
  }

  public PaymentTransAttribute messages(Messages messages) {
    this.messages = messages;
    return this;
  }

   /**
   * Get messages
   * @return messages
  **/
  
  public Messages getMessages() {
    return messages;
  }

  public void setMessages(Messages messages) {
    this.messages = messages;
  }

  public PaymentTransAttribute name(String name) {
    this.name = name;
    return this;
  }

   /**
   * Name of the field included in the corresponding value. For example, &#39;Username&#39;, &#39;Password&#39;, &#39;Signature&#39;, &#39;MerchantID&#39;, &#39;StoreAccountID&#39;, etc.
   * @return name
  **/
  
  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public PaymentTransAttribute orgId(String orgId) {
    this.orgId = orgId;
    return this;
  }

   /**
   * Organization Id
   * @return orgId
  **/
  
  public String getOrgId() {
    return orgId;
  }

  public void setOrgId(String orgId) {
    this.orgId = orgId;
  }

  public PaymentTransAttribute PK(String PK) {
    this.PK = PK;
    return this;
  }

   /**
   * Primary Key
   * @return PK
  **/
  
  public String getPK() {
    return PK;
  }

  public void setPK(String PK) {
    this.PK = PK;
  }

  public PaymentTransAttribute parentPaymentTransaction(PrimaryKey parentPaymentTransaction) {
    this.parentPaymentTransaction = parentPaymentTransaction;
    return this;
  }

   /**
   * Get parentPaymentTransaction
   * @return parentPaymentTransaction
  **/
  
  public PrimaryKey getParentPaymentTransaction() {
    return parentPaymentTransaction;
  }

  public void setParentPaymentTransaction(PrimaryKey parentPaymentTransaction) {
    this.parentPaymentTransaction = parentPaymentTransaction;
  }

  public PaymentTransAttribute translations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
    return this;
  }

  public PaymentTransAttribute putTranslationsItem(String key, Map<String, String> translationsItem) {
    if (this.translations == null) {
      this.translations = new HashMap<String, Map<String, String>>();
    }
    this.translations.put(key, translationsItem);
    return this;
  }

   /**
   * Get translations
   * @return translations
  **/
  
  public Map<String, Map<String, String>> getTranslations() {
    return translations;
  }

  public void setTranslations(Map<String, Map<String, String>> translations) {
    this.translations = translations;
  }

  public PaymentTransAttribute updatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
    return this;
  }

   /**
   * Updated By
   * @return updatedBy
  **/
  
  public String getUpdatedBy() {
    return updatedBy;
  }

  public void setUpdatedBy(String updatedBy) {
    this.updatedBy = updatedBy;
  }

  public PaymentTransAttribute updatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
    return this;
  }

   /**
   * Updated Timestamp
   * @return updatedTimestamp
  **/
  
  public OffsetDateTime getUpdatedTimestamp() {
    return updatedTimestamp;
  }

  public void setUpdatedTimestamp(OffsetDateTime updatedTimestamp) {
    this.updatedTimestamp = updatedTimestamp;
  }

  public PaymentTransAttribute value(String value) {
    this.value = value;
    return this;
  }

   /**
   * Value of the data specified by the corresponding name. For example, if Name &#x3D; &#39;Username&#39;, then Value &#x3D; usernameValue.
   * @return value
  **/
  
  public String getValue() {
    return value;
  }

  public void setValue(String value) {
    this.value = value;
  }

  public PaymentTransAttribute entityName(String entityName) {
    this.entityName = entityName;
    return this;
  }

   /**
   * Get entityName
   * @return entityName
  **/
  
  public String getEntityName() {
    return entityName;
  }

  public void setEntityName(String entityName) {
    this.entityName = entityName;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentTransAttribute paymentTransAttribute = (PaymentTransAttribute) o;
    return Objects.equals(this.actions, paymentTransAttribute.actions) &&
        Objects.equals(this.createdBy, paymentTransAttribute.createdBy) &&
        Objects.equals(this.createdTimestamp, paymentTransAttribute.createdTimestamp) &&
        Objects.equals(this.extended, paymentTransAttribute.extended) &&
        Objects.equals(this.messages, paymentTransAttribute.messages) &&
        Objects.equals(this.name, paymentTransAttribute.name) &&
        Objects.equals(this.orgId, paymentTransAttribute.orgId) &&
        Objects.equals(this.PK, paymentTransAttribute.PK) &&
        Objects.equals(this.parentPaymentTransaction, paymentTransAttribute.parentPaymentTransaction) &&
        Objects.equals(this.translations, paymentTransAttribute.translations) &&
        Objects.equals(this.updatedBy, paymentTransAttribute.updatedBy) &&
        Objects.equals(this.updatedTimestamp, paymentTransAttribute.updatedTimestamp) &&
        Objects.equals(this.value, paymentTransAttribute.value) &&
        Objects.equals(this.entityName, paymentTransAttribute.entityName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(actions, createdBy, createdTimestamp, extended, messages, name, orgId, PK, parentPaymentTransaction, translations, updatedBy, updatedTimestamp, value, entityName);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentTransAttribute {\n");
    
    sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    createdTimestamp: ").append(toIndentedString(createdTimestamp)).append("\n");
    sb.append("    extended: ").append(toIndentedString(extended)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    orgId: ").append(toIndentedString(orgId)).append("\n");
    sb.append("    PK: ").append(toIndentedString(PK)).append("\n");
    sb.append("    parentPaymentTransaction: ").append(toIndentedString(parentPaymentTransaction)).append("\n");
    sb.append("    translations: ").append(toIndentedString(translations)).append("\n");
    sb.append("    updatedBy: ").append(toIndentedString(updatedBy)).append("\n");
    sb.append("    updatedTimestamp: ").append(toIndentedString(updatedTimestamp)).append("\n");
    sb.append("    value: ").append(toIndentedString(value)).append("\n");
    sb.append("    entityName: ").append(toIndentedString(entityName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

