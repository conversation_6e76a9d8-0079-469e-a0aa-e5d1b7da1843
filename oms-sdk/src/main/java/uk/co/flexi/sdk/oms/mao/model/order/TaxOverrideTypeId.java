/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * TaxOverrideTypeId
 */
public class TaxOverrideTypeId {
  public static final String SERIALIZED_NAME_TAX_OVERRIDE_TYPE_ID = "TaxOverrideTypeId";
  @SerializedName(SERIALIZED_NAME_TAX_OVERRIDE_TYPE_ID)
  private String taxOverrideTypeId;

  public TaxOverrideTypeId taxOverrideTypeId(String taxOverrideTypeId) {
    this.taxOverrideTypeId = taxOverrideTypeId;
    return this;
  }

   /**
   * Indicates TaxOverrideType, currently we support &#39;FlatAmount&#39; and &#39;Percentage&#39;.This property holds either of one value.
   * @return taxOverrideTypeId
  **/
  public String getTaxOverrideTypeId() {
    return taxOverrideTypeId;
  }

  public void setTaxOverrideTypeId(String taxOverrideTypeId) {
    this.taxOverrideTypeId = taxOverrideTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaxOverrideTypeId taxOverrideTypeId = (TaxOverrideTypeId) o;
    return Objects.equals(this.taxOverrideTypeId, taxOverrideTypeId.taxOverrideTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(taxOverrideTypeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaxOverrideTypeId {\n");
    
    sb.append("    taxOverrideTypeId: ").append(toIndentedString(taxOverrideTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

