/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 2.71.1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * InteractionModeId
 */
public class InteractionModeId {
  public static final String SERIALIZED_NAME_INTERACTION_MODE_ID = "InteractionModeId";
  @SerializedName(SERIALIZED_NAME_INTERACTION_MODE_ID)
  private String interactionModeId;

  public InteractionModeId interactionModeId(String interactionModeId) {
    this.interactionModeId = interactionModeId;
    return this;
  }

   /**
   * Unique identifier of the interaction mode
   * @return interactionModeId
  **/
  
  public String getInteractionModeId() {
    return interactionModeId;
  }

  public void setInteractionModeId(String interactionModeId) {
    this.interactionModeId = interactionModeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InteractionModeId interactionModeId = (InteractionModeId) o;
    return Objects.equals(this.interactionModeId, interactionModeId.interactionModeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(interactionModeId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InteractionModeId {\n");
    
    sb.append("    interactionModeId: ").append(toIndentedString(interactionModeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

