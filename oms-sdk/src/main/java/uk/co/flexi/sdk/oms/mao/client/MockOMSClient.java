package uk.co.flexi.sdk.oms.mao.client;


import com.google.gson.Gson;
import org.apache.hc.core5.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import uk.co.flexi.sdk.oms.mao.client.config.MockConfig;
import uk.co.flexi.sdk.oms.model.OrderData;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.Base64;


public class MockOMSClient implements OMSClient<MockConfig> {

    private static final Logger log = LoggerFactory.getLogger(MockOMSClient.class);
    private final MockConfig mockConfig;
    private final HttpClient httpClient;
    private final Gson gson = new Gson();

    public MockOMSClient(MockConfig mockConfig) {
        this.mockConfig = mockConfig;
        this.httpClient = HttpClient.newHttpClient();
    }

    private OrderData sendGetRequest(String url) {
        String auth = mockConfig.getUserName() + ":" + mockConfig.getPassword();
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes(StandardCharsets.UTF_8));
        String authHeader = "Basic " + encodedAuth;
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Authorization", authHeader)
                .GET()
                .build();
        try {
            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            if (response.statusCode() == HttpStatus.SC_UNAUTHORIZED) {
                throw new IOException("Unauthorized: Invalid username or password");
            }
            return gson.fromJson(response.body(), OrderData.class);
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public OrderData getReturnOrderByTrackingNumber(String referenceId) {
        String url = String.format("%s/track-number/%s/search", mockConfig.getBasePath(), referenceId);
        return sendGetRequest(url);
    }

    @Override
    public OrderData getOrderById(String orderId) {
        String url = String.format("%s/order-id/%s/search", mockConfig.getBasePath(), orderId);
        return sendGetRequest(url);
    }

    @Override
    public OrderData getOrderByProductSerialNumber(String serialNumber) {
        return null;
    }

    @Override
    public OrderData getOrderByReturnTrackingNumber(String referenceId) {
        return null;
    }

    public static void main(String[] args) {
//        MockConfig config = new MockConfig("http://localhost:8090/api/oms/demo");
//        MockOMSClient client = new MockOMSClient(config);
//        log.info(String.valueOf(client.getReturnOrderByTrackingNumber("TRCK123445")));
//        log.info(String.valueOf(client.getOrderById("ORD1234")));
    }
}
