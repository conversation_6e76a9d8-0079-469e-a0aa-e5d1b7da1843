/*
 * REST API Documentation
 * Rest API description
 *
 * OpenAPI spec version: 1.206.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package uk.co.flexi.sdk.oms.mao.model.order;

import com.google.gson.annotations.SerializedName;

import java.util.Objects;

/**
 * PaymentResponseStatusId
 */
public class PaymentResponseStatusId {
  public static final String SERIALIZED_NAME_PAYMENT_RESPONSE_STATUS_ID = "PaymentResponseStatusId";
  @SerializedName(SERIALIZED_NAME_PAYMENT_RESPONSE_STATUS_ID)
  private String paymentResponseStatusId;

  public PaymentResponseStatusId paymentResponseStatusId(String paymentResponseStatusId) {
    this.paymentResponseStatusId = paymentResponseStatusId;
    return this;
  }

   /**
   * Indicates the outcome of a payment transaction. If response status is Success, then the payment transaction has been successfully processed and the processed amount reflects the amount processed. If response status is Failure, then no amount has been processed on the transaction. If response status is Fraud, then the payment transaction has been successfully processed but was flagged as fraud. If response status is Unavailable, then the payment gateway could not be contacted.
   * @return paymentResponseStatusId
  **/
  
  public String getPaymentResponseStatusId() {
    return paymentResponseStatusId;
  }

  public void setPaymentResponseStatusId(String paymentResponseStatusId) {
    this.paymentResponseStatusId = paymentResponseStatusId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PaymentResponseStatusId paymentResponseStatusId = (PaymentResponseStatusId) o;
    return Objects.equals(this.paymentResponseStatusId, paymentResponseStatusId.paymentResponseStatusId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(paymentResponseStatusId);
  }


  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PaymentResponseStatusId {\n");
    
    sb.append("    paymentResponseStatusId: ").append(toIndentedString(paymentResponseStatusId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

