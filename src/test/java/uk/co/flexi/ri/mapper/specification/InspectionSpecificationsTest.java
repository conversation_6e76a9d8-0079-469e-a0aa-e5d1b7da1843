package uk.co.flexi.ri.mapper.specification;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.junit.jupiter.api.Test;
import org.springframework.data.jpa.domain.Specification;
import uk.co.flexi.ri.model.view.InspectionView;

import java.time.OffsetDateTime;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class InspectionSpecificationsTest {

    Root<InspectionView> root = mock(Root.class);
    CriteriaQuery<?> query = mock(CriteriaQuery.class);
    CriteriaBuilder builder = mock(CriteriaBuilder.class);
    Predicate predicate = mock(Predicate.class);

    @Test
    void withAssigneeGroup_ShouldReturnPredicate() {
        when(builder.equal(any(), eq(1L))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withAssigneeGroup(1L);
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withCreatedGroup_ShouldReturnPredicate() {
        when(builder.equal(any(), eq(2L))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withCreatedGroup(2L);
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withPartnerName_ShouldReturnEqualPredicate() {
        when(builder.equal(any(), eq("PartnerX"))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withPartnerName("PartnerX", "eq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withPartnerName_ShouldReturnNotEqualPredicate() {
        when(builder.notEqual(any(), eq("PartnerX"))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withPartnerName("PartnerX", "neq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withPartnerName_ShouldReturnNullWhenPartnerNameIsNull() {
        Specification<InspectionView> spec = InspectionSpecifications.withPartnerName(null, "eq");
        assertThat(spec.toPredicate(root, query, builder)).isNull();
    }

    @Test
    void withReferenceId_ShouldReturnEqualPredicate() {
        when(builder.equal(any(), eq("REF123"))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withReferenceId("REF123", "eq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withReferenceId_ShouldReturnNotEqualPredicate() {
        when(builder.notEqual(any(), eq("REF123"))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withReferenceId("REF123", "neq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withOrderId_ShouldReturnEqualPredicate() {
        when(builder.equal(any(), eq("ORD456"))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withOrderId("ORD456", "eq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withOrderId_ShouldReturnNullWhenOrderIdIsNull() {
        Specification<InspectionView> spec = InspectionSpecifications.withOrderId(null, "eq");
        assertThat(spec.toPredicate(root, query, builder)).isNull();
    }

    @Test
    void withCreatedAt_ShouldReturnEqualPredicate() {
        when(builder.between(any(), any(OffsetDateTime.class), any(OffsetDateTime.class))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withCreatedAt("2023-01-01", "eq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withCreatedAt_ShouldReturnNullForBlankDate() {
        assertThat(InspectionSpecifications.withCreatedAt("", "eq").toPredicate(root, query, builder)).isNull();
    }

    @Test
    void withStatus_ShouldReturnEqualPredicate() {
        when(builder.equal(any(), eq("Completed"))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withStatus("Completed", "eq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withSellingChannel_ShouldReturnNotEqualPredicate() {
        when(builder.notEqual(any(), eq("Online"))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withSellingChannel("Online", "neq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withAssigneeGroupString_ShouldReturnEqualPredicate() {
        when(builder.equal(any(), eq("Group1"))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withAssigneeGroup("Group1", "eq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withRemainingDays_ShouldReturnNotEqualPredicate() {
        when(builder.notEqual(any(), eq(5))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withRemainingDays(5, "neq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withRemainingDays_ShouldReturnNullWhenNullInput() {
        assertThat(InspectionSpecifications.withRemainingDays(null, "eq").toPredicate(root, query, builder)).isNull();
    }

    @Test
    void constructor_ShouldInstantiateUsingReflection() throws Exception {
        var constructor = InspectionSpecifications.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        Object instance = constructor.newInstance();
        assertThat(instance).isInstanceOf(InspectionSpecifications.class);
    }

    @Test
    void withCreatedAt_ShouldReturnNotEqualPredicate() {
        when(builder.lessThan(any(), any(OffsetDateTime.class))).thenReturn(predicate);
        when(builder.greaterThan(any(), any(OffsetDateTime.class))).thenReturn(predicate);
        when(builder.or(any(Predicate.class), any(Predicate.class))).thenReturn(predicate);

        Specification<InspectionView> spec = InspectionSpecifications.withCreatedAt("2023-01-01", "neq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withCreatedAt_ShouldReturnNullWhenNullInput() {
        Specification<InspectionView> spec = InspectionSpecifications.withCreatedAt(null, "eq");
        assertThat(spec.toPredicate(root, query, builder)).isNull();
    }

    @Test
    void withOrderId_ShouldReturnNotEqualPredicate() {
        when(builder.notEqual(any(), eq("ORD123"))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withOrderId("ORD123", "neq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withReferenceId_ShouldReturnNullWhenReferenceIdIsNull() {
        Specification<InspectionView> spec = InspectionSpecifications.withReferenceId(null, "eq");
        assertThat(spec.toPredicate(root, query, builder)).isNull();
    }

    @Test
    void withPartnerName_ShouldReturnNullWhenNull() {
        Specification<InspectionView> spec = InspectionSpecifications.withPartnerName(null, "eq");
        assertThat(spec.toPredicate(root, query, builder)).isNull();
    }

    @Test
    void withSellingChannel_ShouldReturnEqualPredicate() {
        when(builder.equal(any(), eq("Online"))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withSellingChannel("Online", "eq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withSellingChannel_ShouldReturnNullWhenNull() {
        Specification<InspectionView> spec = InspectionSpecifications.withSellingChannel(null, "eq");
        assertThat(spec.toPredicate(root, query, builder)).isNull();
    }

    @Test
    void withStatus_ShouldReturnNullWhenNull() {
        Specification<InspectionView> spec = InspectionSpecifications.withStatus(null, "eq");
        assertThat(spec.toPredicate(root, query, builder)).isNull();
    }

    @Test
    void withAssigneeGroupString_ShouldReturnNotEqualPredicate() {
        when(builder.notEqual(any(), eq("GroupX"))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withAssigneeGroup("GroupX", "neq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }

    @Test
    void withAssigneeGroupString_ShouldReturnNullWhenNull() {
        Specification<InspectionView> spec = InspectionSpecifications.withAssigneeGroup(null, "eq");
        assertThat(spec.toPredicate(root, query, builder)).isNull();
    }

    @Test
    void withRemainingDays_ShouldReturnEqualPredicate() {
        when(builder.equal(any(), eq(7))).thenReturn(predicate);
        Specification<InspectionView> spec = InspectionSpecifications.withRemainingDays(7, "eq");
        assertThat(spec.toPredicate(root, query, builder)).isEqualTo(predicate);
    }
}
