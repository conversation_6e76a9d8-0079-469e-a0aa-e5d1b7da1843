package uk.co.flexi.ri.mapper.factory;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import uk.co.flexi.ri.mapper.OrderDetailMapper;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrderDetailMapperFactoryTest {

    @Mock
    private OrderDetailMapper defaultMapper;

    @Mock
    private OrderDetailMapper customMapper;

    private OrderDetailMapperFactory factory;
    private Map<String, OrderDetailMapper> mappers;

    @BeforeEach
    void setUp() {
        mappers = new HashMap<>();
        mappers.put("defaultMapper", defaultMapper);
        mappers.put("customMapper", customMapper);
        factory = new OrderDetailMapperFactory(mappers);
    }

    @Test
    void getMapper_WithExistingMapper_ShouldReturnCorrectMapper() {
        // Act
        OrderDetailMapper result = factory.getMapper("customMapper");

        // Assert
        assertNotNull(result);
        assertEquals(customMapper, result);
    }

    @Test
    void getMapper_WithNonExistingMapper_ShouldReturnDefaultMapper() {
        // Act
        OrderDetailMapper result = factory.getMapper("nonExistingMapper");

        // Assert
        assertNotNull(result);
        assertEquals(defaultMapper, result);
    }

    @Test
    void getMapper_WithNullProvider_ShouldReturnDefaultMapper() {
        // Act
        OrderDetailMapper result = factory.getMapper(null);

        // Assert
        assertNotNull(result);
        assertEquals(defaultMapper, result);
    }

    @Test
    void getMapper_WithEmptyProvider_ShouldReturnDefaultMapper() {
        // Act
        OrderDetailMapper result = factory.getMapper("");

        // Assert
        assertNotNull(result);
        assertEquals(defaultMapper, result);
    }
} 