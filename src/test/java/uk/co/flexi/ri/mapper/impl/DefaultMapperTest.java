package uk.co.flexi.ri.mapper.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.dto.OrderDetailDTO;
import uk.co.flexi.ri.dto.ProductDetailDTO;
import uk.co.flexi.sdk.oms.model.OrderData;
import uk.co.flexi.sdk.oms.model.OrderLineData;
import uk.co.flexi.sdk.oms.model.OrderLineItemData;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class DefaultMapperTest {

    private DefaultMapper defaultMapper;
    private OrderData orderData;
    private OrderLineData orderLineData;
    private OrderLineItemData item;

    @BeforeEach
    void setUp() {
        defaultMapper = new DefaultMapper();
        
        // Setup Item
        item = new OrderLineItemData();
        item.setName("Test Product");
        item.setSku("TEST-SKU-001");
        item.setDescription("Test Description");
        item.setQuantity(2);
        item.setImages(List.of("image1.jpg", "image2.jpg"));
        item.setColor("Red/Blue");
        item.setSize("M");
        item.setProductClass("Class A");
        item.setProductSubclass("Subclass B");
        item.setSeason("Summer");
        item.setBrand("Test Brand");
        item.setDepartmentName("Test Department");
        item.setStyle("Casual");
        item.setCurrencyCode("USD");
        item.setUnitPrice(new BigDecimal("99.99"));

        // Setup OrderLineData
        orderLineData = new OrderLineData();
        orderLineData.setItem(item);
        orderLineData.setReturnReason("Wrong Size");
        orderLineData.setCountry("UK");
        orderLineData.setRegion("London");
        orderLineData.setCity("London");
        orderLineData.setItemCondition("New");
        orderLineData.setOrderLineId("OL-001");

        // Setup OrderData
        orderData = new OrderData();
        orderData.setPartnerName("Test Partner");
        orderData.setSellingChannel("Online");
        orderData.setOrderId("ORD-001");
        orderData.setTrackingNumber("TRK-001");
        orderData.setCustomerFirstName("John");
        orderData.setCustomerLastName("Doe");
        orderData.setCustomerEmail("<EMAIL>");
        orderData.setOrderLines(List.of(orderLineData));
    }

    @Test
    void map_ShouldMapOrderDataCorrectly() {
        // Act
        OrderDetailDTO result = defaultMapper.map(orderData, "REF-001");

        // Assert
        assertNotNull(result);
        assertEquals("REF-001", result.getReferenceId());
        assertEquals("Test Partner", result.getPartnerName());
        assertEquals("Online", result.getSellingChannel());
        assertEquals("ORD-001", result.getOrderId());
        assertEquals("TRK-001", result.getReturnTrackingId());
        assertEquals("John", result.getCustomerFirstName());
        assertEquals("Doe", result.getCustomerLastName());
        assertEquals("<EMAIL>", result.getCustomerEmail());
        
        assertNotNull(result.getProductDetails());
        assertEquals(1, result.getProductDetails().size());
        
        ProductDetailDTO productDetail = result.getProductDetails().get(0);
        assertEquals("Test Product", productDetail.getProductName());
        assertEquals("TEST-SKU-001", productDetail.getSku());
        assertEquals("Test Description", productDetail.getDescription());
        assertEquals(BigDecimal.ZERO, productDetail.getReceivedQuantity());
        assertEquals("image1.jpg", productDetail.getProductImage());
        assertEquals(List.of("image1.jpg", "image2.jpg"), productDetail.getProductImages());
        assertEquals("Red,Blue", productDetail.getColour());
        assertEquals("M", productDetail.getSize());
        assertEquals("Wrong Size", productDetail.getReturnReason());
        assertEquals("Class A", productDetail.getProductClass());
        assertEquals("Subclass B", productDetail.getProductSubclass());
        assertEquals("Summer", productDetail.getSeason());
        assertEquals("Test Brand", productDetail.getBrand());
        assertEquals("Test Department", productDetail.getDepartmentName());
        assertEquals("Casual", productDetail.getStyle());
        assertEquals("UK", productDetail.getCountry());
        assertEquals("London", productDetail.getRegion());
        assertEquals("London", productDetail.getCity());
        assertEquals("New", productDetail.getExpectedItemCondition());
        assertEquals("USD", productDetail.getCurrencyCode());
        assertEquals(new BigDecimal("99.99"), productDetail.getUnitPrice());
        assertEquals("OL-001", productDetail.getOrderLineId());
    }

    @Test
    void map_WithReturnOrder_ShouldMapCorrectly() {
        // Arrange
        orderData.setParentOrderId("PARENT-001");

        // Act
        OrderDetailDTO result = defaultMapper.map(orderData, "REF-001");

        // Assert
        assertEquals("PARENT-001", result.getOrderId());
        assertEquals("ORD-001", result.getReturnOrderId());
    }

    @Test
    void map_WithNullValues_ShouldHandleGracefully() {
        // Arrange
        orderData.setPartnerName(null);
        orderData.setSellingChannel(" ");
        orderData.setCustomerFirstName("null");

        // Act
        OrderDetailDTO result = defaultMapper.map(orderData, "REF-001");

        // Assert
        assertNull(result.getPartnerName());
        assertNull(result.getSellingChannel());
    }

    @Test
    void convertToCommaSeparated_ShouldHandleVariousInputs() {
        // Test cases
        assertEquals("Red,Blue", DefaultMapper.convertToCommaSeparated("Red/Blue"));
        assertEquals(null, DefaultMapper.convertToCommaSeparated(null));
        assertEquals("", DefaultMapper.convertToCommaSeparated(""));
        assertEquals("Single", DefaultMapper.convertToCommaSeparated("Single"));
        assertEquals("A,B,C", DefaultMapper.convertToCommaSeparated("A/B/C"));
    }
} 