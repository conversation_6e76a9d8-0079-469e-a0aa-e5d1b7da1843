package uk.co.flexi.ri.mapper.impl;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.dto.OrderDetailDTO;
import uk.co.flexi.ri.dto.ProductDetailDTO;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.model.Order;
import uk.co.flexi.sdk.oms.model.OrderData;
import uk.co.flexi.sdk.oms.model.OrderLineData;
import uk.co.flexi.sdk.oms.model.OrderLineItemData;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class MAOOrderDetailMapperTest {

    private MAOOrderDetailMapper mapper;
    private OrderData orderData;
    private OrderLineData orderLineData;
    private OrderLineItemData item;

    @BeforeEach
    void setUp() {
        mapper = new MAOOrderDetailMapper();
        
        // Setup test data
        item = new OrderLineItemData();
        item.setName("Test Product");
        item.setSku("TEST-SKU-001");
        item.setDescription("Test Description");
        item.setQuantity(2);
        item.setImages(List.of("image1.jpg", "image2.jpg"));
        item.setColor("Red/Blue");
        item.setSize("M");
        item.setProductClass("Clothing");
        item.setProductSubclass("T-Shirt");
        item.setSeason("Summer");
        item.setBrand("Test Brand");
        item.setDepartmentName("Men's Wear");
        item.setStyle("Casual");
        item.setCurrencyCode("USD");
        item.setUnitPrice(new BigDecimal("29.99"));

        orderLineData = new OrderLineData();
        orderLineData.setItem(item);
        orderLineData.setReturnReason("Wrong Size");
        orderLineData.setCountry("UK");
        orderLineData.setRegion("London");
        orderLineData.setCity("London");
        orderLineData.setItemCondition("New");
        orderLineData.setOrderLineId("OL-001");

        orderData = new OrderData();
        orderData.setOrderId("ORD-001");
        orderData.setParentOrderId(null);
        orderData.setPartnerName("Test Partner");
        orderData.setSellingChannel("Online");
        orderData.setTrackingNumber("TRK-001");
        orderData.setCustomerFirstName("John");
        orderData.setCustomerLastName("Doe");
        orderData.setCustomerEmail("<EMAIL>");
        orderData.setOrderLines(List.of(orderLineData));
    }

    @Test
    void map_ShouldMapOrderDataCorrectly() {
        // Act
        OrderDetailDTO result = mapper.map(orderData, "REF-001");

        // Assert
        assertNotNull(result);
        assertEquals("REF-001", result.getReferenceId());
        assertEquals("Test Partner", result.getPartnerName());
        assertEquals("Online", result.getSellingChannel());
        assertEquals("ORD-001", result.getOrderId());
        assertNull(result.getReturnOrderId());
        assertEquals("TRK-001", result.getReturnTrackingId());
        assertEquals("John", result.getCustomerFirstName());
        assertEquals("Doe", result.getCustomerLastName());
        assertEquals("<EMAIL>", result.getCustomerEmail());

        // Verify product details
        assertNotNull(result.getProductDetails());
        assertEquals(1, result.getProductDetails().size());
        
        ProductDetailDTO productDetail = result.getProductDetails().get(0);
        assertEquals("Test Product", productDetail.getProductName());
        assertEquals("TEST-SKU-001", productDetail.getSku());
        assertEquals("Test Description", productDetail.getDescription());
        assertEquals(BigDecimal.ZERO, productDetail.getReceivedQuantity());
        assertEquals("image1.jpg", productDetail.getProductImage());
        assertEquals(List.of("image1.jpg", "image2.jpg"), productDetail.getProductImages());
        assertEquals("Red,Blue", productDetail.getColour());
        assertEquals("M", productDetail.getSize());
        assertEquals("Wrong Size", productDetail.getReturnReason());
        assertEquals("Clothing", productDetail.getProductClass());
        assertEquals("T-Shirt", productDetail.getProductSubclass());
        assertEquals("Summer", productDetail.getSeason());
        assertEquals("Test Brand", productDetail.getBrand());
        assertEquals("Men's Wear", productDetail.getDepartmentName());
        assertEquals("Casual", productDetail.getStyle());
        assertEquals("UK", productDetail.getCountry());
        assertEquals("London", productDetail.getRegion());
        assertEquals("London", productDetail.getCity());
        assertEquals("New", productDetail.getExpectedItemCondition());
        assertEquals("USD", productDetail.getCurrencyCode());
        assertEquals(new BigDecimal("29.99"), productDetail.getUnitPrice());
        assertEquals("OL-001", productDetail.getOrderLineId());
    }

    @Test
    void map_WithReturnOrder_ShouldMapCorrectly() {
        // Arrange
        orderData.setParentOrderId("PARENT-001");

        // Act
        OrderDetailDTO result = mapper.map(orderData, "REF-001");

        // Assert
        assertEquals("PARENT-001", result.getOrderId());
        assertEquals("ORD-001", result.getReturnOrderId());
    }

    @Test
    void map_WithNullValues_ShouldHandleGracefully() {
        // Arrange
        orderData.setPartnerName(null);
        orderData.setSellingChannel("null");
        orderData.setCustomerFirstName("");
        item.setColor(null);
        item.setSize("");
        orderLineData.setReturnReason("null");

        // Act
        OrderDetailDTO result = mapper.map(orderData, "REF-001");

        // Assert
        assertNull(result.getPartnerName());
        assertNull(result.getSellingChannel());
        assertNull(result.getProductDetails().get(0).getColour());
        assertNull(result.getProductDetails().get(0).getReturnReason());
    }

    @Test
    void mapExistingOrderDetailDTO_WithInspectionId_ShouldMapCorrectly() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        
        Inspection inspection = new Inspection();
        inspection.setInspectionId("INS-001");
        
        InspectionItem inspectionItem = new InspectionItem();
        inspectionItem.setReceivedQuantity(new BigDecimal("1"));
        inspection.setInspectionItems(List.of(inspectionItem));
        
        order.setInspections(List.of(inspection));

        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        ProductDetailDTO productDetail = new ProductDetailDTO();
        productDetail.setSku("TEST-SKU-001");
        orderDetailDTO.setProductDetails(List.of(productDetail));

        // Act
        OrderDetailDTO result = mapper.mapExistingOrderDetailDTO(order, orderDetailDTO, "INS-001");

        // Assert
        assertEquals("INS-001", result.getInspectionId());
        assertEquals(1L, result.getOrderPk());
    }

    @Test
    void mapExistingOrderDetailDTO_WithoutInspectionId_ShouldMapCorrectly() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        
        Inspection inspection = new Inspection();
        inspection.setInspectionId("INS-001");
        
        InspectionItem inspectionItem = new InspectionItem();
        inspectionItem.setReceivedQuantity(new BigDecimal("1"));
        inspection.setInspectionItems(List.of(inspectionItem));
        
        order.setInspections(List.of(inspection));

        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        ProductDetailDTO productDetail = new ProductDetailDTO();
        productDetail.setSku("TEST-SKU-001");
        orderDetailDTO.setProductDetails(List.of(productDetail));

        // Act
        OrderDetailDTO result = mapper.mapExistingOrderDetailDTO(order, orderDetailDTO, null);

        // Assert
        assertEquals("INS-001", result.getInspectionId());
        assertEquals(1L, result.getOrderPk());
    }

    @Test
    void mapExistingOrderDetailDTO_WithNonExistentInspectionId_ShouldThrowException() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        
        Inspection inspection = new Inspection();
        inspection.setInspectionId("INS-001");
        order.setInspections(List.of(inspection));

        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () -> 
            mapper.mapExistingOrderDetailDTO(order, orderDetailDTO, "NON-EXISTENT"));
    }

    @Test
    void mapExistingOrderDetailDTO_WithNoInspections_ShouldThrowException() {
        // Arrange
        Order order = new Order();
        order.setId(1L);
        order.setInspections(new ArrayList<>());

        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();

        // Act & Assert
        assertThrows(IllegalStateException.class, () -> 
            mapper.mapExistingOrderDetailDTO(order, orderDetailDTO, null));
    }
} 