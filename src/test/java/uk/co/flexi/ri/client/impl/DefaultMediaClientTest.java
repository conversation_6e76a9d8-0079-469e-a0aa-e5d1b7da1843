package uk.co.flexi.ri.client.impl;

import org.junit.jupiter.api.Test;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.client.MediaClient;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class DefaultMediaClientTest {

    private final DefaultMediaClient mediaClient = new DefaultMediaClient();

    @Test
    void putFile_shouldReturnNull() throws IOException {
        // Arrange
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getOriginalFilename()).thenReturn("test.jpg");
        when(mockFile.getContentType()).thenReturn("image/jpeg");

        // Act
        String result = mediaClient.putFile(mockFile, MediaClient.FileGroup.COMMENT, "test-bucket");

        // Assert
        assertNull(result);
    }

    @Test
    void getFile_shouldReturnNull() {
        // Act
        InputStream result = mediaClient.getFile("test.jpg", "test-bucket");

        // Assert
        assertNull(result);
    }

    @Test
    void deleteFile_shouldDoNothing() {
        // Act & Assert
        assertDoesNotThrow(() -> mediaClient.deleteFile("test.jpg", "test-bucket"));
    }

    @Test
    void createBucketNotExist_shouldDoNothing() {
        // Act & Assert
        assertDoesNotThrow(() -> mediaClient.createBucketNotExist("test-bucket"));
    }

    @Test
    void identifyFileType_shouldReturnImage_forImageContentType() throws IOException {
        // Arrange
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getContentType()).thenReturn("image/jpeg");

        // Act
        String result = mediaClient.identifyFileType(mockFile);

        // Assert
        assertEquals("image", result);
    }

    @Test
    void identifyFileType_shouldReturnVideo_forVideoContentType() throws IOException {
        // Arrange
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getContentType()).thenReturn("video/mp4");

        // Act
        String result = mediaClient.identifyFileType(mockFile);

        // Assert
        assertEquals("video", result);
    }

    @Test
    void identifyFileType_shouldThrow_forUnknownContentType() throws IOException {
        // Arrange
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getContentType()).thenReturn("application/pdf");

        // Act & Assert
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class,
                () -> mediaClient.identifyFileType(mockFile));
        assertEquals("Unknown file type: application/pdf", ex.getMessage());
    }

    @Test
    void identifyFileType_shouldThrow_forNullContentType() throws IOException {
        // Arrange
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getContentType()).thenReturn(null);

        // Act & Assert
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class,
                () -> mediaClient.identifyFileType(mockFile));
        assertEquals("Unknown file type: null", ex.getMessage());
    }
} 