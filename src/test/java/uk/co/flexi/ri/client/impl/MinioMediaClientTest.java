package uk.co.flexi.ri.client.impl;

import io.minio.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.client.MediaClient;
import uk.co.flexi.ri.exception.custom.MinioException;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MinioMediaClientTest {

    private MinioClient minioClient;
    private MinioMediaClient mediaClient;

    @BeforeEach
    void setUp() {
        minioClient = mock(MinioClient.class);
        mediaClient = new MinioMediaClient(minioClient);

        mediaClient.minioImageUpload = "images/";
        mediaClient.minioProfileImageUpload = "profiles/";
        mediaClient.minioTempImageUpload = "temp/";
        mediaClient.minioVideoUpload = "videos/";
    }

    @Test
    void putFile_shouldUploadSuccessfully_forCommentImage() throws Exception {
        MultipartFile file = mockImageFile("comment.jpg", "image/jpeg");

        String result = mediaClient.putFile(file, MediaClient.FileGroup.COMMENT, "test-bucket");

        assertNotNull(result);
        assertTrue(result.startsWith("images/comment"));
        verify(minioClient, times(1)).putObject(any(PutObjectArgs.class));
    }

    @Test
    void putFile_shouldUploadSuccessfully_forProfilePic() throws Exception {
        MultipartFile file = mockImageFile("profile.jpg", "image/jpeg");

        String result = mediaClient.putFile(file, MediaClient.FileGroup.PROFILE_PIC, "test-bucket");

        assertNotNull(result);
        assertTrue(result.startsWith("profiles/profile"));
        verify(minioClient, times(1)).putObject(any(PutObjectArgs.class));
    }

    @Test
    void putFile_shouldUploadSuccessfully_forTempImage() throws Exception {
        MultipartFile file = mockImageFile("temp.jpg", "image/jpeg");

        String result = mediaClient.putFile(file, MediaClient.FileGroup.TEMP_IMAGE, "test-bucket");

        assertNotNull(result);
        assertTrue(result.startsWith("temp/temp"));
        verify(minioClient, times(1)).putObject(any(PutObjectArgs.class));
    }

    @Test
    void putFile_shouldUploadSuccessfully_forVideo() throws Exception {
        MultipartFile file = mockImageFile("video.mp4", "video/mp4");

        String result = mediaClient.putFile(file, MediaClient.FileGroup.COMMENT, "test-bucket");

        assertNotNull(result);
        assertTrue(result.startsWith("videos/video"));
        verify(minioClient, times(1)).putObject(any(PutObjectArgs.class));
    }


    @Test
    void putFile_shouldThrow_onIOException() throws Exception {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getInputStream()).thenThrow(new IOException("IO error"));
        when(file.getOriginalFilename()).thenReturn("file.jpg");
        when(file.getContentType()).thenReturn("image/jpeg");

        MinioException ex = assertThrows(MinioException.class,
                () -> mediaClient.putFile(file, MediaClient.FileGroup.COMMENT, "bucket"));

        assertEquals("File upload failed", ex.getMessage());
    }

    @Test
    void putFile_shouldThrow_onMinioException() throws Exception {
        MultipartFile file = mockImageFile("test.jpg", "image/jpeg");
        doThrow(new MinioException("Minio error")).when(minioClient).putObject(any(PutObjectArgs.class));

        MinioException ex = assertThrows(MinioException.class,
                () -> mediaClient.putFile(file, MediaClient.FileGroup.COMMENT, "bucket"));

        assertEquals("File upload failed", ex.getMessage());
    }

    @Test
    void getFile_shouldReturnStream() throws Exception {
        GetObjectResponse mockResponse = mock(GetObjectResponse.class);
        when(minioClient.getObject(any(GetObjectArgs.class))).thenReturn(mockResponse);

        InputStream result = mediaClient.getFile("file.txt", "bucket");

        assertNotNull(result);
        verify(minioClient, times(1)).getObject(any(GetObjectArgs.class));
    }

    @Test
    void getFile_shouldThrow_onFailure() throws Exception {
        when(minioClient.getObject(any(GetObjectArgs.class))).thenThrow(new MinioException("Minio error"));

        MinioException ex = assertThrows(MinioException.class,
                () -> mediaClient.getFile("file.txt", "bucket"));

        assertEquals("File download failed", ex.getMessage());
    }

    @Test
    void deleteFile_shouldWork() throws Exception {
        mediaClient.deleteFile("file.txt", "bucket");

        verify(minioClient, times(1)).removeObject(any(RemoveObjectArgs.class));
    }

    @Test
    void deleteFile_shouldThrow_onError() throws Exception {
        doThrow(new MinioException("Minio error"))
                .when(minioClient).removeObject(any(RemoveObjectArgs.class));

        MinioException ex = assertThrows(MinioException.class,
                () -> mediaClient.deleteFile("f.txt", "bkt"));

        assertEquals("File deletion from storage failed", ex.getMessage());
    }

    @Test
    void createBucket_shouldCreateIfNotExists() throws Exception {
        when(minioClient.bucketExists(any(BucketExistsArgs.class))).thenReturn(false);

        mediaClient.createBucketNotExist("mybucket");

        verify(minioClient, times(1)).makeBucket(any(MakeBucketArgs.class));
    }

    @Test
    void createBucket_shouldNotCreateIfExists() throws Exception {
        when(minioClient.bucketExists(any(BucketExistsArgs.class))).thenReturn(true);

        mediaClient.createBucketNotExist("existing");
        verify(minioClient, never()).makeBucket(any());
    }

    @Test
    void createBucket_shouldThrow_onFailure() throws Exception {
        when(minioClient.bucketExists(any())).thenThrow(new RuntimeException("boom"));

        RuntimeException ex = assertThrows(RuntimeException.class,
                () -> mediaClient.createBucketNotExist("fail-bucket"));

        assertEquals("Failed to ensure bucket exists", ex.getMessage());
    }

    @Test
    void identifyFileType_shouldReturnImage_forImageContentType() {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("image/jpeg");

        String result = mediaClient.identifyFileType(file);
        assertEquals("image", result);
    }

    @Test
    void identifyFileType_shouldReturnVideo_forVideoContentType() {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("video/mp4");

        String result = mediaClient.identifyFileType(file);
        assertEquals("video", result);
    }

    @Test
    void identifyFileType_shouldThrow_forUnknownContentType() {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("application/pdf");

        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class,
                () -> mediaClient.identifyFileType(file));
        assertEquals("Unknown file type: application/pdf", ex.getMessage());
    }

    @Test
    void identifyFileType_shouldThrow_forNullContentType() {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn(null);

        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class,
                () -> mediaClient.identifyFileType(file));
        assertEquals("Unknown file type: null", ex.getMessage());
    }

//    @Test
//    void putFile_shouldHandleNullFileName() throws Exception {
//        MultipartFile file = mock(MultipartFile.class);
//        when(file.getOriginalFilename()).thenReturn(null);
//        when(file.getContentType()).thenReturn("image/jpeg");
//        when(file.getInputStream()).thenReturn(new ByteArrayInputStream("x".getBytes()));
//        when(file.getSize()).thenReturn(1L);
//
//        String result = mediaClient.putFile(file, MediaClient.FileGroup.COMMENT, "test-bucket");
//
//        assertNotNull(result);
//        assertTrue(result.startsWith("images/test"));
//        verify(minioClient, times(1)).putObject(any(PutObjectArgs.class));
//    }
//
//    @Test
//    void putFile_shouldThrow_onUnsupportedType() throws IOException {
//        MultipartFile file = mock(MultipartFile.class);
//        when(file.getOriginalFilename()).thenReturn("temp.xyz");
//        when(file.getContentType()).thenReturn("application/pdf");
//
//        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class,
//                () -> mediaClient.putFile(file, MediaClient.FileGroup.COMMENT, "bucket"));
//
//        assertEquals("Unsupported file type", ex.getMessage());
//    }

    // Helpers
    private MultipartFile mockImageFile(String name, String contentType) throws IOException {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getOriginalFilename()).thenReturn(name);
        when(file.getContentType()).thenReturn(contentType);
        when(file.getInputStream()).thenReturn(new ByteArrayInputStream("x".getBytes()));
        when(file.getSize()).thenReturn(1L);
        return file;
    }
} 