package uk.co.flexi.ri.client.factory;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.client.MediaClient;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;

class MediaClientFactoryTest {

    private MediaClientFactory factory;
    private MediaClient minioClient;
    private MediaClient defaultClient;

    @BeforeEach
    void setUp() {
        minioClient = mock(MediaClient.class);
        defaultClient = mock(MediaClient.class);

        Map<String, MediaClient> clients = new HashMap<>();
        clients.put("minioMediaClient", minioClient);
        clients.put("defaultMediaClient", defaultClient);

        factory = new MediaClientFactory(clients);
    }

    @Test
    void getClient_shouldReturnMinioClient_whenProviderIsMinio() {
        // Act
        MediaClient result = factory.getClient("minioMediaClient");

        // Assert
        assertNotNull(result);
        assertEquals(minioClient, result);
    }

    @Test
    void getClient_shouldReturnDefaultClient_whenProviderIsDefault() {
        // Act
        MediaClient result = factory.getClient("defaultMediaClient");

        // Assert
        assertNotNull(result);
        assertEquals(defaultClient, result);
    }

    @Test
    void getClient_shouldReturnDefaultClient_whenProviderIsUnknown() {
        // Act
        MediaClient result = factory.getClient("unknownProvider");

        // Assert
        assertNotNull(result);
        assertEquals(defaultClient, result);
    }

    @Test
    void getClient_shouldReturnDefaultClient_whenProviderIsNull() {
        // Act
        MediaClient result = factory.getClient(null);

        // Assert
        assertNotNull(result);
        assertEquals(defaultClient, result);
    }

    @Test
    void getClient_shouldReturnDefaultClient_whenProviderIsEmpty() {
        // Act
        MediaClient result = factory.getClient("");

        // Assert
        assertNotNull(result);
        assertEquals(defaultClient, result);
    }

    @Test
    void getClient_shouldReturnDefaultClient_whenProviderIsBlank() {
        // Act
        MediaClient result = factory.getClient("   ");

        // Assert
        assertNotNull(result);
        assertEquals(defaultClient, result);
    }
} 