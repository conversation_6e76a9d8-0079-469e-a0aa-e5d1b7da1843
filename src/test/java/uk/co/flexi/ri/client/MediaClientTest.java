package uk.co.flexi.ri.client;

import org.junit.jupiter.api.Test;
import org.springframework.web.multipart.MultipartFile;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class MediaClientTest {

    // Simple implementation to test the default method
    private static class TestMediaClient implements MediaClient {
        @Override
        public String putFile(MultipartFile file, FileGroup fileGroup, String bucketName) {
            return null;
        }

        @Override
        public java.io.InputStream getFile(String fileName, String bucketName) {
            return null;
        }

        @Override
        public void deleteFile(String filename, String bucketName) {
        }

        @Override
        public void createBucketNotExist(String bucketName) {
        }
    }

    private final MediaClient mediaClient = new TestMediaClient();

    @Test
    void identifyFileType_shouldReturnImage_forJpegContentType() {
        // Arrange
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("image/jpeg");

        // Act
        String result = mediaClient.identifyFileType(file);

        // Assert
        assertEquals("image", result);
    }

    @Test
    void identifyFileType_shouldReturnImage_forPngContentType() {
        // Arrange
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("image/png");

        // Act
        String result = mediaClient.identifyFileType(file);

        // Assert
        assertEquals("image", result);
    }

    @Test
    void identifyFileType_shouldReturnImage_forGifContentType() {
        // Arrange
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("image/gif");

        // Act
        String result = mediaClient.identifyFileType(file);

        // Assert
        assertEquals("image", result);
    }

    @Test
    void identifyFileType_shouldReturnVideo_forMp4ContentType() {
        // Arrange
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("video/mp4");

        // Act
        String result = mediaClient.identifyFileType(file);

        // Assert
        assertEquals("video", result);
    }

    @Test
    void identifyFileType_shouldReturnVideo_forWebmContentType() {
        // Arrange
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("video/webm");

        // Act
        String result = mediaClient.identifyFileType(file);

        // Assert
        assertEquals("video", result);
    }

    @Test
    void identifyFileType_shouldThrow_forUnknownContentType() {
        // Arrange
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("application/pdf");

        // Act & Assert
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class,
                () -> mediaClient.identifyFileType(file));
        assertEquals("Unknown file type: application/pdf", ex.getMessage());
    }

    @Test
    void identifyFileType_shouldThrow_forNullContentType() {
        // Arrange
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn(null);

        // Act & Assert
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class,
                () -> mediaClient.identifyFileType(file));
        assertEquals("Unknown file type: null", ex.getMessage());
    }

    @Test
    void identifyFileType_shouldThrow_forEmptyContentType() {
        // Arrange
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("");

        // Act & Assert
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class,
                () -> mediaClient.identifyFileType(file));
        assertEquals("Unknown file type: ", ex.getMessage());
    }

    @Test
    void identifyFileType_shouldThrow_forNonImageVideoContentType() {
        // Arrange
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("text/plain");

        // Act & Assert
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class,
                () -> mediaClient.identifyFileType(file));
        assertEquals("Unknown file type: text/plain", ex.getMessage());
    }
} 