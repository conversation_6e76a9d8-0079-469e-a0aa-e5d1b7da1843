package uk.co.flexi.ri.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class HMACUtilsTest {

    @Test
    void generate_ShouldGenerateValidHMAC() {
        // Arrange
        String data = "test data";
        String secret = "test secret";

        // Act
        String hmac = HMACUtils.generate(data, secret);

        // Assert
        assertNotNull(hmac);
        assertTrue(hmac.matches("^[A-Za-z0-9+/=]+$")); // Base64 pattern
    }

    @Test
    void generate_ShouldGenerateConsistentHMAC() {
        // Arrange
        String data = "test data";
        String secret = "test secret";

        // Act
        String hmac1 = HMACUtils.generate(data, secret);
        String hmac2 = HMACUtils.generate(data, secret);

        // Assert
        assertEquals(hmac1, hmac2);
    }


    @Test
    void generate_ShouldThrowExceptionForInvalidSecret() {
        // Arrange
        String data = "test data";
        String secret = null;

        // Act & Assert
        assertThrows(RuntimeException.class, () -> HMACUtils.generate(data, secret));
    }
} 