package uk.co.flexi.ri.util.covertor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class JsonNodeConverterTest {

    private JsonNodeConverter converter;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        converter = new JsonNodeConverter();
        objectMapper = new ObjectMapper();
    }

    @Test
    void convertToDatabaseColumn_WithValidJsonNode_ShouldConvertToString() throws Exception {
        // Arrange
        String json = "{\"name\":\"test\",\"value\":123}";
        JsonNode jsonNode = objectMapper.readTree(json);

        // Act
        String result = converter.convertToDatabaseColumn(jsonNode);

        // Assert
        assertNotNull(result);
        assertEquals(json, result);
    }

    @Test
    void convertToDatabaseColumn_shouldThrowException_whenObjectMapperFails() throws JsonProcessingException {
        // Arrange
        ObjectMapper mockMapper = mock(ObjectMapper.class);
        JsonNode mockNode = mock(JsonNode.class);
        when(mockMapper.writeValueAsString(mockNode))
                .thenThrow(new JsonProcessingException("Mock failure") {
                });

        JsonNodeConverter converter = new JsonNodeConverter(mockMapper);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> converter.convertToDatabaseColumn(mockNode));

        assertTrue(exception.getMessage().contains("Error converting JsonNode to JSON"));
    }

    @Test
    void convertToDatabaseColumn_WithNullInput_ShouldReturnNull() {
        // Act
        String result = converter.convertToDatabaseColumn(null);

        // Assert
        assertNull(result);
    }

    @Test
    void convertToDatabaseColumn_WithNullNode_ShouldReturnNull() {
        // Arrange
        JsonNode nullNode = objectMapper.nullNode();

        // Act
        String result = converter.convertToDatabaseColumn(nullNode);

        // Assert
        assertNull(result);
    }

    @Test
    void convertToEntityAttribute_WithValidJson_ShouldConvertToJsonNode() throws Exception {
        // Arrange
        String json = "{\"name\":\"test\",\"value\":123}";

        // Act
        JsonNode result = converter.convertToEntityAttribute(json);

        // Assert
        assertNotNull(result);
        assertEquals("test", result.get("name").asText());
        assertEquals(123, result.get("value").asInt());
    }

    @Test
    void convertToEntityAttribute_WithNullInput_ShouldReturnNull() {
        // Act
        JsonNode result = converter.convertToEntityAttribute(null);

        // Assert
        assertNull(result);
    }

    @Test
    void convertToEntityAttribute_WithEmptyString_ShouldReturnNull() {
        // Act
        JsonNode result = converter.convertToEntityAttribute("");

        // Assert
        assertNull(result);
    }

    @Test
    void convertToEntityAttribute_WithBlankString_ShouldReturnNull() {
        // Act
        JsonNode result = converter.convertToEntityAttribute("   ");

        // Assert
        assertNull(result);
    }

    @Test
    void convertToEntityAttribute_WithInvalidJson_ShouldThrowException() {
        // Arrange
        String invalidJson = "{invalid json}";

        // Act & Assert
        assertThrows(IllegalArgumentException.class,
                () -> converter.convertToEntityAttribute(invalidJson));
    }
} 