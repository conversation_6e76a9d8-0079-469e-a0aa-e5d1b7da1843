package uk.co.flexi.ri.util;

import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import org.junit.jupiter.api.Test;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class UtilTest {

    @Test
    void extractValidCharacters_ShouldReturnOnlyLetters() {
        // Test cases for extractValidCharacters
        assertEquals("abc", Util.extractValidCharacters("abc@@"));
        assertEquals("hello", Util.extractValidCharacters("hello**"));
        assertEquals("test", Util.extractValidCharacters("test!@#"));
    }

    @Test
    void convertToCommaSeparated_ShouldHandleVariousInputs() {
        // Test cases for convertToCommaSeparated
        List<String> input1 = Arrays.asList("a", "b", "c");
        assertEquals("a,b,c", Util.convertToCommaSeparated(input1));

        List<String> input2 = Collections.singletonList("single");
        assertEquals("single", Util.convertToCommaSeparated(input2));

        List<String> input3 = Collections.emptyList();
        assertNull(Util.convertToCommaSeparated(input3));

        assertNull(Util.convertToCommaSeparated(null));
    }

    @Test
    void convertUrlToMultipartFile_ShouldCreateValidFile() throws Exception {
        // Arrange
        byte[] testContent = "Test content".getBytes();
        String fileName = "test.txt";
        String contentType = "text/plain";

        try (MockWebServer server = new MockWebServer()) {
            server.enqueue(new MockResponse()
                    .setBody("Test content")
                    .addHeader("Content-Type", contentType));
            server.start();

            String fileUrl = server.url("/test.txt").toString();

            // Act
            MultipartFile result = Util.convertUrlToMultipartFile(fileUrl, fileName, contentType);

            // Assert
            assertNotNull(result);
            assertEquals(fileName, result.getOriginalFilename());
            assertEquals(contentType, result.getContentType());
            assertArrayEquals(testContent, result.getBytes());
        }
    }

    @Test
    void convertUrlToMultipartFile_ShouldThrowException_WhenUrlIsInvalid() {
        // Arrange
        String invalidUrl = "invalid-url";
        String fileName = "test.txt";
        String contentType = "text/plain";

        // Act & Assert
        assertThrows(Exception.class, () ->
                Util.convertUrlToMultipartFile(invalidUrl, fileName, contentType)
        );
    }

    @Test
    void splitName_ShouldHandleVariousInputs() {
        // Test cases for splitName
        String[] result1 = Util.splitName("John Doe");
        assertEquals("John", result1[0]);
        assertEquals("Doe", result1[1]);

        String[] result2 = Util.splitName("John");
        assertEquals("John", result2[0]);
        assertEquals("", result2[1]);

        String[] result4 = Util.splitName("");
        assertEquals("", result4[0]);
        assertEquals("", result4[1]);

        String[] result5 = Util.splitName(null);
        assertEquals("", result5[0]);
        assertEquals("", result5[1]);
    }
} 