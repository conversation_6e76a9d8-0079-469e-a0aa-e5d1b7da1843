package uk.co.flexi.ri.util;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;

class MediaUtilTest {

    private MockMultipartFile validImageFile;
    private MockMultipartFile invalidImageFile;
    private MockMultipartFile validVideoFile;

    @BeforeEach
    void setUp() throws IOException {
        // Create a simple 1x1 PNG image
        BufferedImage img = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(img, "png", baos);
        baos.flush();
        validImageFile = new MockMultipartFile("file", "test.png", "image/png", baos.toByteArray());
        baos.close();

        // Invalid image (not an image at all)
        invalidImageFile = new MockMultipartFile("file", "notimage.txt", "image/png", "notanimage".getBytes(StandardCharsets.UTF_8));

        // Video file (just some bytes, not a real video, but enough for testing)
        validVideoFile = new MockMultipartFile("file", "test.mp4", "video/mp4", "fakevideocontent".getBytes(StandardCharsets.UTF_8));
    }

    @AfterEach
    void tearDown() {
        // No resources to clean up
    }

    @Test
    void compressImage_shouldCompressValidImage() throws IOException {
        MultipartFile compressed = MediaUtil.compressImage(validImageFile, 0.5f);
        assertNotNull(compressed);
        assertEquals(validImageFile.getOriginalFilename(), compressed.getOriginalFilename());
        assertEquals(validImageFile.getContentType(), compressed.getContentType());
        assertTrue(compressed.getBytes().length > 0);
    }

    @Test
    void compressImage_shouldThrowOnInvalidImage() {
        IOException ex = assertThrows(IOException.class, () ->
                MediaUtil.compressImage(invalidImageFile, 0.5f)
        );
        assertTrue(ex.getMessage().contains("File is not a valid image"));
    }

    @Test
    void compressImage_shouldThrowOnNullContentTypeOrFilename() throws IOException {
        MockMultipartFile noContentType = new MockMultipartFile("file", null, null, validImageFile.getBytes());
        IOException ex1 = assertThrows(IOException.class, () ->
                MediaUtil.compressImage(noContentType, 0.5f)
        );
        assertTrue(ex1.getMessage().contains("contentType or filename is null"));
    }

    @Test
    void isImage_shouldDetectImageTypes() {
        assertTrue(MediaUtil.isImage("image/png"));
        assertTrue(MediaUtil.isImage("IMAGE/JPEG"));
        assertFalse(MediaUtil.isImage("video/mp4"));
        assertFalse(MediaUtil.isImage(null));
    }

    @Test
    void isVideo_shouldDetectVideoTypes() {
        assertTrue(MediaUtil.isVideo("video/mp4"));
        assertTrue(MediaUtil.isVideo("VIDEO/AVI"));
        assertFalse(MediaUtil.isVideo("image/png"));
        assertFalse(MediaUtil.isVideo(null));
    }

    @Test
    void compressVideo_shouldThrowOnNullContentTypeOrFilename() throws IOException {
        MockMultipartFile noContentType = new MockMultipartFile("file", null, null, validVideoFile.getBytes());
        IOException ex = assertThrows(IOException.class, () ->
                MediaUtil.compressVideo(noContentType)
        );
        assertTrue(ex.getMessage().contains("contentType or filename is null"));
    }

    @Test
    void compressVideo_shouldThrowOnInvalidVideo() {
        // This should throw because the file is not a valid video
        IOException ex = assertThrows(IOException.class, () ->
                MediaUtil.compressVideo(validVideoFile)
        );
        assertTrue(ex.getMessage().contains("Error compressing video with JavaCV"));
    }

    @Test
    void compressVideoWithJavaCV_shouldThrowOnInvalidVideo() {
        // This should throw because the file is not a valid video
        IOException ex = assertThrows(IOException.class, () ->
                MediaUtil.compressVideoWithJavaCV(validVideoFile, "mp4", 28)
        );
        assertTrue(ex.getMessage().contains("Error compressing video with JavaCV"));
    }

    @Test
    void getImageFormat_shouldReturnExtensionFromFilename() {
        assertEquals("png", MediaUtil.getImageFormat("file.png", "image/png"));
        assertEquals("jpeg", MediaUtil.getImageFormat("photo.jpeg", "image/jpeg"));
    }

    @Test
    void getImageFormat_shouldReturnFromContentTypeIfNoExtension() {
        assertEquals("jpeg", MediaUtil.getImageFormat(null, "image/jpeg"));
        assertEquals("gif", MediaUtil.getImageFormat("", "image/gif"));
    }

    @Test
    void getImageFormat_shouldReturnJpgIfNoValidInput() {
        assertEquals("jpg", MediaUtil.getImageFormat(null, null));
        assertEquals("jpg", MediaUtil.getImageFormat("", null));
        assertEquals("jpg", MediaUtil.getImageFormat(null, "application/octet-stream"));
    }
} 