package uk.co.flexi.ri.util.covertor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.LinkedHashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class MapToJsonConverterTest {

    private MapToJsonConverter converter;

    @BeforeEach
    void setUp() {
        converter = new MapToJsonConverter();
    }

    @Test
    void convertToDatabaseColumn_WithValidMap_ShouldConvertToString() {
        // Arrange
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("name", "test");
        map.put("value", 123);
        map.put("nested", Map.of("key", "value"));

        // Act
        String result = converter.convertToDatabaseColumn(map);

        // Assert
        assertNotNull(result);
        assertTrue(result.contains("\"name\":\"test\""));
        assertTrue(result.contains("\"value\":123"));
        assertTrue(result.contains("\"nested\":{\"key\":\"value\"}"));
    }

    @Test
    void convertToDatabaseColumn_shouldThrowException_whenObjectMapperFails() throws JsonProcessingException {
        // Arrange
        ObjectMapper mockMapper = mock(ObjectMapper.class);
        Map<String, Object> testMap = Map.of("key", "value");

        when(mockMapper.writeValueAsString(testMap))
                .thenThrow(new JsonProcessingException("Mock failure") {});

        MapToJsonConverter converter = new MapToJsonConverter(mockMapper);

        // Act & Assert
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class,
                () -> converter.convertToDatabaseColumn(testMap));

        assertTrue(ex.getMessage().contains("Error converting map to JSON"));
    }

    @Test
    void convertToDatabaseColumn_WithNullInput_ShouldReturnNull() {
        // Act
        String result = converter.convertToDatabaseColumn(null);

        // Assert
        assertNull(result);
    }

    @Test
    void convertToDatabaseColumn_WithEmptyMap_ShouldReturnNull() {
        // Arrange
        Map<String, Object> emptyMap = new LinkedHashMap<>();

        // Act
        String result = converter.convertToDatabaseColumn(emptyMap);

        // Assert
        assertNull(result);
    }

    @Test
    void convertToEntityAttribute_WithValidJson_ShouldConvertToMap() {
        // Arrange
        String json = "{\"name\":\"test\",\"value\":123,\"nested\":{\"key\":\"value\"}}";

        // Act
        Map<String, Object> result = converter.convertToEntityAttribute(json);

        // Assert
        assertNotNull(result);
        assertEquals("test", result.get("name"));
        assertEquals(123, result.get("value"));
        assertTrue(result.get("nested") instanceof Map);
        assertEquals("value", ((Map<?, ?>) result.get("nested")).get("key"));
    }

    @Test
    void convertToEntityAttribute_WithNullInput_ShouldReturnNull() {
        // Act
        Map<String, Object> result = converter.convertToEntityAttribute(null);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void convertToEntityAttribute_WithEmptyString_ShouldReturnNull() {
        // Act
        Map<String, Object> result = converter.convertToEntityAttribute("");

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void convertToEntityAttribute_WithBlankString_ShouldReturnNull() {
        // Act
        Map<String, Object> result = converter.convertToEntityAttribute("   ");

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void convertToEntityAttribute_WithInvalidJson_ShouldThrowException() {
        // Arrange
        String invalidJson = "{invalid json}";

        // Act & Assert
        assertThrows(IllegalArgumentException.class, 
            () -> converter.convertToEntityAttribute(invalidJson));
    }

    @Test
    void convertToEntityAttribute_ShouldPreserveMapOrder() {
        // Arrange
        String json = "{\"first\":1,\"second\":2,\"third\":3}";

        // Act
        Map<String, Object> result = converter.convertToEntityAttribute(json);

        // Assert
        assertNotNull(result);
        assertTrue(result instanceof LinkedHashMap);
        assertEquals(1, result.get("first"));
        assertEquals(2, result.get("second"));
        assertEquals(3, result.get("third"));
    }
} 