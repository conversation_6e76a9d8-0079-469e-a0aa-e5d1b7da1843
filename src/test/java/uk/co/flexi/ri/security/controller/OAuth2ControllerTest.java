package uk.co.flexi.ri.security.controller;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class OAuth2ControllerTest {
    @Test
    void testRedirectToGoogleAuthorization() {
        OAuth2Controller controller = new OAuth2Controller();
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        ResponseEntity<Void> result = controller.redirectToGoogleAuthorization(request, response);
        assertEquals(HttpStatus.FOUND, result.getStatusCode());
        assertNotNull(result.getHeaders().getLocation());
        assertEquals("/oauth2/authorization/google", result.getHeaders().getLocation().toString());
    }
} 