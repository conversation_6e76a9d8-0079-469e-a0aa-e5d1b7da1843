package uk.co.flexi.ri.security.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import uk.co.flexi.ri.model.User;
import uk.co.flexi.ri.model.RoleGroup;
import uk.co.flexi.ri.repository.UserRepo;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class UserInfoUserDetailServiceTest {
    @Mock
    private UserRepo userRepo;

    @InjectMocks
    private UserInfoUserDetailService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testLoadUserByUsername_UserFound() {
        User user = new User();
        user.setUserName("testuser");
        user.setIsActive(true);
        user.setRoleGroup(new RoleGroup());
        when(userRepo.findByUserNameAndIsActive("testuser", true)).thenReturn(List.of(user));
        assertTrue(service.loadUserByUsername("testuser") instanceof UserInfoUserDetails);
        verify(userRepo).findByUserNameAndIsActive("testuser", true);
    }

    @Test
    void testLoadUserByUsername_UserNotFound() {
        when(userRepo.findByUserNameAndIsActive("nouser", true)).thenReturn(List.of());
        assertThrows(UsernameNotFoundException.class, () -> service.loadUserByUsername("nouser"));
        verify(userRepo).findByUserNameAndIsActive("nouser", true);
    }
} 