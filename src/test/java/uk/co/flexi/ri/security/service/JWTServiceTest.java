package uk.co.flexi.ri.security.service;

import io.jsonwebtoken.security.Keys;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;
import uk.co.flexi.ri.exception.custom.RIAuthenticationException;
import uk.co.flexi.ri.model.User;

import java.security.Key;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class JWTServiceTest {
    private JWTService jwtService;
    private String secret;

    @BeforeEach
    void setUp() {
        jwtService = new JWTService();
        // 256-bit key for HS256
        Key key = Keys.secretKeyFor(io.jsonwebtoken.SignatureAlgorithm.HS256);
        secret = Base64.getEncoder().encodeToString(key.getEncoded());
        ReflectionTestUtils.setField(jwtService, "jwtSecret", secret);
        ReflectionTestUtils.setField(jwtService, "accessTokenExpirationMs", 1000 * 60 * 60L); // 1 hour
        ReflectionTestUtils.setField(jwtService, "refreshTokenExpirationMs", 1000 * 60 * 60 * 2L); // 2 hours
    }

    @Test
    void testGenerateAndValidateToken() {
        User user = new User();
        user.setUserName("user1");
        user.setTenant(123L);
        String token = jwtService.generateToken(user, "group1");
        assertNotNull(token);
        assertTrue(jwtService.validateToken(token));
        assertEquals("user1", jwtService.extractSubject(token));
        assertEquals("group1", jwtService.extractUserGroup(token));
        assertEquals(123, jwtService.extractTenant(token));
    }

    @Test
    void testGenerateRefreshToken() {
        User user = new User();
        user.setUserName("user2");
        user.setTenant(456L);
        String token = jwtService.generateRefreshToken(user, "group2");
        assertNotNull(token);
        assertTrue(jwtService.validateToken(token));
    }

    @Test
    void testGenerateCustomToken() {
        Map<String, Object> claims = new HashMap<>();
        claims.put("custom", "value");
        String token = jwtService.generateCustomToken("subject", claims);
        assertNotNull(token);
        assertEquals("subject", jwtService.extractSubject(token));
        assertEquals("value", jwtService.extractClaim(token, c -> c.get("custom")));
    }

    @Test
    void testExtractExpiration() {
        Map<String, Object> claims = new HashMap<>();
        String token = jwtService.generateCustomToken("subject", claims);
        Date expiration = jwtService.extractExpiration(token);
        assertNotNull(expiration);
    }

    @Test
    void testExtractUserIdNull() {
        Map<String, Object> claims = new HashMap<>();
        String token = jwtService.generateCustomToken("subject", claims);
        assertNull(jwtService.extractUserId(token));
    }

    @Test
    void testValidateTokenMalformed() {
        String badToken = "bad.token.value";
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> jwtService.validateToken(badToken));
        assertEquals("Malformed jwt token", ex.getMessage());
    }

    @Test
    void testValidateTokenExpired() throws InterruptedException {
        ReflectionTestUtils.setField(jwtService, "accessTokenExpirationMs", 1L); // 1 ms
        User user = new User();
        user.setUserName("user3");
        user.setTenant(789L);
        String token = jwtService.generateToken(user, "group3");
        Thread.sleep(5); // ensure token is expired
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> jwtService.validateToken(token));
        assertEquals("Token expired. Refresh required", ex.getMessage());
    }

    @Test
    void testValidateTokenIllegalArgument() {
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> jwtService.validateToken(null));
        assertEquals("Illegal argument token", ex.getMessage());
    }
} 