package uk.co.flexi.ri.security.service;

import org.junit.jupiter.api.Test;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class SecurityAuditorAwareTest {
    @Test
    void testGetCurrentAuditorAuthenticated() {
        Authentication authentication = mock(Authentication.class);
        when(authentication.isAuthenticated()).thenReturn(true);
        when(authentication.getName()).thenReturn("auditorUser");
        SecurityContext context = mock(SecurityContext.class);
        when(context.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(context);
        SecurityAuditorAware auditorAware = new SecurityAuditorAware();
        Optional<String> auditor = auditorAware.getCurrentAuditor();
        assertTrue(auditor.isPresent());
        assertEquals("auditorUser", auditor.get());
    }

    @Test
    void testGetCurrentAuditorUnauthenticated() {
        Authentication authentication = mock(Authentication.class);
        when(authentication.isAuthenticated()).thenReturn(false);
        SecurityContext context = mock(SecurityContext.class);
        when(context.getAuthentication()).thenReturn(authentication);
        SecurityContextHolder.setContext(context);
        SecurityAuditorAware auditorAware = new SecurityAuditorAware();
        Optional<String> auditor = auditorAware.getCurrentAuditor();
        assertTrue(auditor.isEmpty());
    }

    @Test
    void testGetCurrentAuditorNoAuthentication() {
        SecurityContext context = mock(SecurityContext.class);
        when(context.getAuthentication()).thenReturn(null);
        SecurityContextHolder.setContext(context);
        SecurityAuditorAware auditorAware = new SecurityAuditorAware();
        Optional<String> auditor = auditorAware.getCurrentAuditor();
        assertTrue(auditor.isEmpty());
    }
} 