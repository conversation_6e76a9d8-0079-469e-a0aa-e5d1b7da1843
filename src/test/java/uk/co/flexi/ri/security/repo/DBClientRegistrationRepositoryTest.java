package uk.co.flexi.ri.security.repo;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import uk.co.flexi.ri.model.AuthProvider;
import uk.co.flexi.ri.repository.AuthProviderRepo;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class DBClientRegistrationRepositoryTest {
    private AuthProviderRepo authProviderRepo;
    private DBClientRegistrationRepository repository;

    @BeforeEach
    void setUp() {
        authProviderRepo = mock(AuthProviderRepo.class);
        repository = new DBClientRegistrationRepository(authProviderRepo);
    }

    @Test
    void testFindByRegistrationId_Found() {
        AuthProvider provider = new AuthProvider();
        provider.setRegistrationId("google");
        provider.setClientId("clientId");
        provider.setClientSecret("secret");
        provider.setAuthorizationUri("https://auth");
        provider.setTokenUri("https://token");
        provider.setRedirectUri("https://redirect");
        provider.setScopes("openid,email");
        provider.setUserInfoUri("https://userinfo");
        provider.setNameAttr("sub");
        provider.setJwkSetUri("https://jwk");
        when(authProviderRepo.findByRegistrationId("google")).thenReturn(Optional.of(provider));
        ClientRegistration reg = repository.findByRegistrationId("google");
        assertNotNull(reg);
        assertEquals("google", reg.getRegistrationId());
        assertEquals("clientId", reg.getClientId());
        assertEquals("secret", reg.getClientSecret());
        assertEquals("https://auth", reg.getProviderDetails().getAuthorizationUri());
        assertEquals("https://token", reg.getProviderDetails().getTokenUri());
        assertEquals("https://userinfo", reg.getProviderDetails().getUserInfoEndpoint().getUri());
        assertEquals("sub", reg.getProviderDetails().getUserInfoEndpoint().getUserNameAttributeName());
        assertEquals("https://jwk", reg.getProviderDetails().getJwkSetUri());
        assertArrayEquals(new String[]{"openid","email"}, reg.getScopes().toArray());
    }

    @Test
    void testFindByRegistrationId_NotFound() {
        when(authProviderRepo.findByRegistrationId("notfound")).thenReturn(Optional.empty());
        assertNull(repository.findByRegistrationId("notfound"));
    }
} 