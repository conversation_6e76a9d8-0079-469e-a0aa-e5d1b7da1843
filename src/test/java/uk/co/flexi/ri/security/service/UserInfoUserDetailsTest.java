package uk.co.flexi.ri.security.service;

import org.junit.jupiter.api.Test;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import uk.co.flexi.ri.model.Role;
import uk.co.flexi.ri.model.RoleGroup;
import uk.co.flexi.ri.model.User;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class UserInfoUserDetailsTest {
    @Test
    void testAllGettersAndAuthorities() {
        Role role = new Role();
        role.setName("ADMIN");
        RoleGroup group = new RoleGroup();
        group.setRoles(Set.of(role));
        User user = new User();
        user.setUserName("testuser");
        user.setPassword("pass");
        user.setIsActive(true);
        user.setRoleGroup(group);

        UserInfoUserDetails details = new UserInfoUserDetails(user);
        assertEquals("testuser", details.getUsername());
        assertEquals("pass", details.getPassword());
        assertTrue(details.isEnabled());
        assertTrue(details.isAccountNonExpired());
        assertTrue(details.isAccountNonLocked());
        assertTrue(details.isCredentialsNonExpired());
        List<? extends GrantedAuthority> authorities = (List<? extends GrantedAuthority>) details.getAuthorities();
        assertEquals(1, authorities.size());
        assertEquals(new SimpleGrantedAuthority("ADMIN"), authorities.get(0));
    }

    @Test
    void testIsEnabledFalse() {
        User user = new User();
        user.setUserName("inactive");
        user.setPassword("pass");
        user.setIsActive(false);
        RoleGroup group = new RoleGroup();
        group.setRoles(Collections.emptySet());
        user.setRoleGroup(group);
        UserInfoUserDetails details = new UserInfoUserDetails(user);
        assertFalse(details.isEnabled());
    }
} 