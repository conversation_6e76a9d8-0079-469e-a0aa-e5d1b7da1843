package uk.co.flexi.ri.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import uk.co.flexi.ri.dto.EventQueueDTO;
import uk.co.flexi.ri.dto.event.EventData;
import uk.co.flexi.ri.model.EventQueue;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.service.EventQueueService;
import uk.co.flexi.ri.service.MerchantProductPriceService;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ApplicationEventListenerTest {

    @Mock
    private EventQueueService eventQueueService;

    @Mock
    private MerchantProductPriceService merchantProductPriceService;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private ApplicationEventListener applicationEventListener;

    private EventData<Map<String, Object>> eventData;
    private Inspection inspection;
    private Map<String, Object> payload;

    @BeforeEach
    void setUp() {
        // Setup EventData
        eventData = new EventData<>();
        eventData.setEventId("test-event-id");
        eventData.setEventType(EventQueue.EventType.INSP_CREATED);
        eventData.setReferenceType(EventQueue.ReferenceType.INSPECTION);
        eventData.setUniqueId("test-inspection-id");
        eventData.setPerformedByUser("testuser");
        eventData.setPerformedByGroup("testgroup");
        eventData.setTenant(1001L);

        // Setup payload
        payload = new HashMap<>();
        payload.put("key1", "value1");
        payload.put("key2", "value2");
        eventData.setPayload(payload);

        // Setup Inspection
        inspection = new Inspection();
        inspection.setInspectionId("test-inspection-id");
        inspection.setTenant(1001L);
    }

    @Test
    void publishToEventQueue_ShouldSaveEventQueue() {
        // Arrange
        when(objectMapper.convertValue(any(), any(TypeReference.class))).thenReturn(payload);

        // Act
        applicationEventListener.publishToEventQueue(eventData);

        // Assert
        verify(eventQueueService).save(any(EventQueueDTO.class));
        verify(objectMapper).convertValue(any(), any(TypeReference.class));
    }

    @Test
    void saveToMerchantPrice_ShouldSaveToMerchantProductPrice() {
        // Act
        applicationEventListener.saveToMerchantPrice(inspection);

        // Assert
        verify(merchantProductPriceService).saveToMerchantProductPrice(inspection);
    }

    @Test
    void publishToEventQueue_ShouldHandleNullPayload() {
        // Arrange
        eventData.setPayload(null);
        when(objectMapper.convertValue(any(), any(TypeReference.class))).thenReturn(null);

        // Act
        applicationEventListener.publishToEventQueue(eventData);

        // Assert
        verify(eventQueueService).save(any(EventQueueDTO.class));
        verify(objectMapper).convertValue(any(), any(TypeReference.class));
    }

    @Test
    void publishToEventQueue_ShouldHandleEmptyPayload() {
        // Arrange
        eventData.setPayload(new HashMap<>());
        when(objectMapper.convertValue(any(), any(TypeReference.class))).thenReturn(new HashMap<>());

        // Act
        applicationEventListener.publishToEventQueue(eventData);

        // Assert
        verify(eventQueueService).save(any(EventQueueDTO.class));
        verify(objectMapper).convertValue(any(), any(TypeReference.class));
    }

    @Test
    void saveToMerchantPrice_ShouldHandleNullInspection() {
        // Act
        applicationEventListener.saveToMerchantPrice(null);

        // Assert
        verify(merchantProductPriceService).saveToMerchantProductPrice(null);
    }
} 