package uk.co.flexi.ri.listener;

import jakarta.persistence.EntityManager;
import jakarta.persistence.TypedQuery;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.hibernate.envers.RevisionType;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.model.CustomRevisionEntity;
import uk.co.flexi.ri.model.Media;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class InspectionRevisionListenerTest {

    private EntityManager entityManager;
    private InspectionRevisionListener listener;
    private TypedQuery<String> mockQuery;

    @BeforeEach
    void setUp() {
        entityManager = mock(EntityManager.class);
        listener = new InspectionRevisionListener();
        listener.setEntityManager(entityManager);
        mockQuery = mock(TypedQuery.class);
    }

    @Test
    void entityChanged_WithCommentEntity_ShouldSetUniqueId() {
        CustomRevisionEntity revisionEntity = new CustomRevisionEntity();
        when(entityManager.createQuery(anyString(), eq(String.class))).thenReturn(mockQuery);
        when(mockQuery.setParameter(eq("id"), anyLong())).thenReturn(mockQuery);
        when(mockQuery.getResultStream()).thenReturn(Stream.of("REF_COMMENT"));

        listener.entityChanged(Comment.class, "Comment", 1L, RevisionType.ADD, revisionEntity);

        assertEquals("REF_COMMENT", revisionEntity.getUniqueId());
    }

    @Test
    void entityChanged_WithInspectionEntity_ShouldSetUniqueId() {
        CustomRevisionEntity revisionEntity = new CustomRevisionEntity();
        when(entityManager.createQuery(contains("FROM inspection"), eq(String.class))).thenReturn(mockQuery);
        when(mockQuery.setParameter(eq("id"), anyLong())).thenReturn(mockQuery);
        when(mockQuery.getResultStream()).thenReturn(Stream.of("REF_INSPECTION"));

        listener.entityChanged(Inspection.class, "Inspection", 2L, RevisionType.MOD, revisionEntity);

        assertEquals("REF_INSPECTION", revisionEntity.getUniqueId());
    }

    @Test
    void entityChanged_WithInspectionItemEntity_ShouldSetUniqueId() {
        CustomRevisionEntity revisionEntity = new CustomRevisionEntity();
        when(entityManager.createQuery(contains("FROM InspectionItem"), eq(String.class))).thenReturn(mockQuery);
        when(mockQuery.setParameter(eq("id"), anyLong())).thenReturn(mockQuery);
        when(mockQuery.getResultStream()).thenReturn(Stream.of("REF_ITEM"));

        listener.entityChanged(InspectionItem.class, "InspectionItem", 3L, RevisionType.ADD, revisionEntity);

        assertEquals("REF_ITEM", revisionEntity.getUniqueId());
    }

    @Test
    void entityChanged_WithMediaEntity_ShouldSetUniqueId() {
        CustomRevisionEntity revisionEntity = new CustomRevisionEntity();
        when(entityManager.createQuery(contains("FROM Media"), eq(String.class))).thenReturn(mockQuery);
        when(mockQuery.setParameter(eq("id"), anyLong())).thenReturn(mockQuery);
        when(mockQuery.getResultStream()).thenReturn(Stream.of("REF_MEDIA"));

        listener.entityChanged(Media.class, "Media", 4L, RevisionType.MOD, revisionEntity);

        assertEquals("REF_MEDIA", revisionEntity.getUniqueId());
    }

    @Test
    void entityChanged_WithUnknownEntity_ShouldSetNullUniqueId() {
        CustomRevisionEntity revisionEntity = new CustomRevisionEntity();

        listener.entityChanged(String.class, "String", 5L, RevisionType.ADD, revisionEntity);

        assertNull(revisionEntity.getUniqueId());
    }

    @Test
    void entityChanged_WithExceptionInQuery_ShouldSetNullUniqueId() {
        CustomRevisionEntity revisionEntity = new CustomRevisionEntity();
        when(entityManager.createQuery(anyString(), eq(String.class)))
                .thenThrow(new RuntimeException("DB failure"));

        listener.entityChanged(Comment.class, "Comment", 6L, RevisionType.ADD, revisionEntity);

        assertNull(revisionEntity.getUniqueId());
    }

    @Test
    void newRevision_ShouldDoNothing() {
        // Should not throw any exception
        assertDoesNotThrow(() -> listener.newRevision(new Object()));
    }
}