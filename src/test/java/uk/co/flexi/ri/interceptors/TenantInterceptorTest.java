package uk.co.flexi.ri.interceptors;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import uk.co.flexi.ri.service.TenantIdentifierResolver;
import uk.co.flexi.ri.util.Constants;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TenantInterceptorTest {

    @Mock
    private TenantIdentifierResolver tenantResolver;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private Object handler;

    private TenantInterceptor interceptor;

    @BeforeEach
    void setUp() {
        interceptor = new TenantInterceptor(tenantResolver);
    }

    @Test
    void preHandle_WithTenantAttribute_ShouldSetCurrentTenant() {
        // Arrange
        String tenantId = "123";
        when(request.getAttribute(Constants.TENANT)).thenReturn(tenantId);

        // Act
        boolean result = interceptor.preHandle(request, response, handler);

        // Assert
        verify(tenantResolver).setCurrentTenant(123L);
        assertTrue(result);
    }

    @Test
    void preHandle_WithoutTenantAttribute_ShouldNotSetCurrentTenant() {
        // Arrange
        when(request.getAttribute(Constants.TENANT)).thenReturn(null);

        // Act
        boolean result = interceptor.preHandle(request, response, handler);

        // Assert
        verify(tenantResolver, never()).setCurrentTenant(any());
        assertTrue(result);
    }

    @Test
    void afterCompletion_ShouldClearTenant() {
        // Act
        interceptor.afterCompletion(request, response, handler, null);

        // Assert
        verify(tenantResolver).clear();
    }

    @Test
    void afterCompletion_WithException_ShouldStillClearTenant() {
        // Act
        interceptor.afterCompletion(request, response, handler, new RuntimeException("Test exception"));

        // Assert
        verify(tenantResolver).clear();
    }
} 