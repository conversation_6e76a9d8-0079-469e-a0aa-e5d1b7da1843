package uk.co.flexi.ri.integration.service;

import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.exception.custom.ScriptExecutionException;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.spy;

class JavaScriptExecutorTest {
    private final JavaScriptExecutor executor = new JavaScriptExecutor();

    @Test
    void execute_validScript_returnsResult() throws Exception {
        String inputJson = "{\"foo\":\"bar\"}";
        String script = "var obj = JSON.parse(inputJson); result = JSON.stringify(obj);";
        String result = executor.execute(inputJson, script);
        assertEquals(inputJson, result);
    }

    @Test
    void execute_nullInputJson_throwsException() {
        ScriptExecutionException ex = assertThrows(ScriptExecutionException.class, () ->
                executor.execute(null, "var result = 1;")
        );
        assertTrue(ex.getMessage().contains("Script execution failed"));
    }

    @Test
    void execute_largeScript_throwsException() {
        String largeScript = "a".repeat(10001);
        ScriptExecutionException ex = assertThrows(ScriptExecutionException.class, () ->
                executor.execute("{}", largeScript)
        );
        assertTrue(ex.getMessage().contains("Script execution failed"));
    }

    @Test
    void execute_nullScript_throwsException() {
        ScriptExecutionException ex = assertThrows(ScriptExecutionException.class, () ->
                executor.execute("{}", null)
        );
        assertTrue(ex.getMessage().contains("Script execution failed"));
    }

    @Test
    void execute_scriptTooLarge_throwsException() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 10001; i++) sb.append('a');
        ScriptExecutionException ex = assertThrows(ScriptExecutionException.class, () ->
                executor.execute("{}", sb.toString())
        );
        assertTrue(ex.getMessage().contains("Script execution failed"));
    }

    @Test
    void execute_forbiddenPattern_throwsException() {
        String[] patterns = {"java.lang", "Runtime", "Process", "File", "importPackage", "importClass", "require", "eval", "XMLHttpRequest", "fetch"};
        for (String pattern : patterns) {
            String script = pattern + " = 1; result = 1;";
            ScriptExecutionException ex = assertThrows(ScriptExecutionException.class, () ->
                    executor.execute("{}", script)
            );
            assertTrue(ex.getMessage().contains("Script execution failed"));
        }
    }


    @Test
    void execute_scriptThrows_throwsException() {
        String script = "throw new Error('fail'); result = 1;";
        ScriptExecutionException ex = assertThrows(ScriptExecutionException.class, () ->
                executor.execute("{}", script)
        );
        assertTrue(ex.getMessage().contains("Script execution failed"));
    }

    @Test
    void execute_interrupted_throwsException() {
        JavaScriptExecutor customExecutor = new JavaScriptExecutor() {
            @Override
            public String execute(String inputJson, String script) throws ScriptExecutionException {
                Thread.currentThread().interrupt();
                return super.execute(inputJson, script);
            }
        };
        ScriptExecutionException ex = assertThrows(ScriptExecutionException.class, () ->
                customExecutor.execute("{}", "result = 1;")
        );
        assertTrue(ex.getMessage().contains("interrupted"));
    }

    @Test
    void execute_timeout_throwsException() {
        JavaScriptExecutor customExecutor = new JavaScriptExecutor() {
            @Override
            public String execute(String inputJson, String script) throws ScriptExecutionException {
                try (ExecutorService executor = Executors.newSingleThreadExecutor()) {
                    Future<String> future = executor.submit(() -> { Thread.sleep(6000); return "done"; });
                    return future.get(5, TimeUnit.MILLISECONDS);
                } catch (TimeoutException e) {
                    throw new ScriptExecutionException("Script execution timed out", e);
                } catch (Exception e) {
                    throw new ScriptExecutionException("Script execution failed", e);
                }
            }
        };
        ScriptExecutionException ex = assertThrows(ScriptExecutionException.class, () ->
                customExecutor.execute("{}", "result = 1;")
        );
        assertTrue(ex.getMessage().contains("timed out"));
    }

    @Test
    void execute_threadInterrupted_throwsException() {
        JavaScriptExecutor interruptedExecutor = new JavaScriptExecutor() {
            @Override
            public String execute(String inputJson, String script) throws ScriptExecutionException {
                Thread.currentThread().interrupt();
                return super.execute(inputJson, script);
            }
        };

        ScriptExecutionException ex = assertThrows(ScriptExecutionException.class, () ->
                interruptedExecutor.execute("{}", "var result = 1;")
        );
        assertTrue(ex.getMessage().contains("Script execution was interrupted"));
    }

    @Test
    void execute_scriptTimeout_throwsException() {
        JavaScriptExecutor executor = spy(new JavaScriptExecutor());

        ExecutorService executorService = Executors.newSingleThreadExecutor();

        // Simulate timeout by blocking the future
        try {
            JavaScriptExecutor executorWithTimeout = new JavaScriptExecutor() {
                @Override
                public String execute(String inputJson, String script) throws ScriptExecutionException {
                    ExecutorService slowExecutor = Executors.newSingleThreadExecutor();
                    Future<String> future = slowExecutor.submit(() -> {
                        Thread.sleep(6000); // Simulate long running script
                        return "delayed";
                    });
                    try {
                        return future.get(5000, TimeUnit.MILLISECONDS); // Force timeout
                    } catch (TimeoutException e) {
                        throw new ScriptExecutionException("Script execution timed out", e);
                    } catch (Exception e) {
                        throw new ScriptExecutionException("Unexpected error", e);
                    } finally {
                        slowExecutor.shutdownNow();
                    }
                }
            };

            ScriptExecutionException ex = assertThrows(ScriptExecutionException.class, () ->
                    executorWithTimeout.execute("{}", "var result = 1;")
            );
            assertTrue(ex.getMessage().contains("timed out"));
        } finally {
            executorService.shutdown();
        }
    }

    @Test
    void execute_realTimeout_throwsException() {
        JavaScriptExecutor timeoutExecutor = new JavaScriptExecutor() {
            @Override
            public String execute(String inputJson, String script) throws ScriptExecutionException {
                try (ExecutorService executor = Executors.newSingleThreadExecutor()) {
                    Future<String> future = executor.submit(() -> {
                        Thread.sleep(6000);
                        return "done";
                    });
                    return future.get(5000, TimeUnit.MILLISECONDS);
                } catch (TimeoutException e) {
                    throw new ScriptExecutionException("Script execution timed out", e);
                } catch (Exception e) {
                    throw new ScriptExecutionException("Script execution failed", e);
                }
            }
        };
        ScriptExecutionException ex = assertThrows(ScriptExecutionException.class, () ->
                timeoutExecutor.execute("{}", "result = 1;")
        );
        assertTrue(ex.getMessage().contains("timed out"));
    }
}
