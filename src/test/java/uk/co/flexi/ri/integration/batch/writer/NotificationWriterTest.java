package uk.co.flexi.ri.integration.batch.writer;


import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.batch.item.Chunk;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.model.NotificationResponse;
import uk.co.flexi.ri.repository.NotificationResponseRepo;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
class NotificationWriterTest {

    @Mock
    private NotificationResponseRepo notificationResponseRepo;

    @InjectMocks
    private NotificationWriter notificationWriter;

    @Test
    void write_SavesAllResponses_WhenItemsAreValid() throws Exception {
        // Arrange
        NotificationConfig config1 = new NotificationConfig(1L,null,null,null,null,null, 1L);
        NotificationConfig config2 = new NotificationConfig(2L,null,null,null,null,null, 1L);
        Map<NotificationConfig, Object> item1 = Map.of(config1, "Content1");
        Map<NotificationConfig, Object> item2 = Map.of(config2, "Content2");
        Chunk<Map<NotificationConfig, Object>> items = new Chunk<>(List.of(item1, item2));

        // Act
        notificationWriter.write(items);

        // Assert
        ArgumentCaptor<List<NotificationResponse>> captor = ArgumentCaptor.forClass(List.class);
        verify(notificationResponseRepo).saveAll(captor.capture());
        List<NotificationResponse> savedResponses = captor.getValue();

        assertEquals(2, savedResponses.size());
        assertEquals(config1.getId(), savedResponses.get(0).getConfigId());
        assertEquals("Content1", savedResponses.get(0).getContent());
        assertEquals(config1.getTenant(), savedResponses.get(0).getTenant());
        assertEquals(config2.getId(), savedResponses.get(1).getConfigId());
        assertEquals("Content2", savedResponses.get(1).getContent());
        assertEquals(config2.getTenant(), savedResponses.get(1).getTenant());
    }

    @Test
    void write_DoesNotSave_WhenItemsAreEmpty() throws Exception {
        // Arrange
        Chunk<Map<NotificationConfig, Object>> items = new Chunk<>(List.of());

        // Act
        notificationWriter.write(items);

        // Assert
        verifyNoInteractions(notificationResponseRepo);
    }

    @Test
    void write_HandlesNullContentGracefully() throws Exception {
        // Arrange
        NotificationConfig config = new NotificationConfig(1L,null,null,null,null,null, 1L);
        Map<NotificationConfig, Object> item = new java.util.HashMap<>();
        item.put(config, null);
        Chunk<Map<NotificationConfig, Object>> items = new Chunk<>(List.of(item));

        // Act
        notificationWriter.write(items);

        // Assert
        ArgumentCaptor<List<NotificationResponse>> captor = ArgumentCaptor.forClass(List.class);
        verify(notificationResponseRepo).saveAll(captor.capture());
        List<NotificationResponse> savedResponses = captor.getValue();

        assertEquals(1, savedResponses.size());
        assertEquals(config.getId(), savedResponses.get(0).getConfigId());
        assertNull(savedResponses.get(0).getContent());
        assertEquals(config.getTenant(), savedResponses.get(0).getTenant());
    }
}
