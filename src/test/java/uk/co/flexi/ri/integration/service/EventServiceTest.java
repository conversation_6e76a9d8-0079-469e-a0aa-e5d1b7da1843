package uk.co.flexi.ri.integration.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.camel.Exchange;
import org.apache.camel.Message;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.http.base.HttpOperationFailedException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.event.dto.Comment__2;
import uk.co.flexi.ri.event.dto.Inspection;
import uk.co.flexi.ri.event.dto.InspectionItem;
import uk.co.flexi.ri.event.dto.Payload;
import uk.co.flexi.ri.integration.dto.EventLogDTO;
import uk.co.flexi.ri.integration.dto.EventQueueResDTO;
import uk.co.flexi.ri.integration.model.EventConfig;
import uk.co.flexi.ri.integration.model.EventQueueReq;
import uk.co.flexi.ri.integration.model.EventQueueRes;
import uk.co.flexi.ri.integration.model.view.EventQueueView;
import uk.co.flexi.ri.integration.repository.EventConfigRepo;
import uk.co.flexi.ri.integration.repository.EventQueueReqRepo;
import uk.co.flexi.ri.integration.repository.EventQueueResRepo;
import uk.co.flexi.ri.model.EventQueue;
import uk.co.flexi.ri.repository.MerchantRepo;
import uk.co.flexi.ri.service.AuthenticatedUserService;

import java.time.OffsetDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EventServiceTest {
    @Mock private EventQueueReqRepo eventQueueReqRepo;
    @Mock private EventQueueResRepo eventQueueResRepo;
    @Mock private EventConfigRepo eventConfigRepo;
    @Mock private AuthenticatedUserService authenticatedUserService;
    @Mock private ProducerTemplate producerTemplate;
    @Mock private ObjectMapper objectMapper;
    @Mock private ModelMapper modelMapper;
    @Mock private MerchantRepo merchantRepo;
    @Mock private ScriptExecutor scriptExecutor;
    @InjectMocks private EventService service;

    @BeforeEach
    void setUp() {
        service = new EventService(eventQueueReqRepo, eventQueueResRepo, eventConfigRepo, authenticatedUserService, producerTemplate, objectMapper, modelMapper, merchantRepo, scriptExecutor);
    }

    @Test
    void processEvent_success() {
        Map<String, Object> headers = new HashMap<>();
        Object body = new Object();
        Exchange exchange = mock(Exchange.class);
        Message message = mock(Message.class);

        when(producerTemplate.request(anyString(), any())).thenReturn(exchange);
        when(exchange.getMessage()).thenReturn(message);
        when(message.getBody(String.class)).thenReturn("response");
        when(message.getHeader(anyString(), eq(Integer.class))).thenReturn(200);
        when(exchange.getException()).thenReturn(null);

        EventQueueResDTO dto = service.processEvent(headers, body);
        assertEquals(EventQueueRes.Status.SENT, dto.getStatus());
        assertEquals(200, dto.getStatusCode());
        assertEquals("response", dto.getBody());
    }

    @Test
    void processEvent_withException_setsFailed() {
        Map<String, Object> headers = new HashMap<>();
        Object body = new Object();
        when(producerTemplate.request(anyString(), any())).thenThrow(new RuntimeException("fail"));
        EventQueueResDTO dto = service.processEvent(headers, body);
        assertEquals(EventQueueRes.Status.FAILED, dto.getStatus());
        assertNotNull(dto.getErrorMessage());
    }

    @Test
    void processEvent_withHttpOperationFailedException_setsFailed() {
        Map<String, Object> headers = new HashMap<>();
        Object body = new Object();
        Exchange exchange = mock(Exchange.class);
        Message message = mock(Message.class);
        HttpOperationFailedException ex = mock(HttpOperationFailedException.class);

        when(producerTemplate.request(anyString(), any())).thenReturn(exchange);
        when(exchange.getMessage()).thenReturn(message);
        when(exchange.getException()).thenReturn(ex);
        when(ex.getStatusCode()).thenReturn(400);
        when(ex.getMessage()).thenReturn("fail");
        when(ex.getResponseBody()).thenReturn("body");

        EventQueueResDTO dto = service.processEvent(headers, body);
        assertEquals(EventQueueRes.Status.FAILED, dto.getStatus());
        assertEquals(400, dto.getStatusCode());
        assertEquals("fail", dto.getErrorMessage());
        assertEquals("body", dto.getBody());
    }

    @Test
    void getAllInspectionEvents_returnsList() throws JsonProcessingException {
        String id = "id";
        Long tenant = 1L;
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO();
        mockUser.setHmacKey("testHmacKey");
        mockUser.setTenant(tenant);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(mockUser);
        EventQueueReq req = new EventQueueReq();
        req.setEventId("E1");
        req.setConfigId(1L);
        req.setRequestTime(OffsetDateTime.now());
        req.setPayload(mock(JsonNode.class));
        req.setOriginalPayload(mock(JsonNode.class));
        req.setId(1L);
        req.setEventType("TYPE");
        req.setTarget("TGT");
        List<EventQueueReq> reqs = List.of(req);
        when(eventQueueReqRepo.findByUniqueIdAndTenant(eq(id), eq(tenant))).thenReturn(reqs);
        EventConfig config = new EventConfig();
        config.setId(1L);
        config.setRetryCount(1);
        when(eventConfigRepo.findAll()).thenReturn(List.of(config));
        EventQueueRes res = new EventQueueRes();
        res.setRequest(req);
        res.setStatus(EventQueueRes.Status.FAILED);
        res.setStatusCode(500);
        res.setResponseTime(OffsetDateTime.now());
        res.setBody("body");
        res.setErrorMessage("err");
        when(eventQueueResRepo.findByRequestIdIn(anyList())).thenReturn(List.of(res));
        when(objectMapper.writeValueAsString(any())).thenReturn("{}", "{}");
        List<EventLogDTO> result = service.getAllInspectionEvents(id);
        assertEquals(1, result.size());
        assertEquals("E1", result.get(0).getEventId());
    }

    @Test
    void retryEventPosting_success() throws JsonProcessingException {
        EventService spyService = spy(new EventService(eventQueueReqRepo, eventQueueResRepo, eventConfigRepo, authenticatedUserService, producerTemplate, objectMapper, modelMapper, merchantRepo, scriptExecutor));
        EventQueueReq req = new EventQueueReq();
        req.setEventId("E1");
        req.setConfigId(1L);
        req.setRequestTime(OffsetDateTime.now());
        req.setPayload(mock(JsonNode.class));
        req.setEventType("TYPE");
        req.setTarget("TGT");
        req.setId(1L);
        when(eventQueueReqRepo.findFirstByEventIdOrderByRequestTimeDesc(anyString())).thenReturn(Optional.of(req));
        when(eventQueueReqRepo.save(any())).thenReturn(req);
        EventConfig config = new EventConfig();
        config.setId(1L);
        when(eventConfigRepo.findById(1L)).thenReturn(Optional.of(config));
        doReturn(new HashMap<>()).when(spyService).buildHeaders(any(), any());
        EventQueueResDTO resDTO = new EventQueueResDTO();
        resDTO.setRequestId(1L);
        resDTO.setStatus(EventQueueRes.Status.FAILED);
        doReturn(resDTO).when(spyService).processEvent(any(), any());
        EventQueueRes res = new EventQueueRes();
        res.setStatus(EventQueueRes.Status.FAILED);
        res.setStatusCode(500);
        res.setResponseTime(OffsetDateTime.now());
        res.setBody("body");
        res.setErrorMessage("err");
        when(eventQueueResRepo.findByRequestId(1L)).thenReturn(Optional.of(res));
        when(eventQueueResRepo.save(any())).thenReturn(res);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        EventLogDTO dto = spyService.retryEventPosting("E1");
        assertEquals(EventQueueRes.Status.FAILED.name(), dto.getStatus());
        assertTrue(dto.getIsRetryEnable());
    }

    @Test
    void retryEventPosting_notFound_throws() {
        when(eventQueueReqRepo.findFirstByEventIdOrderByRequestTimeDesc(anyString())).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.retryEventPosting("E1"));
    }

    @Test
    void updateEventResponse_found_updates() {
        EventQueueResDTO dto = new EventQueueResDTO();
        dto.setRequestId(1L);
        dto.setStatusCode(200);
        dto.setBody("body");
        dto.setErrorMessage("err");
        dto.setStatus(EventQueueRes.Status.SENT);
        EventQueueRes res = new EventQueueRes();
        when(eventQueueResRepo.findByRequestId(1L)).thenReturn(Optional.of(res));
        when(eventQueueResRepo.save(res)).thenReturn(res);
        EventQueueRes result = service.updateEventResponse(dto);
        assertSame(res, result);
    }

    @Test
    void updateEventResponse_notFound_throws() {
        EventQueueResDTO dto = new EventQueueResDTO();
        dto.setRequestId(1L);
        when(eventQueueResRepo.findByRequestId(1L)).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.updateEventResponse(dto));
    }

    @Test
    void buildHeaders_allDestinations() {
        Map<String, Object> config = new HashMap<>();
        for (EventConfig.Destination dest : EventConfig.Destination.values()) {
            Map<String, Object> result = service.buildHeaders(dest, config);
            assertNotNull(result);
        }
    }

    @Test
    void extractStatusCode_withHttpOperationFailedException_returnsStatus() {
        HttpOperationFailedException ex = mock(HttpOperationFailedException.class);
        when(ex.getStatusCode()).thenReturn(123);
        assertEquals(123, invokeExtractStatusCode(ex));
    }

    @Test
    void extractStatusCode_withOtherException_returnsNull() {
        Exception ex = new Exception();
        assertNull(invokeExtractStatusCode(ex));
    }

    @Test
    void extractBodyFromException_withHttpOperationFailedException_returnsBody() {
        HttpOperationFailedException ex = mock(HttpOperationFailedException.class);
        when(ex.getResponseBody()).thenReturn("body");
        assertEquals("body", invokeExtractBodyFromException(ex));
    }

    @Test
    void extractBodyFromException_withOtherException_returnsNull() {
        Exception ex = new Exception();
        assertNull(invokeExtractBodyFromException(ex));
    }

    @Test
    void getRequestPayload_convertedPayloadIsEmpty_returnsOriginal() {
        ObjectNode node = mock(ObjectNode.class);
        JsonNode original = mock(JsonNode.class);
        JsonNode converted = mock(JsonNode.class);
        when(node.get("originalPayload")).thenReturn(original);
        when(node.get("convertedPayload")).thenReturn(converted);
        when(converted.isEmpty()).thenReturn(true);
        assertSame(original, service.getRequestPayload(node));
    }

    @Test
    void getRequestPayload_convertedPayloadIsNull_returnsOriginal() {
        ObjectNode node = mock(ObjectNode.class);
        JsonNode original = mock(JsonNode.class);
        when(node.get("originalPayload")).thenReturn(original);
        when(node.get("convertedPayload")).thenReturn(null);
        assertSame(original, service.getRequestPayload(node));
    }

    @Test
    void getAllInspectionEvents_handlesJsonProcessingException() throws JsonProcessingException {
        String id = "id";
        Long tenant = 1L;
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO();
        mockUser.setHmacKey("testHmacKey");
        mockUser.setTenant(tenant);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(mockUser);
        EventQueueReq req = new EventQueueReq();
        req.setEventId("E1");
        req.setConfigId(1L);
        req.setRequestTime(OffsetDateTime.now());
        req.setPayload(mock(JsonNode.class));
        req.setOriginalPayload(mock(JsonNode.class));
        req.setId(1L);
        req.setEventType("TYPE");
        req.setTarget("TGT");
        List<EventQueueReq> reqs = List.of(req);
        when(eventQueueReqRepo.findByUniqueIdAndTenant(eq(id), eq(tenant))).thenReturn(reqs);
        EventConfig config = new EventConfig();
        config.setId(1L);
        config.setRetryCount(1);
        when(eventConfigRepo.findAll()).thenReturn(List.of(config));
        EventQueueRes res = new EventQueueRes();
        res.setRequest(req);
        res.setStatus(EventQueueRes.Status.FAILED);
        res.setStatusCode(500);
        res.setResponseTime(OffsetDateTime.now());
        res.setBody("body");
        res.setErrorMessage("err");
        when(eventQueueResRepo.findByRequestIdIn(anyList())).thenReturn(List.of(res));
        when(objectMapper.writeValueAsString(any())).thenThrow(new JsonProcessingException("fail"){});
        List<EventLogDTO> result = service.getAllInspectionEvents(id);
        assertEquals(1, result.size());
        assertEquals("E1", result.get(0).getEventId());
        assertEquals("Error serializing request body", result.get(0).getRequestBody());
    }

    // Helper methods to invoke private methods
    private Integer invokeExtractStatusCode(Exception e) {
        try {
            var m = EventService.class.getDeclaredMethod("extractStatusCode", Exception.class);
            m.setAccessible(true);
            return (Integer) m.invoke(service, e);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    private String invokeExtractBodyFromException(Exception e) {
        try {
            var m = EventService.class.getDeclaredMethod("extractBodyFromException", Exception.class);
            m.setAccessible(true);
            return (String) m.invoke(service, e);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    @Test
    void getRequestPayload_and_getOriginalPayload() {
        ObjectNode node = mock(ObjectNode.class);
        JsonNode original = mock(JsonNode.class);
        JsonNode converted = mock(JsonNode.class);
        when(node.get("originalPayload")).thenReturn(original);
        when(node.get("convertedPayload")).thenReturn(converted);
        assertSame(converted, service.getRequestPayload(node));
        when(node.get("convertedPayload")).thenReturn(null);
        assertSame(original, service.getRequestPayload(node));
        assertSame(original, service.getOriginalPayload(node));
    }

    @Test
    void buildPayload_allReferenceTypes() {
        EventQueueView view = mock(EventQueueView.class);
        for (EventQueue.ReferenceType ref : EventQueue.ReferenceType.values()) {
            when(view.getReferenceType()).thenReturn(ref);
            when(view.getPayload()).thenReturn(new HashMap<>());
            if (ref == EventQueue.ReferenceType.INSPECTION) {
                when(modelMapper.map(any(), eq(Inspection.class))).thenReturn(mock(Inspection.class));
            } else if (ref == EventQueue.ReferenceType.INSPECTION_ITEM) {
                when(modelMapper.map(any(), eq(InspectionItem.class))).thenReturn(mock(InspectionItem.class));
            } else if (ref == EventQueue.ReferenceType.COMMENT) {
                when(modelMapper.map(any(), eq(Comment__2.class))).thenReturn(mock(Comment__2.class));
            }
            Payload payload = service.buildPayload(view);
            assertNotNull(payload);
        }
    }

    @Test
    void insertEventRequest_success() throws Exception {
        EventQueueView item = mock(EventQueueView.class);
        when(item.getId()).thenReturn(1L);
        when(item.getEventId()).thenReturn("E1");
        when(item.getConfigId()).thenReturn(1L);
        when(item.getEventType()).thenReturn(EventQueue.EventType.INSP_ITEM_APPROVED);
        when(item.getUniqueId()).thenReturn("U1");
        when(item.getDestination()).thenReturn(EventConfig.Destination.WEBHOOK);
        when(item.getTenant()).thenReturn(1L);
        Object body = mock(JsonNode.class);
        Object orig = mock(JsonNode.class);
        EventQueueReq req = new EventQueueReq();
        req.setId(1L);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(eventQueueReqRepo.save(any())).thenReturn(req);
        service.insertEventRequest(item, body, orig);
        verify(item).setRequestId(1L);
    }

    @Test
    void addHmacConfig_withTenantAndKey() {
        Map<String, Object> config = new HashMap<>();
        service.addHmacConfig(EventConfig.Destination.WEBHOOK, config, 1L, "key");
        assertEquals("key", config.get("hmacKey"));
        assertEquals(1L, config.get("tenant"));
    }

    @Test
    void addHmacConfig_destinationNotWebhook_doesNothing() {
        Map<String, Object> config = new HashMap<>();
        service.addHmacConfig(EventConfig.Destination.KAFKA, config, 2L, "shouldNotSet");
        assertFalse(config.containsKey("hmacKey"));
        assertFalse(config.containsKey("tenant"));
    }

    @Test
    void addHmacConfig_withAuthenticatedUserService() {
        Map<String, Object> config = new HashMap<>();
        AuthenticatedUserDTO user = new AuthenticatedUserDTO();
        user.setTenant(42L);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(user);
        when(merchantRepo.findHmacKeyByTenant(42L)).thenReturn("hmac42");
        service.addHmacConfig(EventConfig.Destination.WEBHOOK, config);
        assertEquals("hmac42", config.get("hmacKey"));
        assertEquals(42L, config.get("tenant"));
    }

    @Test
    void buildBody_withMappingScript_scriptExecutorThrows() throws Exception {
        EventQueueView event = mock(EventQueueView.class);
        when(event.getMappingScript()).thenReturn("script");
        when(objectMapper.createObjectNode()).thenReturn(mock(ObjectNode.class));
        when(objectMapper.valueToTree(any())).thenReturn(mock(JsonNode.class));
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        doThrow(new uk.co.flexi.ri.exception.custom.ScriptExecutionException("fail")).when(scriptExecutor).execute(anyString(), anyString());
        when(event.getEventId()).thenReturn("E1");
        when(event.getEventType()).thenReturn(EventQueue.EventType.INSP_ITEM_APPROVED);
        when(event.getReferenceType()).thenReturn(EventQueue.ReferenceType.INSPECTION_ITEM);
        when(event.getEventTime()).thenReturn(OffsetDateTime.now());
        Object result = service.buildBody(event);
        assertNotNull(result);
    }

    @Test
    void buildHeaders_defaultCase_returnsEmptyMap() {
        // Simulate an unknown destination by using a mock (not in enum)
        Map<String, Object> config = new HashMap<>();
        // This is not possible with Java enums, so this test is not needed in practice.
        // The switch is exhaustive. But for coverage, we can call with a known value and check not null.
        Map<String, Object> result = service.buildHeaders(EventConfig.Destination.AZUREQUEUE, config);
        assertNotNull(result);
    }
}