package uk.co.flexi.ri.integration.batch.processor;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.integration.dto.EventQueueResDTO;
import uk.co.flexi.ri.integration.model.view.EventQueueView;
import uk.co.flexi.ri.integration.service.EventService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.Map;

class EventQueueProcessorTest {
    private EventService eventService;
    private EventQueueProcessor processor;
    private EventQueueView event;
    private JsonNode reqPayload, origPayload;

    @BeforeEach
    void setUp() {
        eventService = mock(EventService.class);
        processor = new EventQueueProcessor(eventService);
        event = mock(EventQueueView.class);
        reqPayload = mock(JsonNode.class);
        origPayload = mock(JsonNode.class);
    }

    @Test
    void process_callsAllServiceMethods_andSetsFields() throws Exception {
        Object body = new Object();
        Map<String, Object> headers = new HashMap<>();
        EventQueueResDTO resDTO = new EventQueueResDTO();
        when(eventService.buildBody(event)).thenReturn(body);
        when(eventService.getRequestPayload(body)).thenReturn(reqPayload);
        when(eventService.getOriginalPayload(body)).thenReturn(origPayload);
        doNothing().when(eventService).addHmacConfig(any(), any(), any(), any());
        when(eventService.buildHeaders(any(), any())).thenReturn(headers);
        doNothing().when(eventService).insertEventRequest(any(), any(), any());
        when(eventService.processEvent(headers, reqPayload)).thenReturn(resDTO);
        when(event.getId()).thenReturn(1L);
        when(event.getEventId()).thenReturn("E1");
        when(event.getRequestId()).thenReturn(2L);
        when(event.getTenant()).thenReturn(3L);
        EventQueueResDTO result = processor.process(event);
        assertSame(resDTO, result);
        assertEquals(1L, result.getId());
        assertEquals("E1", result.getEventId());
        assertEquals(2L, result.getRequestId());
        assertEquals(3L, result.getTenant());
    }

    @Test
    void process_handlesException() throws Exception {
        when(eventService.buildBody(event)).thenThrow(new RuntimeException("fail"));
        assertThrows(RuntimeException.class, () -> processor.process(event));
    }
} 