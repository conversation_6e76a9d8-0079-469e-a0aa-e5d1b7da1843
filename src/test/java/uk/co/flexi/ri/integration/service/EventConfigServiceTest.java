package uk.co.flexi.ri.integration.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.camel.Exchange;
import org.apache.camel.ProducerTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.exception.custom.IntegrationException;
import uk.co.flexi.ri.exception.custom.ScriptExecutionException;
import uk.co.flexi.ri.integration.dto.EventConfigDTO;
import uk.co.flexi.ri.integration.dto.ScriptReqDTO;
import uk.co.flexi.ri.integration.model.EventConfig;
import uk.co.flexi.ri.integration.repository.EventConfigRepo;
import uk.co.flexi.ri.model.EventQueue;
import uk.co.flexi.ri.service.AuthenticatedUserService;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EventConfigServiceTest {

    @Mock EventConfigRepo eventConfigRepo;
    @Mock ModelMapper modelMapper;
    @Mock ObjectMapper objectMapper;
    @Mock ProducerTemplate producerTemplate;
    @Mock EventService eventService;
    @Mock AuthenticatedUserService authenticatedUserService;
    @Mock ScriptExecutor scriptExecutor;
    @Mock Exchange exchange;

    @InjectMocks EventConfigService service;

    private EventConfigDTO dto;
    private EventConfig config;

    @BeforeEach
    void setup() {
        dto = new EventConfigDTO();
        dto.setId(1L);
        dto.setDestination(EventConfig.Destination.WEBHOOK);
        dto.setConfig(new HashMap<>());

        config = new EventConfig();
        config.setId(1L);
    }

    @Test
    void getDestinationList_returnsAllDestinations() {
        assertEquals(Arrays.asList(EventConfig.Destination.values()), service.getDestinationList());
    }

    @Test
    void getEventType_returnsAllEventTypes() {
        List<String> result = service.getEventType();
        List<String> expected = Arrays.stream(EventQueue.EventType.values()).map(Enum::name).toList();
        assertEquals(expected, result);
    }

    @Test
    void getEventConfigKeys_returnsConfigWithHmac() {
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO();
        mockUser.setHmacKey("key");
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(mockUser);
        EventConfigDTO result = service.getEventConfigKeys(EventConfig.Destination.WEBHOOK);
        assertNotNull(result.getConfig().get("hmacKey"));
    }

    @ParameterizedTest
    @EnumSource(EventConfig.Destination.class)
    void getEventConfigKeys_buildsConfigForEachDestination(EventConfig.Destination destination) {
        // Arrange
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO();
        mockUser.setHmacKey("testHmacKey");
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(mockUser);

        // Act
        EventConfigDTO result = service.getEventConfigKeys(destination);
        Map<String, Object> config = result.getConfig();

        // Assert
        assertNotNull(config);
        assertEquals("", config.get("destination"));

        if (destination == EventConfig.Destination.WEBHOOK) {
            assertEquals("testHmacKey", config.get("hmacKey"));
            assertTrue(config.containsKey("url"));
            assertTrue(config.containsKey("authKey"));
            assertTrue(config.containsKey("authType"));
            assertTrue(config.containsKey("authValue"));
        }

        if (destination == EventConfig.Destination.KAFKA) {
            assertTrue(config.containsKey("topic"));
            assertTrue(config.containsKey("brokers"));
            assertTrue(config.containsKey("userName"));
            assertTrue(config.containsKey("password"));
        }

        if (destination == EventConfig.Destination.SQS) {
            assertTrue(config.containsKey("queueName"));
            assertTrue(config.containsKey("accessKey"));
            assertTrue(config.containsKey("secretKey"));
            assertTrue(config.containsKey("region"));
        }

        if (destination == EventConfig.Destination.GOOGLEPUBSUB) {
            assertTrue(config.containsKey("projectId"));
            assertTrue(config.containsKey("topic"));
            assertTrue(config.containsKey("credPath"));
        }

        if (destination == EventConfig.Destination.AZUREQUEUE) {
            assertTrue(config.containsKey("accountName"));
            assertTrue(config.containsKey("client"));
        }
    }

    @Test
    void getEventConfig_returnsAllConfigs() {
        when(eventConfigRepo.findAll()).thenReturn(List.of(config));
        when(modelMapper.map(any(), eq(EventConfigDTO.class))).thenReturn(dto);
        assertEquals(1, service.getEventConfig().size());
    }

    @Test
    void findById_returnsEventConfigDTO() {
        when(eventConfigRepo.findById(1L)).thenReturn(Optional.of(config));
        when(modelMapper.map(config, EventConfigDTO.class)).thenReturn(dto);
        assertEquals(dto, service.findById(1L));
    }

    @Test
    void findById_throwsNoSuchElementException() {
        when(eventConfigRepo.findById(1L)).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.findById(1L));
    }

    @Test
    void addEventConfig_savesAndReturnsDTO() {
        when(modelMapper.map(dto, EventConfig.class)).thenReturn(config);
        when(eventConfigRepo.save(config)).thenReturn(config);
        when(modelMapper.map(config, EventConfigDTO.class)).thenReturn(dto);
        assertEquals(dto, service.addEventConfig(dto));
    }

    @Test
    void updateEventConfig_updatesAndReturnsDTO() {
        when(eventConfigRepo.findById(1L)).thenReturn(Optional.of(config));
        when(modelMapper.map(dto, EventConfig.class)).thenReturn(config);
        when(eventConfigRepo.save(config)).thenReturn(config);
        when(modelMapper.map(config, EventConfigDTO.class)).thenReturn(dto);
        assertEquals(dto, service.updateEventConfig(1L, dto));
    }

    @Test
    void updateEventConfig_throwsNoSuchElementException() {
        when(eventConfigRepo.findById(1L)).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.updateEventConfig(1L, dto));
    }

    @Test
    void deleteEventConfig_deletesSuccessfully() {
        service.deleteEventConfig(1L);
        verify(eventConfigRepo).deleteById(1L);
    }

    @Test
    void testConnection_returnsSuccess() {
        when(eventService.buildHeaders(any(), any())).thenReturn(Map.of());
        when(producerTemplate.request(anyString(), any())).thenAnswer(invocation -> {
            Exchange exchange = mock(Exchange.class);
            when(exchange.getException()).thenReturn(null);
            return exchange;
        });

        Map<String, String> response = service.testConnection(dto);
        assertEquals("success", response.get("status"));
    }

    @Test
    void testConnection_throwsIntegrationException() {
        when(eventService.buildHeaders(any(), any())).thenReturn(Map.of());
        when(producerTemplate.request(anyString(), any())).thenAnswer(invocation -> {
            Exchange exchange = mock(Exchange.class);
            when(exchange.getException()).thenReturn(new RuntimeException("error"));
            return exchange;
        });

        assertThrows(IntegrationException.class, () -> service.testConnection(dto));
    }

    @Test
    void executeScript_returnsJsonNode() throws Exception {
        ScriptReqDTO req = new ScriptReqDTO();
        req.setInputJson(new ObjectMapper().valueToTree(Map.of("key", "value")));
        req.setScript("return input;");

        when(objectMapper.writeValueAsString(any())).thenReturn("{}\n");
        when(scriptExecutor.execute(any(), any())).thenReturn("{}\n");
        when(objectMapper.readTree(anyString())).thenReturn(mock(JsonNode.class));

        JsonNode result = service.executeScript(req);
        assertNotNull(result);
    }

    @Test
    void executeScript_throwsJsonProcessingException() throws Exception {
        ScriptReqDTO req = new ScriptReqDTO();
        req.setInputJson(new ObjectMapper().valueToTree(Map.of("key", "value")));
        req.setScript("bad");

        when(objectMapper.writeValueAsString(any())).thenThrow(JsonProcessingException.class);
        assertThrows(JsonProcessingException.class, () -> service.executeScript(req));
    }

    @Test
    void executeScript_throwsScriptExecutionException() throws Exception {
        ScriptReqDTO req = new ScriptReqDTO();
        req.setInputJson(new ObjectMapper().valueToTree(Map.of("key", "value")));
        req.setScript("bad");

        when(objectMapper.writeValueAsString(any())).thenReturn("{}");
        when(scriptExecutor.execute(any(), any())).thenThrow(ScriptExecutionException.class);
        assertThrows(ScriptExecutionException.class, () -> service.executeScript(req));
    }
} 