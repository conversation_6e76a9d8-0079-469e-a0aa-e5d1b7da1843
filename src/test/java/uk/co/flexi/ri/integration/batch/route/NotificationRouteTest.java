package uk.co.flexi.ri.integration.batch.route;

import org.apache.camel.builder.AdviceWith;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.component.mock.MockEndpoint;
import org.apache.camel.test.junit5.CamelTestSupport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Map;

class NotificationRouteTest extends CamelTestSupport {


    @BeforeEach
    void setup() throws Exception {
        RouteBuilder builder = new NotificationRoute();
        context.addRoutes(builder);
    }

    @Test
    void testEmailNotificationRoute() throws Exception {

        AdviceWith.adviceWith(context, "emailNotificationRoute", a -> {
            a.interceptSendToEndpoint("smtp:*")
                    .skipSendToOriginalEndpoint()
                    .to("mock:smtp");
        });

        context.start();

        MockEndpoint mockSmtp = getMockEndpoint("mock:smtp");
        mockSmtp.expectedMessageCount(1);
        template.sendBodyAndHeaders("direct:emailNotification", null, Map.of(
                "smtpHost", "smtp.test.com",
                "smtpPort", "587",
                "smtpUsername", "user",
                "smtpPassword", "pass",
                "emailTo", "<EMAIL>",
                "emailFrom", "<EMAIL>",
                "emailSubject", "Test Subject"
        ));
        mockSmtp.assertIsSatisfied();
    }


    @Test
    void testSlackNotificationRoute() throws Exception {
        AdviceWith.adviceWith(context, "slackNotificationRoute", a -> {
            a.interceptSendToEndpoint("https://hooks.slack.com/*")
                    .skipSendToOriginalEndpoint()
                    .to("mock:slack");
        });
        context.start();

        MockEndpoint mockSlack = getMockEndpoint("mock:slack");
        mockSlack.expectedMessageCount(1);

        template.sendBodyAndHeaders("direct:slackNotification", "{\"text\":\"Hello\"}", Map.of(
                "slackWebhookUrl", "https://hooks.slack.com/services/TEST"
        ));
        mockSlack.assertIsSatisfied();
    }

    @Test
    void testTeamsNotificationRoute() throws Exception {
        AdviceWith.adviceWith(context, "teamsNotificationRoute", a -> {
            a.interceptSendToEndpoint("https://outlook.office.com/webhook/*")
                    .skipSendToOriginalEndpoint()
                    .to("mock:teams");
        });
        context.start();

        MockEndpoint mockTeams = getMockEndpoint("mock:teams");
        mockTeams.expectedMessageCount(1);

        template.sendBodyAndHeaders("direct:teamsNotification", "{\"text\":\"Hello Teams\"}", Map.of(
                "teamsWebhookUrl", "https://outlook.office.com/webhook/TEST"
        ));

        mockTeams.assertIsSatisfied();
    }

    @Test
    void testNotificationDispatcherRoute_Email() throws Exception {
        AdviceWith.adviceWith(context, "notificationDispatcher", a -> {
            a.weaveByToUri("direct:emailNotification").replace().to("mock:email");
        });
        context.start();

        MockEndpoint mockEmail = getMockEndpoint("mock:email");
        mockEmail.expectedMessageCount(1);

        template.sendBodyAndHeaders("direct:sendNotification", null, Map.of(
                "notificationType", "EMAIL"
        ));

        mockEmail.assertIsSatisfied();
    }

    @Test
    void testNotificationDispatcherRoute_Slack() throws Exception {
        AdviceWith.adviceWith(context, "notificationDispatcher", a -> {
            a.weaveByToUri("direct:slackNotification").replace().to("mock:slack");
        });
        context.start();

        MockEndpoint mockSlack = getMockEndpoint("mock:slack");
        mockSlack.expectedMessageCount(1);

        template.sendBodyAndHeaders("direct:sendNotification", null, Map.of(
                "notificationType", "SLACK"
        ));

        mockSlack.assertIsSatisfied();
    }

    @Test
    void testNotificationDispatcherRoute_Teams() throws Exception {
        AdviceWith.adviceWith(context, "notificationDispatcher", a -> {
            a.weaveByToUri("direct:teamsNotification").replace().to("mock:teams");
        });
        context.start();

        MockEndpoint mockTeams = getMockEndpoint("mock:teams");
        mockTeams.expectedMessageCount(1);

        template.sendBodyAndHeaders("direct:sendNotification", null, Map.of(
                "notificationType", "TEAMS"
        ));

        mockTeams.assertIsSatisfied();
    }

}