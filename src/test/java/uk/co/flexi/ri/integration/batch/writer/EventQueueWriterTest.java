package uk.co.flexi.ri.integration.batch.writer;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.batch.item.Chunk;
import uk.co.flexi.ri.integration.dto.EventQueueResDTO;
import uk.co.flexi.ri.integration.model.EventQueueReq;
import uk.co.flexi.ri.integration.model.EventQueueRes;
import uk.co.flexi.ri.integration.repository.EventQueueResRepo;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class EventQueueWriterTest {
    private EventQueueResRepo repo;
    private EventQueueWriter writer;

    @BeforeEach
    void setUp() {
        repo = mock(EventQueueResRepo.class);
        writer = new EventQueueWriter(repo);
    }

    @Test
    void write_savesAllItems_andSetsFields() throws Exception {
        EventQueueResDTO dto = new EventQueueResDTO();
        dto.setRequestId(1L);
        dto.setEventId("E1");
        dto.setStatusCode(200);
        dto.setBody("body");
        dto.setErrorMessage("err");
        dto.setStatus(EventQueueRes.Status.SENT);
        dto.setTenant(2L);
        Chunk<EventQueueResDTO> chunk = new Chunk<>(List.of(dto));
        writer.write(chunk);
        verify(repo).saveAll(anyList());
    }

    @Test
    void write_handlesEmptyInput() throws Exception {
        Chunk<EventQueueResDTO> chunk = new Chunk<>(List.of());
        writer.write(chunk);
        verify(repo).saveAll(anyList());
    }
} 