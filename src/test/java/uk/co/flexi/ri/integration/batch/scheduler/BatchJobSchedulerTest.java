package uk.co.flexi.ri.integration.batch.scheduler;


import org.junit.jupiter.api.Test;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class BatchJobSchedulerTest {

    @Test
    void testRunJob_Success() throws Exception {
        // Arrange
        JobLauncher jobLauncher = mock(JobLauncher.class);
        Job notificationSendingJob = mock(Job.class);
        Job eventPublisherJob = mock(Job.class);

        BatchJobScheduler scheduler = new BatchJobScheduler(jobLauncher, notificationSendingJob, eventPublisherJob);

        when(jobLauncher.run(eq(eventPublisherJob), any(JobParameters.class)))
                .thenReturn(mock(JobExecution.class));

        // Act
        scheduler.runJob();

        // Assert
        verify(jobLauncher, times(1)).run(eq(eventPublisherJob), any(JobParameters.class));
    }

    @Test
    void testRunJob_Exception() throws Exception {
        // Arrange
        JobLauncher jobLauncher = mock(JobLauncher.class);
        Job notificationSendingJob = mock(Job.class);
        Job eventPublisherJob = mock(Job.class);

        BatchJobScheduler scheduler = new BatchJobScheduler(jobLauncher, notificationSendingJob, eventPublisherJob);

        when(jobLauncher.run(eq(eventPublisherJob), any(JobParameters.class)))
                .thenThrow(new RuntimeException("Simulated failure"));

        // Act (should not throw)
        scheduler.runJob();

        // Assert
        verify(jobLauncher, times(1)).run(eq(eventPublisherJob), any(JobParameters.class));
        // Exception is printed but not thrown
    }

    @Test
    void testRunNotificationJob_Success() throws Exception {
        // Arrange
        JobLauncher jobLauncher = mock(JobLauncher.class);
        Job notificationSendingJob = mock(Job.class);
        Job eventPublisherJob = mock(Job.class);

        BatchJobScheduler scheduler = new BatchJobScheduler(jobLauncher, notificationSendingJob, eventPublisherJob);

        when(jobLauncher.run(eq(notificationSendingJob), any(JobParameters.class)))
                .thenReturn(mock(JobExecution.class));
        // Act
        scheduler.runNotificationJob();

        // Assert
        verify(jobLauncher, times(1)).run(eq(notificationSendingJob), any(JobParameters.class));
    }

    @Test
    void testRunNotificationJob_Exception() throws Exception {
        // Arrange
        JobLauncher jobLauncher = mock(JobLauncher.class);
        Job notificationSendingJob = mock(Job.class);
        Job eventPublisherJob = mock(Job.class);

        BatchJobScheduler scheduler = new BatchJobScheduler(jobLauncher, notificationSendingJob, eventPublisherJob);

        when(jobLauncher.run(eq(notificationSendingJob), any(JobParameters.class)))
                .thenThrow(new RuntimeException("Simulated failure"));

        // Act (should not throw)
        scheduler.runNotificationJob();

        // Assert
        verify(jobLauncher, times(1)).run(eq(notificationSendingJob), any(JobParameters.class));
        // Exception is printed but not thrown
    }

}