package uk.co.flexi.ri.integration.batch.reader;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.repository.NotificationConfigRepo;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NotificationConfigReaderTest {

    @Mock
    private NotificationConfigRepo notificationConfigRepo;

    @InjectMocks
    private NotificationConfigReader notificationConfigReader;

    @Test
    void read_ReturnsNextNotificationConfig_WhenQueueIsNotEmpty() {
        // Arrange
        NotificationConfig notification1 = new NotificationConfig();
        NotificationConfig notification2 = new NotificationConfig();
        List<NotificationConfig> notifications = List.of(notification1, notification2);
        when(notificationConfigRepo.findAllNotifications()).thenReturn(notifications);

        // Act
        NotificationConfig result1 = notificationConfigReader.read();
        NotificationConfig result2 = notificationConfigReader.read();
        NotificationConfig result3 = notificationConfigReader.read();

        // Assert
        assertEquals(notification1, result1);
        assertEquals(notification2, result2);
        assertNull(result3);
        verify(notificationConfigRepo).findAllNotifications();
    }

    @Test
    void read_ReturnsNull_WhenNoNotificationsExist() {
        // Arrange
        when(notificationConfigRepo.findAllNotifications()).thenReturn(List.of());

        // Act
        NotificationConfig result = notificationConfigReader.read();

        // Assert
        assertNull(result);
        verify(notificationConfigRepo).findAllNotifications();
    }


}