package uk.co.flexi.ri.integration.batch.processor;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import uk.co.flexi.ri.integration.notification.factory.NotificationHandlerFactory;
import uk.co.flexi.ri.integration.notification.handler.NotificationHandler;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.util.Util;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NotificationProcessorTest {

    @Mock
    private NotificationHandlerFactory notificationHandlerFactory;

    @InjectMocks
    private NotificationProcessor notificationProcessor;


    @Test
    void process_ReturnsResponse_WhenFrequencyIsImmediately() throws Exception {
        // Arrange
        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        NotificationHandler handler = mock(NotificationHandler.class);
        Map<NotificationConfig, Object> response = Map.of(notificationConfig, new Object());

        when(notificationConfig.getFrequency()).thenReturn("IMMEDIATELY");
        when(notificationHandlerFactory.getHandler(notificationConfig.getNotificationType())).thenReturn(handler);
        when(handler.handle(notificationConfig)).thenReturn(response);

        //Act
        Map<NotificationConfig, Object> result = notificationProcessor.process(notificationConfig);

        // Assert
        assertEquals(response, result);
        verify(notificationHandlerFactory).getHandler(notificationConfig.getNotificationType());
        verify(handler).handle(notificationConfig);
    }

    @Test
    void process_ReturnsNull_WhenFrequencyShouldNotTriggerNow() throws Exception {
        // Arrange
        NotificationConfig notificationConfig = mock(NotificationConfig.class);

        when(notificationConfig.getFrequency()).thenReturn("DAILY");
        try (MockedStatic<Util> utilMock = mockStatic(Util.class)) {
            utilMock.when(() -> Util.shouldTriggerNow("DAILY")).thenReturn(false);

            // Act
            Map<NotificationConfig, Object> result = notificationProcessor.process(notificationConfig);

            // Assert
            assertNull(result);
            verifyNoInteractions(notificationHandlerFactory);
        }
    }

    @Test
    void process_ShouldThrowsException_WhenHandlerFails() throws Exception {
        // Arrange
        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        NotificationHandler handler = mock(NotificationHandler.class);

        when(notificationConfig.getFrequency()).thenReturn("IMMEDIATELY");
        when(notificationHandlerFactory.getHandler(notificationConfig.getNotificationType())).thenReturn(handler);
        when(handler.handle(notificationConfig)).thenThrow(new RuntimeException("Handler error"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> notificationProcessor.process(notificationConfig));
        verify(notificationHandlerFactory).getHandler(notificationConfig.getNotificationType());
        verify(handler).handle(notificationConfig);
    }
}