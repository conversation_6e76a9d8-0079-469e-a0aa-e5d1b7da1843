package uk.co.flexi.ri.integration.notification.handler;

import org.apache.camel.ProducerTemplate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import uk.co.flexi.ri.integration.service.JobExecutionService;
import uk.co.flexi.ri.model.NotificationChannel;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.repository.InspectionRepo;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AssignToMeNotificationHandlerTest {

    @Mock
    private InspectionRepo inspectionRepo;

    @Mock
    private ProducerTemplate producerTemplate;

    @Mock
    private JobExecutionService jobExecutionService;

    @InjectMocks
    private AssignToMeNotificationHandler assignToMeNotificationHandler;

    @Test
    void handle_ShouldSendsNotification_WhenEmailAndImmediately() throws Exception {
        //Arrange
        NotificationChannel channel = new NotificationChannel();
        channel.setChannel(NotificationChannel.Channel.EMAIL);
        channel.setConfig(Map.of("smtpHost", "1", "smtpPort", "2"));

        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        when(notificationConfig.getUserGroupId()).thenReturn("group1");
        when(notificationConfig.getFrequency()).thenReturn("IMMEDIATELY");
        when(notificationConfig.getTenant()).thenReturn(1L);
        when(notificationConfig.getChannel()).thenReturn(channel);
        when(notificationConfig.getDestination()).thenReturn("<EMAIL>");

        OffsetDateTime lastExecutionTime = OffsetDateTime.now().minusDays(1);
        when(jobExecutionService.getLastJobExecutionTime("notificationSendingJob")).thenReturn(lastExecutionTime);
        when(inspectionRepo.findNotificationEligibleInspection("group1", lastExecutionTime, 1L))
                .thenReturn(List.of("ref1", "ref2"));

        //Act
        Map<NotificationConfig, Object> result = assignToMeNotificationHandler.handle(notificationConfig);

        //Assert
        verify(producerTemplate).sendBodyAndHeaders(eq("direct:sendNotification"), any(), any());
        assertNotNull(result);
        assertTrue(result.containsKey(notificationConfig));
    }

    @Test
    void handle_ShouldSendsNotification_WhenSlackAndOnehour() throws Exception {
        // Arrange
        NotificationChannel channel = new NotificationChannel();
        channel.setChannel(NotificationChannel.Channel.SLACK);
        channel.setConfig(Map.of("slackWebhookUrl", "webhookUrl"));

        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        when(notificationConfig.getUserGroupId()).thenReturn("group1");
        when(notificationConfig.getFrequency()).thenReturn("EVERY_ONE_HOUR");
        when(notificationConfig.getTenant()).thenReturn(1L);
        when(notificationConfig.getChannel()).thenReturn(channel);
        when(notificationConfig.getDestination()).thenReturn("<EMAIL>");
        when(inspectionRepo.findNotificationEligibleInspection("group1", 1L))
                .thenReturn(List.of("ref1", "ref2"));

        // Act
        Map<NotificationConfig, Object> result = assignToMeNotificationHandler.handle(notificationConfig);

        // Assert
        verify(producerTemplate).sendBodyAndHeaders(eq("direct:sendNotification"), any(), any());
        assertNotNull(result);
        assertTrue(result.containsKey(notificationConfig));
    }

    @Test
    void handle_ShouldSendsNotification_WhenTeamsAndCustom() throws Exception {
        // Arrange
        NotificationChannel channel = new NotificationChannel();
        channel.setChannel(NotificationChannel.Channel.TEAMS);
        channel.setConfig(Map.of("slackWebhookUrl", "webhookUrl"));

        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        when(notificationConfig.getUserGroupId()).thenReturn("group1");
        when(notificationConfig.getFrequency()).thenReturn("CUSTOM");
        when(notificationConfig.getTenant()).thenReturn(1L);
        when(notificationConfig.getChannel()).thenReturn(channel);
        when(notificationConfig.getDestination()).thenReturn("<EMAIL>");
        when(inspectionRepo.findNotificationEligibleInspection("group1", 1L))
                .thenReturn(List.of("ref1", "ref2"));

        // Act
        Map<NotificationConfig, Object> result = assignToMeNotificationHandler.handle(notificationConfig);

        // Assert
        verify(producerTemplate).sendBodyAndHeaders(eq("direct:sendNotification"), any(), any());
        assertNotNull(result);
        assertTrue(result.containsKey(notificationConfig));
    }

    @Test
    void handle_ShouldNotSendNotification_WhenNoEligibleInspectionsExist() throws Exception {
        // Arrange
        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        when(notificationConfig.getUserGroupId()).thenReturn("group1");
        when(notificationConfig.getFrequency()).thenReturn("IMMEDIATELY");
        when(notificationConfig.getTenant()).thenReturn(1L);

        OffsetDateTime lastExecutionTime = OffsetDateTime.now().minusDays(1);
        when(jobExecutionService.getLastJobExecutionTime("notificationSendingJob")).thenReturn(lastExecutionTime);
        when(inspectionRepo.findNotificationEligibleInspection("group1", lastExecutionTime, 1L))
                .thenReturn(List.of());
        // Act
        Map<NotificationConfig, Object> result = assignToMeNotificationHandler.handle(notificationConfig);

        // Assert
        verifyNoInteractions(producerTemplate);
        assertNull(result);
    }

    @Test
    void handle_ShouldThrowsException_WhenInspectionRepoFails() {
        // Arrange
        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        when(notificationConfig.getUserGroupId()).thenReturn("group1");
        when(notificationConfig.getFrequency()).thenReturn("IMMEDIATELY");
        when(notificationConfig.getTenant()).thenReturn(1L);

        when(jobExecutionService.getLastJobExecutionTime("notificationSendingJob")).thenReturn(OffsetDateTime.now().minusDays(1));
        when(inspectionRepo.findNotificationEligibleInspection(anyString(), any(), anyLong()))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        Exception exception = assertThrows(RuntimeException.class, () -> assignToMeNotificationHandler.handle(notificationConfig));

        assertEquals("Database error", exception.getMessage());
        verifyNoInteractions(producerTemplate);
    }
}