package uk.co.flexi.ri.integration.notification.factory;

import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.integration.notification.handler.NotificationHandler;
import uk.co.flexi.ri.model.NotificationConfig;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class NotificationHandlerFactoryTest {



    @Test
    void getHandlerReturnsCorrectHandlerForSupportedType() {
        NotificationHandler emailHandler = mock(NotificationHandler.class);
        NotificationHandler slackHandler = mock(NotificationHandler.class);
        when(emailHandler.getSupportedType()).thenReturn(NotificationConfig.NotificationType.ASSIGN_TO_ME);
        when(slackHandler.getSupportedType()).thenReturn(NotificationConfig.NotificationType.INTEGRATION_FAILURE);

        NotificationHandlerFactory factory = new NotificationHandlerFactory(List.of(email<PERSON>and<PERSON>, slackHandler));

        NotificationHandler result = factory.getHandler(NotificationConfig.NotificationType.ASSIGN_TO_ME);

        assertEquals(emailHandler, result);
    }

    @Test
    void getHandlerThrowsExceptionForUnsupportedType() {
        NotificationHandler emailHandler = mock(NotificationHandler.class);
        when(emailHandler.getSupportedType()).thenReturn(NotificationConfig.NotificationType.ASSIGN_TO_ME);

        NotificationHandlerFactory factory = new NotificationHandlerFactory(List.of(emailHandler));

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
            () -> factory.getHandler(NotificationConfig.NotificationType.INTEGRATION_FAILURE));

        assertEquals("No handler found for notification type: INTEGRATION_FAILURE", exception.getMessage());
    }

    @Test
    void getHandlerThrowsExceptionWhenHandlerListIsEmpty() {
        NotificationHandlerFactory factory = new NotificationHandlerFactory(List.of());

        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
            () -> factory.getHandler(NotificationConfig.NotificationType.ASSIGN_TO_ME));

        assertEquals("No handler found for notification type: ASSIGN_TO_ME", exception.getMessage());
    }
}