package uk.co.flexi.ri.integration.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.exception.custom.ScriptExecutionException;
import uk.co.flexi.ri.integration.dto.EventConfigDTO;
import uk.co.flexi.ri.integration.dto.ScriptReqDTO;
import uk.co.flexi.ri.integration.model.EventConfig;
import uk.co.flexi.ri.integration.service.EventConfigService;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EventConfigControllerTest {
    @Mock private EventConfigService eventConfigService;
    @InjectMocks private EventConfigController controller;

    private EventConfigDTO dto;
    private ScriptReqDTO scriptReqDTO;
    private JsonNode jsonNode;

    @BeforeEach
    void setUp() {
        dto = new EventConfigDTO();
        scriptReqDTO = new ScriptReqDTO();
        jsonNode = mock(JsonNode.class);
    }

    @Test
    void getDestinationList_returnsList() {
        List<EventConfig.Destination> list = List.of(EventConfig.Destination.WEBHOOK);
        when(eventConfigService.getDestinationList()).thenReturn(list);
        ResponseEntity<List<EventConfig.Destination>> response = controller.getDestinationList();
        assertEquals(list, response.getBody());
    }

    @Test
    void getEventConfigKeys_returnsDto() {
        when(eventConfigService.getEventConfigKeys(any())).thenReturn(dto);
        ResponseEntity<EventConfigDTO> response = controller.getEventConfigKeys(EventConfig.Destination.WEBHOOK);
        assertEquals(dto, response.getBody());
    }

    @Test
    void getEventType_returnsList() {
        List<String> types = List.of("A", "B");
        when(eventConfigService.getEventType()).thenReturn(types);
        ResponseEntity<List<String>> response = controller.getEventType();
        assertEquals(types, response.getBody());
    }

    @Test
    void getEventConfig_returnsList() {
        List<EventConfigDTO> list = List.of(dto);
        when(eventConfigService.getEventConfig()).thenReturn(list);
        ResponseEntity<List<EventConfigDTO>> response = controller.getEventConfig();
        assertEquals(list, response.getBody());
    }

    @Test
    void findById_returnsDto() {
        when(eventConfigService.findById(1L)).thenReturn(dto);
        ResponseEntity<EventConfigDTO> response = controller.findById(1L);
        assertEquals(dto, response.getBody());
    }

    @Test
    void addEventConfig_returnsDto() {
        when(eventConfigService.addEventConfig(any())).thenReturn(dto);
        ResponseEntity<EventConfigDTO> response = controller.addEventConfig(dto);
        assertEquals(dto, response.getBody());
    }

    @Test
    void updateEventConfig_returnsDto() {
        when(eventConfigService.updateEventConfig(eq(1L), any())).thenReturn(dto);
        ResponseEntity<EventConfigDTO> response = controller.updateEventConfig(1L, dto);
        assertEquals(dto, response.getBody());
    }

    @Test
    void deleteEventConfig_callsService() {
        doNothing().when(eventConfigService).deleteEventConfig(1L);
        controller.deleteEventConfig(1L);
        verify(eventConfigService).deleteEventConfig(1L);
    }

    @Test
    void testConnection_returnsMap() {
        Map<String, String> map = Map.of("status", "success");
        when(eventConfigService.testConnection(any())).thenReturn(map);
        ResponseEntity<Map<String, String>> response = controller.testConnection(dto);
        assertEquals(map, response.getBody());
    }

    @Test
    void executeScript_returnsJsonNode() throws Exception {
        when(eventConfigService.executeScript(any())).thenReturn(jsonNode);
        ResponseEntity<JsonNode> response = controller.executeScript(scriptReqDTO);
        assertEquals(jsonNode, response.getBody());
    }

    @Test
    void executeScript_throwsJsonProcessingException() throws Exception {
        when(eventConfigService.executeScript(any())).thenThrow(new JsonProcessingException("fail"){});
        assertThrows(JsonProcessingException.class, () -> controller.executeScript(scriptReqDTO));
    }

    @Test
    void executeScript_throwsScriptExecutionException() throws Exception {
        when(eventConfigService.executeScript(any())).thenThrow(new ScriptExecutionException("fail"));
        assertThrows(ScriptExecutionException.class, () -> controller.executeScript(scriptReqDTO));
    }
} 