package uk.co.flexi.ri.integration.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.integration.dto.EventLogDTO;
import uk.co.flexi.ri.integration.service.EventService;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EventControllerTest {
    @Mock private EventService eventService;
    @InjectMocks private EventController controller;

    private EventLogDTO eventLogDTO;

    @BeforeEach
    void setUp() {
        eventLogDTO = new EventLogDTO();
    }

    @Test
    void getEvents_returnsList() {
        List<EventLogDTO> list = List.of(eventLogDTO);
        when(eventService.getAllInspectionEvents(anyString())).thenReturn(list);
        ResponseEntity<List<EventLogDTO>> response = controller.getEvents("id");
        assertEquals(list, response.getBody());
    }

    @Test
    void retryEventPosting_returnsDto() {
        when(eventService.retryEventPosting(anyString())).thenReturn(eventLogDTO);
        ResponseEntity<EventLogDTO> response = controller.retryEventPosting("eventId");
        assertEquals(eventLogDTO, response.getBody());
    }

    @Test
    void retryEventPosting_handlesException() {
        when(eventService.retryEventPosting(anyString())).thenThrow(new RuntimeException("fail"));
        assertThrows(RuntimeException.class, () -> controller.retryEventPosting("eventId"));
    }
} 