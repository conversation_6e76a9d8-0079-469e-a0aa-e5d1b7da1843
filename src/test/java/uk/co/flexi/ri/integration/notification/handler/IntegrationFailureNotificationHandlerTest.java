package uk.co.flexi.ri.integration.notification.handler;

import org.apache.camel.ProducerTemplate;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import uk.co.flexi.ri.integration.repository.EventQueueResRepo;
import uk.co.flexi.ri.integration.service.JobExecutionService;
import uk.co.flexi.ri.model.NotificationChannel;
import uk.co.flexi.ri.model.NotificationConfig;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class IntegrationFailureNotificationHandlerTest {

    @Mock
    private EventQueueResRepo eventQueueResRepo;

    @Mock
    private ProducerTemplate producerTemplate;

    @Mock
    private JobExecutionService jobExecutionService;

    @InjectMocks
    private IntegrationFailureNotificationHandler integrationFailureNotificationHandler;

    @Test
    void handle_ShouldSendNotification_WhenFailedResponsesExist_Email() throws Exception {
        // Arrange
        NotificationChannel channel = new NotificationChannel();
        channel.setChannel(NotificationChannel.Channel.EMAIL);
        channel.setConfig(Map.of("smtpHost", "1", "smtpPort", "2"));

        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        when(notificationConfig.getUserGroupId()).thenReturn("group1");
        when(notificationConfig.getTenant()).thenReturn(1L);
        when(notificationConfig.getChannel()).thenReturn(channel);
        when(notificationConfig.getDestination()).thenReturn("<EMAIL>");

        OffsetDateTime lastExecutionTime = OffsetDateTime.now().minusDays(1);
        when(jobExecutionService.getLastJobExecutionTime("notificationSendingJob")).thenReturn(lastExecutionTime);
        when(eventQueueResRepo.findFailedResponseForNotification(1L, lastExecutionTime))
                .thenReturn(List.of("event1", "event2"));

        // Act
        Map<NotificationConfig, Object> result = integrationFailureNotificationHandler.handle(notificationConfig);

        // Assert
        verify(producerTemplate).sendBodyAndHeaders(eq("direct:sendNotification"), any(), any());
        assertNotNull(result);
        assertTrue(result.containsKey(notificationConfig));
    }

    @Test
    void handle_ShouldSendNotification_WhenFailedResponsesExist_Slack() throws Exception {
        // Arrange
        NotificationChannel channel = new NotificationChannel();
        channel.setChannel(NotificationChannel.Channel.SLACK);
        channel.setConfig(Map.of("slackWebhookUrl", "1"));

        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        when(notificationConfig.getUserGroupId()).thenReturn("group1");
        when(notificationConfig.getTenant()).thenReturn(1L);
        when(notificationConfig.getChannel()).thenReturn(channel);
        when(notificationConfig.getDestination()).thenReturn("<EMAIL>");

        OffsetDateTime lastExecutionTime = OffsetDateTime.now().minusDays(1);
        when(jobExecutionService.getLastJobExecutionTime("notificationSendingJob")).thenReturn(lastExecutionTime);
        when(eventQueueResRepo.findFailedResponseForNotification(1L, lastExecutionTime))
                .thenReturn(List.of("event1", "event2"));

        // Act
        Map<NotificationConfig, Object> result = integrationFailureNotificationHandler.handle(notificationConfig);

        // Assert
        verify(producerTemplate).sendBodyAndHeaders(eq("direct:sendNotification"), any(), any());
        assertNotNull(result);
        assertTrue(result.containsKey(notificationConfig));
    }

    @Test
    void handle_ShouldSendNotification_WhenFailedResponsesExist_Teams() throws Exception {
        // Arrange
        NotificationChannel channel = new NotificationChannel();
        channel.setChannel(NotificationChannel.Channel.TEAMS);
        channel.setConfig(Map.of("teamsWebhookUrl", "1"));

        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        when(notificationConfig.getUserGroupId()).thenReturn("group1");
        when(notificationConfig.getTenant()).thenReturn(1L);
        when(notificationConfig.getChannel()).thenReturn(channel);
        when(notificationConfig.getDestination()).thenReturn("<EMAIL>");

        OffsetDateTime lastExecutionTime = OffsetDateTime.now().minusDays(1);
        when(jobExecutionService.getLastJobExecutionTime("notificationSendingJob")).thenReturn(lastExecutionTime);
        when(eventQueueResRepo.findFailedResponseForNotification(1L, lastExecutionTime))
                .thenReturn(List.of("event1", "event2"));

        // Act
        Map<NotificationConfig, Object> result = integrationFailureNotificationHandler.handle(notificationConfig);

        // Assert
        verify(producerTemplate).sendBodyAndHeaders(eq("direct:sendNotification"), any(), any());
        assertNotNull(result);
        assertTrue(result.containsKey(notificationConfig));
    }

    @Test
    void handle_DoesNotSendNotification_WhenNoFailedResponsesExist() throws Exception {
        // Arrange
        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        when(notificationConfig.getUserGroupId()).thenReturn("group1");
        when(notificationConfig.getTenant()).thenReturn(1L);


        OffsetDateTime lastExecutionTime = OffsetDateTime.now().minusDays(1);
        when(jobExecutionService.getLastJobExecutionTime("notificationSendingJob")).thenReturn(lastExecutionTime);
        when(eventQueueResRepo.findFailedResponseForNotification(1L, lastExecutionTime))
                .thenReturn(List.of());
        // Act
        Map<NotificationConfig, Object> result = integrationFailureNotificationHandler.handle(notificationConfig);

        // Assert
        verifyNoInteractions(producerTemplate);
        assertNull(result);
    }

    @Test
    void handleThrowsExceptionWhenEventQueueResRepoFails() {
        // Arrange
        NotificationConfig notificationConfig = mock(NotificationConfig.class);
        when(notificationConfig.getUserGroupId()).thenReturn("group1");
        when(notificationConfig.getTenant()).thenReturn(1L);

        when(jobExecutionService.getLastJobExecutionTime("notificationSendingJob")).thenReturn(OffsetDateTime.now().minusDays(1));
        when(eventQueueResRepo.findFailedResponseForNotification(anyLong(), any()))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        Exception exception = assertThrows(RuntimeException.class, () -> integrationFailureNotificationHandler.handle(notificationConfig));

        assertEquals("Database error", exception.getMessage());
        verifyNoInteractions(producerTemplate);
    }
}