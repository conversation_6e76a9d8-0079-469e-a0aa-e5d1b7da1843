package uk.co.flexi.ri.integration.batch.reader;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.integration.model.view.EventQueueView;
import uk.co.flexi.ri.integration.repository.view.EventQueueViewRepo;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class EventQueueReaderTest {
    private EventQueueViewRepo repo;
    private EventQueueReader reader;
    private EventQueueView event1, event2;

    @BeforeEach
    void setUp() {
        repo = mock(EventQueueViewRepo.class);
        event1 = mock(EventQueueView.class);
        event2 = mock(EventQueueView.class);
        reader = new EventQueueReader(repo);
    }

    @Test
    void read_returnsItemsInOrder_thenNull() {
        when(repo.findAll()).thenReturn(Arrays.asList(event1, event2));
        assertSame(event1, reader.read());
        assertSame(event2, reader.read());
        assertNull(reader.read());
        // Should not call findAll again
        assertNull(reader.read());
        verify(repo, times(1)).findAll();
    }
} 