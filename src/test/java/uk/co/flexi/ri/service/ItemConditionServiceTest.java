package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.InspectionItemConditionDTO;
import uk.co.flexi.ri.model.InspectionItemCondition;
import uk.co.flexi.ri.repository.InspectionItemConditionRepo;

import java.util.Collections;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ItemConditionServiceTest {
    private ModelMapper modelMapper;
    private InspectionItemConditionRepo itemConditionRepo;
    private ItemConditionService service;

    @BeforeEach
    void setUp() {
        modelMapper = mock(ModelMapper.class);
        itemConditionRepo = mock(InspectionItemConditionRepo.class);
        service = new ItemConditionService(modelMapper, itemConditionRepo);
    }

    @Test
    void findById_found() {
        InspectionItemCondition cond = new InspectionItemCondition();
        InspectionItemConditionDTO dto = new InspectionItemConditionDTO();
        when(itemConditionRepo.findByItemConditionId("cid")).thenReturn(Optional.of(cond));
        when(modelMapper.map(cond, InspectionItemConditionDTO.class)).thenReturn(dto);
        assertSame(dto, service.findById("cid"));
    }

    @Test
    void findById_notFound_throws() {
        when(itemConditionRepo.findByItemConditionId("cid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.findById("cid"));
    }

    @Test
    void save_success() {
        InspectionItemConditionDTO dto = new InspectionItemConditionDTO();
        InspectionItemCondition cond = new InspectionItemCondition();
        when(modelMapper.map(dto, InspectionItemCondition.class)).thenReturn(cond);
        when(itemConditionRepo.save(cond)).thenReturn(cond);
        when(modelMapper.map(cond, InspectionItemConditionDTO.class)).thenReturn(dto);
        assertSame(dto, service.save(dto));
    }

    @Test
    void findByConditionType_mapsAll() {
        InspectionItemCondition cond = new InspectionItemCondition();
        InspectionItemConditionDTO dto = new InspectionItemConditionDTO();
        InspectionItemCondition.ConditionType type = InspectionItemCondition.ConditionType.APPROVED;
        when(itemConditionRepo.findByConditionType(type)).thenReturn(Collections.singletonList(cond));
        when(modelMapper.map(cond, InspectionItemConditionDTO.class)).thenReturn(dto);
        List<InspectionItemConditionDTO> result = service.findByConditionType(type);
        assertEquals(1, result.size());
        assertSame(dto, result.get(0));
    }

    @Test
    void update_found() {
        InspectionItemCondition cond = new InspectionItemCondition();
        InspectionItemConditionDTO dto = new InspectionItemConditionDTO();
        dto.setName("new name");
        when(itemConditionRepo.findByItemConditionId("cid")).thenReturn(Optional.of(cond));
        when(itemConditionRepo.save(cond)).thenReturn(cond);
        when(modelMapper.map(cond, InspectionItemConditionDTO.class)).thenReturn(dto);
        InspectionItemConditionDTO result = service.update("cid", dto);
        assertSame(dto, result);
        assertEquals("new name", cond.getName());
    }

    @Test
    void update_notFound_throws() {
        InspectionItemConditionDTO dto = new InspectionItemConditionDTO();
        when(itemConditionRepo.findByItemConditionId("cid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.update("cid", dto));
    }

    @Test
    void delete_found() {
        InspectionItemCondition cond = new InspectionItemCondition();
        when(itemConditionRepo.findByItemConditionId("cid")).thenReturn(Optional.of(cond));
        doNothing().when(itemConditionRepo).delete(cond);
        service.delete("cid");
        verify(itemConditionRepo).delete(cond);
    }

    @Test
    void delete_notFound_throws() {
        when(itemConditionRepo.findByItemConditionId("cid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.delete("cid"));
    }
} 