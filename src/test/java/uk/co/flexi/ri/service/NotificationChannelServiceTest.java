package uk.co.flexi.ri.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.NotificationChannelDTO;
import uk.co.flexi.ri.model.NotificationChannel;
import uk.co.flexi.ri.repository.NotificationChannelRepo;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NotificationChannelServiceTest {

    @Mock
    private NotificationChannelRepo notificationChannelRepo;

    @Mock
    private ModelMapper modelMapper;

    @InjectMocks
    private NotificationChannelService notificationChannelService;

    @Test
    void getNotificationChannelById_ShouldReturnsDTO() {
        //Arrange
        Long id = 1L;
        NotificationChannel notificationChannel = new NotificationChannel();
        NotificationChannelDTO notificationChannelDTO = new NotificationChannelDTO();

        when(notificationChannelRepo.findById(id)).thenReturn(Optional.of(notificationChannel));
        when(modelMapper.map(notificationChannel, NotificationChannelDTO.class)).thenReturn(notificationChannelDTO);

        //Act
        NotificationChannelDTO result = notificationChannelService.getNotificationChannelById(id);

        //Assert
        assertNotNull(result);
        assertEquals(notificationChannelDTO, result);
        verify(notificationChannelRepo).findById(id);
        verify(modelMapper).map(notificationChannel, NotificationChannelDTO.class);
    }

    @Test
    void getNotificationChannelById_ShouldThrowsException_WhenChannelDoesNotExist() {
        //Arrange
        Long id = 1L;

        when(notificationChannelRepo.findById(id)).thenReturn(Optional.empty());

        //Act & Assert
        NoSuchElementException exception = assertThrows(NoSuchElementException.class, () ->
                notificationChannelService.getNotificationChannelById(id)
        );

        assertEquals("NotificationChannel not found with id: " + id, exception.getMessage());
        verify(notificationChannelRepo).findById(id);
    }

    @Test
    void createNotificationChannel_ShouldSaveNotificationChannel() {
        //Arrange
        NotificationChannelDTO inputDTO = new NotificationChannelDTO();
        NotificationChannel notificationChannel = new NotificationChannel();
        NotificationChannelDTO outputDTO = new NotificationChannelDTO();
        AuthenticatedUserDTO userDetails = new AuthenticatedUserDTO();
        userDetails.setTenant(1L);


        when(modelMapper.map(inputDTO, NotificationChannel.class)).thenReturn(notificationChannel);
        when(notificationChannelRepo.save(notificationChannel)).thenReturn(notificationChannel);
        when(modelMapper.map(notificationChannel, NotificationChannelDTO.class)).thenReturn(outputDTO);

        //Act
        NotificationChannelDTO result = notificationChannelService.createNotificationChannel(inputDTO);

        //Assert
        assertNotNull(result);
        assertEquals(outputDTO, result);
        verify(notificationChannelRepo).save(notificationChannel);
        verify(modelMapper).map(inputDTO, NotificationChannel.class);
        verify(modelMapper).map(notificationChannel, NotificationChannelDTO.class);
    }

    @Test
    void updateNotificationChannel_ShouldUpdatesNotificationChannel() {
        //Arrange
        Long id = 1L;
        NotificationChannelDTO inputDTO = new NotificationChannelDTO();
        NotificationChannel existingChannel = new NotificationChannel();
        NotificationChannelDTO outputDTO = new NotificationChannelDTO();


        when(notificationChannelRepo.findById(id)).thenReturn(Optional.of(existingChannel));
        doNothing().when(modelMapper).map(inputDTO, existingChannel);
        when(notificationChannelRepo.save(existingChannel)).thenReturn(existingChannel);
        when(modelMapper.map(existingChannel, NotificationChannelDTO.class)).thenReturn(outputDTO);

        //Act
        NotificationChannelDTO result = notificationChannelService.updateNotificationChannel(id, inputDTO);

        //Assert
        assertNotNull(result);
        assertEquals(outputDTO, result);
        verify(notificationChannelRepo).findById(id);
        verify(notificationChannelRepo).save(existingChannel);
        verify(modelMapper).map(inputDTO, existingChannel);
        verify(modelMapper).map(existingChannel, NotificationChannelDTO.class);
    }

    @Test
    void updateNotificationChannel_ShouldThrowsException_WhenChannelDoesNotExist() {
        //Arrange
        Long id = 1L;
        NotificationChannelDTO inputDTO = new NotificationChannelDTO();

        when(notificationChannelRepo.findById(id)).thenReturn(Optional.empty());

        //Act &Assert
        NoSuchElementException exception = assertThrows(NoSuchElementException.class, () ->
                notificationChannelService.updateNotificationChannel(id, inputDTO)
        );

        assertEquals("NotificationChannel not found with id: " + id, exception.getMessage());
        verify(notificationChannelRepo).findById(id);
    }

    @Test
    void deleteNotificationChannel_ShouldDelete() {
        //Arrange
        Long id = 1L;
        NotificationChannel notificationChannel = new NotificationChannel();

        when(notificationChannelRepo.findById(id)).thenReturn(Optional.of(notificationChannel));

        //Act
        notificationChannelService.deleteNotificationChannel(id);

        //Assert
        verify(notificationChannelRepo).findById(id);
        verify(notificationChannelRepo).delete(notificationChannel);
    }

    @Test
    void deleteNotificationChannel_ShouldThrowsException_WhenChannelDoesNotExist() {
        //Arrange
        Long id = 1L;

        when(notificationChannelRepo.findById(id)).thenReturn(Optional.empty());

        //Act & Assert
        NoSuchElementException exception = assertThrows(NoSuchElementException.class, () ->
                notificationChannelService.deleteNotificationChannel(id)
        );

        assertEquals("NotificationChannel not found with id: " + id, exception.getMessage());
        verify(notificationChannelRepo).findById(id);
    }

    @Test
    void getChannelList_ShouldReturnAllChannels(){
        //Act & Assert
        assertEquals(Arrays.stream(NotificationChannel.Channel.values()).toList(),
                notificationChannelService.getChannelList());
    }

    @Test
    void getChannelConfigKeys_ShouldReturn_EmailConfigKeys(){
        //Act
        Map<String,Object> configKeys=notificationChannelService.getChannelConfigKeys(NotificationChannel.Channel.EMAIL);

        //Assert
        assertEquals(Map.of(
                "smtpHost", "",
                "smtpPort", "",
                "smtpUsername", "",
                "smtpPassword", "",
                "emailFrom", ""
        ), configKeys);

    }

    @Test
    void getChannelConfigKeys_ShouldReturn_SlackConfigKeys(){
        //Act
        Map<String,Object> configKeys=notificationChannelService.getChannelConfigKeys(NotificationChannel.Channel.SLACK);

        //Assert
        assertEquals(Map.of("slackWebhookUrl", ""), configKeys);
    }

    @Test
    void getChannelConfigKeys_ShouldReturn_TeamsConfigKeys(){
        //Act
        Map<String,Object> configKeys=notificationChannelService.getChannelConfigKeys(NotificationChannel.Channel.TEAMS);

        //Assert
        assertEquals(Map.of("teamsWebhookUrl", ""), configKeys);
    }


    @Test
    void getAllNotificationChannelsReturnsMappedDTOs() {
        NotificationChannel channel1 = new NotificationChannel();
        NotificationChannel channel2 = new NotificationChannel();
        NotificationChannelDTO dto1 = new NotificationChannelDTO();
        NotificationChannelDTO dto2 = new NotificationChannelDTO();

        when(notificationChannelRepo.findAll()).thenReturn(List.of(channel1, channel2));
        when(modelMapper.map(channel1, NotificationChannelDTO.class)).thenReturn(dto1);
        when(modelMapper.map(channel2, NotificationChannelDTO.class)).thenReturn(dto2);

        List<NotificationChannelDTO> result = notificationChannelService.getAllNotificationChannels();

        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(dto1, result.get(0));
        assertEquals(dto2, result.get(1));
        verify(notificationChannelRepo).findAll();
        verify(modelMapper).map(channel1, NotificationChannelDTO.class);
        verify(modelMapper).map(channel2, NotificationChannelDTO.class);
    }

    @Test
    void getAllNotificationChannelsReturnsEmptyListWhenNoChannelsExist() {
        when(notificationChannelRepo.findAll()).thenReturn(List.of());

        List<NotificationChannelDTO> result = notificationChannelService.getAllNotificationChannels();

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(notificationChannelRepo).findAll();
    }

}