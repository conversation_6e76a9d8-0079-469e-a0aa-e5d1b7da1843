package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import uk.co.flexi.ri.model.OMSProvider;
import uk.co.flexi.ri.model.SearchStrategy;
import uk.co.flexi.sdk.oms.mao.client.OMSClient;
import uk.co.flexi.sdk.oms.model.OrderData;

import java.util.List;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
class OrderLookUpServiceTest {

    @Mock
    private SearchStrategyRegistry strategyRegistry;

    @Mock
    private OMSClientService omsClientService;

    @InjectMocks
    private OrderLookUpService orderLookUpService;

    private static OMSProvider omsProvider;
    private OMSClient mockOmsClient;
    private final String  input = "TRACK-123";

    @BeforeEach
    void setup()
    {
        omsProvider = new OMSProvider();
        omsProvider.setProviderName("MOCK");
        omsProvider.setSearchStrategy(List.of(
                "RETURN_BY_TRACKING_NUMBER",
                "ORDER_BY_ID",
                "ORDER_BY_RETURN_TRACKING",
                "ORDER_BY_PRODUCT_SERIAL_NUMBER"
        ));

        mockOmsClient= mock(OMSClient.class);
        when(omsClientService.getClient(any(), any())).thenReturn(mockOmsClient);
    }

    @Test
    void orderLookUp_shouldReturnOrderData_whenFound(){

        List<String> strategy = List.of(
                "RETURN_BY_TRACKING_NUMBER",
                "ORDER_BY_ID",
                "ORDER_BY_RETURN_TRACKING",
                "ORDER_BY_PRODUCT_SERIAL_NUMBER"
        );
        OrderLookUpService.OMSSearchData searchData = new OrderLookUpService.OMSSearchData(input, strategy);

        Set<SearchStrategy> validStrategies = Set.of(
                SearchStrategy.RETURN_BY_TRACKING_NUMBER,
                SearchStrategy.ORDER_BY_ID,
                SearchStrategy.ORDER_BY_RETURN_TRACKING,
                SearchStrategy.ORDER_BY_PRODUCT_SERIAL_NUMBER
        );
        when(strategyRegistry.getStrategiesFor(any())).thenReturn(validStrategies);

        OrderData expectedOrderData = new OrderData();
        when(mockOmsClient.getReturnOrderByTrackingNumber(input)).thenReturn(expectedOrderData);

        OrderData result=orderLookUpService.orderLookUp(searchData ,omsProvider);

        assertNotNull(result);
        assertSame(expectedOrderData, result);

        verify(mockOmsClient).getReturnOrderByTrackingNumber(input);

    }

    @Test
    void orderLookUp_shouldReturnNull_whenOrderisNull(){

        List<String> strategy = List.of(
                "RETURN_BY_TRACKING_NUMBER",
                "ORDER_BY_ID",
                "ORDER_BY_RETURN_TRACKING",
                "ORDER_BY_PRODUCT_SERIAL_NUMBER"
        );
        OrderLookUpService.OMSSearchData searchData = new OrderLookUpService.OMSSearchData(input, strategy);


        Set<SearchStrategy> validStrategies = Set.of(
                SearchStrategy.RETURN_BY_TRACKING_NUMBER,
                SearchStrategy.ORDER_BY_ID,
                SearchStrategy.ORDER_BY_RETURN_TRACKING,
                SearchStrategy.ORDER_BY_PRODUCT_SERIAL_NUMBER
        );
        when(strategyRegistry.getStrategiesFor(any())).thenReturn(validStrategies);

        when(mockOmsClient.getReturnOrderByTrackingNumber(input)).thenReturn(null);

        OrderData result=orderLookUpService.orderLookUp(searchData ,omsProvider);

        assertNull(result);

        verify(mockOmsClient).getReturnOrderByTrackingNumber(input);

    }

    @Test
    void orderLookUp_shouldReturnNull_whenStrategyNotPresent(){

        List<String> strategy = List.of(
                "RETURN_BY_TRACKING_NUMBER"
        );
        OrderLookUpService.OMSSearchData searchData = new OrderLookUpService.OMSSearchData(input, strategy);

        Set<SearchStrategy> validStrategies = Set.of(
                SearchStrategy.ORDER_BY_ID,
                SearchStrategy.ORDER_BY_RETURN_TRACKING,
                SearchStrategy.ORDER_BY_PRODUCT_SERIAL_NUMBER
        );
        when(strategyRegistry.getStrategiesFor(any())).thenReturn(validStrategies);

        OrderData result=orderLookUpService.orderLookUp(searchData ,omsProvider);

        assertNull(result);

        verify(mockOmsClient, never()).getReturnOrderByTrackingNumber(input);
    }

    @Test
    void orderLookUp_shouldThrowIllegalArgumentException_whenWrongStrategyGiven(){

        List<String> strategy = List.of(
                "return_by_number"
        );
        OrderLookUpService.OMSSearchData searchData = new OrderLookUpService.OMSSearchData(input, strategy);

        Set<SearchStrategy> validStrategies = Set.of(
                SearchStrategy.RETURN_BY_TRACKING_NUMBER,
                SearchStrategy.ORDER_BY_ID,
                SearchStrategy.ORDER_BY_RETURN_TRACKING,
                SearchStrategy.ORDER_BY_PRODUCT_SERIAL_NUMBER
        );
        when(strategyRegistry.getStrategiesFor(any())).thenReturn(validStrategies);

        assertThatThrownBy(()->orderLookUpService.orderLookUp(searchData ,omsProvider))
                .isInstanceOf(UnsupportedOperationException.class)
                .hasMessageContaining("Unsupported search strategy: return_by_number" );

    }
}
