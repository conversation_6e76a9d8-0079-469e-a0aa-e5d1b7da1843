package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.SupportedLanguageDTO;
import uk.co.flexi.ri.model.SupportedLanguage;
import uk.co.flexi.ri.repository.SupportedLanguageRepo;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class LanguageServiceTest {
    private SupportedLanguageRepo supportedLanguageRepo;
    private ModelMapper modelMapper;
    private LanguageService languageService;

    @BeforeEach
    void setUp() {
        supportedLanguageRepo = mock(SupportedLanguageRepo.class);
        modelMapper = mock(ModelMapper.class);
        languageService = new LanguageService(supportedLanguageRepo, modelMapper);
    }

    @Test
    void findAllSupportedLanguages_returnsMappedList() {
        SupportedLanguage lang = new SupportedLanguage();
        SupportedLanguageDTO dto = new SupportedLanguageDTO();
        when(supportedLanguageRepo.findAll()).thenReturn(List.of(lang));
        when(modelMapper.map(lang, SupportedLanguageDTO.class)).thenReturn(dto);
        List<SupportedLanguageDTO> result = languageService.findAllSupportedLanguages();
        assertEquals(1, result.size());
        assertSame(dto, result.get(0));
    }

    @Test
    void findAllSupportedLanguages_emptyList() {
        when(supportedLanguageRepo.findAll()).thenReturn(List.of());
        List<SupportedLanguageDTO> result = languageService.findAllSupportedLanguages();
        assertTrue(result.isEmpty());
    }
} 