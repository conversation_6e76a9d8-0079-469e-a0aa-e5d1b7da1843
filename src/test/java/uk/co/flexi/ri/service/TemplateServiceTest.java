package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.TemplateDTO;
import uk.co.flexi.ri.model.Template;
import uk.co.flexi.ri.model.UserGroup;
import uk.co.flexi.ri.repository.TemplateRepo;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class TemplateServiceTest {
    private TemplateRepo templateRepo;
    private ModelMapper modelMapper;
    private AuthenticatedUserService authenticatedUserService;
    private UserGroupService userGroupService;
    private TemplateService service;

    @BeforeEach
    void setUp() {
        templateRepo = mock(TemplateRepo.class);
        modelMapper = mock(ModelMapper.class);
        authenticatedUserService = mock(AuthenticatedUserService.class);
        userGroupService = mock(UserGroupService.class);
        service = new TemplateService(templateRepo, modelMapper, authenticatedUserService, userGroupService);
    }

    @Test
    void getTemplates_returnsMappedList() {
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        UserGroup group = new UserGroup();
        userDTO.setUserGroup(group);
        Template template = new Template();
        TemplateDTO dto = new TemplateDTO();
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        when(templateRepo.findByUserGroup(group)).thenReturn(List.of(template));
        when(modelMapper.map(template, TemplateDTO.class)).thenReturn(dto);
        List<TemplateDTO> result = service.getTemplates();
        assertEquals(1, result.size());
        assertSame(dto, result.get(0));
    }

    @Test
    void getTemplates_emptyList() {
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        UserGroup group = new UserGroup();
        userDTO.setUserGroup(group);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        when(templateRepo.findByUserGroup(group)).thenReturn(List.of());
        List<TemplateDTO> result = service.getTemplates();
        assertTrue(result.isEmpty());
    }

    @Test
    void getTemplates_fallbackToAdminGroup() {
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        UserGroup userGroup = new UserGroup();
        UserGroup adminGroup = new UserGroup();
        userDTO.setUserGroup(userGroup);
        Template adminTemplate = new Template();
        TemplateDTO adminDto = new TemplateDTO();
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        when(templateRepo.findByUserGroup(userGroup)).thenReturn(List.of());
        when(userGroupService.findAdminGroup()).thenReturn(adminGroup);
        when(templateRepo.findByUserGroup(adminGroup)).thenReturn(List.of(adminTemplate));
        when(modelMapper.map(adminTemplate, TemplateDTO.class)).thenReturn(adminDto);
        List<TemplateDTO> result = service.getTemplates();
        assertEquals(1, result.size());
        assertSame(adminDto, result.get(0));
    }
} 