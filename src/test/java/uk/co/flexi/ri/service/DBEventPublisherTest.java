package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.ApplicationEventPublisher;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.CommentDTO;
import uk.co.flexi.ri.dto.event.EventData;
import uk.co.flexi.ri.model.*;
import static org.mockito.Mockito.*;
import java.util.Collections;
import java.util.List;

class DBEventPublisherTest {
    private ApplicationEventPublisher eventPublisher;
    private AuthenticatedUserService authenticatedUserService;
    private DBEventPublisher dbEventPublisher;
    private AuthenticatedUserDTO userDTO;
    private UserGroup userGroup;

    @BeforeEach
    void setUp() {
        eventPublisher = mock(ApplicationEventPublisher.class);
        authenticatedUserService = mock(AuthenticatedUserService.class);
        dbEventPublisher = new DBEventPublisher(eventPublisher, authenticatedUserService);
        userGroup = new UserGroup();
        userGroup.setName("group");
        userDTO = new AuthenticatedUserDTO();
        userDTO.setUserName("user");
        userDTO.setUserGroup(userGroup);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
    }

    @Test
    void testPublishInspection() {
        Inspection inspection = new Inspection();
        inspection.setInspectionId("insp1");
        inspection.setTenant(1L);
        inspection.setStatus(Inspection.InspectionStatus.IN_PROGRESS);
        inspection.setReferenceId("ref1");
        User assignee = new User();
        assignee.setUserName("assigneeUser");
        inspection.setAssignee(assignee);
        UserGroup group = new UserGroup();
        group.setName("groupName");
        inspection.setAssigneeGroup(group);
        dbEventPublisher.publish(inspection, EventQueue.EventType.INSP_CREATED);
        verify(eventPublisher).publishEvent(any(Inspection.class));
        verify(eventPublisher).publishEvent(any(EventData.class));
    }

    @Test
    void testPublishInspectionItem() {
        Inspection inspection = new Inspection();
        inspection.setInspectionId("insp3");
        InspectionItem item = new InspectionItem();
        item.setInspection(inspection);
        item.setTenant(3L);
        dbEventPublisher.publish(item, EventQueue.EventType.INSP_ITEM_APPROVED);
        verify(eventPublisher).publishEvent(any(EventData.class));
    }

    @Test
    void testPublishComment() {
        Inspection inspection = new Inspection();
        inspection.setInspectionId("insp4");
        InspectionItem item = new InspectionItem();
        item.setInspection(inspection);
        Comment comment = new Comment();
        comment.setInspectionItem(item);
        comment.setTenant(4L);
        CommentDTO commentDTO = new CommentDTO();
        dbEventPublisher.publish(comment, commentDTO, EventQueue.EventType.INSP_ITEM_COMMENT_ADDED);
        verify(eventPublisher).publishEvent(any(EventData.class));
    }

    @Test
    void testPublishInspectionItem_NullFields() {
        Inspection inspection = new Inspection();
        inspection.setInspectionId("insp6");
        InspectionItem item = new InspectionItem();
        item.setInspection(inspection);
        dbEventPublisher.publish(item, EventQueue.EventType.INSP_ITEM_APPROVED);
        verify(eventPublisher).publishEvent(any(EventData.class));
    }

    @Test
    void testPublishComment_NullFields() {
        Inspection inspection = new Inspection();
        inspection.setInspectionId("insp7");
        InspectionItem item = new InspectionItem();
        item.setInspection(inspection);
        Comment comment = new Comment();
        comment.setInspectionItem(item);
        CommentDTO commentDTO = new CommentDTO();
        dbEventPublisher.publish(comment, commentDTO, EventQueue.EventType.INSP_ITEM_COMMENT_ADDED);
        verify(eventPublisher).publishEvent(any(EventData.class));
    }

    @Test
    void testCreateInspectionPayload_WithOrderAndItems() {
        Inspection inspection = new Inspection();
        inspection.setInspectionId("inspX");
        inspection.setTenant(10L);
        inspection.setStatus(Inspection.InspectionStatus.COMPLETED);
        inspection.setReferenceId("refX");
        User assignee = new User();
        assignee.setUserName("assigneeUser");
        inspection.setAssignee(assignee);
        UserGroup group = new UserGroup();
        group.setName("groupName");
        inspection.setAssigneeGroup(group);
        // Set order
        Order order = new Order();
        order.setOrderId("orderId1");
        order.setReturnOrderId("returnOrderId1");
        order.setReturnTrackingId("trackingId1");
        inspection.setOrder(order);
        // Set items
        InspectionItem item = new InspectionItem();
        item.setId(123L);
        Product product = new Product();
        product.setSku("sku123");
        item.setProduct(product);
        item.setOrderLineId("line1");
        item.setReturnReason("reason");
        item.setExpectedItemCondition("expected");
        item.setItemCondition("actual");
        item.setItemStatus(InspectionItem.InspectionItemStatus.APPROVED);
        item.setCompletedBy("userX");
        item.setComments(Collections.emptyList());
        inspection.setInspectionItems(List.of(item));
        dbEventPublisher.publish(inspection, EventQueue.EventType.INSP_CREATED);
        verify(eventPublisher).publishEvent(any(Inspection.class));
        verify(eventPublisher).publishEvent(any(EventData.class));
    }

    @Test
    void testCreateCommentPayload_WithMedia() {
        Comment comment = new Comment();
        comment.setCommentId("c1");
        comment.setContent("test comment");
        User author = new User();
        author.setUserName("author1");
        comment.setAuthor(author);
        Inspection inspection = new Inspection();
        InspectionItem item = new InspectionItem();
        item.setInspection(inspection);
        comment.setInspectionItem(item);
        inspection.setInspectionId("inspZ");
        CommentDTO commentDTO = new CommentDTO();
        uk.co.flexi.ri.dto.MediaDTO mediaDTO = new uk.co.flexi.ri.dto.MediaDTO();
        mediaDTO.setMediaId("m1");
        mediaDTO.setMediaType("image");
        mediaDTO.setFileName("file.jpg");
        commentDTO.setMedia(List.of(mediaDTO));
        dbEventPublisher.publish(comment, commentDTO, EventQueue.EventType.INSP_ITEM_COMMENT_ADDED);
        verify(eventPublisher).publishEvent(any(EventData.class));
    }

    @Test
    void testMapComments_NonEmpty() {
        Comment comment = new Comment();
        comment.setId(1L);
        comment.setContent("c");
        User author = new User();
        author.setUserName("a");
        comment.setAuthor(author);
        Media media = new Media();
        media.setId(2L);
        media.setMediaType("image");
        media.setFileName("f.jpg");
        comment.setMedia(List.of(media));
        List<Comment> comments = List.of(comment);
        // Add comment to InspectionItem and publish
        Inspection inspection = new Inspection();
        inspection.setInspectionId("inspY");
        inspection.setTenant(11L);
        inspection.setStatus(Inspection.InspectionStatus.IN_PROGRESS);
        inspection.setReferenceId("refY");
        User assignee = new User();
        assignee.setUserName("assigneeUser");
        inspection.setAssignee(assignee);
        UserGroup group = new UserGroup();
        group.setName("groupName");
        inspection.setAssigneeGroup(group);
        InspectionItem item = new InspectionItem();
        item.setInspection(inspection);
        item.setComments(comments);
        dbEventPublisher.publish(item, EventQueue.EventType.INSP_ITEM_APPROVED);
        verify(eventPublisher).publishEvent(any(EventData.class));
    }
} 