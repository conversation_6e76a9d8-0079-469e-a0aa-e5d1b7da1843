package uk.co.flexi.ri.service;


import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Path;


import org.springframework.data.jpa.domain.Specification;
import uk.co.flexi.ri.model.view.InspectionView;
import uk.co.flexi.ri.dto.AssignedPackagesDTO;
import uk.co.flexi.ri.dto.AssignedPackagesFilterDTO;
import uk.co.flexi.ri.exception.custom.InspectionUpdateException;
import uk.co.flexi.ri.exception.custom.OrderNotFoundException;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.dto.InspectionItemDTO;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.model.*;
import uk.co.flexi.ri.repository.InspectionRepo;
import uk.co.flexi.ri.repository.InspectionItemRepo;
import uk.co.flexi.ri.repository.view.AssignedPackagesRepo;


import java.io.IOException;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;
import static uk.co.flexi.ri.model.Inspection.InspectionStatus.IN_PROGRESS;

@ExtendWith(MockitoExtension.class)
class InspectionServiceTest {

    // === mocks of all collaborators ===
    @Mock
    AuthenticatedUserService mockAuthUserSvc;
    @Mock
    InspectionRepo mockInspectionRepo;
    @Mock
    UserGroupService mockUserGroupSvc;
    @Mock
    ModelMapper mockMapper;
    @Mock
    InspectionItemRepo mockItemRepo;
    @Mock
    OrderService mockOrderSvc;
    @Mock
    CommentService mockCommentSvc;
    @Mock
    AssignedPackagesRepo mockAssignedRepo;
    @Mock
    ItemConditionService mockConditionSvc;
    @Mock
    EventPublisher mockPublisher;

    // === class under test ===
    InspectionService svc;

    @BeforeEach
    void setUp() {
        svc = new InspectionService(
                mockAuthUserSvc,
                mockInspectionRepo,
                mockUserGroupSvc,
                mockMapper,
                mockItemRepo,
                mockOrderSvc,
                mockCommentSvc,
                mockAssignedRepo,
                mockConditionSvc,
                mockPublisher
        );
    }
    @Test
    void save_whenAllDepsReturnValid_thenSavesAndPublishesAndReturnsDTO() {
        //arrange
        ProductDTO product = new ProductDTO();
        product.setSku("ABC123");
        InspectionItemDTO itemDto = new InspectionItemDTO();
        itemDto.setProduct(product);
        itemDto.setReceivedQuantity(BigDecimal.ONE);
        InspectionDTO inDto = new InspectionDTO();
        inDto.setReferenceId("REF-001");
        inDto.setInspectionItems(List.of(itemDto));
        ProductDetailDTO productDetail = new ProductDetailDTO();
        productDetail.setSku("ABC123");
        productDetail.setColour(" green ");
        productDetail.setExpectedQuantity(BigDecimal.ONE);


        OrderDetailDTO orderDetail = new OrderDetailDTO();
        orderDetail.setReferenceId("REF-001");
        orderDetail.setProductDetails(List.of(productDetail));
        orderDetail.setOrderPk(1L);
        when(mockOrderSvc.getOrderDetails("REF-001", null))
                .thenReturn(orderDetail);
        when(mockOrderSvc.findById(1L)).thenReturn(new Order());


        UserGroup userGroup = new UserGroup();
        userGroup.setName("GroupA");
        userGroup.setVisibility(Comment.CommentVisibility.INTERNAL);
        AuthenticatedUserDTO user = new AuthenticatedUserDTO(null, null, 99L, null, null, null, null, null, userGroup);
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(user);


        Inspection saved = new Inspection();
        saved.setInspectionId("INSP-007");
        when(mockInspectionRepo.save(any(Inspection.class))).thenReturn(saved);


        InspectionDTO outDto = new InspectionDTO();
        outDto.setInspectionId("INSP-007");
        when(mockMapper.map(saved, InspectionDTO.class)).thenReturn(outDto);

        //act
        InspectionDTO result = svc.save(inDto);

        //assert
        assertThat(result.getInspectionId()).isEqualTo("INSP-007");

        //verify
        verify(mockInspectionRepo).save(any(Inspection.class));
        verify(mockPublisher).publish(saved, EventQueue.EventType.INSP_CREATED);
    }

    @Test
    void save_withSameSkus_mergeKeepsFirstQuantity() {
        // arrange
        ProductDTO product = new ProductDTO();
        product.setSku("DUPLICATE-SKU");

        InspectionItemDTO firstItem = new InspectionItemDTO();
        firstItem.setProduct(product);
        firstItem.setReceivedQuantity(BigDecimal.ONE);

        InspectionItemDTO secondItem = new InspectionItemDTO();
        secondItem.setProduct(product);
        secondItem.setReceivedQuantity(BigDecimal.TWO);

        InspectionDTO inDto = new InspectionDTO();
        inDto.setReferenceId("REF-DUP");
        inDto.setInspectionItems(List.of(firstItem, secondItem));


        ProductDetailDTO detail = new ProductDetailDTO();
        detail.setSku("DUPLICATE-SKU");
        detail.setExpectedQuantity(BigDecimal.ONE);
        OrderDetailDTO orderDetail = new OrderDetailDTO();
        orderDetail.setReferenceId("REF-DUP");
        orderDetail.setProductDetails(List.of(detail));
        when(mockOrderSvc.getOrderDetails("REF-DUP", null))
                .thenReturn(orderDetail);


        UserGroup userGroup = new UserGroup();
        userGroup.setName("GroupA");
        userGroup.setVisibility(Comment.CommentVisibility.INTERNAL);
        AuthenticatedUserDTO user = new AuthenticatedUserDTO(42L, null, 99L, null, null, null, null, null, userGroup);
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(user);


        ArgumentCaptor<Inspection> captor = ArgumentCaptor.forClass(Inspection.class);
        when(mockInspectionRepo.save(captor.capture()))
                .thenAnswer(invocation -> invocation.getArgument(0));


        when(mockMapper.map(any(Inspection.class), eq(InspectionDTO.class)))
                .thenReturn(new InspectionDTO());

        //act
        svc.save(inDto);

        //assert
        Inspection passed = captor.getValue();
        assertThat(passed.getInspectionItems()).hasSize(1);
    }

    @Test
    void findByID_forPositiveCase() {
        // Arrange
        UserGroup mockAssigneeGroup = new UserGroup();
        mockAssigneeGroup.setName("Group-A");
        mockAssigneeGroup.setUserGroupId("G-001");
        Inspection mockInspection = new Inspection();
        mockInspection.setInspectionId("INSP-001");
        mockInspection.setAssigneeGroup(mockAssigneeGroup);
        mockInspection.setStatus(IN_PROGRESS);
        when(mockInspectionRepo.findByInspectionId("INSP-001")).thenReturn(Optional.of(mockInspection));

        UserGroup mockUserGroup = new UserGroup();
        mockUserGroup.setName("Group-A");
        mockUserGroup.setUserGroupId("G-001");
        mockUserGroup.setVisibility(Comment.CommentVisibility.INTERNAL);
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(null, null, null, null, null, null, null, null, mockUserGroup);
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);


        ProductDTO prodDto = new ProductDTO();
        prodDto.setReturnReason("Damaged");
        prodDto.setProperties(Map.of("color", "red"));

        CommentDTO commentDto = new CommentDTO();
        commentDto.setVisibility(Comment.CommentVisibility.INTERNAL);

        InspectionItemDTO itemDto = new InspectionItemDTO();
        itemDto.setProduct(prodDto);
        itemDto.setComments(List.of(commentDto));

        InspectionDTO dto = new InspectionDTO();
        dto.setInspectionId("INSP-001");
        dto.setInspectionItems(List.of(itemDto));
        when(mockMapper.map(mockInspection, InspectionDTO.class))
                .thenReturn(dto);

        // Act
        InspectionDTO result = svc.findById("INSP-001");

        // Assert
        assertThat(result.getInspectionId()).isEqualTo("INSP-001");
        assertThat(result.getStatus()).isEqualTo(IN_PROGRESS);
        assertThat(result.getInspectionItems()).hasSize(1);
        InspectionItemDTO item = result.getInspectionItems().get(0);
        assertThat(item.getEnableReturnReasonEdit()).isFalse();
        assertThat(item.getProduct().getProperties()).containsEntry("color", "red");
        assertThat(item.getComments()).hasSize(1);
        assertThat(item.getComments().get(0).getVisibility())
                .isEqualTo(Comment.CommentVisibility.INTERNAL);

        // verify
        verify(mockAuthUserSvc).getAuthenticatedUserDetails();
        verify(mockMapper).map(mockInspection, InspectionDTO.class);

    }

    @Test
    void findByID_forInspectionItemWithNoProduct() {
        // Arrange
        UserGroup mockAssigneeGroup = new UserGroup();
        mockAssigneeGroup.setName("Group-A");
        mockAssigneeGroup.setUserGroupId("G-001");
        Inspection mockInspection = new Inspection();
        mockInspection.setInspectionId("INSP-001");
        mockInspection.setAssigneeGroup(mockAssigneeGroup);
        mockInspection.setStatus(IN_PROGRESS);
        when(mockInspectionRepo.findByInspectionId("INSP-001")).thenReturn(Optional.of(mockInspection));

        UserGroup mockUserGroup = new UserGroup();
        mockUserGroup.setName("Group-A");
        mockUserGroup.setUserGroupId("G-001");
        mockUserGroup.setVisibility(Comment.CommentVisibility.INTERNAL);
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(null, null, null, null, null, null, null, null, mockUserGroup);
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);


        InspectionItemDTO itemDto = new InspectionItemDTO();
        itemDto.setInspectionItemId("ITEM-001");

        InspectionDTO dto = new InspectionDTO();
        dto.setInspectionId("INSP-001");
        dto.setInspectionItems(List.of(itemDto));
        when(mockMapper.map(mockInspection, InspectionDTO.class))
                .thenReturn(dto);

        // Act
        InspectionDTO result = svc.findById("INSP-001");

        // Assert
        assertThat(result.getInspectionId()).isEqualTo("INSP-001");
        assertThat(result.getStatus()).isEqualTo(IN_PROGRESS);
        assertThat(result.getInspectionItems()).hasSize(1);
        InspectionItemDTO item = result.getInspectionItems().get(0);
        assertThat(item.getInspectionItemId()).isEqualTo("ITEM-001");
        assertThat(item.getProduct()).isNull();

        // verify interactions
        verify(mockAuthUserSvc).getAuthenticatedUserDetails();
        verify(mockMapper).map(mockInspection, InspectionDTO.class);

    }

    @Test
    void findByID_forInspectionItemProduct_noReturnReason_noProperty_noComments() {
        // Arrange
        UserGroup mockAssigneeGroup = new UserGroup();
        mockAssigneeGroup.setName("Group-A");
        mockAssigneeGroup.setUserGroupId("G-002");
        Inspection mockInspection = new Inspection();
        mockInspection.setInspectionId("INSP-001");
        mockInspection.setAssigneeGroup(mockAssigneeGroup);
        mockInspection.setStatus(IN_PROGRESS);
        when(mockInspectionRepo.findByInspectionId("INSP-001")).thenReturn(Optional.of(mockInspection));

        UserGroup mockUserGroup = new UserGroup();
        mockUserGroup.setName("Group-A");
        mockUserGroup.setUserGroupId("G-001");
        mockUserGroup.setVisibility(Comment.CommentVisibility.EXTERNAL);
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(null, null, null, null, null, null, null, null, mockUserGroup);
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);

        ProductDTO prodDto = new ProductDTO();

        CommentDTO commentDto = new CommentDTO();
        commentDto.setVisibility(Comment.CommentVisibility.INTERNAL);


        InspectionItemDTO itemDto = new InspectionItemDTO();
        itemDto.setInspectionItemId("ITEM-001");
        itemDto.setProduct(prodDto);
        itemDto.setComments(List.of(commentDto));


        InspectionDTO dto = new InspectionDTO();
        dto.setInspectionId("INSP-001");
        dto.setInspectionItems(List.of(itemDto));
        when(mockMapper.map(mockInspection, InspectionDTO.class))
                .thenReturn(dto);

        // Act
        InspectionDTO result = svc.findById("INSP-001");

        // Assert
        assertThat(result.getInspectionId()).isEqualTo("INSP-001");
        assertThat(result.getStatus()).isEqualTo(IN_PROGRESS);
        assertThat(result.getUserStatus()).isEqualTo("IN_REVIEW");
        assertThat(result.getInspectionItems()).hasSize(1);
        InspectionItemDTO item = result.getInspectionItems().get(0);
        assertThat(item.getEnableReturnReasonEdit()).isTrue();
        assertThat(item.getComments()).hasSize(0);
        assertThat(item.getProduct().getProperties()).isEmpty();

        // verify interactions
        verify(mockAuthUserSvc).getAuthenticatedUserDetails();
        verify(mockMapper).map(mockInspection, InspectionDTO.class);

    }

    @Test
    void findById_inspectionStatus_forDifferentUsergroups(){
        // Arrange
        Inspection mockInspection = new Inspection();
        mockInspection.setInspectionId("INSP-001");
        mockInspection.setStatus(IN_PROGRESS);
        when(mockInspectionRepo.findByInspectionId("INSP-001")).thenReturn(Optional.of(mockInspection));

        UserGroup mockUserGroup = new UserGroup();
        mockUserGroup.setVisibility(Comment.CommentVisibility.EXTERNAL);
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(null, null, null, null, null, null, null, null, mockUserGroup);
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);


        InspectionItemDTO itemDto = new InspectionItemDTO();
        InspectionDTO dto = new InspectionDTO();
        dto.setInspectionId("INSP-001");
        dto.setInspectionItems(List.of(itemDto));
        when(mockMapper.map(mockInspection, InspectionDTO.class))
                .thenReturn(dto);

        // Act
        InspectionDTO result = svc.findById("INSP-001");

        // Assert
        assertThat(result.getInspectionId()).isEqualTo("INSP-001");
        assertThat(result.getStatus()).isEqualTo(IN_PROGRESS);

        // verify interactions
        verify(mockAuthUserSvc).getAuthenticatedUserDetails();
        verify(mockMapper).map(mockInspection, InspectionDTO.class);

    }

    @Test
    void findByReferenceIdId_forPositiveCase() {
        // Arrange
        UserGroup mockUserGroup = new UserGroup();
        mockUserGroup.setUserGroupId("Group-A");
        mockUserGroup.setVisibility(Comment.CommentVisibility.INTERNAL);
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(
            null, null, null, null, null, null, null, null, mockUserGroup
        );
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);


        Inspection insp1 = new Inspection();
        insp1.setInspectionId("INSP-100");
        insp1.setStatus(IN_PROGRESS);
        UserGroup assigneeGroup = new UserGroup();
        assigneeGroup.setName("Group-A");
        Inspection insp2 = new Inspection();
        insp2.setInspectionId("INSP-101");
        insp2.setStatus(IN_PROGRESS);
        insp2.setAssigneeGroup(assigneeGroup);


        Order order = new Order();
        order.setInspections(List.of(insp1, insp2));
        when(mockOrderSvc.findByReferenceId("REF-001")).thenReturn(Optional.of(order));


        InspectionDTO dto1 = new InspectionDTO();
        dto1.setInspectionId("INSP-100");
        dto1.setStatus(IN_PROGRESS);
        InspectionDTO dto2 = new InspectionDTO();
        dto2.setInspectionId("INSP-101");
        dto2.setStatus(Inspection.InspectionStatus.COMPLETED);
        when(mockMapper.map(insp1, InspectionDTO.class)).thenReturn(dto1);
        when(mockMapper.map(insp2, InspectionDTO.class)).thenReturn(dto2);

        // Act
        List<InspectionDTO> result = svc.findByReferenceIdId("REF-001");

        // Assert
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getInspectionId()).isEqualTo("INSP-100");
        assertThat(result.get(0).getStatus()).isEqualTo(IN_PROGRESS);
        assertThat(result.get(1).getInspectionId()).isEqualTo("INSP-101");
        assertThat(result.get(1).getStatus()).isEqualTo(Inspection.InspectionStatus.COMPLETED);
        assertThat(result.get(1).getAssigneeGroup()).isEqualTo("Group-A");

        // verify interactions
        verify(mockAuthUserSvc).getAuthenticatedUserDetails();
        verify(mockOrderSvc).findByReferenceId("REF-001");
        verify(mockMapper).map(insp1, InspectionDTO.class);
        verify(mockMapper).map(insp2, InspectionDTO.class);

    }

    @Test
    void updataStatus_forInspectionStatus_Completed(){
        // Arrange
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(
                null, null, null, null, "testUser", null, null, null, null
        );
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);

        Inspection insp = new Inspection();
        insp.setId(1L);
        insp.setInspectionId("INSP-100");
        when(mockInspectionRepo.findById(1l)).thenReturn(Optional.of(insp));
        when(mockInspectionRepo.save(any(Inspection.class)))
            .thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        svc.updateStatus(1L, Inspection.InspectionStatus.COMPLETED);

        // Assert
        ArgumentCaptor<Inspection> captor = ArgumentCaptor.forClass(Inspection.class);
        verify(mockInspectionRepo).save(captor.capture());
        Inspection savedInsp = captor.getValue();

        assertThat(savedInsp.getStatus()).isEqualTo(Inspection.InspectionStatus.COMPLETED);
        assertThat(savedInsp.getCompletedBy()).isEqualTo("testUser");
        assertThat(savedInsp.getCompletedDate()).isNotNull();

        verify(mockMapper).map(savedInsp, InspectionDTO.class);
    }

    @Test
    void updataStatus_forInspectionStatus_IN_PROGRESS(){
        // Arrange
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(
                null, null, null, null, "testUser", null, null, null, null
        );
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);

        Inspection insp = new Inspection();
        insp.setId(1L);
        insp.setInspectionId("INSP-100");
        when(mockInspectionRepo.findById(1l)).thenReturn(Optional.of(insp));
        when(mockInspectionRepo.save(any(Inspection.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        // Act
        svc.updateStatus(1L, IN_PROGRESS);

        // Assert
        ArgumentCaptor<Inspection> captor = ArgumentCaptor.forClass(Inspection.class);
        verify(mockInspectionRepo).save(captor.capture());
        Inspection savedInsp = captor.getValue();

        assertThat(savedInsp.getStatus()).isEqualTo(IN_PROGRESS);

        verify(mockMapper).map(savedInsp, InspectionDTO.class);
    }

    @Test
    void updateStatus_forPositiveCase() throws java.io.IOException {
        // Arrange
        InspectionService spySvc = spy(svc);


        UserGroup initialGroup = new UserGroup();
        initialGroup.setName("OldGroup");
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(
            null, "testUser", null, null, null, null, null, null, initialGroup
        );
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);


        Inspection insp = new Inspection();
        insp.setInspectionId("INSP-200");
        insp.setStatus(IN_PROGRESS);
        InspectionItem item = new InspectionItem();
        item.setInspectionItemId("ITEM-200");
        insp.setInspectionItems(List.of(item));
        when(mockInspectionRepo.findByInspectionId("INSP-200"))
            .thenReturn(Optional.of(insp));


        UserGroup newGroup = new UserGroup();
        newGroup.setUserGroupId("Group-001");
        newGroup.setName("Group");
        when(mockUserGroupSvc.findByUserGroupId("Group-001")).thenReturn(newGroup);


        when(mockInspectionRepo.save(any(Inspection.class)))
            .thenAnswer(invocation -> invocation.getArgument(0));


        InspectionStatusUpdateReqDTO req = new InspectionStatusUpdateReqDTO();
        req.setUserGroupId("Group-001");
        req.setComment("Review comment");


        when(mockCommentSvc.save(any(CommentMediaDTO.class))).thenReturn(null);


        InspectionDTO returnedDto = new InspectionDTO();
        returnedDto.setInspectionId("INSP-200");
        doReturn(returnedDto).when(spySvc).findById("INSP-200");

        // Act
        InspectionDTO result = spySvc.updateStatus("INSP-200", req);

        // Assert
        ArgumentCaptor<Inspection> captor = ArgumentCaptor.forClass(Inspection.class);
        verify(mockInspectionRepo).save(captor.capture());
        Inspection saved = captor.getValue();
        assertThat(saved.getAssigneeGroup().getName()).isEqualTo("Group");
        assertThat(saved.getAssigneeGroupName()).isEqualTo("Group");
        assertThat(saved.getLastAssignedGroup()).isEqualTo(initialGroup);
        assertThat(saved.getAssignedDate()).isNotNull();

        verify(mockCommentSvc).save(any(CommentMediaDTO.class));

        verify(mockPublisher).publish(saved, EventQueue.EventType.INSP_SEND_FOR_REVIEW);

        assertThat(result).isSameAs(returnedDto);
    }

    @Test
    void updateStatus_whenAlreadyCompleted_throwsIllegalStateException() {
        // Arrange
        UserGroup mockUserGroup = new UserGroup();
        mockUserGroup.setUserGroupId("Group-001");
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(
                null, "testUser", null, null, null, null, null, null, mockUserGroup
        );
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);

        Inspection completedInsp = new Inspection();
        completedInsp.setInspectionId("INSP-300");
        completedInsp.setStatus(Inspection.InspectionStatus.COMPLETED);
        when(mockInspectionRepo.findByInspectionId("INSP-300"))
                .thenReturn(Optional.of(completedInsp));

        InspectionStatusUpdateReqDTO req = new InspectionStatusUpdateReqDTO();
        req.setUserGroupId("Group-001");
        req.setComment("Irrelevant");

        // Act & Assert
        assertThrows(IllegalStateException.class, () ->
                svc.updateStatus("INSP-300", req)
        );
    }

    @Test
    void updateStatus_whenCommentServiceThrowsIOException_thenRuntimeException() {
        // Arrange
        UserGroup userGroup = new UserGroup();
        userGroup.setUserGroupId("Group-400");
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(
                null, "ioUser", null, null, null, null, null, null, userGroup
        );
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);

        Inspection insp = new Inspection();
        insp.setInspectionId("INSP-400");
        insp.setStatus(IN_PROGRESS);
        InspectionItem item = new InspectionItem();
        item.setInspectionItemId("ITEM-400");
        insp.setInspectionItems(List.of(item));
        when(mockInspectionRepo.findByInspectionId("INSP-400")).thenReturn(Optional.of(insp));

        UserGroup targetGroup = new UserGroup();
        targetGroup.setUserGroupId("Group-400");
        targetGroup.setName("Group-400");
        when(mockUserGroupSvc.findByUserGroupId("Group-400")).thenReturn(targetGroup);

        when(mockInspectionRepo.save(any(Inspection.class)))
                .thenAnswer(invocation -> invocation.getArgument(0));

        InspectionStatusUpdateReqDTO req = new InspectionStatusUpdateReqDTO();
        req.setUserGroupId("Group-400");
        req.setComment("Test IOException");

        try {
            when(mockCommentSvc.save(any(CommentMediaDTO.class)))
                    .thenThrow(new IOException("disk error"));
        } catch (IOException e) {

        }

        // Act & Assert
        RuntimeException thrown = assertThrows(RuntimeException.class, () ->
                svc.updateStatus("INSP-400", req)
        );
        assertThat(thrown.getCause()).isInstanceOf(IOException.class);
    }

    @Test
    void deleteInspection_forExistingReference_deletesAllEntities() {
        // Arrange
        String referenceId = "REF-DEL";

        Product product = new Product();
        product.setId(10L);
        InspectionItem item = new InspectionItem();
        item.setId(20L);
        item.setProduct(product);

        Media media = new Media();
        media.setId(50L);
        Comment comment = new Comment();
        comment.setId(40L);
        comment.setMedia(List.of(media));
        item.setComments(List.of(comment));


        Inspection inspection = new Inspection();
        inspection.setId(30L);
        inspection.setInspectionId("INSP-DEL");
        inspection.setReferenceId(referenceId);
        inspection.setInspectionItems(List.of(item));
        Order order = new Order();
        order.setId(100L);
        inspection.setOrder(order);

        when(mockInspectionRepo.findByReferenceId(referenceId))
                .thenReturn(List.of(inspection));

        // Act
        svc.deleteInspection(referenceId);

        // Assert
        verify(mockInspectionRepo).findByReferenceId(referenceId);

        verify(mockInspectionRepo).deleteMedia(media.getId());
        verify(mockInspectionRepo).deleteMediaAud(media.getId());
        verify(mockInspectionRepo).deleteComment(comment.getId());
        verify(mockInspectionRepo).deleteCommentAud(comment.getId());

        verify(mockInspectionRepo).deleteInspectionItem(item.getId());
        verify(mockInspectionRepo).deleteInspectionItemAud(item.getId());

        verify(mockInspectionRepo).deleteInspection(inspection.getId());
        verify(mockInspectionRepo).deleteInspectionAud(inspection.getId());

        verify(mockInspectionRepo).deleteProducts(Set.of(product.getId()));

        verify(mockInspectionRepo).deleteEventRes(inspection.getInspectionId());
        verify(mockInspectionRepo).deleteEventReq(inspection.getInspectionId());
        verify(mockInspectionRepo).deleteEventQueue(inspection.getInspectionId());

        verify(mockInspectionRepo).deleteOrder(order.getId());
    }

    @Test
    void deleteInspection_whenNoInspectionsFound_throwsOrderNotFoundException() {
        // Arrange
        String referenceId = "NONEXISTENT-REF";
        when(mockInspectionRepo.findByReferenceId(referenceId))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        assertThrows(OrderNotFoundException.class, () ->
                svc.deleteInspection(referenceId)
        );

        // verify
        verify(mockInspectionRepo, never()).deleteInspection(anyLong());
        verify(mockInspectionRepo, never()).deleteOrder(anyLong());
    }

    @Test
    void deleteAllInspection_forMatchingMerchant_delegatesToDeleteInspection() {
        // Arrange
        String merchantName = "MERCHANT_X";
        UserGroup userGroup = new UserGroup();
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(
                null, null, null, 2L, null, merchantName, null, null, userGroup
        );
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);


        Inspection insp1 = new Inspection();
        insp1.setReferenceId("REF1");
        Inspection insp2 = new Inspection();
        insp2.setReferenceId("REF2");
        when(mockInspectionRepo.findAll()).thenReturn(List.of(insp1, insp2));

        InspectionService spySvc = spy(svc);
        doNothing().when(spySvc).deleteInspection(anyString());

        // Act
        spySvc.deleteAllInspection(merchantName);

        // Assert
        verify(mockInspectionRepo).findAll();
        verify(spySvc).deleteInspection("REF1");
        verify(spySvc).deleteInspection("REF2");
    }

    @Test
    void deleteAllInspection_whenMerchantMismatch_throwsIllegalArgumentException() {
        // Arrange
        String actualMerchant = "MERCHANT_A";
        String requestedMerchant = "MERCHANT_B";

        UserGroup userGroup = new UserGroup();
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(
                null, null, null, null, null, actualMerchant , null, null, userGroup
        );
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () ->
                svc.deleteAllInspection(requestedMerchant)
        );

        // verify
        verify(mockInspectionRepo, never()).findAll();
    }


    @Test
    void updateInspectionQty_whenIncreasingQuantity_addsNewInspectionItems() {
        // Arrange
        String inspectionId = "INS-100";

        Product product = new Product();
        product.setSku("SKU-1");
        InspectionItem existing1 = new InspectionItem();
        existing1.setProduct(product);
        InspectionItem existing2 = new InspectionItem();
        existing2.setProduct(product);
        Inspection inspection = new Inspection();
        inspection.setInspectionId(inspectionId);
        inspection.setInspectionItems(List.of(existing1, existing2));
        when(mockInspectionRepo.findByInspectionId(inspectionId))
                .thenReturn(Optional.of(inspection));

        InspectionQtyUpdateReqDTO req = new InspectionQtyUpdateReqDTO();
        req.setReferenceId(inspectionId);
        req.setSku("SKU-1");
        req.setReceivedQuantity(5);
        List<InspectionQtyUpdateReqDTO> reqList = List.of(req);

        ArgumentCaptor<List<InspectionItem>> captor = ArgumentCaptor.forClass(List.class);

        // Act
        InspectionDTO result = svc.updateInspectionQty(inspectionId, reqList);

        // Assert
        verify(mockItemRepo).saveAll(captor.capture());
        List<InspectionItem> newItems = captor.getValue();

        assertThat(newItems).hasSize(3);

        assertThat(newItems)
                .allSatisfy(item -> assertThat(item.getProduct().getSku()).isEqualTo("SKU-1"));

        assertThat(result.getInspectionId()).isEqualTo(inspectionId);
    }

    @Test
    void updateInspectionQty_whenNewSku_addsNewInspectionItems() {
        // Arrange
        String inspectionId = "INS-NEW";

        Inspection inspection = new Inspection();
        inspection.setInspectionId(inspectionId);
        inspection.setReferenceId("REF-NEW");
        inspection.setInspectionItems(Collections.emptyList());
        when(mockInspectionRepo.findByInspectionId(inspectionId))
                .thenReturn(Optional.of(inspection));


        ProductDetailDTO detail = new ProductDetailDTO();
        detail.setSku("NEW-SKU");
        detail.setExpectedQuantity(BigDecimal.ONE);
        detail.setReturnReason("Reason");
        detail.setCountry("UK");
        detail.setRegion("London");
        detail.setCity("London");
        detail.setExpectedItemCondition("Good");
        detail.setOrderLineId("123");
        OrderDetailDTO orderDetail = new OrderDetailDTO();
        orderDetail.setReferenceId("REF-NEW");
        orderDetail.setProductDetails(List.of(detail));
        when(mockOrderSvc.getOrderDetails("REF-NEW", inspectionId))
                .thenReturn(orderDetail);


        InspectionQtyUpdateReqDTO req = new InspectionQtyUpdateReqDTO();
        req.setReferenceId(inspectionId);
        req.setSku("NEW-SKU");
        req.setReceivedQuantity(4);
        List<InspectionQtyUpdateReqDTO> reqList = List.of(req);

        ArgumentCaptor<List<InspectionItem>> captor = ArgumentCaptor.forClass(List.class);

        // Act
        svc.updateInspectionQty(inspectionId, reqList);

        // Assert
        verify(mockItemRepo).saveAll(captor.capture());
        List<InspectionItem> newItems = captor.getValue();

        assertThat(newItems).hasSize(4);
        assertThat(newItems)
                .allSatisfy(item -> assertThat(item.getProduct().getSku()).isEqualTo("NEW-SKU"));
    }

    @Test
    void updateInspectionQty_whenDecreasingQuantity_removesInspectionItems() {
        // Arrange
        String inspectionId = "INS-REMOVE";
        Product product = new Product();
        product.setSku("SKU-REMOVE");
        Comment comment = new Comment();
        comment.setCommentId("CMM-1");

        InspectionItem item1 = new InspectionItem();
        item1.setId(10L);
        item1.setProduct(product);
        InspectionItem item2 = new InspectionItem();
        item2.setId(20L);
        item2.setProduct(product);
        item1.setComments(List.of(comment));
        item2.setComments(Collections.emptyList());
        Inspection inspection = new Inspection();
        inspection.setInspectionId(inspectionId);
        inspection.setInspectionItems(new ArrayList<>(List.of(item1, item2)));
        when(mockInspectionRepo.findByInspectionId(inspectionId))
                .thenReturn(Optional.of(inspection));


        InspectionQtyUpdateReqDTO req = new InspectionQtyUpdateReqDTO();
        req.setReferenceId(inspectionId);
        req.setSku("SKU-REMOVE");
        req.setReceivedQuantity(1);
        List<InspectionQtyUpdateReqDTO> reqList = List.of(req);

        // Act
        InspectionDTO result = svc.updateInspectionQty(inspectionId, reqList);

        // Assert
        verify(mockItemRepo).deleteById(10L);
        verify(mockItemRepo).flush();

        assertThat(inspection.getInspectionItems()).hasSize(1);
        assertThat(result.getInspectionId()).isEqualTo(inspectionId);
    }

    @Test
    void updateInspectionQty_whenQuantityUnchanged_doesNothing() {
        // Arrange
        String inspectionId = "INS-NO-CHANGE";
        Product product = new Product();
        product.setSku("SKU-XYZ");

        InspectionItem item1 = new InspectionItem();
        item1.setId(1L);
        item1.setProduct(product);
        InspectionItem item2 = new InspectionItem();
        item2.setId(2L);
        item2.setProduct(product);
        InspectionItem item3 = new InspectionItem();
        item3.setId(3L);
        item3.setProduct(product);
        Inspection inspection = new Inspection();
        inspection.setInspectionId(inspectionId);
        inspection.setInspectionItems(new ArrayList<>(List.of(item1, item2, item3)));
        when(mockInspectionRepo.findByInspectionId(inspectionId))
                .thenReturn(Optional.of(inspection));


        InspectionQtyUpdateReqDTO req = new InspectionQtyUpdateReqDTO();
        req.setReferenceId(inspectionId);
        req.setSku("SKU-XYZ");
        req.setReceivedQuantity(3);
        List<InspectionQtyUpdateReqDTO> reqList = List.of(req);

        // Act
        InspectionDTO result = svc.updateInspectionQty(inspectionId, reqList);

        // Assert
        verify(mockItemRepo, never()).saveAll(any());
        verify(mockItemRepo, never()).deleteById(anyLong());

        assertThat(result.getInspectionId()).isEqualTo(inspectionId);
    }

    @Test
    void updateInspectionQty_whenRemovingAllItems_deletesAllAndDeletesProducts() {
        // Arrange
        String inspectionId = "INS-DELALL";
        Product product = new Product();
        product.setId(77L);
        product.setSku("SKU-ALL");
        Comment comment = new Comment();
        comment.setCommentId("CMM-1");


        InspectionItem item1 = new InspectionItem();
        item1.setId(100L);
        item1.setProduct(product);
        item1.setComments(List.of(comment));
        InspectionItem item2 = new InspectionItem();
        item2.setId(101L);
        item2.setProduct(product);
        item2.setComments(List.of(comment));

        Inspection inspection = new Inspection();
        inspection.setInspectionId(inspectionId);
        inspection.setInspectionItems(new ArrayList<>(List.of(item1, item2)));
        when(mockInspectionRepo.findByInspectionId(inspectionId))
                .thenReturn(Optional.of(inspection));

        InspectionQtyUpdateReqDTO req = new InspectionQtyUpdateReqDTO();
        req.setReferenceId(inspectionId);
        req.setSku("SKU-ALL");
        req.setReceivedQuantity(0);
        List<InspectionQtyUpdateReqDTO> reqList = List.of(req);

        // Act
        svc.updateInspectionQty(inspectionId, reqList);

        // Assert
        verify(mockCommentSvc, times(2)).deleteComment("CMM-1");
        verify(mockItemRepo).deleteById(100L);
        verify(mockItemRepo).deleteById(101L);
        verify(mockInspectionRepo).deleteProducts(Set.of(77L));
        assertThat(inspection.getInspectionItems()).isEmpty();
    }


    @Test
    void updateInspectionQtyCheck_whenRemovingWithNoComments_returnsSuccessMap() {
        // Arrange
        String inspectionId = "INS-CHECK";
        Product product = new Product();
        product.setSku("SKU-CHECK");
        InspectionItem item = new InspectionItem();
        item.setProduct(product);
        item.setComments(Collections.emptyList());
        Inspection inspection = new Inspection();
        inspection.setInspectionId(inspectionId);
        inspection.setInspectionItems(List.of(item));
        when(mockInspectionRepo.findByInspectionId(inspectionId))
                .thenReturn(Optional.of(inspection));

        InspectionQtyUpdateReqDTO req = new InspectionQtyUpdateReqDTO();
        req.setReferenceId(inspectionId);
        req.setSku("SKU-CHECK");
        req.setReceivedQuantity(0);
        List<InspectionQtyUpdateReqDTO> reqList = List.of(req);

        // Act
        Map<String, Object> result = svc.updateInspectionQtyCheck(inspectionId, reqList);

        // Assert
        assertThat(result).containsEntry("status", "success")
                .containsEntry("message", "Inspection update quantity check success");
    }

    @Test
    void updateInspectionQtyCheck_whenRemovingWithComments_throwsInspectionUpdateException() {
        // Arrange
        String inspectionId = "INS-CHECK";
        Product product = new Product();
        product.setSku("SKU-CHECK");
        Comment comment = new Comment();
        comment.setCommentId("CMM-1");
        InspectionItem item = new InspectionItem();
        item.setProduct(product);
        item.setComments(List.of(comment));
        Inspection inspection = new Inspection();
        inspection.setInspectionId(inspectionId);
        inspection.setInspectionItems(List.of(item));
        when(mockInspectionRepo.findByInspectionId(inspectionId))
                .thenReturn(Optional.of(inspection));

        InspectionQtyUpdateReqDTO req = new InspectionQtyUpdateReqDTO();
        req.setReferenceId(inspectionId);
        req.setSku("SKU-CHECK");
        req.setReceivedQuantity(0);
        List<InspectionQtyUpdateReqDTO> reqList = List.of(req);

        // Act & Assert
        assertThrows(InspectionUpdateException.class, () ->
                svc.updateInspectionQtyCheck(inspectionId, reqList)
        );
    }

    @Test
    void updateInspectionQtyCheck_whenQuantityMatchesExisting_returnsSuccessMap() {
        // Arrange
        String inspectionId = "INS-MATCH";
        Product product = new Product();
        product.setSku("SKU-MATCH");

        InspectionItem itemA = new InspectionItem();
        itemA.setProduct(product);
        InspectionItem itemB = new InspectionItem();
        itemB.setProduct(product);
        Inspection inspection = new Inspection();
        inspection.setInspectionId(inspectionId);
        inspection.setInspectionItems(List.of(itemA, itemB));
        when(mockInspectionRepo.findByInspectionId(inspectionId))
                .thenReturn(Optional.of(inspection));


        InspectionQtyUpdateReqDTO req = new InspectionQtyUpdateReqDTO();
        req.setReferenceId(inspectionId);
        req.setSku("SKU-MATCH");
        req.setReceivedQuantity(2);
        List<InspectionQtyUpdateReqDTO> reqList = List.of(req);

        // Act
        Map<String, Object> result = svc.updateInspectionQtyCheck(inspectionId, reqList);

        // Assert
        assertThat(result)
                .containsEntry("status", "success")
                .containsEntry("message", "Inspection update quantity check success");

        verify(mockCommentSvc, never()).deleteComment(anyString());
    }

    @Test
    void updateInspectionQtyCheck_whenSkuExistsButNoItems_returnsSuccessMap() {
        // Arrange
        String inspectionId = "INS-EMPTY-KEY";
        InspectionItem ghostItem = mock(InspectionItem.class);
        when(ghostItem.getProduct()).thenReturn(new Product() {{ setSku("GHOST-SKU"); }});
        Inspection inspection = new Inspection();
        inspection.setInspectionId(inspectionId);
        inspection.setInspectionItems(List.of(ghostItem));
        when(mockInspectionRepo.findByInspectionId(inspectionId))
                .thenReturn(Optional.of(inspection));

        InspectionQtyUpdateReqDTO req = new InspectionQtyUpdateReqDTO();
        req.setReferenceId(inspectionId);
        req.setSku("GHOST-SKU");
        req.setReceivedQuantity(0);
        List<InspectionQtyUpdateReqDTO> reqList = List.of(req);

        // Act
        Map<String, Object> result = svc.updateInspectionQtyCheck(inspectionId, reqList);

        // Assert
        assertThat(result)
                .containsEntry("status", "success")
                .containsEntry("message", "Inspection update quantity check success");
    }


    @Test
    void getAllAssignedPackages_withValidOperator_returnsMappedPage() {
        // Arrange
        UserGroup userGroup = new UserGroup();
        userGroup.setId(5L);
        userGroup.setName("AssigneeGroup");
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(
                null, null, null, null, null, null, null, null, userGroup
        );
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);

        AssignedPackagesFilterDTO filter = new AssignedPackagesFilterDTO();
        filter.setOper("eq");
        filter.setPartnerName("PartnerX");
        filter.setReferenceId("REF-123");
        filter.setOrderId("");
        filter.setCreatedAt("");
        filter.setStatus("IN_PROGRESS");
        filter.setSellingChannel("Online");
        filter.setAssigneeGroup("GroupA");
        filter.setRemainingDays(10);

        Pageable pageable = PageRequest.of(0, 10);

        InspectionView view = new InspectionView();
        view.setInspectionId("INSP-500");
        view.setReferenceId("REF-123");
        view.setOrderId("ORD-42");
        view.setCreatedAt(OffsetDateTime.parse("2023-10-01T12:00:00Z"));
        view.setStatus(IN_PROGRESS);
        view.setSellingChannel("Online");
        view.setPartnerName("PartnerX");
        view.setAssigneeGroup("AssigneeGroup");
        view.setRemainingDays(10);
        Page<InspectionView> page = new PageImpl<>(List.of(view), pageable, 1);
        when(mockAssignedRepo.findAll(any(Specification.class), eq(pageable))).thenReturn(page);

        // Act
        Page<AssignedPackagesDTO> result = svc.getAllAssignedPackages(filter, pageable);

        // Assert
        assertThat(result.getTotalElements()).isEqualTo(1);
        AssignedPackagesDTO dto = result.getContent().get(0);
        assertThat(dto.getInspectionId()).isEqualTo("INSP-500");
        assertThat(dto.getReferenceId()).isEqualTo("REF-123");
        assertThat(dto.getOrderId()).isEqualTo("ORD-42");
        assertThat(dto.getPartnerName()).isEqualTo("PartnerX");
        assertThat(dto.getAssigneeGroup()).isEqualTo("AssigneeGroup");
        assertThat(dto.getStatus()).isEqualTo("IN_PROGRESS");
        assertThat(dto.getRemainingDays()).isEqualTo(10);
    }

    @Test
    void getAllAssignedPackages_specificationToPredicate_returnsPredicate() {
        // Arrange
        UserGroup userGroup = new UserGroup();
        userGroup.setId(99L);
        userGroup.setName("MyGroup");
        AuthenticatedUserDTO mockUser = new AuthenticatedUserDTO(
                null, null, null, null, null, null, null, null, userGroup
        );
        when(mockAuthUserSvc.getAuthenticatedUserDetails()).thenReturn(mockUser);

        AssignedPackagesFilterDTO filter = new AssignedPackagesFilterDTO();
        filter.setOper("eq");
        filter.setPartnerName("PartnerZ");
        Pageable pageable = PageRequest.of(0, 1);

        Page<InspectionView> emptyPage = new PageImpl<>(List.of(), pageable, 0);

        @SuppressWarnings("unchecked")
        ArgumentCaptor<Specification<InspectionView>> specCap =
                ArgumentCaptor.forClass(Specification.class);
        when(mockAssignedRepo.findAll(specCap.capture(), eq(pageable))).thenReturn(emptyPage);

        // Act
        svc.getAllAssignedPackages(filter, pageable);

        Specification<InspectionView> spec = specCap.getValue();

        CriteriaBuilder cb = mock(CriteriaBuilder.class);
        CriteriaQuery<InspectionView> query = mock(CriteriaQuery.class);
        Root<InspectionView> root = mock(Root.class);

        Path<Object> assigneeGroupPath = mock(Path.class);
        Path<Object> remainingDaysPath = mock(Path.class);
        when(root.get("assigneeGroup")).thenReturn(assigneeGroupPath);
        when(root.get("remainingDays")).thenReturn(remainingDaysPath);

        CriteriaBuilder.Case<Integer> selectCase = mock(CriteriaBuilder.Case.class);
        doReturn(selectCase).when(cb).selectCase();
        Predicate whenPred = mock(Predicate.class);
        when(cb.equal(assigneeGroupPath, "MyGroup")).thenReturn(whenPred);
        when(selectCase.when(eq(whenPred), eq(0))).thenReturn(selectCase);
        when(selectCase.otherwise(eq(1))).thenReturn(selectCase);
        jakarta.persistence.criteria.Order order1 = mock(jakarta.persistence.criteria.Order.class);
        jakarta.persistence.criteria.Order order2 = mock(jakarta.persistence.criteria.Order.class);
        when(cb.asc(selectCase)).thenReturn(order1);
        when(cb.asc(remainingDaysPath)).thenReturn(order2);


        spec.toPredicate(root, query, cb);

        // Assert
        verify(query).orderBy(order1, order2);
    }

    @Test
    void getAllAssignedPackages_withInvalidOperator_throwsIllegalArgumentException() {
        // Arrange

        AssignedPackagesFilterDTO filter = new AssignedPackagesFilterDTO();
        filter.setOper("invalid");

        Pageable pageable = PageRequest.of(0, 5);

        // Act & Assert
        assertThrows(IllegalArgumentException.class, () ->
                svc.getAllAssignedPackages(filter, pageable)
        );

        // verify
        verifyNoInteractions(mockAssignedRepo);
    }

    @Test
    void getInspectionItemCondition_returnsMappedDTOs() {
        // Arrange
        InspectionItemConditionDTO dto2 = new InspectionItemConditionDTO();
        dto2.setItemConditionId("ItemCon");
        dto2.setConditionType(InspectionItemCondition.ConditionType.APPROVED);
        dto2.setName("Name");

        when(mockConditionSvc.findByConditionType(InspectionItemCondition.ConditionType.APPROVED))
            .thenReturn(List.of(dto2));
        when(mockMapper.map(dto2, InspectionItemConditionDTO.class)).thenReturn(dto2);

        // Act
        List<InspectionItemConditionDTO> result =
            svc.getInspectionItemCondition(InspectionItemCondition.ConditionType.APPROVED);

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(dto2);
    }

}
