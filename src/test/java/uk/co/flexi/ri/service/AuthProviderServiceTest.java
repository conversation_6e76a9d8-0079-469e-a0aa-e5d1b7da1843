package uk.co.flexi.ri.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.AuthProviderConfigDTO;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.model.AuthProvider;
import uk.co.flexi.ri.repository.AuthProviderRepo;

import java.io.IOException;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class AuthProviderServiceTest {
    private AuthProviderRepo authProviderRepo;
    private ModelMapper modelMapper;
    private ObjectMapper objectMapper;
    private AuthenticatedUserService authenticatedUserService;
    private AuthProviderService service;

    @BeforeEach
    void setUp() {
        authProviderRepo = mock(AuthProviderRepo.class);
        modelMapper = mock(ModelMapper.class);
        objectMapper = mock(ObjectMapper.class);
        authenticatedUserService = mock(AuthenticatedUserService.class);
        service = new AuthProviderService(authProviderRepo, modelMapper, objectMapper, authenticatedUserService);
    }

    @Test
    void getAuthProviders_excludesDB() {
        var providers = service.getAuthProviders();
        assertTrue(providers.contains("GOOGLE"));
        assertFalse(providers.contains("DB"));
    }

    @Test
    void findByProvider_found() {
        AuthProvider.Provider provider = AuthProvider.Provider.GOOGLE;
        AuthProvider authProvider = new AuthProvider();
        when(authProviderRepo.findByProvider(provider)).thenReturn(Optional.of(authProvider));
        assertSame(authProvider, service.findByProvider(provider));
    }

    @Test
    void findByProvider_notFound_throws() {
        AuthProvider.Provider provider = AuthProvider.Provider.GOOGLE;
        when(authProviderRepo.findByProvider(provider)).thenReturn(Optional.empty());
        assertThrows(java.util.NoSuchElementException.class, () -> service.findByProvider(provider));
    }

    @Test
    void addSSOConfig_existingProvider() throws IOException {
        AuthProvider.Provider provider = AuthProvider.Provider.GOOGLE;
        AuthProvider authProvider = new AuthProvider();
        JsonNode reqDTO = mock(JsonNode.class);
        ObjectReader reader = mock(ObjectReader.class);
        AuthProvider updated = new AuthProvider();
        when(authProviderRepo.findByProvider(provider)).thenReturn(Optional.of(authProvider));
        when(objectMapper.readerForUpdating(authProvider)).thenReturn(reader);
        when(reader.readValue(reqDTO)).thenReturn(updated);
        when(authProviderRepo.save(any())).thenReturn(updated);
        AuthProviderConfigDTO dto = new AuthProviderConfigDTO();
        when(modelMapper.map(updated, AuthProviderConfigDTO.class)).thenReturn(dto);
        AuthProviderConfigDTO result = service.addSSOConfig(provider, reqDTO);
        assertSame(dto, result);
    }

    @Test
    void addSSOConfig_newProvider() throws IOException {
        AuthProvider.Provider provider = AuthProvider.Provider.GOOGLE;
        JsonNode reqDTO = mock(JsonNode.class);
        ObjectReader reader = mock(ObjectReader.class);
        AuthProvider updated = new AuthProvider();
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        userDTO.setMerchantId(1L);
        when(authProviderRepo.findByProvider(provider)).thenReturn(Optional.empty());
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        when(objectMapper.readerForUpdating(any())).thenReturn(reader);
        when(reader.readValue(reqDTO)).thenReturn(updated);
        when(authProviderRepo.save(any())).thenReturn(updated);
        AuthProviderConfigDTO dto = new AuthProviderConfigDTO();
        when(modelMapper.map(updated, AuthProviderConfigDTO.class)).thenReturn(dto);
        AuthProviderConfigDTO result = service.addSSOConfig(provider, reqDTO);
        assertSame(dto, result);
    }

    @Test
    void getSSOConfig_google() {
        AuthProviderConfigDTO dto = service.getSSOConfig(AuthProvider.Provider.GOOGLE);
        assertNotNull(dto);
        assertEquals("", dto.getClientId());
    }

    @Test
    void getSSOConfig_azure() {
        AuthProviderConfigDTO dto = service.getSSOConfig(AuthProvider.Provider.AZURE);
        assertNotNull(dto);
        assertEquals("", dto.getClientId());
        assertEquals("", dto.getTenantId());
    }

    @Test
    void getSSOConfig_okta() {
        AuthProviderConfigDTO dto = service.getSSOConfig(AuthProvider.Provider.OKTA);
        assertNotNull(dto);
        assertEquals("", dto.getClientId());
        assertEquals("", dto.getDomain());
    }

    @Test
    void getSSOConfig_manhattan() {
        AuthProviderConfigDTO dto = service.getSSOConfig(AuthProvider.Provider.MANHATTAN);
        assertNotNull(dto);
        assertEquals("", dto.getClientId());
        assertEquals("", dto.getDomain());
    }

    @Test
    void setProviderValues_google() {
        AuthProvider authProvider = new AuthProvider();
        authProvider.setRedirectUri("/code/google");
        service.setProviderValues(AuthProvider.Provider.GOOGLE, authProvider);
        assertEquals("google", authProvider.getRegistrationId());
        assertTrue(authProvider.getAuthUrl().contains("/oauth2/authorization/"));
        assertEquals("https://accounts.google.com/o/oauth2/auth", authProvider.getAuthorizationUri());
    }

    @Test
    void setProviderValues_azure() {
        AuthProvider authProvider = new AuthProvider();
        authProvider.setRedirectUri("/code/azure");
        authProvider.setTenantId("tid");
        service.setProviderValues(AuthProvider.Provider.AZURE, authProvider);
        assertEquals("azure", authProvider.getRegistrationId());
        assertTrue(authProvider.getAuthorizationUri().contains("login.microsoftonline.com"));
    }

    @Test
    void setProviderValues_okta() {
        AuthProvider authProvider = new AuthProvider();
        authProvider.setRedirectUri("/code/okta");
        authProvider.setDomain("https://okta.com");
        service.setProviderValues(AuthProvider.Provider.OKTA, authProvider);
        assertEquals("okta", authProvider.getRegistrationId());
        assertTrue(authProvider.getAuthorizationUri().contains("okta.com"));
    }

    @Test
    void setProviderValues_manhattan() {
        AuthProvider authProvider = new AuthProvider();
        authProvider.setRedirectUri("/code/manhattan");
        authProvider.setDomain("https://mao.com");
        service.setProviderValues(AuthProvider.Provider.MANHATTAN, authProvider);
        assertEquals("manhattan", authProvider.getRegistrationId());
        assertTrue(authProvider.getAuthorizationUri().contains("mao.com"));
    }

    @Test
    void extractRegistrationId_found() {
        String url = "/code/google";
        String id = AuthProviderService.extractRegistrationId(url);
        assertEquals("google", id);
    }

    @Test
    void extractRegistrationId_notFound() {
        String url = "/something/else";
        String id = AuthProviderService.extractRegistrationId(url);
        assertNull(id);
    }
} 