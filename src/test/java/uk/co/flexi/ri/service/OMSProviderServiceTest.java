package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.exception.custom.OMSException;
import uk.co.flexi.ri.model.OMSProvider;
import uk.co.flexi.ri.model.SearchStrategy;
import uk.co.flexi.ri.repository.OMSProviderRepo;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class OMSProviderServiceTest {
    private OMSProviderRepo omsProviderRepo;
    private OrderLookUpService orderLookUpService;
    private SearchStrategyRegistry strategyRegistry;
    private OMSProviderService service;

    @BeforeEach
    void setUp() {
        omsProviderRepo = mock(OMSProviderRepo.class);
        orderLookUpService = mock(OrderLookUpService.class);
        strategyRegistry = mock(SearchStrategyRegistry.class);
        service = new OMSProviderService(omsProviderRepo, orderLookUpService, strategyRegistry);
    }

    @Test
    void getOMSList_returnsNames() {
        OMSProvider p1 = new OMSProvider();
        p1.setProviderName("A");
        OMSProvider p2 = new OMSProvider();
        p2.setProviderName("B");
        when(omsProviderRepo.findAll()).thenReturn(List.of(p1, p2));
        List<String> result = service.getOMSList();
        assertEquals(List.of("A", "B"), result);
    }

    @Test
    void getOMSList_empty() {
        when(omsProviderRepo.findAll()).thenReturn(List.of());
        List<String> result = service.getOMSList();
        assertTrue(result.isEmpty());
    }

    @Test
    void getOMSProviderInfo_found() {
        OMSProvider p = new OMSProvider();
        p.setSearchStrategy(List.of("S1", "S2"));
        when(omsProviderRepo.findAll()).thenReturn(List.of(p));
        OMSProviderService.OMSProviderInfo info = service.getOMSProviderInfo();
        assertSame(p, info.omsProvider());
        assertEquals(List.of("S1", "S2"), info.searchStrategy());
    }

    @Test
    void getOMSProviderInfo_notFound_throws() {
        when(omsProviderRepo.findAll()).thenReturn(List.of());
        assertThrows(NoSuchElementException.class, () -> service.getOMSProviderInfo());
    }

    @Test
    void getOMSProviderInfo_byName_found() {
        OMSProvider p = new OMSProvider();
        p.setSearchStrategy(List.of("S1"));
        when(omsProviderRepo.findByProviderName("X")).thenReturn(Optional.of(p));
        OMSProviderService.OMSProviderInfo info = service.getOMSProviderInfo("X");
        assertSame(p, info.omsProvider());
        assertEquals(List.of("S1"), info.searchStrategy());
    }

    @Test
    void getOMSProviderInfo_byName_notFound_throws() {
        when(omsProviderRepo.findByProviderName("X")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.getOMSProviderInfo("X"));
    }

    @Test
    void getSearchStrategies_found() {
        OMSProvider p = new OMSProvider();
        p.setSearchStrategy(List.of("A", "B"));
        when(omsProviderRepo.findByProviderName("Y")).thenReturn(Optional.of(p));
        List<String> result = service.getSearchStrategies("Y");
        assertEquals(List.of("A", "B"), result);
    }

    @Test
    void getSearchStrategies_notFound_throws() {
        when(omsProviderRepo.findByProviderName("Y")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.getSearchStrategies("Y"));
    }

    @Test
    void getAllSearchStrategies_valid() {
        Set<SearchStrategy> strategies = Set.of(SearchStrategy.ORDER_BY_ID, SearchStrategy.ORDER_BY_RETURN_TRACKING);
        when(strategyRegistry.getStrategiesFor(any())).thenReturn(strategies);
        List<String> result = service.getAllSearchStrategies("MAO");
        assertTrue(result.contains(SearchStrategy.ORDER_BY_ID.name()));
        assertTrue(result.contains(SearchStrategy.ORDER_BY_RETURN_TRACKING.name()));
    }

    @Test
    void getAllSearchStrategies_invalidProvider_throws() {
        assertThrows(NoSuchElementException.class, () -> service.getAllSearchStrategies("invalid!"));
    }

    @Test
    void addSearchStrategies_found() {
        OMSProvider p = new OMSProvider();
        when(omsProviderRepo.findByProviderName("Z")).thenReturn(Optional.of(p));
        when(omsProviderRepo.save(p)).thenReturn(p);
        List<String> result = service.addSearchStrategies("Z", List.of("S1", "S2"));
        assertEquals(List.of("S1", "S2"), result);
    }

    @Test
    void addSearchStrategies_notFound_throws() {
        when(omsProviderRepo.findByProviderName("Z")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.addSearchStrategies("Z", List.of("S1")));
    }

    @Test
    void testConnection_omsException() {
        OMSProvider p = new OMSProvider();
        p.setSearchStrategy(List.of("S1"));
        when(omsProviderRepo.findByProviderName("X")).thenReturn(Optional.of(p));
        doThrow(new RuntimeException("fail")).when(orderLookUpService).orderLookUp(any(), eq(p));
        assertThrows(OMSException.class, () -> service.testConnection("X"));
    }

    enum TestEnum { A, B }
} 