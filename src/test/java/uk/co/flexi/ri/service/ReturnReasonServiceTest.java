package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.ReturnReasonDTO;
import uk.co.flexi.ri.model.ReturnReason;
import uk.co.flexi.ri.repository.ReturnReasonRepo;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ReturnReasonServiceTest {
    private ModelMapper modelMapper;
    private ReturnReasonRepo returnReasonRepo;
    private ReturnReasonService service;

    @BeforeEach
    void setUp() {
        modelMapper = mock(ModelMapper.class);
        returnReasonRepo = mock(ReturnReasonRepo.class);
        service = new ReturnReasonService(modelMapper, returnReasonRepo);
    }

    @Test
    void findById_found() {
        ReturnReason reason = new ReturnReason();
        ReturnReasonDTO dto = new ReturnReasonDTO();
        when(returnReasonRepo.findByReturnReasonId("rid")).thenReturn(Optional.of(reason));
        when(modelMapper.map(reason, ReturnReasonDTO.class)).thenReturn(dto);
        assertSame(dto, service.findById("rid"));
    }

    @Test
    void findById_notFound_throws() {
        when(returnReasonRepo.findByReturnReasonId("rid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.findById("rid"));
    }

    @Test
    void save_success() {
        ReturnReasonDTO dto = new ReturnReasonDTO();
        ReturnReason reason = new ReturnReason();
        when(modelMapper.map(dto, ReturnReason.class)).thenReturn(reason);
        when(returnReasonRepo.save(reason)).thenReturn(reason);
        when(modelMapper.map(reason, ReturnReasonDTO.class)).thenReturn(dto);
        assertSame(dto, service.save(dto));
    }

    @Test
    void findAll_mapsAll() {
        ReturnReason reason = new ReturnReason();
        ReturnReasonDTO dto = new ReturnReasonDTO();
        when(returnReasonRepo.findAll()).thenReturn(List.of(reason));
        when(modelMapper.map(reason, ReturnReasonDTO.class)).thenReturn(dto);
        List<ReturnReasonDTO> result = service.findAll();
        assertEquals(1, result.size());
        assertSame(dto, result.get(0));
    }

    @Test
    void update_found() {
        ReturnReason reason = new ReturnReason();
        ReturnReasonDTO dto = new ReturnReasonDTO();
        dto.setReason("new reason");
        when(returnReasonRepo.findByReturnReasonId("rid")).thenReturn(Optional.of(reason));
        when(returnReasonRepo.save(reason)).thenReturn(reason);
        when(modelMapper.map(reason, ReturnReasonDTO.class)).thenReturn(dto);
        ReturnReasonDTO result = service.update("rid", dto);
        assertSame(dto, result);
        assertEquals("new reason", reason.getReason());
    }

    @Test
    void update_notFound_throws() {
        ReturnReasonDTO dto = new ReturnReasonDTO();
        when(returnReasonRepo.findByReturnReasonId("rid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.update("rid", dto));
    }

    @Test
    void delete_found() {
        ReturnReason reason = new ReturnReason();
        when(returnReasonRepo.findByReturnReasonId("rid")).thenReturn(Optional.of(reason));
        doNothing().when(returnReasonRepo).delete(reason);
        service.delete("rid");
        verify(returnReasonRepo).delete(reason);
    }

    @Test
    void delete_notFound_throws() {
        when(returnReasonRepo.findByReturnReasonId("rid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.delete("rid"));
    }
} 