package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.model.Instruction;
import uk.co.flexi.ri.model.ProductInstruction;
import uk.co.flexi.ri.repository.InstructionRepo;
import uk.co.flexi.ri.repository.ProductInstructionRepo;

import jakarta.persistence.EntityManager;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class ProductServiceTest {
    @Mock ProductInstructionRepo productInstructionRepo;
    @Mock InstructionRepo instructionRepo;
    @Mock FileFormatServiceFactory fileFormatServiceFactory;
    @Mock LanguageService languageService;
    @Mock ModelMapper modelMapper;
    @Mock EntityManager entityManager;
    @Mock MultipartFile multipartFile;
    @Mock FileFormatService fileFormatService;

    @InjectMocks ProductService productService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        productService = new ProductService(
                productInstructionRepo, instructionRepo, fileFormatServiceFactory,
                languageService, modelMapper, entityManager
        );
    }

    @Test
    void testCreateProductInstruction() {
        ProductInstructionDTO dto = new ProductInstructionDTO();
        ProductInstruction pi = new ProductInstruction();
        pi.setInstructions(new ArrayList<>());
        when(modelMapper.map(dto, ProductInstruction.class)).thenReturn(pi);
        when(productInstructionRepo.save(pi)).thenReturn(pi);
        when(modelMapper.map(pi, ProductInstructionDTO.class)).thenReturn(dto);
        assertEquals(dto, productService.createProductInstruction(dto));
    }

    @Test
    void testFindAllProductInstruction() {
        ProductInstruction pi = new ProductInstruction();
        ProductInstructionDTO dto = new ProductInstructionDTO();
        when(productInstructionRepo.findAll()).thenReturn(List.of(pi));
        when(modelMapper.map(pi, ProductInstructionDTO.class)).thenReturn(dto);
        assertEquals(List.of(dto), productService.findAllProductInstruction());
    }

    @Test
    void testFindAllProductInstructionByIds() {
        ProductInstruction pi = new ProductInstruction();
        ProductInstructionDTO dto = new ProductInstructionDTO();
        when(productInstructionRepo.findByProductInstructionIdIn(List.of("id1"))).thenReturn(List.of(pi));
        when(modelMapper.map(pi, ProductInstructionDTO.class)).thenReturn(dto);
        assertEquals(List.of(dto), productService.findAllProductInstruction(List.of("id1")));
    }

    @Test
    void testFindByProductInstructionId_found() {
        ProductInstruction pi = new ProductInstruction();
        when(productInstructionRepo.findByProductInstructionId("id")).thenReturn(Optional.of(pi));
        assertEquals(pi, productService.findByProductInstructionId("id"));
    }

    @Test
    void testFindByProductInstructionId_notFound() {
        when(productInstructionRepo.findByProductInstructionId("id")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> productService.findByProductInstructionId("id"));
    }

    @Test
    void testUpdateProductInstruction() {
        ProductInstruction pi = new ProductInstruction();
        pi.setId(1L);
        when(productInstructionRepo.findByProductInstructionId("id")).thenReturn(Optional.of(pi));
        when(modelMapper.map(any(), eq(ProductInstructionDTO.class))).thenReturn(new ProductInstructionDTO());
        List<ProductInstructionUpdateDTO> updates = List.of(new ProductInstructionUpdateDTO("en", "content"));
        assertNotNull(productService.updateProductInstruction("id", updates));
        verify(instructionRepo).deleteAllInstruction(1L);
        verify(instructionRepo).saveAll(anyList());
    }

    @Test
    void testFindByIdAndLanguage_found() {
        ProductInstruction pi = new ProductInstruction();
        when(productInstructionRepo.findByProductInstructionIdAndInstructions_LanguageCode("id", "en")).thenReturn(Optional.of(pi));
        when(modelMapper.map(pi, ProductInstructionDTO.class)).thenReturn(new ProductInstructionDTO());
        assertNotNull(productService.findByIdAndLanguage("id", "en"));
    }

    @Test
    void testFindByIdAndLanguage_notFound() {
        when(productInstructionRepo.findByProductInstructionIdAndInstructions_LanguageCode("id", "en")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> productService.findByIdAndLanguage("id", "en"));
    }

    @Test
    void testFindByProductDetails_found() {
        Instruction ins = new Instruction();
        when(instructionRepo.findByProductInstruction_ProductClassAndProductInstruction_ProductSubclassAndLanguageCode("c", "s", "en"))
                .thenReturn(Optional.of(ins));
        when(modelMapper.map(ins, InstructionDTO.class)).thenReturn(new InstructionDTO());
        assertNotNull(productService.findByProductDetails("c", "s", "en"));
    }

    @Test
    void testFindByProductDetails_fallbackToEnglish() {
        Instruction ins = new Instruction();
        when(instructionRepo.findByProductInstruction_ProductClassAndProductInstruction_ProductSubclassAndLanguageCode("c", "s", "fr"))
                .thenReturn(Optional.empty());
        when(instructionRepo.findByProductInstruction_ProductClassAndProductInstruction_ProductSubclassAndLanguageCode("c", "s", "en"))
                .thenReturn(Optional.of(ins));
        when(modelMapper.map(ins, InstructionDTO.class)).thenReturn(new InstructionDTO());
        assertNotNull(productService.findByProductDetails("c", "s", "fr"));
    }

    @Test
    void testFindByProductDetails_notFound() {
        when(instructionRepo.findByProductInstruction_ProductClassAndProductInstruction_ProductSubclassAndLanguageCode(any(), any(), any()))
                .thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> productService.findByProductDetails("c", "s", "xx"));
    }

    @Test
    void testDeleteById_found() {
        ProductInstruction pi = new ProductInstruction();
        when(productInstructionRepo.findByProductInstructionId("id")).thenReturn(Optional.of(pi));
        productService.deleteById("id");
        verify(productInstructionRepo).delete(pi);
    }

    @Test
    void testDeleteById_notFound() {
        when(productInstructionRepo.findByProductInstructionId("id")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> productService.deleteById("id"));
    }

    @Test
    void testBatchUpsertProductInstructions_empty() {
        productService.batchUpsertProductInstructions(Collections.emptyList());
        verifyNoInteractions(modelMapper, entityManager);
    }

    @Test
    void testImportProductInstruction_success() throws IOException {
        when(languageService.findAllSupportedLanguages()).thenReturn(List.of(new SupportedLanguageDTO("en", "Enlish")));
        when(fileFormatServiceFactory.getService("xlsx")).thenReturn(fileFormatService);
        when(multipartFile.getInputStream()).thenReturn(mock(InputStream.class));
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("Product Class", "A");
        map.put("Product Sub Class", "B");
        map.put("Instruction[en]", "content");
        when(fileFormatService.importData(any())).thenReturn(List.of(map));
        doNothing().when(entityManager).flush();
        doNothing().when(entityManager).clear();
        when(productInstructionRepo.findAll()).thenReturn(Collections.emptyList());
        // FIX: Add this line
        when(modelMapper.map(any(ProductInstructionDTO.class), eq(ProductInstruction.class)))
                .thenAnswer(invocation -> {
                    ProductInstructionDTO dto = invocation.getArgument(0);
                    ProductInstruction pi = new ProductInstruction();
                    pi.setProductClass(dto.getProductClass());
                    pi.setProductSubclass(dto.getProductSubclass());
                    pi.setInstructions(new ArrayList<>());
                    return pi;
                });
        List<ProductInstructionDTO> result = productService.importProductInstruction("xlsx", multipartFile);
        assertNotNull(result);
    }

    @Test
    void testImportProductInstruction_invalidClass() throws IOException {
        when(languageService.findAllSupportedLanguages()).thenReturn(List.of(new SupportedLanguageDTO("en", "Enlish")));
        when(fileFormatServiceFactory.getService("xlsx")).thenReturn(fileFormatService);
        when(multipartFile.getInputStream()).thenReturn(mock(InputStream.class));
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("Product Class", "");
        map.put("Product Sub Class", "B");
        map.put("Instruction[en]", "content");
        when(fileFormatService.importData(any())).thenReturn(List.of(map));
        assertThrows(IllegalArgumentException.class, () -> productService.importProductInstruction("xlsx", multipartFile));
    }

    @Test
    void testImportProductInstruction_invalidSubClass() throws IOException {
        when(languageService.findAllSupportedLanguages()).thenReturn(List.of(new SupportedLanguageDTO("en", "Enlish")));
        when(fileFormatServiceFactory.getService("xlsx")).thenReturn(fileFormatService);
        when(multipartFile.getInputStream()).thenReturn(mock(InputStream.class));
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put("Product Class", "A");
        map.put("Product Sub Class", "");
        map.put("Instruction[en]", "content");
        when(fileFormatService.importData(any())).thenReturn(List.of(map));
        assertThrows(IllegalArgumentException.class, () -> productService.importProductInstruction("xlsx", multipartFile));
    }

    @Test
    void testExportProductInstruction_emptyList() {
        when(languageService.findAllSupportedLanguages()).thenReturn(List.of(new SupportedLanguageDTO("en", "Enlish")));
        when(productInstructionRepo.findAll()).thenReturn(Collections.emptyList());
        when(fileFormatServiceFactory.getService("xlsx")).thenReturn(fileFormatService);
        when(fileFormatService.exportData(any())).thenReturn(new byte[0]);
        byte[] result = productService.exportProductInstruction("xlsx", null);
        assertNotNull(result);
    }

    @Test
    void testExportProductInstruction_withList() {
        when(languageService.findAllSupportedLanguages()).thenReturn(List.of(new SupportedLanguageDTO("en", "Enlish")));
        ProductInstructionDTO dto = new ProductInstructionDTO();
        dto.setProductClass("A");
        dto.setProductSubclass("B");
        InstructionDTO ins = new InstructionDTO();
        ins.setLanguageCode("en");
        ins.setContent("content");
        dto.setInstructions(List.of(ins));
        when(productInstructionRepo.findByProductInstructionIdIn(anyList())).thenReturn(Collections.emptyList());
        when(fileFormatServiceFactory.getService("xlsx")).thenReturn(fileFormatService);
        when(fileFormatService.exportData(any())).thenReturn(new byte[0]);
        when(modelMapper.map(any(), eq(ProductInstructionDTO.class))).thenReturn(dto);
        byte[] result = productService.exportProductInstruction("xlsx", List.of("id1"));
        assertNotNull(result);
    }

    @Test
    void testDeleteByIds() {
        ProductInstruction pi = new ProductInstruction();
        when(productInstructionRepo.findByProductInstructionIdIn(anyList())).thenReturn(List.of(pi));
        productService.deleteByIds(List.of("id1"));
        verify(productInstructionRepo).deleteAll(List.of(pi));
    }

    @Test
    void testMergeInstructions_fullCoverage() {
        ProductInstruction existing = new ProductInstruction();
        Instruction existingIns = new Instruction();
        existingIns.setLanguageCode("en");
        existingIns.setId(1L);
        existingIns.setTenant(2L);
        existing.setInstructions(new ArrayList<>(List.of(existingIns)));

        ProductInstruction incoming = new ProductInstruction();
        Instruction incomingIns = new Instruction();
        incomingIns.setLanguageCode("en");
        incoming.setInstructions(new ArrayList<>(List.of(incomingIns)));

        productService.mergeInstructions(existing, incoming);
        // Test with non-matching language code
        Instruction incomingIns2 = new Instruction();
        incomingIns2.setLanguageCode("fr");
        incoming.setInstructions(new ArrayList<>(List.of(incomingIns2)));
        productService.mergeInstructions(existing, incoming);
    }

    @Test
    void testConvertToMap_fullCoverage() {
        ProductInstructionDTO dto = new ProductInstructionDTO();
        dto.setProductClass("A");
        dto.setProductSubclass("B");
        InstructionDTO ins = new InstructionDTO();
        ins.setLanguageCode("en");
        ins.setContent("content");
        dto.setInstructions(List.of(ins));
        List<String> lang = List.of("en", "fr");
        LinkedHashMap<String, Object> map = productService.convertToMap(dto, lang);
        assertEquals("A", map.get("Product Class"));
        assertEquals("B", map.get("Product Sub Class"));
        assertEquals("content", map.get("Instruction[en]"));
        assertEquals("", map.get("Instruction[fr]"));
    }


    @Test
    void testBatchUpsertProductInstructions_batchFlush() {
        List<ProductInstructionDTO> dtos = new ArrayList<>();
        for (int i = 0; i < 51; i++) {
            ProductInstructionDTO dto = new ProductInstructionDTO();
            dto.setProductClass("A" + i);
            dto.setProductSubclass("B" + i);
            dtos.add(dto);
        }
        when(modelMapper.map(any(ProductInstructionDTO.class), eq(ProductInstruction.class)))
            .thenAnswer(invocation -> {
                ProductInstructionDTO dto = invocation.getArgument(0);
                ProductInstruction pi = new ProductInstruction();
                pi.setProductClass(dto.getProductClass());
                pi.setProductSubclass(dto.getProductSubclass());
                pi.setInstructions(new ArrayList<>());
                return pi;
            });
        ProductService spyService = spy(productService);
        doReturn(Collections.emptyMap()).when(spyService).fetchExistingProductInstructions(any());
        doNothing().when(spyService).assignBackReference(any());
        doNothing().when(entityManager).persist(any());
        doNothing().when(entityManager).flush();
        doNothing().when(entityManager).clear();
        spyService.batchUpsertProductInstructions(dtos);
    }

    @Test
    void testAssignBackReference() {
        ProductInstruction pi = new ProductInstruction();
        Instruction ins1 = new Instruction();
        Instruction ins2 = new Instruction();
        pi.setInstructions(List.of(ins1, ins2));
        productService.assignBackReference(pi);
        assertSame(pi, ins1.getProductInstruction());
        assertSame(pi, ins2.getProductInstruction());
    }

    @Test
    void testConvertToInstructionDTO_missingLangKey() {
        LinkedHashMap<String, Object> input = new LinkedHashMap<>();
        input.put("Product Class", "A");
        input.put("Product Sub Class", "B");
        List<String> lang = List.of("en", "fr");
        ProductInstructionDTO dto = productService.convertToInstructionDTO(input, lang);
        assertEquals("A", dto.getProductClass());
        assertEquals("B", dto.getProductSubclass());
        assertEquals(0, dto.getInstructions().size());
    }
} 