package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.model.OMSProvider;
import uk.co.flexi.sdk.oms.mao.client.MaoOmsClient;
import uk.co.flexi.sdk.oms.mao.client.MockOMSClient;
import uk.co.flexi.sdk.oms.mao.client.OMSClient;
import uk.co.flexi.sdk.oms.mao.client.config.MaoConfig;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;


class OMSClientServiceTest {

    private static OMSConfigService omsConfigService;

    private OMSClientService omsClientService;

    @BeforeEach
    void setup(){
        omsConfigService = mock(OMSConfigService.class);

        omsClientService = new OMSClientService(omsConfigService);
    }

    @Test
    void getClient_shouldReturnMockClient(){
        OMSProvider omsProvider = new OMSProvider();

        OMSClient<?> client = omsClientService.getClient(OMSClientService.OMSType.MOCK,omsProvider);

        assertThat(client)
                .isNotNull()
                .isInstanceOf(MockOMSClient.class);
    }

    @Test
    void getClient_shouldReturnMaoClient(){
        OMSProvider omsProvider = new OMSProvider();

        when(omsConfigService.getConfig(omsProvider,MaoConfig.class)).thenReturn(mock(MaoConfig.class));

        OMSClient<?> client = omsClientService.getClient(OMSClientService.OMSType.MAO,omsProvider);

        assertThat(client)
                .isNotNull()
                .isInstanceOf(MaoOmsClient.class);
    }

}
