package uk.co.flexi.ri.service;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.web.client.RestClient;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CurrencyConversionServiceTest {
    private RestClient restClient;
    private CurrencyConversionService service;

    @BeforeEach
    void setUp() {
        restClient = mock(RestClient.class);
        service = new CurrencyConversionService(restClient);
    }

    @Test
    void convert_nullPrice_throws() {
        assertThrows(IllegalArgumentException.class, () -> service.convert(null, "USD", "EUR"));
    }

    @Test
    void convert_sameCurrency_returnsRounded() {
        BigDecimal price = new BigDecimal("123.456");
        BigDecimal result = service.convert(price, "USD", "usd");
        assertEquals(new BigDecimal("123.46"), result);
    }

    @Test
    void convert_successfulApiCall_returnsConverted() {
        BigDecimal price = new BigDecimal("100");
        String from = "USD", to = "EUR";
        String uri = "https://api.frankfurter.app/latest?amount=100&from=USD&to=EUR";

        // Mock RestClient fluent API
        RestClient.RequestHeadersUriSpec uriSpec = mock(RestClient.RequestHeadersUriSpec.class);
        RestClient.RequestHeadersSpec headersSpec = mock(RestClient.RequestHeadersSpec.class);
        RestClient.ResponseSpec responseSpec = mock(RestClient.ResponseSpec.class);
        JsonNode response = mock(JsonNode.class);
        JsonNode ratesNode = mock(JsonNode.class);
        JsonNode rateNode = mock(JsonNode.class);

        when(restClient.get()).thenReturn(uriSpec);
        when(uriSpec.uri(anyString())).thenReturn(headersSpec);
        when(headersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.body(JsonNode.class)).thenReturn(response);
        when(response.path("rates")).thenReturn(ratesNode);
        when(ratesNode.isMissingNode()).thenReturn(false);
        when(ratesNode.path(to)).thenReturn(rateNode);
        when(rateNode.isMissingNode()).thenReturn(false);
        when(rateNode.isNumber()).thenReturn(true);
        when(rateNode.decimalValue()).thenReturn(new BigDecimal("92.34"));

        BigDecimal result = service.convert(price, from, to);
        assertEquals(new BigDecimal("92.34"), result);
    }

    @Test
    void convert_responseNull_throws() {
        BigDecimal price = new BigDecimal("100");
        RestClient.RequestHeadersUriSpec uriSpec = mock(RestClient.RequestHeadersUriSpec.class);
        RestClient.RequestHeadersSpec headersSpec = mock(RestClient.RequestHeadersSpec.class);
        RestClient.ResponseSpec responseSpec = mock(RestClient.ResponseSpec.class);

        when(restClient.get()).thenReturn(uriSpec);
        when(uriSpec.uri(anyString())).thenReturn(headersSpec);
        when(headersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.body(JsonNode.class)).thenReturn(null);

        assertThrows(IllegalStateException.class, () -> service.convert(price, "USD", "EUR"));
    }

    @Test
    void convert_ratesMissing_throws() {
        BigDecimal price = new BigDecimal("100");
        RestClient.RequestHeadersUriSpec uriSpec = mock(RestClient.RequestHeadersUriSpec.class);
        RestClient.RequestHeadersSpec headersSpec = mock(RestClient.RequestHeadersSpec.class);
        RestClient.ResponseSpec responseSpec = mock(RestClient.ResponseSpec.class);
        JsonNode response = mock(JsonNode.class);
        JsonNode ratesNode = mock(JsonNode.class);

        when(restClient.get()).thenReturn(uriSpec);
        when(uriSpec.uri(anyString())).thenReturn(headersSpec);
        when(headersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.body(JsonNode.class)).thenReturn(response);
        when(response.path("rates")).thenReturn(ratesNode);
        when(ratesNode.isMissingNode()).thenReturn(true);

        assertThrows(IllegalStateException.class, () -> service.convert(price, "USD", "EUR"));
    }

    @Test
    void convert_rateNodeMissing_throws() {
        BigDecimal price = new BigDecimal("100");
        RestClient.RequestHeadersUriSpec uriSpec = mock(RestClient.RequestHeadersUriSpec.class);
        RestClient.RequestHeadersSpec headersSpec = mock(RestClient.RequestHeadersSpec.class);
        RestClient.ResponseSpec responseSpec = mock(RestClient.ResponseSpec.class);
        JsonNode response = mock(JsonNode.class);
        JsonNode ratesNode = mock(JsonNode.class);
        JsonNode rateNode = mock(JsonNode.class);

        when(restClient.get()).thenReturn(uriSpec);
        when(uriSpec.uri(anyString())).thenReturn(headersSpec);
        when(headersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.body(JsonNode.class)).thenReturn(response);
        when(response.path("rates")).thenReturn(ratesNode);
        when(ratesNode.isMissingNode()).thenReturn(false);
        when(ratesNode.path("EUR")).thenReturn(rateNode);
        when(rateNode.isMissingNode()).thenReturn(true);

        assertThrows(IllegalStateException.class, () -> service.convert(price, "USD", "EUR"));
    }

    @Test
    void convert_rateNodeNotNumber_throws() {
        BigDecimal price = new BigDecimal("100");
        RestClient.RequestHeadersUriSpec uriSpec = mock(RestClient.RequestHeadersUriSpec.class);
        RestClient.RequestHeadersSpec headersSpec = mock(RestClient.RequestHeadersSpec.class);
        RestClient.ResponseSpec responseSpec = mock(RestClient.ResponseSpec.class);
        JsonNode response = mock(JsonNode.class);
        JsonNode ratesNode = mock(JsonNode.class);
        JsonNode rateNode = mock(JsonNode.class);

        when(restClient.get()).thenReturn(uriSpec);
        when(uriSpec.uri(anyString())).thenReturn(headersSpec);
        when(headersSpec.retrieve()).thenReturn(responseSpec);
        when(responseSpec.body(JsonNode.class)).thenReturn(response);
        when(response.path("rates")).thenReturn(ratesNode);
        when(ratesNode.isMissingNode()).thenReturn(false);
        when(ratesNode.path("EUR")).thenReturn(rateNode);
        when(rateNode.isMissingNode()).thenReturn(false);
        when(rateNode.isNumber()).thenReturn(false);

        assertThrows(IllegalStateException.class, () -> service.convert(price, "USD", "EUR"));
    }
} 