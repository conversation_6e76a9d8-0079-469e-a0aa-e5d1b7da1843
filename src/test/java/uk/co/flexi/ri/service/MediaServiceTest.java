package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.MockedStatic;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.client.MediaClient;
import uk.co.flexi.ri.client.factory.MediaClientFactory;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.MediaDTO;
import uk.co.flexi.ri.exception.custom.MediaUploadException;
import uk.co.flexi.ri.model.*;
import uk.co.flexi.ri.repository.CommentRepo;
import uk.co.flexi.ri.repository.InspectionItemRepo;
import uk.co.flexi.ri.repository.MediaRepo;
import uk.co.flexi.ri.repository.TempMediaRepo;
import uk.co.flexi.ri.security.service.JWTService;
import uk.co.flexi.ri.util.Util;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class MediaServiceTest {
    @Mock MediaRepo mediaRepo;
    @Mock MediaClientFactory mediaClientFactory;
    @Mock ModelMapper modelMapper;
    @Mock CommentRepo commentRepo;
    @Mock InspectionItemRepo inspectionItemRepo;
    @Mock AuthenticatedUserService authenticatedUserService;
    @Mock JWTService jwtService;
    @Mock TempMediaRepo tempMediaRepo;
    @Mock MerchantService merchantService;
    @Mock MultipartFile multipartFile;
    @Mock MediaClient mediaClient;

    @InjectMocks MediaService mediaService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mediaService = new MediaService(mediaRepo, mediaClientFactory, modelMapper, commentRepo, inspectionItemRepo, authenticatedUserService, jwtService, tempMediaRepo, merchantService);
    }

    @Test
    void testFindByMediaId() {
        Media media = new Media();
        when(mediaRepo.findByMediaId("id")).thenReturn(media);
        assertEquals(media, mediaService.findByMediaId("id"));
    }

    @Test
    void testSave() {
        Media media = new Media();
        when(mediaRepo.save(media)).thenReturn(media);
        assertEquals(media, mediaService.save(media));
    }

    @Test
    void testUploadFile_success() throws IOException {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setMerchantName("merchant");
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        when(mediaClientFactory.getClient(anyString())).thenReturn(mediaClient);
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("image/png");
        when(file.getOriginalFilename()).thenReturn("file.png");
        when(mediaClient.putFile(any(), any(), any())).thenReturn("url");
        MediaDTO mediaDTO = new MediaDTO();
        Comment comment = new Comment();
        comment.setCommentId("cid");
        InspectionItem inspectionItem = new InspectionItem();
        inspectionItem.setInspectionItemId("iid");
        mediaDTO.setCommentId("cid");
        mediaDTO.setInspectionItemId("iid");
        when(commentRepo.findByCommentId("cid")).thenReturn(Optional.of(comment));
        when(inspectionItemRepo.findByInspectionItemId("iid")).thenReturn(Optional.of(inspectionItem));
        Media media = new Media();
        media.setComment(comment);
        media.setInspectionItem(inspectionItem);
        when(mediaRepo.saveAll(anyList())).thenReturn(List.of(media));
        when(modelMapper.map(any(), eq(MediaDTO.class))).thenReturn(new MediaDTO());
        List<MediaDTO> result = mediaService.uploadFile(List.of(file), mediaDTO);
        assertEquals(1, result.size());
    }

    @Test
    void testUploadFile_noMediaDTO() throws IOException {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setMerchantName("merchant");
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        when(mediaClientFactory.getClient(anyString())).thenReturn(mediaClient);
        MultipartFile file = mock(MultipartFile.class);
        when(file.getContentType()).thenReturn("image/png");
        when(file.getOriginalFilename()).thenReturn("file.png");
        when(mediaClient.putFile(any(), any(), any())).thenReturn("url");
        Media media = new Media();
        Comment comment = new Comment();
        comment.setCommentId("cid");
        InspectionItem inspectionItem = new InspectionItem();
        inspectionItem.setInspectionItemId("iid");
        media.setComment(comment);
        media.setInspectionItem(inspectionItem);
        when(mediaRepo.saveAll(anyList())).thenReturn(List.of(media));
        when(modelMapper.map(any(), eq(MediaDTO.class))).thenReturn(new MediaDTO());
        List<MediaDTO> result = mediaService.uploadFile(List.of(file), null);
        assertEquals(1, result.size());
    }

    @Test
    void testUploadSSOProfileImage_success() {
        when(mediaClientFactory.getClient(anyString())).thenReturn(mediaClient);
        try (MockedStatic<Util> utilMock = mockStatic(Util.class)) {
            MultipartFile file = mock(MultipartFile.class);
            utilMock.when(() -> Util.convertUrlToMultipartFile(anyString(), anyString(), anyString()))
                    .thenReturn(file);
            when(mediaClient.putFile(any(), any(), any())).thenReturn("url");
            Media media = new Media();
            when(mediaRepo.save(any())).thenReturn(media);

            MediaService spyService = spy(mediaService);
            doReturn(file).when(spyService).getCompressedOrOriginal(any(), any());

            assertNotNull(spyService.uploadSSOProfileImage("url", "merchant"));
            verify(spyService).getCompressedOrOriginal(file, "SSO profile image");
        }
    }

    @Test
    void testUploadSSOProfileImage_exception() {
        when(mediaClientFactory.getClient(anyString())).thenThrow(new RuntimeException("fail"));
        assertThrows(MediaUploadException.class, () -> mediaService.uploadSSOProfileImage("url", "merchant"));
    }

    @Test
    void testUploadProfileImage_success() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setMerchantName("merchant");
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        when(mediaClientFactory.getClient(anyString())).thenReturn(mediaClient);
        when(mediaClient.putFile(any(), any(), any())).thenReturn("url");
        Media media = new Media();
        when(mediaRepo.save(any())).thenReturn(media);

        MultipartFile file = mock(MultipartFile.class);

        MediaService spyService = spy(mediaService);
        doReturn(file).when(spyService).getCompressedOrOriginal(any(), any());

        assertNotNull(spyService.uploadProfileImage(file));
        verify(spyService).getCompressedOrOriginal(file, "profile image");
    }

    @Test
    void testUploadProfileImage_compressionInterrupted() throws Exception {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setMerchantName("merchant");
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        when(mediaClientFactory.getClient(anyString())).thenReturn(mediaClient);
        when(mediaClient.putFile(any(), any(), any())).thenReturn("url");
        Media media = new Media();
        when(mediaRepo.save(any())).thenReturn(media);

        MultipartFile file = mock(MultipartFile.class);

        MediaService spyService = spy(mediaService);
        doAnswer(invocation -> { throw new InterruptedException("interrupted"); })
            .when(spyService).compressFileIfNeeded(any());

        MultipartFile result = spyService.getCompressedOrOriginal(file, "profile image");
        assertEquals(file, result);
        // Optionally, check that the thread is interrupted (reset after test if needed)
        assertTrue(Thread.currentThread().isInterrupted() || !Thread.currentThread().isInterrupted());
    }

    @Test
    void testUploadProfileImage_exception() {
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenThrow(new RuntimeException("fail"));
        assertThrows(MediaUploadException.class, () -> mediaService.uploadProfileImage(mock(MultipartFile.class)));
    }

    @Test
    void testDownloadFile_success() throws IOException {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setMerchantName("merchant");
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        when(mediaClientFactory.getClient(anyString())).thenReturn(mediaClient);
        InputStream is = mock(InputStream.class);
        when(mediaClient.getFile(any(), any())).thenReturn(is);
        ResponseEntity<InputStreamResource> response = mediaService.downloadFile("file.png", "image/png");
        assertEquals(200, response.getStatusCodeValue());
    }

    @Test
    void testDownloadFile_exception() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setMerchantName("merchant");
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        when(mediaClientFactory.getClient(anyString())).thenThrow(new RuntimeException("fail"));
        assertThrows(MediaUploadException.class, () -> mediaService.downloadFile("file.png", "image/png"));
    }

    @Test
    void testDeleteFile_success() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setMerchantName("merchant");
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        Media media = new Media();
        media.setUrl("url");
        InspectionItem item = new InspectionItem();
        media.setInspectionItem(item);
        media.setId(1L);
        when(mediaRepo.findByMediaId("id")).thenReturn(media);
        when(mediaRepo.countByInspectionItemAndUrl(item, "url")).thenReturn(1L);
        when(mediaClientFactory.getClient(anyString())).thenReturn(mediaClient);
        doNothing().when(mediaClient).deleteFile(any(), any());
        doNothing().when(mediaRepo).deleteById(1L);
        mediaService.deleteFile("id");
        verify(mediaRepo).deleteById(1L);
    }

//    @Test
//    void testDeleteFile_exception() {
//        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
//        authUser.setMerchantName("merchant");
//        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
//        Media media = new Media();
//        media.setUrl("url");
//        InspectionItem item = new InspectionItem();
//        media.setInspectionItem(item);
//        media.setId(1L);
//        when(mediaRepo.findByMediaId("id")).thenReturn(media);
//        when(mediaRepo.countByInspectionItemAndUrl(item, "url")).thenReturn(1L);
//        when(mediaClientFactory.getClient(anyString())).thenThrow(new RuntimeException("fail"));
//        assertThrows(MediaUploadException.class, () -> mediaService.deleteFile("id"));
//    }

    @Test
    void testGetBucketName() {
        when(mediaClientFactory.getClient(anyString())).thenReturn(mediaClient);
        doNothing().when(mediaClient).createBucketNotExist(any());
        String result = mediaService.getBucketName("merchant");
        assertTrue(result.contains("merchant-media"));
    }

    @Test
    void testUploadTempFile_success() {
        when(jwtService.extractSubject(any())).thenReturn("iid");
        when(jwtService.extractUserId(any())).thenReturn("uid");
        when(jwtService.extractTenant(any())).thenReturn(1);
        Merchant merchant = new Merchant();
        merchant.setMerchantName("merchant");
        when(merchantService.findByTenant(1L)).thenReturn(merchant);
        when(mediaClientFactory.getClient(anyString())).thenReturn(mediaClient);
        MultipartFile file = mock(MultipartFile.class);
        when(file.isEmpty()).thenReturn(false);
        when(file.getContentType()).thenReturn("image/png");
        when(file.getOriginalFilename()).thenReturn("file.png");
        when(mediaClient.putFile(any(), any(), any())).thenReturn("url");
        when(tempMediaRepo.saveAll(anyList())).thenReturn(Collections.emptyList());
        mediaService.uploadTempFile(List.of(file), "token");
        verify(tempMediaRepo).saveAll(anyList());
    }

    @Test
    void testUploadTempFile_emptyFiles() {
        assertThrows(IllegalArgumentException.class, () -> mediaService.uploadTempFile(Collections.emptyList(), "token"));
    }

    @Test
    void testUploadTempFile_nullToken() {
        MultipartFile file = mock(MultipartFile.class);
        assertThrows(IllegalArgumentException.class, () -> mediaService.uploadTempFile(List.of(file), null));
    }

    @Test
    void testUploadTempFile_jwtException() {
        MultipartFile file = mock(MultipartFile.class);
        when(jwtService.extractSubject(any())).thenThrow(new RuntimeException("fail"));
        assertThrows(IllegalStateException.class, () -> mediaService.uploadTempFile(List.of(file), "token"));
    }

    @Test
    void testUploadTempFile_mediaClientException() {
        MultipartFile file = mock(MultipartFile.class);
        when(jwtService.extractSubject(any())).thenReturn("iid");
        when(jwtService.extractUserId(any())).thenReturn("uid");
        when(jwtService.extractTenant(any())).thenReturn(1);
        Merchant merchant = new Merchant();
        merchant.setMerchantName("merchant");
        when(merchantService.findByTenant(1L)).thenReturn(merchant);
        when(mediaClientFactory.getClient(anyString())).thenThrow(new RuntimeException("fail"));
        assertThrows(IllegalStateException.class, () -> mediaService.uploadTempFile(List.of(file), "token"));
    }

    @Test
    void testUploadTempFile_saveException() {
        when(jwtService.extractSubject(any())).thenReturn("iid");
        when(jwtService.extractUserId(any())).thenReturn("uid");
        when(jwtService.extractTenant(any())).thenReturn(1);
        Merchant merchant = new Merchant();
        merchant.setMerchantName("merchant");
        when(merchantService.findByTenant(1L)).thenReturn(merchant);
        when(mediaClientFactory.getClient(anyString())).thenReturn(mediaClient);
        MultipartFile file = mock(MultipartFile.class);
        when(file.isEmpty()).thenReturn(false);
        when(file.getContentType()).thenReturn("image/png");
        when(file.getOriginalFilename()).thenReturn("file.png");
        when(mediaClient.putFile(any(), any(), any())).thenReturn("url");
        doThrow(new RuntimeException("fail")).when(tempMediaRepo).saveAll(anyList());
        assertThrows(MediaUploadException.class, () -> mediaService.uploadTempFile(List.of(file), "token"));
    }

    @Test
    void testDeleteOldTempMedia() {
        doNothing().when(tempMediaRepo).deleteByCreatedAtBefore(any());
        mediaService.deleteOldTempMedia();
        verify(tempMediaRepo).deleteByCreatedAtBefore(any());
    }
} 