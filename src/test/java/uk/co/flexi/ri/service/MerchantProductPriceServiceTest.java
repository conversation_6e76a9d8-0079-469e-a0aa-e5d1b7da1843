package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.model.*;
import uk.co.flexi.ri.repository.MerchantProductPriceRepo;
import uk.co.flexi.ri.repository.MerchantRepo;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class MerchantProductPriceServiceTest {
    private CurrencyConversionService currencyConversionService;
    private MerchantProductPriceRepo merchantProductPriceRepo;
    private MerchantRepo merchantRepo;
    private MerchantProductPriceService service;

    @BeforeEach
    void setUp() {
        currencyConversionService = mock(CurrencyConversionService.class);
        merchantProductPriceRepo = mock(MerchantProductPriceRepo.class);
        merchantRepo = mock(MerchantRepo.class);
        service = new MerchantProductPriceService(currencyConversionService, merchantProductPriceRepo, merchantRepo);
    }

    @Test
    void saveToMerchantProductPrice_existingPrice_updated() {
        Inspection inspection = new Inspection();
        inspection.setTenant(1L);
        Product product = new Product();
        product.setSku("sku1");
        product.setUnitPrice(BigDecimal.TEN);
        product.setCurrencyCode("USD");
        InspectionItem item = new InspectionItem();
        item.setProduct(product);
        inspection.setInspectionItems(List.of(item));
        MerchantProductPrice existing = new MerchantProductPrice();
        existing.setSku("sku1");
        existing.setTotalUnit(BigDecimal.ONE);
        existing.setUnitPrice(BigDecimal.TEN);
        Merchant merchant = new Merchant();
        merchant.setReportingCurrency("USD");
        when(merchantProductPriceRepo.findByTenantAndSkuIn(1L, Set.of("sku1"))).thenReturn(List.of(existing));
        when(merchantRepo.findByTenantNative(1L)).thenReturn(merchant);
        when(merchantProductPriceRepo.save(any())).thenReturn(existing);
        service.saveToMerchantProductPrice(inspection);
        verify(merchantProductPriceRepo).save(any());
    }

    @Test
    void saveToMerchantProductPrice_newPrice_created() {
        Inspection inspection = new Inspection();
        inspection.setTenant(1L);
        Product product = new Product();
        product.setSku("sku2");
        product.setUnitPrice(BigDecimal.TEN);
        product.setCurrencyCode("USD");
        InspectionItem item = new InspectionItem();
        item.setProduct(product);
        inspection.setInspectionItems(List.of(item));
        Merchant merchant = new Merchant();
        merchant.setReportingCurrency("USD");
        when(merchantProductPriceRepo.findByTenantAndSkuIn(1L, Set.of("sku2"))).thenReturn(List.of());
        when(merchantRepo.findByTenantNative(1L)).thenReturn(merchant);
        when(merchantProductPriceRepo.save(any())).thenReturn(new MerchantProductPrice());
        service.saveToMerchantProductPrice(inspection);
        verify(merchantProductPriceRepo).save(any());
    }

    @Test
    void saveToMerchantProductPrice_zeroPrice_skipped() {
        Inspection inspection = new Inspection();
        inspection.setTenant(1L);
        Product product = new Product();
        product.setSku("sku3");
        product.setUnitPrice(BigDecimal.ZERO);
        product.setCurrencyCode("USD");
        InspectionItem item = new InspectionItem();
        item.setProduct(product);
        inspection.setInspectionItems(List.of(item));
        Merchant merchant = new Merchant();
        merchant.setReportingCurrency("USD");
        when(merchantProductPriceRepo.findByTenantAndSkuIn(1L, Set.of("sku3"))).thenReturn(List.of());
        when(merchantRepo.findByTenantNative(1L)).thenReturn(merchant);
        service.saveToMerchantProductPrice(inspection);
        verify(merchantProductPriceRepo, never()).save(any());
    }

    @Test
    void saveToMerchantProductPrice_currencyConversion() {
        Inspection inspection = new Inspection();
        inspection.setTenant(1L);
        Product product = new Product();
        product.setSku("sku4");
        product.setUnitPrice(BigDecimal.TEN);
        product.setCurrencyCode("EUR");
        InspectionItem item = new InspectionItem();
        item.setProduct(product);
        inspection.setInspectionItems(List.of(item));
        Merchant merchant = new Merchant();
        merchant.setReportingCurrency("USD");
        when(merchantProductPriceRepo.findByTenantAndSkuIn(1L, Set.of("sku4"))).thenReturn(List.of());
        when(merchantRepo.findByTenantNative(1L)).thenReturn(merchant);
        when(currencyConversionService.convert(BigDecimal.TEN, "EUR", "USD")).thenReturn(BigDecimal.ONE);
        when(merchantProductPriceRepo.save(any())).thenReturn(new MerchantProductPrice());
        service.saveToMerchantProductPrice(inspection);
        verify(currencyConversionService).convert(BigDecimal.TEN, "EUR", "USD");
        verify(merchantProductPriceRepo).save(any());
    }

    @Test
    void saveToMerchantProductPrice_noProducts() {
        Inspection inspection = new Inspection();
        inspection.setTenant(1L);
        inspection.setInspectionItems(List.of());
        Merchant merchant = new Merchant();
        merchant.setReportingCurrency("USD");
        when(merchantProductPriceRepo.findByTenantAndSkuIn(1L, Set.of())).thenReturn(List.of());
        when(merchantRepo.findByTenantNative(1L)).thenReturn(merchant);
        service.saveToMerchantProductPrice(inspection);
        verify(merchantProductPriceRepo, never()).save(any());
    }
} 