package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.EventQueueDTO;
import uk.co.flexi.ri.model.EventQueue;
import uk.co.flexi.ri.integration.repository.EventConfigRepo;
import uk.co.flexi.ri.repository.EventQueueRepo;

import java.util.List;

import static org.mockito.Mockito.*;

class EventQueueServiceTest {
    private ModelMapper modelMapper;
    private EventQueueRepo eventQueueRepo;
    private EventConfigRepo eventConfigRepo;
    private EventQueueService service;

    @BeforeEach
    void setUp() {
        modelMapper = mock(ModelMapper.class);
        eventQueueRepo = mock(EventQueueRepo.class);
        eventConfigRepo = mock(EventConfigRepo.class);
        service = new EventQueueService(modelMapper, eventQueueRepo, eventConfigRepo);
    }

    @Test
    void save_withIds_savesForEach() {
        EventQueueDTO dto = new EventQueueDTO();
        dto.setTenant(1L);
        dto.setEventType(EventQueue.EventType.INSP_CREATED);
        when(eventConfigRepo.findIdByTenantId(1L, "INSP_CREATED")).thenReturn(List.of(10L, 20L));
        EventQueue eventQueue = new EventQueue();
        when(modelMapper.map(dto, EventQueue.class)).thenReturn(eventQueue);
        when(eventQueueRepo.save(any())).thenReturn(eventQueue);
        service.save(dto);
        verify(eventQueueRepo, times(2)).save(any(EventQueue.class));
    }

    @Test
    void save_noIds_found_nothingSaved() {
        EventQueueDTO dto = new EventQueueDTO();
        dto.setTenant(1L);
        dto.setEventType(EventQueue.EventType.INSP_CREATED);
        when(eventConfigRepo.findIdByTenantId(1L, "INSP_CREATED")).thenReturn(List.of());
        service.save(dto);
        verify(eventQueueRepo, never()).save(any());
    }
} 