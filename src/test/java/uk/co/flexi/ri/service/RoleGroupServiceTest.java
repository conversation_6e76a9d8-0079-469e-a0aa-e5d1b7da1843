package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.RoleDTO;
import uk.co.flexi.ri.dto.RoleGroupDTO;
import uk.co.flexi.ri.model.Role;
import uk.co.flexi.ri.model.RoleGroup;
import uk.co.flexi.ri.repository.RoleGroupRepo;
import uk.co.flexi.ri.repository.RoleRepo;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class RoleGroupServiceTest {
    private RoleGroupRepo roleGroupRepo;
    private RoleRepo roleRepo;
    private ModelMapper modelMapper;
    private RoleGroupService service;

    @BeforeEach
    void setUp() {
        roleGroupRepo = mock(RoleGroupRepo.class);
        roleRepo = mock(RoleRepo.class);
        modelMapper = mock(ModelMapper.class);
        service = new RoleGroupService(roleGroupRepo, roleRepo, modelMapper);
    }

    @Test
    void getRoleGroups_mapsAll() {
        RoleGroup group = new RoleGroup();
        RoleGroupDTO dto = new RoleGroupDTO();
        Role role = new Role();
        RoleDTO roleDTO = new RoleDTO();
        when(roleGroupRepo.findAll()).thenReturn(List.of(group));
        when(modelMapper.map(group, RoleGroupDTO.class)).thenReturn(dto);
        when(roleRepo.findByRoleGroup(group)).thenReturn(List.of(role));
        when(modelMapper.map(role, RoleDTO.class)).thenReturn(roleDTO);
        List<RoleGroupDTO> result = service.getRoleGroups();
        assertEquals(1, result.size());
        assertTrue(result.get(0).getRoles().contains(roleDTO));
    }

    @Test
    void createRoleGroup_success() {
        RoleGroupDTO dto = new RoleGroupDTO();
        RoleDTO roleDTO = new RoleDTO();
        roleDTO.setId(1L);
        dto.setRoles(Set.of(roleDTO));
        RoleGroup group = new RoleGroup();
        Role role = new Role();
        role.setId(1L);
        when(modelMapper.map(dto, RoleGroup.class)).thenReturn(group);
        when(roleRepo.findById(1L)).thenReturn(Optional.of(role));
        when(roleGroupRepo.save(group)).thenReturn(group);
        when(modelMapper.map(group, RoleGroupDTO.class)).thenReturn(dto);
        RoleGroupDTO result = service.createRoleGroup(dto);
        assertSame(dto, result);
    }

    @Test
    void createRoleGroup_roleNotFound_throws() {
        RoleGroupDTO dto = new RoleGroupDTO();
        RoleDTO roleDTO = new RoleDTO();
        roleDTO.setId(2L);
        dto.setRoles(Set.of(roleDTO));
        RoleGroup group = new RoleGroup();
        when(modelMapper.map(dto, RoleGroup.class)).thenReturn(group);
        when(roleRepo.findById(2L)).thenReturn(Optional.empty());
        assertThrows(IllegalArgumentException.class, () -> service.createRoleGroup(dto));
    }

    @Test
    void findRoleGroupDTOById_found() {
        RoleGroup group = new RoleGroup();
        RoleGroupDTO dto = new RoleGroupDTO();
        Role role = new Role();
        RoleDTO roleDTO = new RoleDTO();
        when(roleGroupRepo.findByRoleGroupId("gid")).thenReturn(Optional.of(group));
        when(modelMapper.map(group, RoleGroupDTO.class)).thenReturn(dto);
        when(roleRepo.findByRoleGroup(group)).thenReturn(List.of(role));
        when(modelMapper.map(role, RoleDTO.class)).thenReturn(roleDTO);
        RoleGroupDTO result = service.findRoleGroupDTOById("gid");
        assertSame(dto, result);
        assertTrue(result.getRoles().contains(roleDTO));
    }

    @Test
    void findRoleGroupDTOById_notFound_throws() {
        when(roleGroupRepo.findByRoleGroupId("gid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.findRoleGroupDTOById("gid"));
    }

    @Test
    void findByRoleGroupId_found() {
        RoleGroup group = new RoleGroup();
        when(roleGroupRepo.findByRoleGroupId("gid")).thenReturn(Optional.of(group));
        assertSame(group, service.findByRoleGroupId("gid"));
    }

    @Test
    void findByRoleGroupId_notFound_throws() {
        when(roleGroupRepo.findByRoleGroupId("gid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.findByRoleGroupId("gid"));
    }

    @Test
    void deleteRoleGroups_found() {
        RoleGroup group = new RoleGroup();
        when(roleGroupRepo.findByRoleGroupId("gid")).thenReturn(Optional.of(group));
        doNothing().when(roleGroupRepo).delete(group);
        service.deleteRoleGroups("gid");
        verify(roleGroupRepo).delete(group);
    }

    @Test
    void deleteRoleGroups_notFound_throws() {
        when(roleGroupRepo.findByRoleGroupId("gid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.deleteRoleGroups("gid"));
    }

    @Test
    void getRoles_mapsAll() {
        Role role = new Role();
        RoleDTO roleDTO = new RoleDTO();
        when(roleRepo.findAll()).thenReturn(List.of(role));
        when(modelMapper.map(role, RoleDTO.class)).thenReturn(roleDTO);
        List<RoleDTO> result = service.getRoles();
        assertEquals(1, result.size());
        assertSame(roleDTO, result.get(0));
    }

    @Test
    void updateRoleGroup_success() {
        RoleGroup group = new RoleGroup();
        RoleGroupDTO dto = new RoleGroupDTO();
        RoleDTO roleDTO = new RoleDTO();
        roleDTO.setId(1L);
        dto.setRoles(Set.of(roleDTO));
        Role role = new Role();
        role.setId(1L);
        when(roleGroupRepo.findByRoleGroupId("gid")).thenReturn(Optional.of(group));
        when(roleRepo.findById(1L)).thenReturn(Optional.of(role));
        when(roleGroupRepo.save(group)).thenReturn(group);
        when(modelMapper.map(group, RoleGroupDTO.class)).thenReturn(dto);
        RoleGroupDTO result = service.updateRoleGroup("gid", dto);
        assertSame(dto, result);
    }

    @Test
    void updateRoleGroup_roleNotFound_throws() {
        RoleGroup group = new RoleGroup();
        RoleGroupDTO dto = new RoleGroupDTO();
        RoleDTO roleDTO = new RoleDTO();
        roleDTO.setId(2L);
        dto.setRoles(Set.of(roleDTO));
        when(roleGroupRepo.findByRoleGroupId("gid")).thenReturn(Optional.of(group));
        when(roleRepo.findById(2L)).thenReturn(Optional.empty());
        assertThrows(IllegalArgumentException.class, () -> service.updateRoleGroup("gid", dto));
    }
} 