package uk.co.flexi.ri.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class ExcelFileFormatServiceTest {
    private ExcelFileFormatService service;

    @BeforeEach
    void setUp() {
        service = new ExcelFileFormatService(new ObjectMapper());
    }

    @Test
    void getFileExtensions_returnsExcelTypes() {
        List<String> exts = service.getFileExtensions();
        assertTrue(exts.contains("xlsx"));
        assertTrue(exts.contains("xls"));
    }

    @Test
    void exportData_empty_returnsEmptyArray() {
        byte[] result = service.exportData(new ArrayList<>());
        assertNotNull(result);
        assertEquals(0, result.length);
    }

    @Test
    void exportData_and_importData_roundTrip() {
        LinkedHashMap<String, Object> row1 = new LinkedHashMap<>();
        row1.put("A", "foo");
        row1.put("B", 123);
        LinkedHashMap<String, Object> row2 = new LinkedHashMap<>();
        row2.put("A", "bar");
        row2.put("B", 456);
        List<LinkedHashMap<String, Object>> data = List.of(row1, row2);
        byte[] excel = service.exportData(data);
        assertTrue(excel.length > 0);
        List<LinkedHashMap<String, Object>> imported = service.importData(new ByteArrayInputStream(excel));
        assertEquals(2, imported.size());
        assertEquals("foo", imported.get(0).get("A"));
        assertEquals("123", imported.get(0).get("B"));
        assertEquals("bar", imported.get(1).get("A"));
        assertEquals("456", imported.get(1).get("B"));
    }

    @Test
    void exportData_throwsOnIOException() {
        ExcelFileFormatService brokenService = new ExcelFileFormatService(new ObjectMapper()) {
            @Override
            public byte[] exportData(List<LinkedHashMap<String, Object>> data) {
                throw new RuntimeException("Failed to export Excel");
            }
        };
        RuntimeException ex = assertThrows(RuntimeException.class, () -> brokenService.exportData(List.of(new LinkedHashMap<>())));
        assertTrue(ex.getMessage().contains("Failed to export Excel"));
    }

    @Test
    void importData_throwsOnIOException() {
        InputStream badStream = new InputStream() {
            @Override public int read() throws IOException { throw new IOException("fail"); }
        };
        RuntimeException ex = assertThrows(RuntimeException.class, () -> service.importData(badStream));
        assertTrue(ex.getMessage().contains("Failed to import Excel"));
    }

    @Test
    void importData_emptySheet() throws IOException {
        Workbook workbook = new XSSFWorkbook();
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        workbook.createSheet("Data");
        workbook.write(out);
        workbook.close();
        List<LinkedHashMap<String, Object>> result = service.importData(new ByteArrayInputStream(out.toByteArray()));
        assertTrue(result.isEmpty());
    }
} 