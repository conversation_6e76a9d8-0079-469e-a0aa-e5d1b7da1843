package uk.co.flexi.ri.service;

import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.query.AuditQuery;
import org.hibernate.envers.query.AuditQueryCreator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import uk.co.flexi.ri.dto.AuditEventDTO;
import uk.co.flexi.ri.dto.HistoryResDTO;
import uk.co.flexi.ri.model.AuditEvent;
import uk.co.flexi.ri.repository.AuditEventRepo;

import jakarta.persistence.EntityManager;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.mockito.ArgumentCaptor;

class AuditServiceTest {
    private AuditEventRepo auditEventRepo;
    private TenantIdentifierResolver tenantResolver;
    private EntityManager entityManager;
    private ModelMapper modelMapper;
    private AuditService service;

    @BeforeEach
    void setUp() {
        auditEventRepo = mock(AuditEventRepo.class);
        tenantResolver = mock(TenantIdentifierResolver.class);
        entityManager = mock(EntityManager.class);
        modelMapper = mock(ModelMapper.class);
        service = new AuditService("db", auditEventRepo, tenantResolver, entityManager, modelMapper);
        // Set fields that would be injected by @Value
        setField(service, "inspectionFields", List.of("field1"));
        setField(service, "inspectionItemFields", List.of("field1"));
        setField(service, "commentItemFields", List.of("field1"));
        setField(service, "mediaFields", List.of("field1"));
    }

    private void setField(Object target, String field, Object value) {
        try {
            Field f = target.getClass().getDeclaredField(field);
            f.setAccessible(true);
            f.set(target, value);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void constructor_invalidRepositoryType_throws() {
        assertThrows(IllegalArgumentException.class, () ->
            new AuditService("invalid", auditEventRepo, tenantResolver, entityManager, modelMapper));
    }

    @Test
    void saveAuditEvent_mapsAndSaves() {
        AuditEventDTO dto = new AuditEventDTO();
        AuditEvent event = new AuditEvent();
        when(modelMapper.map(dto, AuditEvent.class)).thenReturn(event);
        service.saveAuditEvent(dto);
        verify(auditEventRepo).saveEvent(event);
    }

    @Test
    void saveAuditEvents_mapsAndSaves() {
        AuditEventDTO dto1 = new AuditEventDTO();
        AuditEventDTO dto2 = new AuditEventDTO();
        AuditEvent event1 = new AuditEvent();
        AuditEvent event2 = new AuditEvent();
        when(modelMapper.map(dto1, AuditEvent.class)).thenReturn(event1);
        when(modelMapper.map(dto2, AuditEvent.class)).thenReturn(event2);

        service.saveAuditEvents(List.of(dto1, dto2));

        ArgumentCaptor<List> captor = ArgumentCaptor.forClass(List.class);
        verify(auditEventRepo).saveEvents(captor.capture());
        List<?> captured = captor.getValue();
        assertEquals(2, captured.size());
    }

    @Test
    void getCountByDate_sumsCounts() {
        LocalDate start = LocalDate.now();
        LocalDate end = start.plusDays(1);
        when(auditEventRepo.countByDateBetweenAndEventType(any(), any(), any())).thenReturn(2L, 3L);
        long result = service.getCountByDate(start, end, AuditEvent.EventType.INSPECTION_ITEM_CREATE, AuditEvent.EventType.INSPECTION_ITEM_DELETE);
        assertEquals(5L, result);
    }

    @Test
    void getCreatedCountByDate_returnsDifference() {
        LocalDate start = LocalDate.now();
        LocalDate end = start.plusDays(1);
        AuditService spyService = spy(service);
        doReturn(10L).when(spyService).getCountByDate(start, end, AuditEvent.EventType.INSPECTION_ITEM_CREATE);
        doReturn(4L).when(spyService).getCountByDate(start, end, AuditEvent.EventType.INSPECTION_ITEM_DELETE);
        assertEquals(6L, spyService.getCreatedCountByDate(start, end));
    }

    @Test
    void getCompletedCountByDate_returnsSum() {
        LocalDate start = LocalDate.now();
        LocalDate end = start.plusDays(1);
        AuditService spyService = spy(service);
        doReturn(7L).when(spyService).getCountByDate(start, end, AuditEvent.EventType.INSPECTION_ITEM_APPROVED, AuditEvent.EventType.INSPECTION_ITEM_REJECTED);
        assertEquals(7L, spyService.getCompletedCountByDate(start, end));
    }

    @Test
    void getSendForReviewCountByDate_returnsValue() {
        LocalDate start = LocalDate.now();
        LocalDate end = start.plusDays(1);
        AuditService spyService = spy(service);
        doReturn(3L).when(spyService).getCountByDate(start, end, AuditEvent.EventType.INSPECTION_ITEM_SEND_FOR_REVIEW);
        assertEquals(3L, spyService.getSendForReviewCountByDate(start, end));
    }

    @Test
    void getCategories_returnsAll() {
        List<String> categories = service.getCategories();
        assertTrue(categories.contains("INSPECTION"));
        assertTrue(categories.contains("COMMENT"));
        assertTrue(categories.contains("MEDIA"));
        assertTrue(categories.contains("INSPECTION_ITEM"));
    }

    // getAuditLogs and private methods are best tested with integration or partial mocking, but we can test basic paging and filtering logic:
    @Test
    void getAuditLogs_emptyHistory_returnsEmptyPage() {
        AuditService spyService = spy(service);
        doReturn(Collections.emptyList()).when(spyService).getAuditEntries(any(), any(), any(), any(), any(), any(), any());
        Pageable pageable = PageRequest.of(0, 10);
        Page<HistoryResDTO> page = spyService.getAuditLogs(AuditService.AuditCategory.INSPECTION, null, null, null, null, null, pageable);
        assertTrue(page.isEmpty());
        assertEquals(0, page.getTotalElements());
    }

    @Test
    void getAuditLogs_withUniqueId_filtersResults() {
        AuditService spyService = spy(service);
        HistoryResDTO dto1 = new HistoryResDTO();
        dto1.setUniqueId("A");
        HistoryResDTO dto2 = new HistoryResDTO();
        dto2.setUniqueId("B");
        doReturn(List.of(dto1, dto2)).when(spyService).getAuditEntries(any(), any(), any(), any(), any(), any(), any());
        Pageable pageable = PageRequest.of(0, 10);
        Page<HistoryResDTO> page = spyService.getAuditLogs(AuditService.AuditCategory.INSPECTION, "A", null, null, null, null, pageable);
        assertEquals(1, page.getTotalElements());
        assertEquals("A", page.getContent().get(0).getUniqueId());
    }

    @Test
    void getAuditLogs_pagingWorks() {
        AuditService spyService = spy(service);
        List<HistoryResDTO> dtos = new ArrayList<>();
        for (int i = 0; i < 15; i++) {
            HistoryResDTO dto = new HistoryResDTO();
            dto.setUniqueId("U" + i);
            dtos.add(dto);
        }
        doReturn(dtos).when(spyService).getAuditEntries(any(), any(), any(), any(), any(), any(), any());
        Pageable pageable = PageRequest.of(1, 10); // page 1, size 10
        Page<HistoryResDTO> page = spyService.getAuditLogs(AuditService.AuditCategory.INSPECTION, null, null, null, null, null, pageable);
        assertEquals(5, page.getContent().size());
        assertEquals(15, page.getTotalElements());
    }

    @Test
    void getAuditEntries_and_extractHistory_fullCoverage() {
        // Mock static AuditReaderFactory
        AuditReader reader = mock(AuditReader.class);
        AuditQueryCreator queryCreator = mock(AuditQueryCreator.class);
        AuditQuery query = mock(AuditQuery.class, RETURNS_DEEP_STUBS);
        try (MockedStatic<AuditReaderFactory> staticMock = mockStatic(AuditReaderFactory.class)) {
            staticMock.when(() -> AuditReaderFactory.get(entityManager)).thenReturn(reader);
            when(reader.createQuery()).thenReturn(queryCreator);
            when(queryCreator.forRevisionsOfEntity(any(), anyBoolean(), anyBoolean())).thenReturn(query);
            when(query.addOrder(any())).thenReturn(query);
            when(query.add(any())).thenReturn(query);

            // Prepare test data
            TestEntity entity1 = new TestEntity(1L, "A", "user1", java.time.OffsetDateTime.now());
            TestEntity entity2 = new TestEntity(1L, "B", "user2", java.time.OffsetDateTime.now());
            uk.co.flexi.ri.model.CustomRevisionEntity revEntity = new uk.co.flexi.ri.model.CustomRevisionEntity();
            revEntity.setUniqueId("ref1");
            List<Object[]> results = List.of(
                new Object[]{entity1, revEntity, org.hibernate.envers.RevisionType.ADD},
                new Object[]{entity2, revEntity, org.hibernate.envers.RevisionType.DEL}
            );
            when(query.getResultList()).thenReturn(results);
            when(tenantResolver.resolveCurrentTenantIdentifier()).thenReturn(1L);

            setField(service, "inspectionFields", List.of("value", "updatedBy"));

            // Call getAuditEntries (this will also call extractHistory, getFieldValue, getAllFields)
            List<uk.co.flexi.ri.dto.HistoryResDTO> history = service.getAuditEntries(
                TestEntity.class,
                t -> ((TestEntity) t).getId(),
                null,
                List.of("value", "updatedBy"),
                null, null, null
            );
            assertFalse(history.isEmpty());
            assertTrue(history.stream().anyMatch(h -> "DEL".equals(h.getRevisionType())));
        }
    }

    static class TestEntity {
        private Long id;
        private String value;
        private String updatedBy;
        private java.time.OffsetDateTime updatedAt;
        public TestEntity(Long id, String value, String updatedBy, java.time.OffsetDateTime updatedAt) {
            this.id = id; this.value = value; this.updatedBy = updatedBy; this.updatedAt = updatedAt;
        }
        public Long getId() { return id; }
        public String getValue() { return value; }
        public String getUpdatedBy() { return updatedBy; }
        public java.time.OffsetDateTime getUpdatedAt() { return updatedAt; }
    }
}