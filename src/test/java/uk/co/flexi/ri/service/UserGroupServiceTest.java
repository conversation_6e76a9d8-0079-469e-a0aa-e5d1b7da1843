package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.UserGroupDTO;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.model.UserGroup;
import uk.co.flexi.ri.repository.UserGroupRepo;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class UserGroupServiceTest {
    private ModelMapper modelMapper;
    private UserGroupRepo userGroupRepo;
    private AuthenticatedUserService authenticatedUserService;
    private UserGroupService service;

    @BeforeEach
    void setUp() {
        modelMapper = mock(ModelMapper.class);
        userGroupRepo = mock(UserGroupRepo.class);
        authenticatedUserService = mock(AuthenticatedUserService.class);
        service = new UserGroupService(modelMapper, userGroupRepo, authenticatedUserService);
    }

    @Test
    void findById_found() {
        UserGroup group = new UserGroup();
        when(userGroupRepo.findById(1L)).thenReturn(Optional.of(group));
        assertSame(group, service.findById(1L));
    }

    @Test
    void findById_notFound_throws() {
        when(userGroupRepo.findById(1L)).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.findById(1L));
    }

    @Test
    void findByUserGroupId_found() {
        UserGroup group = new UserGroup();
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        assertSame(group, service.findByUserGroupId("gid"));
    }

    @Test
    void findByUserGroupId_notFound_throws() {
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.findByUserGroupId("gid"));
    }

    @Test
    void getByUserGroupId_maps() {
        UserGroup group = new UserGroup();
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        assertSame(dto, service.getByUserGroupId("gid"));
    }

    @Test
    void save_external_setsPartnerName() {
        UserGroupDTO dto = new UserGroupDTO();
        dto.setVisibility(Comment.CommentVisibility.EXTERNAL);
        dto.setPartnerName("p");
        UserGroup group = new UserGroup();
        when(modelMapper.map(dto, UserGroup.class)).thenReturn(group);
        when(userGroupRepo.save(group)).thenReturn(group);
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        UserGroupDTO result = service.save(dto);
        assertEquals("p", result.getPartnerName());
    }

    @Test
    void save_internal_nullPartnerName() {
        UserGroupDTO dto = new UserGroupDTO();
        dto.setVisibility(Comment.CommentVisibility.INTERNAL);
        UserGroup group = new UserGroup();
        when(modelMapper.map(dto, UserGroup.class)).thenReturn(group);
        when(userGroupRepo.save(group)).thenReturn(group);
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        UserGroupDTO result = service.save(dto);
        assertNull(result.getPartnerName());
    }

    @Test
    void update_external_setsPartnerName() {
        UserGroup group = new UserGroup();
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        UserGroupDTO dto = new UserGroupDTO();
        dto.setVisibility(Comment.CommentVisibility.EXTERNAL);
        dto.setPartnerName("p");
        when(userGroupRepo.save(group)).thenReturn(group);
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        UserGroupDTO result = service.update("gid", dto);
        assertEquals("p", result.getPartnerName());
    }

    @Test
    void update_internal_nullPartnerName() {
        UserGroup group = new UserGroup();
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        UserGroupDTO dto = new UserGroupDTO();
        dto.setVisibility(Comment.CommentVisibility.INTERNAL);
        when(userGroupRepo.save(group)).thenReturn(group);
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        UserGroupDTO result = service.update("gid", dto);
        assertNull(result.getPartnerName());
    }

    @Test
    void findAllUserGroups_mapsAll() {
        UserGroup group = new UserGroup();
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.findAll()).thenReturn(List.of(group));
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        List<UserGroupDTO> result = service.findAllUserGroups();
        assertEquals(1, result.size());
        assertSame(dto, result.get(0));
    }

    @Test
    void findUserGroupDTOById_found() {
        UserGroup group = new UserGroup();
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        assertSame(dto, service.findUserGroupDTOById("gid"));
    }

    @Test
    void findUserGroupDTOById_notFound_throws() {
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.findUserGroupDTOById("gid"));
    }

    @Test
    void activateUserGroup_found() {
        UserGroup group = new UserGroup();
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.findUserGroupById("gid")).thenReturn(Optional.of(group));
        when(userGroupRepo.save(group)).thenReturn(group);
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        UserGroupDTO result = service.activateUserGroup("gid");
        assertSame(dto, result);
    }

    @Test
    void activateUserGroup_notFound_throws() {
        when(userGroupRepo.findUserGroupById("gid")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.activateUserGroup("gid"));
    }

    @Test
    void deactivateUserGroup_success() {
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        UserGroup userGroup = new UserGroup();
        userGroup.setUserGroupId("other");
        userDTO.setUserGroup(userGroup);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        UserGroup group = new UserGroup();
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.save(group)).thenReturn(group);
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        UserGroupDTO result = service.deactivateUserGroup("gid");
        assertSame(dto, result);
    }

    @Test
    void deactivateUserGroup_self_throws() {
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        UserGroup userGroup = new UserGroup();
        userGroup.setUserGroupId("gid");
        userDTO.setUserGroup(userGroup);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        assertThrows(IllegalArgumentException.class, () -> service.deactivateUserGroup("gid"));
    }

    @Test
    void findAll_includeInactive() {
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        userDTO.setTenant(1L);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        UserGroup group = new UserGroup();
        group.setName("group");
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.findAllUserGroup(1L)).thenReturn(List.of(group));
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        List<UserGroupDTO> result = service.findAll(true);
        assertEquals(1, result.size());
        assertSame(dto, result.get(0));
    }

    @Test
    void findAll_excludeInactive() {
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        userDTO.setTenant(1L);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        UserGroup group = new UserGroup();
        group.setName("group");
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.findAllActiveUserGroup(1L)).thenReturn(List.of(group));
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        List<UserGroupDTO> result = service.findAll(false);
        assertEquals(1, result.size());
        assertSame(dto, result.get(0));
    }

    @Test
    void getUserGroupReviewers_mapsAll() {
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        UserGroup loggedGroup = new UserGroup();
        loggedGroup.setId(1L);
        userDTO.setUserGroup(loggedGroup);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        UserGroup reviewer = new UserGroup();
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.findReviewersByUserGroupId(1L)).thenReturn(Set.of(reviewer));
        when(modelMapper.map(reviewer, UserGroupDTO.class)).thenReturn(dto);
        List<UserGroupDTO> result = service.getUserGroupReviewers();
        assertEquals(1, result.size());
        assertSame(dto, result.get(0));
    }

    @Test
    void updateReviewers_success() {
        UserGroup group = new UserGroup();
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        UserGroup reviewer = new UserGroup();
        when(userGroupRepo.findByUserGroupId("rid")).thenReturn(Optional.of(reviewer));
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.save(group)).thenReturn(group);
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        UserGroupDTO result = service.updateReviewers("gid", List.of("rid"));
        assertSame(dto, result);
    }

    @Test
    void updateReviewers_self_throws() {
        UserGroup group = new UserGroup();
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        assertThrows(IllegalArgumentException.class, () -> service.updateReviewers("gid", List.of("gid")));
    }

    @Test
    void addReviewers_success() {
        UserGroup group = new UserGroup();
        group.setReviewers(Collections.emptySet());
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        UserGroup reviewer = new UserGroup();
        when(userGroupRepo.findByUserGroupId("rid")).thenReturn(Optional.of(reviewer));
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.save(group)).thenReturn(group);
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        UserGroupDTO result = service.addReviewers("gid", List.of("rid"));
        assertSame(dto, result);
    }

    @Test
    void addReviewers_self_throws() {
        UserGroup group = new UserGroup();
        group.setReviewers(Collections.emptySet());
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        assertThrows(IllegalArgumentException.class, () -> service.addReviewers("gid", List.of("gid")));
    }

    @Test
    void addReviewers_alreadyAdded_throws() {
        UserGroup group = new UserGroup();
        group.setReviewers(Set.of(new UserGroup()));
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        assertThrows(IllegalArgumentException.class, () -> service.addReviewers("gid", List.of("rid")));
    }

    @Test
    void deleteReviewers_callsRepo() {
        UserGroup group = new UserGroup();
        group.setId(1L);
        when(userGroupRepo.findByUserGroupId("gid")).thenReturn(Optional.of(group));
        doNothing().when(userGroupRepo).deleteAllReviewers(1L);
        service.deleteReviewers("gid");
        verify(userGroupRepo).deleteAllReviewers(1L);
    }

    @Test
    void getAllReviewers_filtersAndMaps() {
        UserGroup group = new UserGroup();
        group.setName("notadmin");
        group.setReviewers(Set.of(new UserGroup()));
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.findAllWithReviewers()).thenReturn(List.of(group));
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        List<UserGroupDTO> result = service.getAllReviewers();
        assertEquals(1, result.size());
        assertSame(dto, result.get(0));
    }

    @Test
    void findAllGroups_mapsAll() {
        UserGroup group = new UserGroup();
        UserGroupDTO dto = new UserGroupDTO();
        when(userGroupRepo.findAllWithReviewers()).thenReturn(List.of(group));
        when(modelMapper.map(group, UserGroupDTO.class)).thenReturn(dto);
        List<UserGroupDTO> result = service.findAllGroups();
        assertEquals(1, result.size());
        assertSame(dto, result.get(0));
    }

    @Test
    void findAdminGroup_found() {
        UserGroup adminGroup = new UserGroup();
        when(userGroupRepo.findByNameIgnoreCase("admin")).thenReturn(Optional.of(adminGroup));
        assertSame(adminGroup, service.findAdminGroup());
    }

    @Test
    void findAdminGroup_notFound_throws() {
        when(userGroupRepo.findByNameIgnoreCase("admin")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.findAdminGroup());
    }
} 