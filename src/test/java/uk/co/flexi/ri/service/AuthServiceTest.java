package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.exception.custom.RIAuthenticationException;
import uk.co.flexi.ri.model.User;
import uk.co.flexi.ri.model.UserGroup;
import uk.co.flexi.ri.repository.UserRepo;
import uk.co.flexi.ri.security.service.JWTService;

import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class AuthServiceTest {
    @Mock JWTService jwtService;
    @Mock AuthenticationManager authenticationManager;
    @Mock UserRepo userRepo;
    @Mock Authentication authentication;

    @InjectMocks AuthService authService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        authService = new AuthService(jwtService, authenticationManager, userRepo);
    }

    @Test
    void authenticate_success() {
        AuthReqDTO req = new AuthReqDTO();
        req.setUserName("user");
        req.setPassword("pass");
        User user = new User();
        UserGroup group = new UserGroup();
        group.setUserGroupId("gid");
        user.setUserGroups(Set.of(group));
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class))).thenReturn(authentication);
        when(authentication.isAuthenticated()).thenReturn(true);
        when(userRepo.findByUserNameAndIsActive(eq("user"), eq(true))).thenReturn(java.util.List.of(user));
        when(jwtService.generateToken(eq(user), eq("gid"))).thenReturn("token");
        when(jwtService.generateRefreshToken(eq(user), eq("gid"))).thenReturn("refresh");
        AuthResDTO res = authService.authenticate(req);
        assertEquals("token", res.getAccessToken());
        assertEquals("refresh", res.getRefreshToken());
    }

    @Test
    void authenticate_notAuthenticated() {
        AuthReqDTO req = new AuthReqDTO();
        req.setUserName("user");
        req.setPassword("pass");
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class))).thenReturn(authentication);
        when(authentication.isAuthenticated()).thenReturn(false);
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> authService.authenticate(req));
        assertTrue(ex.getMessage().contains("Authentication failed"));
    }

    @Test
    void authenticate_userNotFound() {
        AuthReqDTO req = new AuthReqDTO();
        req.setUserName("user");
        req.setPassword("pass");
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class))).thenReturn(authentication);
        when(authentication.isAuthenticated()).thenReturn(true);
        when(userRepo.findByUserNameAndIsActive(eq("user"), eq(true))).thenReturn(java.util.List.of());
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> authService.authenticate(req));
        assertTrue(ex.getMessage().contains("Active user not found"));
    }

    @Test
    void authenticate_userGroupNotFound() {
        AuthReqDTO req = new AuthReqDTO();
        req.setUserName("user");
        req.setPassword("pass");
        User user = new User();
        user.setUserGroups(Set.of());
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class))).thenReturn(authentication);
        when(authentication.isAuthenticated()).thenReturn(true);
        when(userRepo.findByUserNameAndIsActive(eq("user"), eq(true))).thenReturn(java.util.List.of(user));
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> authService.authenticate(req));
        assertTrue(ex.getMessage().contains("User group not found"));
    }

    @Test
    void authenticate_badCredentials() {
        AuthReqDTO req = new AuthReqDTO();
        req.setUserName("user");
        req.setPassword("pass");
        when(authenticationManager.authenticate(any(UsernamePasswordAuthenticationToken.class))).thenThrow(new BadCredentialsException("bad creds"));
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> authService.authenticate(req));
        assertTrue(ex.getMessage().contains("Authentication failed"));
    }

    @Test
    void generateRefreshToken_success() {
        RefreshTokenReqDTO req = new RefreshTokenReqDTO();
        req.setRefreshToken("refresh");
        when(jwtService.validateToken("refresh")).thenReturn(true);
        when(jwtService.extractSubject("refresh")).thenReturn("user");
        when(jwtService.extractTenant("refresh")).thenReturn(1);
        User user = new User();
        UserGroup group = new UserGroup();
        group.setUserGroupId("gid");
        user.setUserGroups(Set.of(group));
        when(userRepo.findByUserNameAndIsActive(eq("user"), eq(1L), eq(true))).thenReturn(Optional.of(user));
        when(jwtService.generateToken(eq(user), eq("gid"))).thenReturn("token");
        when(jwtService.generateRefreshToken(eq(user), eq("gid"))).thenReturn("refresh2");
        AuthResDTO res = authService.generateRefreshToken(req);
        assertEquals("token", res.getAccessToken());
        assertEquals("refresh2", res.getRefreshToken());
    }

    @Test
    void generateRefreshToken_invalidToken() {
        RefreshTokenReqDTO req = new RefreshTokenReqDTO();
        req.setRefreshToken("refresh");
        when(jwtService.validateToken("refresh")).thenReturn(false);
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> authService.generateRefreshToken(req));
        assertTrue(ex.getMessage().contains("Invalid refresh token"));
    }

    @Test
    void generateRefreshToken_userNotFound() {
        RefreshTokenReqDTO req = new RefreshTokenReqDTO();
        req.setRefreshToken("refresh");
        when(jwtService.validateToken("refresh")).thenReturn(true);
        when(jwtService.extractSubject("refresh")).thenReturn("user");
        when(jwtService.extractTenant("refresh")).thenReturn(1);
        when(userRepo.findByUserNameAndIsActive(eq("user"), eq(1L), eq(true))).thenReturn(Optional.empty());
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> authService.generateRefreshToken(req));
        assertTrue(ex.getMessage().contains("Active user not found"));
    }

    @Test
    void generateRefreshToken_userGroupNotFound() {
        RefreshTokenReqDTO req = new RefreshTokenReqDTO();
        req.setRefreshToken("refresh");
        when(jwtService.validateToken("refresh")).thenReturn(true);
        when(jwtService.extractSubject("refresh")).thenReturn("user");
        when(jwtService.extractTenant("refresh")).thenReturn(1);
        User user = new User();
        user.setUserGroups(Set.of());
        when(userRepo.findByUserNameAndIsActive(eq("user"), eq(1L), eq(true))).thenReturn(Optional.of(user));
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> authService.generateRefreshToken(req));
        assertTrue(ex.getMessage().contains("User group not found"));
    }

    @Test
    void regenerateToken_success() {
        UpdateTokenReqDTO dto = new UpdateTokenReqDTO();
        dto.setUserName("user");
        dto.setTenant(1L);
        User user = new User();
        UserGroup group = new UserGroup();
        group.setUserGroupId("gid");
        user.setUserGroups(Set.of(group));
        when(userRepo.findByUserNameAndIsActive(eq("user"), eq(1L), eq(true))).thenReturn(Optional.of(user));
        when(jwtService.generateToken(eq(user), eq("gid"))).thenReturn("token");
        when(jwtService.generateRefreshToken(eq(user), eq("gid"))).thenReturn("refresh");
        AuthResDTO res = authService.regenerateToken(dto);
        assertEquals("token", res.getAccessToken());
        assertEquals("refresh", res.getRefreshToken());
    }

    @Test
    void regenerateToken_userNotFound() {
        UpdateTokenReqDTO dto = new UpdateTokenReqDTO();
        dto.setUserName("user");
        dto.setTenant(1L);
        when(userRepo.findByUserNameAndIsActive(eq("user"), eq(1L), eq(true))).thenReturn(Optional.empty());
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> authService.regenerateToken(dto));
        assertTrue(ex.getMessage().contains("Active user not found"));
    }

    @Test
    void regenerateToken_userGroupNotFound() {
        UpdateTokenReqDTO dto = new UpdateTokenReqDTO();
        dto.setUserName("user");
        dto.setTenant(1L);
        User user = new User();
        user.setUserGroups(Set.of());
        when(userRepo.findByUserNameAndIsActive(eq("user"), eq(1L), eq(true))).thenReturn(Optional.of(user));
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> authService.regenerateToken(dto));
        assertTrue(ex.getMessage().contains("User group not found"));
    }

    @Test
    void generateImageToken_success() {
        ImageTokenReqDTO req = new ImageTokenReqDTO();
        req.setUserId("1");
        req.setInspectionItemId("item");
        User user = new User();
        user.setTenant(2L);
        when(userRepo.findByUserId("1")).thenReturn(Optional.of(user));
        when(jwtService.generateCustomToken(eq("item"), anyMap())).thenReturn("token");
        String token = authService.generateImageToken(req);
        assertEquals("token", token);
    }

    @Test
    void generateImageToken_userNotFound() {
        ImageTokenReqDTO req = new ImageTokenReqDTO();
        req.setUserId("1");
        when(userRepo.findByUserId("1")).thenReturn(Optional.empty());
        RIAuthenticationException ex = assertThrows(RIAuthenticationException.class, () -> authService.generateImageToken(req));
        assertTrue(ex.getMessage().contains("User not found"));
    }
} 