package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.repository.view.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class DashboardServiceTest {
    private ReturnReasonViewRepo returnReasonViewRepo;
    private ItemConditionViewRepo itemConditionViewRepo;
    private InspectionCompletedViewRepo inspectionCompletedViewRepo;
    private AuditService auditService;
    private TopProductReturnViewRepo topProductReturnViewRepo;
    private TimeTrackerViewRepo timeTrackerViewRepo;
    private DashboardService service;

    @BeforeEach
    void setUp() {
        returnReasonViewRepo = mock(ReturnReasonViewRepo.class);
        itemConditionViewRepo = mock(ItemConditionViewRepo.class);
        inspectionCompletedViewRepo = mock(InspectionCompletedViewRepo.class);
        auditService = mock(AuditService.class);
        topProductReturnViewRepo = mock(TopProductReturnViewRepo.class);
        timeTrackerViewRepo = mock(TimeTrackerViewRepo.class);
        service = new DashboardService(returnReasonViewRepo, itemConditionViewRepo, inspectionCompletedViewRepo, auditService, topProductReturnViewRepo, timeTrackerViewRepo);
    }

    @Test
    void getCommonReturnReasons_level0() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        TreeMapResponse resp = new TreeMapResponse();
        resp.setValue(5L);
        when(returnReasonViewRepo.findGroupedDataByReturnReason(s, e)).thenReturn(List.of(resp));
        TreeMapResponseDTO dto = service.getCommonReturnReasons(s, e, null, 0);
        assertEquals(5L, dto.getTotal());
        assertEquals(1, dto.getData().size());
    }

    @Test
    void getCommonReturnReasons_level1_withFilter() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        TreeMapResponse resp = new TreeMapResponse();
        resp.setValue(2L);
        when(returnReasonViewRepo.findGroupedDataByProductClass(eq(s), eq(e), eq("A"))).thenReturn(List.of(resp));
        TreeMapResponseDTO dto = service.getCommonReturnReasons(s, e, "A", 1);
        assertEquals(2L, dto.getTotal());
    }

    @Test
    void getCommonReturnReasons_level2_withFilter() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        TreeMapResponse resp = new TreeMapResponse();
        resp.setValue(3L);
        when(returnReasonViewRepo.findGroupedDataByProductSubClass(eq(s), eq(e), eq("A"), eq("B"))).thenReturn(List.of(resp));
        TreeMapResponseDTO dto = service.getCommonReturnReasons(s, e, "A,B", 2);
        assertEquals(3L, dto.getTotal());
    }

    @Test
    void getCommonReturnReasons_level3_withFilter() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        TreeMapResponse resp = new TreeMapResponse();
        resp.setValue(4L);
        when(returnReasonViewRepo.findGroupedDataByProductStyle(eq(s), eq(e), eq("A"), eq("B"), eq("C"))).thenReturn(List.of(resp));
        TreeMapResponseDTO dto = service.getCommonReturnReasons(s, e, "A,B,C", 3);
        assertEquals(4L, dto.getTotal());
    }

    @Test
    void getCommonReturnReasons_level4_withFilter() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        TreeMapResponse resp = new TreeMapResponse();
        resp.setValue(6L);
        when(returnReasonViewRepo.findGroupedDataByProductSku(eq(s), eq(e), eq("A"), eq("B"), eq("C"), eq("D"))).thenReturn(List.of(resp));
        TreeMapResponseDTO dto = service.getCommonReturnReasons(s, e, "A,B,C,D", 4);
        assertEquals(6L, dto.getTotal());
    }

    @Test
    void getCommonReturnReasons_invalidLevel_throws() {
        assertThrows(IllegalArgumentException.class, () -> service.getCommonReturnReasons(LocalDate.now(), LocalDate.now(), null, 99));
    }

    @Test
    void getCommonItemCondition_returnsList() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        ItemConditionResponseDTO dto = new ItemConditionResponseDTO();
        when(itemConditionViewRepo.findGroupedData(s, e)).thenReturn(List.of(dto));
        List<ItemConditionResponseDTO> result = service.getCommonItemCondition(s, e);
        assertEquals(1, result.size());
        assertSame(dto, result.get(0));
    }

    @Test
    void getItemConditionGroupedByChannelAndCategory_productClass() throws Exception {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        Object[] row = new Object[]{"classA", InspectionItem.InspectionItemStatus.APPROVED, "{}", 1L, BigDecimal.ONE};
        when(itemConditionViewRepo.findGroupedDataByProductClass(s, e)).thenReturn(Collections.singletonList(row));
        Map<String, Map<String, Map<String, Map<String, Object>>>> result = service.getItemConditionGroupedByChannelAndCategory(s, e, "productClass");
        assertTrue(result.containsKey("classA"));
    }

    @Test
    void getInspectionStatusWithPercentage_allZero() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        when(auditService.getCreatedCountByDate(s, e)).thenReturn(0L);
        when(auditService.getCompletedCountByDate(s, e)).thenReturn(0L);
        when(auditService.getSendForReviewCountByDate(s, e)).thenReturn(0L);
        Map<String, Map<String, Object>> result = service.getInspectionStatusWithPercentage(s, e);
        assertEquals("0.00", result.get("created").get("percentage"));
        assertEquals("0.00", result.get("completed").get("percentage"));
        assertEquals("0.00", result.get("sendForReview").get("percentage"));
    }

    @Test
    void getInspectionStatusWithPercentage_nonZero() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        when(auditService.getCreatedCountByDate(s, e)).thenReturn(2L);
        when(auditService.getCompletedCountByDate(s, e)).thenReturn(3L);
        when(auditService.getSendForReviewCountByDate(s, e)).thenReturn(5L);
        Map<String, Map<String, Object>> result = service.getInspectionStatusWithPercentage(s, e);
        assertEquals("20.00", result.get("created").get("percentage"));
        assertEquals("30.00", result.get("completed").get("percentage"));
        assertEquals("50.00", result.get("sendForReview").get("percentage"));
    }

    @Test
    void getTopProductsReturns_level0_style() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        TreeMapResponse resp = new TreeMapResponse();
        resp.setValue(7L);
        when(topProductReturnViewRepo.findGroupedDataByProductStyle(s, e)).thenReturn(List.of(resp));
        TreeMapResponseDTO dto = service.getTopProductsReturns(s, e, "style", null, null, 0);
        assertEquals(7L, dto.getTotal());
    }

    @Test
    void getTopProductsReturns_level0_sku() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        TreeMapResponse resp = new TreeMapResponse();
        resp.setValue(8L);
        when(topProductReturnViewRepo.findGroupedDataByProductSku(s, e)).thenReturn(List.of(resp));
        TreeMapResponseDTO dto = service.getTopProductsReturns(s, e, "sku", null, null, 0);
        assertEquals(8L, dto.getTotal());
    }

    @Test
    void getTopProductsReturns_level0_invalidGroupBy_throws() {
        assertThrows(IllegalArgumentException.class, () -> service.getTopProductsReturns(LocalDate.now(), LocalDate.now(), "bad", null, null, 0));
    }

    @Test
    void getTopProductsReturns_level1_reason() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        TreeMapResponse resp = new TreeMapResponse();
        resp.setValue(9L);
        when(topProductReturnViewRepo.findGroupedDataByReturnReason(s, e, "f1", "f2")).thenReturn(List.of(resp));
        TreeMapResponseDTO dto = service.getTopProductsReturns(s, e, "reason", "f1", "f2", 1);
        assertEquals(9L, dto.getTotal());
    }

    @Test
    void getTopProductsReturns_level1_channel() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        TreeMapResponse resp = new TreeMapResponse();
        resp.setValue(10L);
        when(topProductReturnViewRepo.findGroupedDataByReturnChannel(s, e, "f1", "f2")).thenReturn(List.of(resp));
        TreeMapResponseDTO dto = service.getTopProductsReturns(s, e, "channel", "f1", "f2", 1);
        assertEquals(10L, dto.getTotal());
    }

    @Test
    void getTopProductsReturns_level1_invalidGroupBy_throws() {
        assertThrows(IllegalArgumentException.class, () -> service.getTopProductsReturns(LocalDate.now(), LocalDate.now(), "bad", null, null, 1));
    }

    @Test
    void getTopProductsReturns_invalidLevel_throws() {
        assertThrows(IllegalArgumentException.class, () -> service.getTopProductsReturns(LocalDate.now(), LocalDate.now(), "style", null, null, 99));
    }

    @Test
    void getInspectionCompletedStatus_returnsDTO() {
        LocalDate s = LocalDate.now(), e = LocalDate.now();
        Object[] row1 = new Object[]{"2024-01-01", "user1", 2};
        Object[] row2 = new Object[]{"2024-01-01", "user2", 3};
        Object[] row3 = new Object[]{"2024-01-02", "user1", 5};
        when(inspectionCompletedViewRepo.findCompletedStatus(s, e, "day")).thenReturn(List.of(row1, row2, row3));
        InspectionCompletedDTO dto = service.getInspectionCompletedStatus(s, e, "day");
        assertNotNull(dto);
        assertTrue(dto.getData().containsKey("2024-01-01"));
        assertTrue(dto.getData().containsKey("2024-01-02"));
        assertTrue(dto.getHighest().containsKey("date"));
        assertTrue(dto.getHighest().containsKey("total"));
    }
} 