package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.cache.CacheManager;
import org.springframework.cache.Cache;
import uk.co.flexi.ri.model.OMSConfig;
import uk.co.flexi.ri.model.OMSProvider;
import uk.co.flexi.ri.repository.OMSConfigRepo;
import uk.co.flexi.ri.repository.OMSProviderRepo;
import uk.co.flexi.sdk.oms.mao.client.config.MaoConfig;
import uk.co.flexi.sdk.oms.mao.client.config.MockConfig;
import uk.co.flexi.sdk.oms.model.OMSClientConfig;

import java.util.*;
import java.util.stream.StreamSupport;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class OMSConfigServiceTest {
    private OMSConfigRepo omsConfigRepo;
    private OMSProviderRepo omsProviderRepo;
    private CacheManager cacheManager;
    private OMSConfigService service;

    @BeforeEach
    void setUp() {
        omsConfigRepo = mock(OMSConfigRepo.class);
        omsProviderRepo = mock(OMSProviderRepo.class);
        cacheManager = mock(CacheManager.class);
        service = new OMSConfigService(omsConfigRepo, omsProviderRepo, cacheManager);
    }

    @Test
    void getConfig_maoConfig() {
        OMSProvider provider = new OMSProvider();
        OMSConfig c1 = new OMSConfig(); c1.setConfigKey("auth_url"); c1.setConfigValue("url");
        OMSConfig c2 = new OMSConfig(); c2.setConfigKey("base_path"); c2.setConfigValue("base");
        when(omsConfigRepo.findByOMSProvider(provider)).thenReturn(List.of(c1, c2));
        MaoConfig config = service.getConfig(provider, MaoConfig.class);
        assertEquals("url", config.getAuthUrl());
        assertEquals("base", config.getBasePath());
    }

    @Test
    void getConfig_mockConfig() {
        OMSProvider provider = new OMSProvider();
        OMSConfig c1 = new OMSConfig(); c1.setConfigKey("base_path"); c1.setConfigValue("base");
        OMSConfig c2 = new OMSConfig(); c2.setConfigKey("user_name"); c2.setConfigValue("user");
        OMSConfig c3 = new OMSConfig(); c3.setConfigKey("password"); c3.setConfigValue("pass");
        when(omsConfigRepo.findByOMSProvider(provider)).thenReturn(List.of(c1, c2, c3));
        MockConfig config = service.getConfig(provider, MockConfig.class);
        assertEquals("base", config.getBasePath());
        assertEquals("user", config.getUserName());
        assertEquals("pass", config.getPassword());
    }

    @Test
    void getConfig_unsupportedType_throws() {
        OMSProvider provider = new OMSProvider();
        when(omsConfigRepo.findByOMSProvider(provider)).thenReturn(List.of());
        assertThrows(UnsupportedOperationException.class, () -> service.getConfig(provider, DummyConfig.class));
    }

    @Test
    void addOMSConfig_notFound_throws() {
        when(omsProviderRepo.findByProviderName("MAO")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.addOMSConfig("MAO", Map.of()));
    }

    @Test
    void getOMSConfig_success() {
        OMSProvider provider = new OMSProvider();
        when(omsProviderRepo.findByProviderName("MAO")).thenReturn(Optional.of(provider));
        OMSConfig c1 = new OMSConfig(); c1.setConfigKey("k1"); c1.setConfigValue("v1");
        OMSConfig c2 = new OMSConfig(); c2.setConfigKey("k2"); c2.setConfigValue("v2");
        when(omsConfigRepo.findByOMSProvider(provider)).thenReturn(List.of(c1, c2));
        Map<String, String> result = service.getOMSConfig("MAO");
        assertEquals("v1", result.get("k1"));
        assertEquals("v2", result.get("k2"));
    }

    @Test
    void getOMSConfig_notFound_throws() {
        when(omsProviderRepo.findByProviderName("MAO")).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> service.getOMSConfig("MAO"));
    }

    @Test
    void getConfig_maoConfig_missingKeys_defaultsToEmpty() {
        OMSProvider provider = new OMSProvider();
        // No configs provided, all should default to ""
        when(omsConfigRepo.findByOMSProvider(provider)).thenReturn(Collections.emptyList());
        MaoConfig config = service.getConfig(provider, MaoConfig.class);
        assertEquals("", config.getAuthUrl());
        assertEquals("", config.getBasePath());
        assertEquals("", config.getUsername());
        assertEquals("", config.getPassword());
        assertEquals("", config.getBasicAuthUser());
        assertEquals("", config.getBasicAuthPassword());
        assertEquals("", config.getSearchOrganizations());
        assertEquals("", config.getProductOrganization());
    }

    @Test
    void getConfig_mockConfig_missingKeys_defaultsToEmpty() {
        OMSProvider provider = new OMSProvider();
        // No configs provided, all should default to ""
        when(omsConfigRepo.findByOMSProvider(provider)).thenReturn(Collections.emptyList());
        MockConfig config = service.getConfig(provider, MockConfig.class);
        assertEquals("", config.getBasePath());
        assertEquals("", config.getUserName());
        assertEquals("", config.getPassword());
    }

    @Test
    void addOMSConfig_happyPath_updatesAndDeletesAndEvictsCache() {
        OMSProvider provider = new OMSProvider();
        provider.setProviderName("MAO");
        provider.setTenant(1L);

        // Existing configs in DB
        OMSConfig existing1 = new OMSConfig();
        existing1.setConfigKey("k1");
        existing1.setConfigValue("old1");
        existing1.setOMSProvider(provider);

        OMSConfig existing2 = new OMSConfig();
        existing2.setConfigKey("oldKey");
        existing2.setConfigValue("toDelete");
        existing2.setOMSProvider(provider);

        List<OMSConfig> existingList = Arrays.asList(existing1, existing2);

        // Input configs to add/update
        Map<String, String> input = new HashMap<>();
        input.put("k1", "v1"); // update existing
        input.put("k2", "v2"); // new

        when(omsProviderRepo.findByProviderName("MAO")).thenReturn(Optional.of(provider));
        when(omsConfigRepo.findByOMSProvider(provider)).thenReturn(existingList);
        when(omsConfigRepo.saveAll(anyList())).thenAnswer(invocation -> invocation.getArgument(0));

        // Mock cache
        Cache cache = mock(Cache.class);
        when(cacheManager.getCache("omsClients")).thenReturn(cache);

        Map<String, String> result = service.addOMSConfig("MAO", input);

        // Should update k1, add k2, delete oldKey, and evict cache
        assertEquals("v1", result.get("k1"));
        assertEquals("v2", result.get("k2"));
        assertEquals(2, result.size());

        verify(omsConfigRepo).saveAll(argThat(iterable ->
            StreamSupport.stream(iterable.spliterator(), false)
                .anyMatch(c -> "k1".equals(((OMSConfig) c).getConfigKey()) && "v1".equals(((OMSConfig) c).getConfigValue())) &&
            StreamSupport.stream(iterable.spliterator(), false)
                .anyMatch(c -> "k2".equals(((OMSConfig) c).getConfigKey()) && "v2".equals(((OMSConfig) c).getConfigValue()))
        ));
        verify(omsConfigRepo).deleteAll(argThat(iterable ->
            StreamSupport.stream(iterable.spliterator(), false)
                .anyMatch(c -> "oldKey".equals(((OMSConfig) c).getConfigKey()))
        ));
        verify(cache).evictIfPresent("MAO_1");
    }

    static class DummyConfig implements OMSClientConfig {}
}