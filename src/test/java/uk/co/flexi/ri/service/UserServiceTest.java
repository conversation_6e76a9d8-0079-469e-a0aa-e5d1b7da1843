package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.exception.custom.UserNotFoundException;
import uk.co.flexi.ri.model.*;
import uk.co.flexi.ri.repository.UserRepo;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class UserServiceTest {
    @Mock ModelMapper modelMapper;
    @Mock AuthenticatedUserService authenticatedUserService;
    @Mock UserRepo userRepo;
    @Mock PasswordEncoder passwordEncoder;
    @Mock MediaService mediaService;
    @Mock UserGroupService userGroupService;
    @Mock RoleGroupService roleGroupService;
    @Mock AuthProviderService authProviderService;
    @Mock MultipartFile multipartFile;

    @InjectMocks UserService userService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        userService = new UserService(modelMapper, authenticatedUserService, userRepo, passwordEncoder, mediaService, userGroupService, roleGroupService, authProviderService);
    }

    @Test
    void testSave() {
        UserDTO userDTO = new UserDTO();
        userDTO.setPassword("pass");
        userDTO.setUserGroup("groupId");
        userDTO.setRoleGroup("roleId");
        AuthProviderDTO authProviderDTO = new AuthProviderDTO();
        authProviderDTO.setProvider(AuthProvider.Provider.GOOGLE);
        userDTO.setAuthProvider(authProviderDTO);
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setMerchantId(1L);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        User user = new User();
        user.setPassword("pass");
        when(modelMapper.map(userDTO, User.class)).thenReturn(user);
        when(passwordEncoder.encode("pass")).thenReturn("encoded");
        UserGroup userGroup = new UserGroup();
        userGroup.setName("groupName");
        when(userGroupService.findByUserGroupId("groupId")).thenReturn(userGroup);
        RoleGroup roleGroup = new RoleGroup();
        roleGroup.setName("roleName");
        roleGroup.setRoleGroupId("roleId");
        when(roleGroupService.findByRoleGroupId("roleId")).thenReturn(roleGroup);
        AuthProvider authProvider = new AuthProvider();
        authProvider.setProvider(AuthProvider.Provider.GOOGLE);
        when(authProviderService.findByProvider(AuthProvider.Provider.GOOGLE)).thenReturn(authProvider);
        user.setUserGroups(Set.of(userGroup));
        user.setRoleGroup(roleGroup);
        user.setAuthProvider(authProvider);
        user.setMerchant(new Merchant());
        when(userRepo.save(any())).thenReturn(user);
        UserDTO mappedDto = new UserDTO();
        when(modelMapper.map(user, UserDTO.class)).thenReturn(mappedDto);
        mappedDto.setUserGroup("groupName");
        mappedDto.setRoleGroup("roleName");
        UserDTO result = userService.save(userDTO);
        assertEquals("groupName", result.getUserGroup());
        assertEquals("roleName", result.getRoleGroup());
    }

    @Test
    void testUpdate_nonAdmin() {
        User user = new User();
        user.setUserGroups(Set.of(new UserGroup()));
        RoleGroup roleGroup = new RoleGroup();
        roleGroup.setRoleGroupId("roleId");
        user.setRoleGroup(roleGroup);
        when(userRepo.findByUserId("id")).thenReturn(Optional.of(user));
        UserDTO userDTO = new UserDTO();
        userDTO.setRoleGroup("roleId");
        userDTO.setUserGroup("groupId");
        AuthProviderDTO authProviderDTO = new AuthProviderDTO();
        authProviderDTO.setProvider(AuthProvider.Provider.GOOGLE);
        userDTO.setAuthProvider(authProviderDTO);
        UserGroup userGroup = new UserGroup();
        userGroup.setName("groupName");
        when(userGroupService.findByUserGroupId("groupId")).thenReturn(userGroup);
        RoleGroup newRoleGroup = new RoleGroup();
        newRoleGroup.setName("roleName");
        newRoleGroup.setRoleGroupId("roleId");
        when(roleGroupService.findByRoleGroupId("roleId")).thenReturn(newRoleGroup);
        AuthProvider authProvider = new AuthProvider();
        authProvider.setProvider(AuthProvider.Provider.GOOGLE);
        when(authProviderService.findByProvider(AuthProvider.Provider.GOOGLE)).thenReturn(authProvider);
        user.setUserGroups(Set.of(userGroup));
        user.setRoleGroup(newRoleGroup);
        user.setAuthProvider(authProvider);
        when(userRepo.save(any())).thenReturn(user);
        UserDTO mappedDto = new UserDTO();
        when(modelMapper.map(user, UserDTO.class)).thenReturn(mappedDto);
        mappedDto.setUserGroup("groupName");
        mappedDto.setRoleGroup("roleName");
        UserDTO result = userService.update("id", userDTO);
        assertEquals("groupName", result.getUserGroup());
        assertEquals("roleName", result.getRoleGroup());
    }

    @Test
    void testFindAll_includeInactive() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setTenant(1L);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        User user = new User();
        UserGroup group = new UserGroup();
        group.setName("groupName");
        user.setUserGroups(Set.of(group));
        RoleGroup roleGroup = new RoleGroup();
        roleGroup.setName("roleName");
        user.setRoleGroup(roleGroup);
        Page<User> page = new PageImpl<>(List.of(user));
        when(userRepo.findAllUsers(1L, Pageable.unpaged())).thenReturn(page);
        when(modelMapper.map(user, UserDTO.class)).thenReturn(new UserDTO());
        Page<UserDTO> result = userService.findAll(true, Pageable.unpaged());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    void testFindAll_excludeInactive() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        User user = new User();
        UserGroup group = new UserGroup();
        group.setName("groupName");
        user.setUserGroups(Set.of(group));
        RoleGroup roleGroup = new RoleGroup();
        roleGroup.setName("roleName");
        user.setRoleGroup(roleGroup);
        Page<User> page = new PageImpl<>(List.of(user));
        when(userRepo.findAll(Pageable.unpaged())).thenReturn(page);
        when(modelMapper.map(user, UserDTO.class)).thenReturn(new UserDTO());
        Page<UserDTO> result = userService.findAll(false, Pageable.unpaged());
        assertEquals(1, result.getTotalElements());
    }

    @Test
    void testFindByUserId_found() {
        User user = new User();
        UserGroup group = new UserGroup();
        group.setName("groupName");
        user.setUserGroups(Set.of(group));
        RoleGroup roleGroup = new RoleGroup();
        roleGroup.setName("roleName");
        user.setRoleGroup(roleGroup);
        when(userRepo.findByUserId("id")).thenReturn(Optional.of(user));
        UserDTO dto = new UserDTO();
        when(modelMapper.map(user, UserDTO.class)).thenReturn(dto);
        UserDTO result = userService.findByUserId("id");
        assertEquals(dto, result);
    }

    @Test
    void testFindByUserId_notFound() {
        when(userRepo.findByUserId("id")).thenReturn(Optional.empty());
        assertThrows(UserNotFoundException.class, () -> userService.findByUserId("id"));
    }

    @Test
    void testActivateUser_found() {
        User user = new User();
        when(userRepo.findUserById("id")).thenReturn(Optional.of(user));
        when(userRepo.save(user)).thenReturn(user);
        UserDTO dto = new UserDTO();
        when(modelMapper.map(user, UserDTO.class)).thenReturn(dto);
        UserDTO result = userService.activateUser("id");
        assertEquals(dto, result);
    }

    @Test
    void testActivateUser_notFound() {
        when(userRepo.findUserById("id")).thenReturn(Optional.empty());
        assertThrows(UserNotFoundException.class, () -> userService.activateUser("id"));
    }

    @Test
    void testDeactivateUser_self() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setUserId("id");
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        assertThrows(IllegalArgumentException.class, () -> userService.deactivateUser("id"));
    }

    @Test
    void testDeactivateUser_other() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setUserId("other");
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        User user = new User();
        when(userRepo.findByUserId("id")).thenReturn(Optional.of(user));
        when(userRepo.save(user)).thenReturn(user);
        UserDTO dto = new UserDTO();
        when(modelMapper.map(user, UserDTO.class)).thenReturn(dto);
        UserDTO result = userService.deactivateUser("id");
        assertEquals(dto, result);
    }

    @Test
    void testFindById_found() {
        User user = new User();
        when(userRepo.findById(1L)).thenReturn(Optional.of(user));
        assertEquals(user, userService.findById(1L));
    }

    @Test
    void testFindById_notFound() {
        when(userRepo.findById(1L)).thenReturn(Optional.empty());
        assertThrows(UserNotFoundException.class, () -> userService.findById(1L));
    }

    @Test
    void testFindByUserName_found() {
        User user = new User();
        AuthProvider authProvider = new AuthProvider();
        authProvider.setProvider(AuthProvider.Provider.GOOGLE);
        authProvider.setAuthUrl("url");
        user.setAuthProvider(authProvider);
        user.setUserName("uname");
        when(userRepo.findByUserNameAndIsActive("uname", true)).thenReturn(List.of(user));
        UserLoginDTO dto = userService.findByUserName("uname");
        assertEquals("GOOGLE", dto.getAuthProvider());
        assertEquals("uname", dto.getUserName());
    }

    @Test
    void testFindByUserName_notFound() {
        when(userRepo.findByUserNameAndIsActive("uname", true)).thenReturn(Collections.emptyList());
        assertThrows(UserNotFoundException.class, () -> userService.findByUserName("uname"));
    }

    @Test
    void testFindUserByUserName_found() {
        User user = new User();
        when(userRepo.findByUserNameAndIsActive("uname", true)).thenReturn(List.of(user));
        UserDTO dto = new UserDTO();
        when(modelMapper.map(user, UserDTO.class)).thenReturn(dto);
        UserDTO result = userService.findUserByUserName("uname");
        assertEquals(dto, result);
    }

    @Test
    void testFindUserByUserName_notFound() {
        when(userRepo.findByUserNameAndIsActive("uname", true)).thenReturn(Collections.emptyList());
        assertThrows(UserNotFoundException.class, () -> userService.findUserByUserName("uname"));
    }

    @Test
    void testGetLoggedInUserDetails_found() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setId(1L);
        authUser.setReportingCurrency("USD");
        UserGroup userGroup = new UserGroup();
        userGroup.setName("groupName");
        authUser.setUserGroup(userGroup);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        User user = new User();
        RoleGroup roleGroup = new RoleGroup();
        Role role = new Role();
        roleGroup.setRoles(Set.of(role));
        user.setRoleGroup(roleGroup);
        when(userRepo.findById(1L)).thenReturn(Optional.of(user));
        UserDetailDTO userDetailDTO = new UserDetailDTO();
        when(modelMapper.map(user, UserDetailDTO.class)).thenReturn(userDetailDTO);
        when(modelMapper.map(role, RoleDTO.class)).thenReturn(new RoleDTO());
        UserDetailDTO result = userService.getLoggedInUserDetails();
        assertEquals(userDetailDTO, result);
    }

    @Test
    void testGetLoggedInUserDetails_notFound() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setId(1L);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        when(userRepo.findById(1L)).thenReturn(Optional.empty());
        assertThrows(UserNotFoundException.class, () -> userService.getLoggedInUserDetails());
    }

    @Test
    void testUpdateLoggedInUserTimeZone_found() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setId(1L);
        UserGroup userGroup = new UserGroup();
        userGroup.setName("groupName");
        authUser.setUserGroup(userGroup);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        User user = new User();
        when(userRepo.findById(1L)).thenReturn(Optional.of(user));
        when(userRepo.save(user)).thenReturn(user);
        UserDetailDTO userDetailDTO = new UserDetailDTO();
        when(modelMapper.map(user, UserDetailDTO.class)).thenReturn(userDetailDTO);
        UserDetailDTO result = userService.updateLoggedInUserTimeZone("Asia/Kolkata");
        assertEquals(userDetailDTO, result);
    }

    @Test
    void testUpdateLoggedInUserTimeZone_notFound() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setId(1L);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        when(userRepo.findById(1L)).thenReturn(Optional.empty());
        assertThrows(UserNotFoundException.class, () -> userService.updateLoggedInUserTimeZone("Asia/Kolkata"));
    }

    @Test
    void testUpdateProfilePicture_success() {
        when(multipartFile.isEmpty()).thenReturn(false);
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setId(1L);
        UserGroup userGroup = new UserGroup();
        userGroup.setName("groupName");
        authUser.setUserGroup(userGroup);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        User user = new User();
        when(userRepo.findById(1L)).thenReturn(Optional.of(user));
        Media media = new Media();
        when(mediaService.uploadProfileImage(multipartFile)).thenReturn(media);
        when(userRepo.save(user)).thenReturn(user);
        UserDetailDTO userDetailDTO = new UserDetailDTO();
        when(modelMapper.map(user, UserDetailDTO.class)).thenReturn(userDetailDTO);
        UserDetailDTO result = userService.updateProfilePicture(multipartFile);
        assertEquals(userDetailDTO, result);
    }

    @Test
    void testUpdateProfilePicture_emptyFile() {
        when(multipartFile == null || multipartFile.isEmpty()).thenReturn(true);
        assertThrows(IllegalArgumentException.class, () -> userService.updateProfilePicture(multipartFile));
    }

    @Test
    void testRemoveProfilePicture_success() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setId(1L);
        UserGroup userGroup = new UserGroup();
        userGroup.setName("groupName");
        authUser.setUserGroup(userGroup);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        User user = new User();
        when(userRepo.findById(1L)).thenReturn(Optional.of(user));
        when(userRepo.save(user)).thenReturn(user);
        UserDetailDTO userDetailDTO = new UserDetailDTO();
        when(modelMapper.map(user, UserDetailDTO.class)).thenReturn(userDetailDTO);
        UserDetailDTO result = userService.removeProfilePicture();
        assertEquals(userDetailDTO, result);
    }

    @Test
    void testRemoveProfilePicture_notFound() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setId(1L);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        when(userRepo.findById(1L)).thenReturn(Optional.empty());
        assertThrows(UserNotFoundException.class, () -> userService.removeProfilePicture());
    }

    @Test
    void testUpdateLoggedInUserLanguage_found() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setId(1L);
        UserGroup userGroup = new UserGroup();
        userGroup.setName("groupName");
        authUser.setUserGroup(userGroup);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        User user = new User();
        when(userRepo.findById(1L)).thenReturn(Optional.of(user));
        when(userRepo.save(user)).thenReturn(user);
        UserDetailDTO userDetailDTO = new UserDetailDTO();
        when(modelMapper.map(user, UserDetailDTO.class)).thenReturn(userDetailDTO);
        UserDetailDTO result = userService.updateLoggedInUserLanguage("en");
        assertEquals(userDetailDTO, result);
    }

    @Test
    void testUpdateLoggedInUserLanguage_notFound() {
        AuthenticatedUserDTO authUser = new AuthenticatedUserDTO();
        authUser.setId(1L);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(authUser);
        when(userRepo.findById(1L)).thenReturn(Optional.empty());
        assertThrows(UserNotFoundException.class, () -> userService.updateLoggedInUserLanguage("en"));
    }

    @Test
    void testGetAllMerchantByUserName() {
        User user = new User();
        Merchant merchant = new Merchant();
        user.setMerchant(merchant);
        when(userRepo.findByUserNameAndIsActive("uname", true)).thenReturn(List.of(user));
        MerchantDTO merchantDTO = new MerchantDTO();
        when(modelMapper.map(merchant, MerchantDTO.class)).thenReturn(merchantDTO);
        List<MerchantDTO> result = userService.getAllMerchantByUserName("uname");
        assertEquals(List.of(merchantDTO), result);
    }

    @Test
    void testSearchUserName() {
        List<String> expectedUserNames = Arrays.asList("alice", "bob", "charlie");
        when(userRepo.findUserNamesBySearch("searchTerm")).thenReturn(expectedUserNames);
        List<String> result = userService.searchUserName("searchTerm");
        assertEquals(expectedUserNames, result);
        verify(userRepo).findUserNamesBySearch("searchTerm");
    }
} 