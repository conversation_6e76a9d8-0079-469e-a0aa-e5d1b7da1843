package uk.co.flexi.ri.service;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.model.*;
import uk.co.flexi.ri.repository.InspectionItemRepo;
import uk.co.flexi.ri.repository.TempMediaRepo;

import java.util.List;

import uk.co.flexi.ri.model.InspectionItem;

import java.io.IOException;
import java.util.NoSuchElementException;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
class InspectionItemServiceTest {

    @Mock
    InspectionItemRepo inspectionItemRepo;

    @Mock
    InspectionService inspectionService;

    @Mock
    ModelMapper modelMapper;

    @Mock
    ObjectMapper objectMapper;

    @Mock
    AuthenticatedUserService authenticatedUserService;

    @Mock
    ItemConditionService itemConditionService;

    @Mock
    ProductService productService;

    @Mock
    ReturnReasonService returnReasonService;

    @Mock
    EventPublisher eventPublisher;

    @Mock
    TempMediaRepo tempMediaRepo;

    InspectionItemService inspectionItemService;

    @BeforeEach
    void setUp() {
        inspectionItemService = new InspectionItemService(
                inspectionItemRepo,
                inspectionService,
                modelMapper,
                objectMapper,
                authenticatedUserService,
                itemConditionService,
                productService,
                returnReasonService,
                eventPublisher,
                tempMediaRepo
        );
    }

    @Test
    void findByInspection_shouldReturnListFromRepo() {
        //Arrange
        Inspection inspection = new Inspection();
        InspectionItem item1 = new InspectionItem();
        InspectionItem item2 = new InspectionItem();

        List<InspectionItem> expectedItems = List.of(item1, item2);
        when(inspectionItemRepo.findByInspection(inspection)).thenReturn(expectedItems);

        //Act
        List<InspectionItem> actualItems = inspectionItemService.findByInspection(inspection);

        //Assert
        assertEquals(expectedItems, actualItems);
        verify(inspectionItemRepo).findByInspection(inspection);
    }


    @Test
    void update_shouldMapAndSaveAndReturnDto() throws IOException {
        // Arrange
        String itemId = "item123";
        InspectionItem originalItem = new InspectionItem();
        InspectionItem mergedItem = new InspectionItem();
        InspectionItem savedItem = new InspectionItem();
        InspectionItemDTO dto = new InspectionItemDTO();
        InspectionItemUpdateReqDTO updateReq = new InspectionItemUpdateReqDTO();
        JsonNode jsonNode = mock(JsonNode.class);
        updateReq.setData(jsonNode);
        ObjectReader objectReader = mock(ObjectReader.class);

        when(inspectionItemRepo.findByInspectionItemId(itemId)).thenReturn(Optional.of(originalItem));
        when(objectMapper.readerForUpdating(originalItem)).thenReturn(objectReader);
        when(objectReader.readValue(jsonNode)).thenReturn(mergedItem);
        when(inspectionItemRepo.save(mergedItem)).thenReturn(savedItem);
        when(modelMapper.map(savedItem, InspectionItemDTO.class)).thenReturn(dto);

        // Act
        InspectionItemDTO result = inspectionItemService.update(itemId, updateReq);

        // Assert
        assertEquals(dto, result);
        verify(objectMapper).readerForUpdating(originalItem);
        verify(objectReader).readValue(jsonNode);
        verify(inspectionItemRepo).save(mergedItem);
        verify(eventPublisher).publish(savedItem, EventQueue.EventType.INSP_ITEM_RETURN_REASON_UPDATED);
    }

    @Test
    void update_whenNotFound_shouldThrow() {
        // Arrange
        String inspectionItemId = "missing";
        when(inspectionItemRepo.findByInspectionItemId(inspectionItemId)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(NoSuchElementException.class, () -> inspectionItemService.update(inspectionItemId, null));
    }

    @Test
    void updateStatus_whenApproved_shouldPublishApprovedAndCompleteInspection() {
        // Arrange
        String itemId = "item123";

        Inspection inspection = new Inspection();
        inspection.setId(1L);
        inspection.setInspectionId("insp1");

        InspectionItem originalItem = new InspectionItem();
        originalItem.setInspectionItemId(itemId);
        originalItem.setInspection(inspection);
        originalItem.setItemStatus(InspectionItem.InspectionItemStatus.APPROVED);

        // Stub repository lookups and saves
        when(inspectionItemRepo.findByInspectionItemId(itemId))
                .thenReturn(Optional.of(originalItem));
        when(inspectionItemRepo.save(originalItem))
                .thenReturn(originalItem);
        when(inspectionItemRepo.findByInspection(inspection))
                .thenReturn(List.of(originalItem));

        // Stub authenticated user context
        AuthenticatedUserDTO userDto = new AuthenticatedUserDTO();
        userDto.setMerchantId(1L);
        userDto.setUserName("tester");
        when(authenticatedUserService.getAuthenticatedUserDetails())
                .thenReturn(userDto);

        // Stub condition lookup
        InspectionItemConditionDTO conditionDto = new InspectionItemConditionDTO();
        conditionDto.setConditionType(InspectionItemCondition.ConditionType.APPROVED);
        when(itemConditionService.findById("insp1"))
                .thenReturn(conditionDto);

        // Stub model mapping
        InspectionItemDTO expectedDto = new InspectionItemDTO();
        expectedDto.setInspectionItemId(itemId);
        expectedDto.setItemStatus(InspectionItem.InspectionItemStatus.APPROVED);
        when(modelMapper.map(originalItem, InspectionItemDTO.class))
                .thenReturn(expectedDto);

        // Prepare request object
        ItemStatusUpdateReqDTO statusReq = new ItemStatusUpdateReqDTO();
        statusReq.setStatus(InspectionItem.InspectionItemStatus.APPROVED);
        statusReq.setItemConditionId("insp1");

        // Act
        InspectionItemDTO resultDto = inspectionItemService.updateStatus(itemId, statusReq);

        // Assert: verify return value and interactions
        assertNotNull(resultDto);
        assertEquals(InspectionItem.InspectionItemStatus.APPROVED, resultDto.getItemStatus());

        verify(inspectionItemRepo).save(originalItem);
        verify(eventPublisher).publish(originalItem, EventQueue.EventType.INSP_ITEM_APPROVED);
        verify(inspectionService).updateStatus(1L, Inspection.InspectionStatus.COMPLETED);
        verify(eventPublisher).publish(inspection, EventQueue.EventType.INSP_COMPLETED);
        verify(modelMapper).map(originalItem, InspectionItemDTO.class);
    }

    @Test
    void updateStatus_whenRejected_shouldPublishRejectedButNotCompleteInspection() {
        // Arrange
        String itemId = "item456";

        Inspection inspection = new Inspection();
        inspection.setId(2L);
        inspection.setInspectionId("insp2");

        InspectionItem originalItem = new InspectionItem();
        originalItem.setInspectionItemId(itemId);
        originalItem.setInspection(inspection);

        // Stub repository lookup and save
        when(inspectionItemRepo.findByInspectionItemId(itemId))
                .thenReturn(Optional.of(originalItem));
        when(inspectionItemRepo.save(originalItem))
                .thenReturn(originalItem);
        when(inspectionItemRepo.findByInspection(inspection))
                .thenReturn(List.of(originalItem, new InspectionItem()));

        // Stub authenticated user context
        AuthenticatedUserDTO userDto = new AuthenticatedUserDTO();
        userDto.setMerchantId(1L);
        userDto.setUserName("tester2");
        when(authenticatedUserService.getAuthenticatedUserDetails())
                .thenReturn(userDto);

        // Stub condition lookup
        InspectionItemConditionDTO conditionDto = new InspectionItemConditionDTO();
        conditionDto.setConditionType(InspectionItemCondition.ConditionType.REJECTED);
        when(itemConditionService.findById("insp2"))
                .thenReturn(conditionDto);

        // Stub model mapping
        InspectionItemDTO expectedDto = new InspectionItemDTO();
        expectedDto.setInspectionItemId(itemId);
        expectedDto.setItemStatus(InspectionItem.InspectionItemStatus.REJECTED);
        when(modelMapper.map(originalItem, InspectionItemDTO.class))
                .thenReturn(expectedDto);

        // Prepare status update request
        ItemStatusUpdateReqDTO statusReq = new ItemStatusUpdateReqDTO();
        statusReq.setStatus(InspectionItem.InspectionItemStatus.REJECTED);
        statusReq.setItemConditionId("insp2");

        // Act
        InspectionItemDTO resultDto = inspectionItemService.updateStatus(itemId, statusReq);

        // Assert: verify DTO and interactions
        assertNotNull(resultDto);
        assertEquals(InspectionItem.InspectionItemStatus.REJECTED, resultDto.getItemStatus());

        verify(inspectionItemRepo).save(originalItem);
        verify(eventPublisher).publish(originalItem, EventQueue.EventType.INSP_ITEM_REJECTED);
        verify(inspectionService).updateStatus(2L, Inspection.InspectionStatus.IN_PROGRESS);
        verify(eventPublisher, never())
                .publish(inspection, EventQueue.EventType.INSP_COMPLETED);
        verify(modelMapper)
                .map(originalItem, InspectionItemDTO.class);
    }


    @Test
    void getReturnReasons_shouldReturnListOfReasons() {
        // Arrange: stub returnReasonService to return DTOs
        ReturnReasonDTO dto1 = new ReturnReasonDTO();
        dto1.setReason("Reason A");
        ReturnReasonDTO dto2 = new ReturnReasonDTO();
        dto2.setReason("Reason B");
        when(returnReasonService.findAll())
                .thenReturn(List.of(dto1, dto2));

        // Act
        List<String> reasons = inspectionItemService.getReturnReasons();

        // Assert
        assertNotNull(reasons);
        assertEquals(2, reasons.size());
        assertEquals(List.of("Reason A", "Reason B"), reasons);

        // Verify that the service called the repository layer
        verify(returnReasonService).findAll();
    }

    @Test
    void findByProductInstruction_shouldReturnInstructionDto() {
        // Arrange
        String itemId = "item789";
        String lang = "en";

        // Create product with class & subclass
        Product product = new Product();
        product.setProductClass("classA");
        product.setProductSubclass("subA");

        // Create inspection item pointing to product
        InspectionItem item = new InspectionItem();
        item.setInspectionItemId(itemId);
        item.setProduct(product);

        // Stub repository to return the item
        when(inspectionItemRepo.findByInspectionItemId(itemId))
                .thenReturn(Optional.of(item));

        // Stub productService to return a dummy InstructionDTO
        InstructionDTO expectedInstr = new InstructionDTO();
        expectedInstr.setContent("Use it carefully");
        when(productService.findByProductDetails("classA", "subA", lang))
                .thenReturn(expectedInstr);

        // Act
        InstructionDTO result = inspectionItemService.findByProductInstruction(itemId, lang);

        // Assert
        assertNotNull(result, "Result should not be null");
        assertEquals("Use it carefully", result.getContent());
        verify(inspectionItemRepo).findByInspectionItemId(itemId);
        verify(productService).findByProductDetails("classA", "subA", lang);
    }

    @Test
    void findByProductInstruction_whenProductDetailsMissing_shouldThrow() {
        // Arrange
        String itemId = "item000";
        InspectionItem item = new InspectionItem();
        item.setInspectionItemId(itemId);

        Product product = new Product();

        item.setProduct(product);

        when(inspectionItemRepo.findByInspectionItemId(itemId))
                .thenReturn(Optional.of(item));

        // Act & Assert
        NoSuchElementException ex = assertThrows(
                NoSuchElementException.class,
                () -> inspectionItemService.findByProductInstruction(itemId, "en")
        );
        assertTrue(ex.getMessage().contains("Instruction not found for this inspection item"));
    }

    @Test
    void findByProductInstruction_whenItemNotFound_shouldThrow() {
        // Arrange
        String missingId = "noSuchItem";
        when(inspectionItemRepo.findByInspectionItemId(missingId))
                .thenReturn(Optional.empty());

        // Act & Assert
        NoSuchElementException ex = assertThrows(
                NoSuchElementException.class,
                () -> inspectionItemService.findByProductInstruction(missingId, "en")
        );
        assertTrue(ex.getMessage().contains(missingId));
    }

    @Test
    void getTempImages_shouldReturnMappedTempMediaDtos() {
        // Arrange
        String itemId = "itemABC";

        // Stub authenticated user context
        AuthenticatedUserDTO userDto = new AuthenticatedUserDTO();
        userDto.setTenant(1l);
        when(authenticatedUserService.getAuthenticatedUserDetails())
                .thenReturn(userDto);

        // Prepare some TempMedia entities
        TempMedia media1 = new TempMedia();
        media1.setInspectionItemId(itemId);
        media1.setTenant(1L);

        TempMedia media2 = new TempMedia();
        media2.setInspectionItemId(itemId);
        media2.setTenant(1l);

        List<TempMedia> mediaEntities = List.of(media1, media2);

        // Stub repository to return these
        when(tempMediaRepo.findByInspectionItemIdAndTenant(itemId, 1L))
                .thenReturn(mediaEntities);

        // Prepare corresponding DTOs
        TempMediaDTO dto1 = new TempMediaDTO();
        dto1.setUserId("m1");
        TempMediaDTO dto2 = new TempMediaDTO();
        dto2.setUserId("m2");

        // Stub model mapping for each entity
        when(modelMapper.map(media1, TempMediaDTO.class)).thenReturn(dto1);
        when(modelMapper.map(media2, TempMediaDTO.class)).thenReturn(dto2);

        // Act
        List<TempMediaDTO> result = inspectionItemService.getTempImages(itemId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(List.of(dto1, dto2), result);

        // Verify interactions
        verify(authenticatedUserService).getAuthenticatedUserDetails();
        verify(tempMediaRepo).findByInspectionItemIdAndTenant(itemId, 1L);
        verify(modelMapper).map(media1, TempMediaDTO.class);
        verify(modelMapper).map(media2, TempMediaDTO.class);
    }

}
