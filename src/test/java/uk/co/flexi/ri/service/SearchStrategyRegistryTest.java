package uk.co.flexi.ri.service;

import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.model.SearchStrategy;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class SearchStrategyRegistryTest {
    @Test
    void getStrategiesFor_knownTypes() {
        SearchStrategyRegistry registry = new SearchStrategyRegistry();
        Set<SearchStrategy> maoStrategies = registry.getStrategiesFor(OMSClientService.OMSType.MAO);
        assertTrue(maoStrategies.contains(SearchStrategy.RETURN_BY_TRACKING_NUMBER));
        assertTrue(maoStrategies.contains(SearchStrategy.ORDER_BY_ID));
        assertTrue(maoStrategies.contains(SearchStrategy.ORDER_BY_RETURN_TRACKING));
        assertTrue(maoStrategies.contains(SearchStrategy.ORDER_BY_PRODUCT_SERIAL_NUMBER));
        Set<SearchStrategy> mockStrategies = registry.getStrategiesFor(OMSClientService.OMSType.MOCK);
        assertEquals(maoStrategies, mockStrategies);
    }

    @Test
    void getStrategiesFor_unknownType_returnsEmptySet() {
        SearchStrategyRegistry registry = new SearchStrategyRegistry();
        Set<SearchStrategy> result = registry.getStrategiesFor(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
} 