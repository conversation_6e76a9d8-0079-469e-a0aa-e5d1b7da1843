package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.MerchantDTO;
import uk.co.flexi.ri.model.Merchant;
import uk.co.flexi.ri.repository.MerchantRepo;

import java.util.NoSuchElementException;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class MerchantServiceTest {
    private AuthenticatedUserService authenticatedUserService;
    private MerchantRepo merchantRepo;
    private ModelMapper modelMapper;
    private MerchantService merchantService;

    @BeforeEach
    void setUp() {
        authenticatedUserService = mock(AuthenticatedUserService.class);
        merchantRepo = mock(MerchantRepo.class);
        modelMapper = mock(ModelMapper.class);
        merchantService = new MerchantService(authenticatedUserService, merchantRepo, modelMapper);
    }

    @Test
    void findById_found() {
        Merchant merchant = new Merchant();
        when(merchantRepo.findById(1L)).thenReturn(Optional.of(merchant));
        assertSame(merchant, merchantService.findById(1L));
    }

    @Test
    void findById_notFound_throws() {
        when(merchantRepo.findById(1L)).thenReturn(Optional.empty());
        NoSuchElementException ex = assertThrows(NoSuchElementException.class, () -> merchantService.findById(1L));
        assertTrue(ex.getMessage().contains("Merchant not found"));
    }

    @Test
    void findByTenant_returnsMerchant() {
        Merchant merchant = new Merchant();
        when(merchantRepo.findByTenantNative(2L)).thenReturn(merchant);
        assertSame(merchant, merchantService.findByTenant(2L));
    }

    @Test
    void getMerchantDetails_success() {
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        userDTO.setMerchantId(3L);
        Merchant merchant = new Merchant();
        MerchantDTO merchantDTO = new MerchantDTO();
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        when(merchantRepo.findById(3L)).thenReturn(Optional.of(merchant));
        when(modelMapper.map(merchant, MerchantDTO.class)).thenReturn(merchantDTO);
        MerchantDTO result = merchantService.getMerchantDetails();
        assertSame(merchantDTO, result);
    }

    @Test
    void getMerchantDetails_merchantNotFound_throws() {
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        userDTO.setMerchantId(4L);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        when(merchantRepo.findById(4L)).thenReturn(Optional.empty());
        assertThrows(NoSuchElementException.class, () -> merchantService.getMerchantDetails());
    }
} 