package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class FileFormatServiceFactoryTest {
    private FileFormatServiceFactory factory;
    private FileFormatService csvService;
    private FileFormatService xlsService;

    @BeforeEach
    void setUp() {
        csvService = new DummyService(List.of("csv"));
        xlsService = new DummyService(List.of("xls", "xlsx"));
        factory = new FileFormatServiceFactory(List.of(csvService, xlsService));
    }

    @Test
    void getService_supportedExtension_returnsService() {
        assertSame(csvService, factory.getService("csv"));
        assertSame(xlsService, factory.getService("xls"));
        assertSame(xlsService, factory.getService("xlsx"));
    }

    @Test
    void getService_unsupportedExtension_throws() {
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class, () -> factory.getService("pdf"));
        assertTrue(ex.getMessage().contains("Unsupported file extension"));
    }

    static class DummyService implements FileFormatService {
        private final List<String> extensions;
        DummyService(List<String> extensions) { this.extensions = extensions; }
        @Override public List<String> getFileExtensions() { return extensions; }
        @Override public byte[] exportData(List<java.util.LinkedHashMap<String, Object>> data) { return new byte[0]; }
        @Override public List<java.util.LinkedHashMap<String, Object>> importData(java.io.InputStream inputStream) { return List.of(); }
    }
} 