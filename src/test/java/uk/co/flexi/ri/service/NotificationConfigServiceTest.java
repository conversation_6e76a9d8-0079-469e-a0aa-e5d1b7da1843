package uk.co.flexi.ri.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;

import uk.co.flexi.ri.dto.NotificationConfigDTO;
import uk.co.flexi.ri.model.NotificationChannel;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.repository.NotificationChannelRepo;
import uk.co.flexi.ri.repository.NotificationConfigRepo;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
class NotificationConfigServiceTest {

    @Mock
    private NotificationConfigRepo notificationConfigRepo;

    @Mock
    private NotificationChannelRepo notificationChannelRepo;

    @Mock
    private ModelMapper modelMapper;

    @InjectMocks
    private NotificationConfigService notificationConfigService;


    @Test
    void getNotificationTypes_ShouldReturnAllNotificationTypes() {
        //Act & Assert
        assertEquals(Arrays.stream(NotificationConfig.NotificationType.values()).toList(), notificationConfigService.getNotificationTypes());

    }

    @Test
    void getFrequencies_ShouldReturnAllFrequencies() {
        //Act & Assert
        assertEquals(Arrays.stream(NotificationConfigDTO.Frequency.values()).toList(), notificationConfigService.getFrequencies());
    }

    @Test
    void save_ShouldSaveNotificationConfig() {

        NotificationConfigDTO inputDTO = new NotificationConfigDTO();
        inputDTO.setNotificationType(NotificationConfig.NotificationType.ASSIGN_TO_ME);
        inputDTO.setChannelId(1L);
        inputDTO.setFrequency(NotificationConfigDTO.Frequency.EVERY_ONE_HOUR);
        inputDTO.setUserGroupId("userGroup123");
        inputDTO.setDestination("<EMAIL>");


        NotificationConfig notificationConfig = new NotificationConfig();
        notificationConfig.setId(1L);
        notificationConfig.setNotificationType(NotificationConfig.NotificationType.ASSIGN_TO_ME);
        notificationConfig.setDestination("<EMAIL>");

        NotificationChannel channel = new NotificationChannel();
        channel.setId(1L);

        //Arrange
        when(modelMapper.map(any(NotificationConfigDTO.class), eq(NotificationConfig.class)))
                .thenReturn(notificationConfig);
        when(notificationChannelRepo.findById(anyLong())).thenReturn(Optional.of(channel));
        when(notificationConfigRepo.save(any(NotificationConfig.class)))
                .thenReturn(notificationConfig);
        when(modelMapper.map(notificationConfig, NotificationConfigDTO.class))
                .thenReturn(inputDTO);

        //Act
        NotificationConfigDTO result;
        try {
            result = notificationConfigService.save(inputDTO);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }

        //Assert
        assertNotNull(result);
        assertEquals(inputDTO, result);
        assertEquals("userGroup123",notificationConfig.getUserGroupId());
        assertEquals("0 0 0/1 ? * * *", notificationConfig.getFrequency());
        verify(notificationChannelRepo).findById(1L);
    }

    @Test
    void save_ShouldThrowNoSuchElementException_WhenChannelNotFound() {
        // Arrange
        NotificationConfigDTO inputDTO = new NotificationConfigDTO();
        inputDTO.setNotificationType(NotificationConfig.NotificationType.ASSIGN_TO_ME);
        inputDTO.setChannelId(99L);
        inputDTO.setFrequency(NotificationConfigDTO.Frequency.IMMEDIATELY);
        inputDTO.setUserGroupId("userGroup123");

        when(modelMapper.map(any(NotificationConfigDTO.class), eq(NotificationConfig.class)))
                .thenReturn(new NotificationConfig());
        when(notificationChannelRepo.findById(anyLong()))
                .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(NoSuchElementException.class, () -> notificationConfigService.save(inputDTO));

        verify(notificationChannelRepo).findById(99L);
    }

    @Test
    void save_ShouldThrowIllegalAccessException_WhenUserGroupIdIsNullForAssignToMe() {
        // Arrange
        NotificationConfigDTO inputDTO = new NotificationConfigDTO();
        inputDTO.setNotificationType(NotificationConfig.NotificationType.ASSIGN_TO_ME);
        inputDTO.setChannelId(1L);
        inputDTO.setFrequency(NotificationConfigDTO.Frequency.IMMEDIATELY);
        inputDTO.setUserGroupId(null);

        NotificationConfig notificationConfig = new NotificationConfig();
        notificationConfig.setNotificationType(NotificationConfig.NotificationType.ASSIGN_TO_ME);

        NotificationChannel channel = new NotificationChannel();
        channel.setId(1L);

        when(modelMapper.map(any(NotificationConfigDTO.class), eq(NotificationConfig.class)))
                .thenReturn(notificationConfig);
        when(notificationChannelRepo.findById(anyLong()))
                .thenReturn(Optional.of(channel));

        // Act & Assert
        assertThrows(IllegalAccessException.class, () -> notificationConfigService.save(inputDTO));

        verify(notificationChannelRepo).findById(1L);
    }

    @Test
    void save_ShouldSetFrequencyToImmediately_WhenNotificationTypeIsIntegrationFailure() throws IllegalAccessException {
        // Arrange
        NotificationConfigDTO inputDTO = new NotificationConfigDTO();
        inputDTO.setNotificationType(NotificationConfig.NotificationType.INTEGRATION_FAILURE);
        inputDTO.setChannelId(1L);
        inputDTO.setUserGroupId("group123");

        NotificationConfig config = new NotificationConfig();
        config.setNotificationType(NotificationConfig.NotificationType.INTEGRATION_FAILURE);

        NotificationChannel channel = new NotificationChannel();
        channel.setId(1L);

        when(modelMapper.map(any(NotificationConfigDTO.class), eq(NotificationConfig.class)))
                .thenReturn(config);
        when(notificationChannelRepo.findById(anyLong()))
                .thenReturn(Optional.of(channel));
        when(notificationConfigRepo.save(any(NotificationConfig.class)))
                .thenReturn(config);
        when(modelMapper.map(config, NotificationConfigDTO.class))
                .thenReturn(inputDTO);

        // Act
        notificationConfigService.save(inputDTO);

        // Assert
        assertEquals("IMMEDIATELY", config.getFrequency());
    }


    @Test
    void findById_ShouldReturnNotificationConfigDTO() {

        Long configId = 1L;

        NotificationConfig notificationConfig = new NotificationConfig();
        notificationConfig.setId(configId);
        NotificationChannel channel = new NotificationChannel();
        channel.setId(10L);
        notificationConfig.setChannel(channel);

        NotificationConfigDTO expectedDto = new NotificationConfigDTO();

        // Arrange
        when(notificationConfigRepo.findById(configId)).thenReturn(Optional.of(notificationConfig));
        when(modelMapper.map(notificationConfig, NotificationConfigDTO.class)).thenReturn(expectedDto);

        // Act
        NotificationConfigDTO result = notificationConfigService.findById(configId);

        // Assert
        assertNotNull(result);
        verify(notificationConfigRepo).findById(configId);
    }

    @Test
    void findById_ShouldThrowException_WhenNotFound() {
        Long configId = 99L;

        // Arrange
        when(notificationConfigRepo.findById(configId)).thenReturn(Optional.empty());

        // Act & Assert
        NoSuchElementException exception = assertThrows(NoSuchElementException.class, () ->
                notificationConfigService.findById(configId)
        );

        assertEquals("NotificationConfig not found with id: 99", exception.getMessage());
        verify(notificationConfigRepo).findById(configId);
    }


    @Test
    void updateConfig_ShouldUpdateAndReturnDTO() {
        // Arrange
        Long configId = 1L;

        NotificationConfig existingConfig = new NotificationConfig();
        existingConfig.setId(configId);

        NotificationChannel channel = new NotificationChannel();
        channel.setId(100L);

        NotificationConfigDTO updateReqDTO = new NotificationConfigDTO();
        updateReqDTO.setChannelId(100L);
        updateReqDTO.setFrequency(NotificationConfigDTO.Frequency.EVERY_TWO_HOUR);
        updateReqDTO.setNotificationType(NotificationConfig.NotificationType.ASSIGN_TO_ME);

        NotificationConfig updatedConfig = new NotificationConfig();
        updatedConfig.setId(configId);

        updatedConfig.setChannel(channel);

        NotificationConfigDTO expectedDTO = new NotificationConfigDTO();

        when(notificationConfigRepo.findById(configId)).thenReturn(Optional.of(existingConfig));
        when(notificationChannelRepo.findById(100L)).thenReturn(Optional.of(channel));
        doNothing().when(modelMapper).map(updateReqDTO, existingConfig);
        when(notificationConfigRepo.save(existingConfig)).thenReturn(updatedConfig);
        when(modelMapper.map(updatedConfig, NotificationConfigDTO.class)).thenReturn(expectedDTO);


        // Act
        NotificationConfigDTO result = notificationConfigService.updateConfig(configId, updateReqDTO);

        // Assert
        assertEquals(expectedDTO, result);

        verify(notificationConfigRepo).findById(configId);
        verify(notificationChannelRepo).findById(100L);
        verify(notificationConfigRepo).save(existingConfig);
    }

    @Test
    void updateConfig_ShouldSkipChannelUpdate_WhenChannelIdIsNull() {
        // Arrange
        Long configId = 1L;

        NotificationConfig existingConfig = new NotificationConfig();
        existingConfig.setId(configId);

        NotificationConfigDTO updateReqDTO = new NotificationConfigDTO();
        updateReqDTO.setChannelId(null);
        updateReqDTO.setFrequency(NotificationConfigDTO.Frequency.IMMEDIATELY);
        updateReqDTO.setNotificationType(NotificationConfig.NotificationType.INTEGRATION_FAILURE);

        NotificationConfig updatedConfig = new NotificationConfig();
        updatedConfig.setId(configId);

        NotificationConfigDTO expectedDTO = new NotificationConfigDTO();

        when(notificationConfigRepo.findById(configId)).thenReturn(Optional.of(existingConfig));
        doNothing().when(modelMapper).map(updateReqDTO, existingConfig);
        when(notificationConfigRepo.save(existingConfig)).thenReturn(updatedConfig);
        when(modelMapper.map(updatedConfig, NotificationConfigDTO.class)).thenReturn(expectedDTO);

        // Act
        NotificationConfigDTO result = notificationConfigService.updateConfig(configId, updateReqDTO);

        // Assert
        assertEquals(expectedDTO, result);
        verify(notificationChannelRepo, never()).findById(anyLong());
    }

    @Test
    void updateConfig_ShouldThrowException_WhenIdNotFound() {
        Long configId = 2L;
        NotificationConfigDTO updateReqDTO = new NotificationConfigDTO();

        // Arrange
        when(notificationConfigRepo.findById(configId)).thenReturn(Optional.empty());

        // Act & Assert
        NoSuchElementException exception = assertThrows(NoSuchElementException.class, () ->
                notificationConfigService.updateConfig(configId, updateReqDTO)
        );

        assertEquals("NotificationConfig not found with id: 2", exception.getMessage());
        verify(notificationConfigRepo).findById(configId);
    }

    @Test
    void updateConfig_ShouldSkipFrequencyUpdate_WhenFrequencyIsNull() {
        // Arrange
        Long configId = 1L;

        NotificationConfig existingConfig = new NotificationConfig();
        existingConfig.setId(configId);

        NotificationConfigDTO updateReqDTO = new NotificationConfigDTO();
        updateReqDTO.setChannelId(100L);
        updateReqDTO.setFrequency(null);
        updateReqDTO.setNotificationType(NotificationConfig.NotificationType.ASSIGN_TO_ME);

        NotificationChannel channel = new NotificationChannel();
        channel.setId(100L);

        NotificationConfig updatedConfig = new NotificationConfig();
        updatedConfig.setId(configId);

        NotificationConfigDTO expectedDTO = new NotificationConfigDTO();

        when(notificationConfigRepo.findById(configId)).thenReturn(Optional.of(existingConfig));
        when(notificationChannelRepo.findById(100L)).thenReturn(Optional.of(channel));
        doNothing().when(modelMapper).map(updateReqDTO, existingConfig);
        when(notificationConfigRepo.save(existingConfig)).thenReturn(updatedConfig);
        when(modelMapper.map(updatedConfig, NotificationConfigDTO.class)).thenReturn(expectedDTO);

        // Act
        NotificationConfigDTO result = notificationConfigService.updateConfig(configId, updateReqDTO);

        // Assert
        assertEquals(expectedDTO, result);
        verify(notificationConfigRepo).findById(configId);
        verify(notificationChannelRepo).findById(100L);
    }

    @Test
    void updateConfig_ShouldThrowException_WhenChannelNotFound() {
        Long configId = 4L;
        NotificationConfig existingConfig = new NotificationConfig();
        existingConfig.setId(configId);

        NotificationConfigDTO updateReqDTO = new NotificationConfigDTO();
        updateReqDTO.setChannelId(100L);

        // Arrange
        when(notificationConfigRepo.findById(configId)).thenReturn(Optional.of(existingConfig));
        when(notificationChannelRepo.findById(anyLong())).thenThrow(new NoSuchElementException());

        // Act & Assert
        assertThrows(NoSuchElementException.class, () ->
                notificationConfigService.updateConfig(configId, updateReqDTO)
        );
        verify(notificationConfigRepo).findById(configId);
    }



    @Test
    void deleteById_ShouldDeleteNotificationConfig() {
        Long configId = 5L;
        NotificationConfig notificationConfig = new NotificationConfig();
        notificationConfig.setId(configId);

        // Arrange
        when(notificationConfigRepo.findById(configId)).thenReturn(Optional.of(notificationConfig));

        // Act
        notificationConfigService.deleteById(configId);

        // Assert
        verify(notificationConfigRepo).findById(configId);
        verify(notificationConfigRepo).delete(notificationConfig);
    }


    @Test
    void deleteById_ShouldThrowException_WhenNotFound() {
        Long configId = 6L;

        // Arrange
        when(notificationConfigRepo.findById(configId)).thenReturn(Optional.empty());

        // Act & Assert
        NoSuchElementException exception = assertThrows(NoSuchElementException.class, () ->
                notificationConfigService.deleteById(configId)
        );
        assertEquals("NotificationConfig not found with id: 6", exception.getMessage());
        verify(notificationConfigRepo).findById(configId);
    }

    @Test
    void getAllNotificationConfigs_ShouldReturnAllConfigs() {
        NotificationConfig config1 = new NotificationConfig();
        config1.setId(1L);
        NotificationChannel channel1 = new NotificationChannel();
        channel1.setId(10L);
        config1.setChannel(channel1);

        NotificationConfig config2 = new NotificationConfig();
        config2.setId(2L);
        NotificationChannel channel2 = new NotificationChannel();
        channel2.setId(20L);
        config2.setChannel(channel2);

        NotificationConfigDTO dto1 = new NotificationConfigDTO();
        dto1.setChannelId(10L);
        NotificationConfigDTO dto2 = new NotificationConfigDTO();
        dto2.setChannelId(20L);

        when(notificationConfigRepo.findAll()).thenReturn(Arrays.asList(config1, config2));
        when(modelMapper.map(config1, NotificationConfigDTO.class)).thenReturn(dto1);
        when(modelMapper.map(config2, NotificationConfigDTO.class)).thenReturn(dto2);

        List<NotificationConfigDTO> result = notificationConfigService.getAllNotificationConfigs();

        assertEquals(2, result.size());
        assertEquals(10L, result.get(0).getChannelId());
        assertEquals(20L, result.get(1).getChannelId());
        verify(notificationConfigRepo).findAll();
    }

    @Test
    void getAllNotificationConfigs_ShouldReturnEmptyList_WhenNoConfigsExist() {
        when(notificationConfigRepo.findAll()).thenReturn(Collections.emptyList());

        List<NotificationConfigDTO> result = notificationConfigService.getAllNotificationConfigs();

        assertTrue(result.isEmpty());
        verify(notificationConfigRepo).findAll();
    }
}