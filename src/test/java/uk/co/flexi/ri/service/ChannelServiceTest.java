package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import uk.co.flexi.ri.dto.ChannelDTO;
import uk.co.flexi.ri.model.Channel;
import uk.co.flexi.ri.repository.ChannelRepo;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ChannelServiceTest {

    @Mock
    private ModelMapper modelMapper;

    @Mock
    private ChannelRepo channelRepo;

    @InjectMocks
    private ChannelService channelService;

    private Channel channel;
    private ChannelDTO channelDTO;

    @BeforeEach
    void setUp() {
        // Setup test data
        channel = new Channel();
        channel.setChannelId("TEST-CHANNEL");
        channel.setName("Test Channel");
        channel.setServiceLevelAgreement(24);

        channelDTO = new ChannelDTO();
        channelDTO.setChannelId("TEST-CHANNEL");
        channelDTO.setName("Test Channel");
        channelDTO.setServiceLevelAgreement(24);
    }

    @Test
    void findById_WhenChannelExists_ShouldReturnChannelDTO() {
        // Arrange
        when(channelRepo.findByChannelId("TEST-CHANNEL")).thenReturn(Optional.of(channel));
        when(modelMapper.map(channel, ChannelDTO.class)).thenReturn(channelDTO);

        // Act
        ChannelDTO result = channelService.findById("TEST-CHANNEL");

        // Assert
        assertNotNull(result);
        assertEquals("TEST-CHANNEL", result.getChannelId());
        assertEquals("Test Channel", result.getName());
        assertEquals(24, result.getServiceLevelAgreement());
        verify(channelRepo).findByChannelId("TEST-CHANNEL");
        verify(modelMapper).map(channel, ChannelDTO.class);
    }

    @Test
    void findById_WhenChannelDoesNotExist_ShouldThrowException() {
        // Arrange
        when(channelRepo.findByChannelId("NON-EXISTENT")).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(NoSuchElementException.class, () -> channelService.findById("NON-EXISTENT"));
        verify(channelRepo).findByChannelId("NON-EXISTENT");
    }

    @Test
    void save_ShouldSaveAndReturnChannelDTO() {
        // Arrange
        when(modelMapper.map(channelDTO, Channel.class)).thenReturn(channel);
        when(channelRepo.save(channel)).thenReturn(channel);
        when(modelMapper.map(channel, ChannelDTO.class)).thenReturn(channelDTO);

        // Act
        ChannelDTO result = channelService.save(channelDTO);

        // Assert
        assertNotNull(result);
        assertEquals("TEST-CHANNEL", result.getChannelId());
        verify(modelMapper).map(channelDTO, Channel.class);
        verify(channelRepo).save(channel);
        verify(modelMapper).map(channel, ChannelDTO.class);
    }

    @Test
    void findAll_ShouldReturnListOfChannelDTOs() {
        // Arrange
        List<Channel> channels = Arrays.asList(channel);
        when(channelRepo.findAll()).thenReturn(channels);
        when(modelMapper.map(channel, ChannelDTO.class)).thenReturn(channelDTO);

        // Act
        List<ChannelDTO> results = channelService.findAll();

        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals("TEST-CHANNEL", results.get(0).getChannelId());
        verify(channelRepo).findAll();
        verify(modelMapper).map(channel, ChannelDTO.class);
    }

    @Test
    void update_WhenChannelExists_ShouldUpdateAndReturnChannelDTO() {
        // Arrange
        ChannelDTO updatedDTO = new ChannelDTO();
        updatedDTO.setName("Updated Channel");
        updatedDTO.setServiceLevelAgreement(48);

        Channel updatedChannel = new Channel();
        updatedChannel.setChannelId("TEST-CHANNEL");
        updatedChannel.setName("Updated Channel");
        updatedChannel.setServiceLevelAgreement(48);

        when(channelRepo.findByChannelId("TEST-CHANNEL")).thenReturn(Optional.of(channel));
        when(channelRepo.save(any(Channel.class))).thenReturn(updatedChannel);
        when(modelMapper.map(updatedChannel, ChannelDTO.class)).thenReturn(updatedDTO);

        // Act
        ChannelDTO result = channelService.update("TEST-CHANNEL", updatedDTO);

        // Assert
        assertNotNull(result);
        assertEquals("Updated Channel", result.getName());
        assertEquals(48, result.getServiceLevelAgreement());
        verify(channelRepo).findByChannelId("TEST-CHANNEL");
        verify(channelRepo).save(any(Channel.class));
        verify(modelMapper).map(updatedChannel, ChannelDTO.class);
    }

    @Test
    void update_WhenChannelDoesNotExist_ShouldThrowException() {
        // Arrange
        when(channelRepo.findByChannelId("NON-EXISTENT")).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(NoSuchElementException.class,
                () -> channelService.update("NON-EXISTENT", channelDTO));
        verify(channelRepo).findByChannelId("NON-EXISTENT");
        verify(channelRepo, never()).save(any(Channel.class));
    }

    @Test
    void delete_WhenChannelExists_ShouldDeleteChannel() {
        // Arrange
        when(channelRepo.findByChannelId("TEST-CHANNEL")).thenReturn(Optional.of(channel));
        doNothing().when(channelRepo).delete(channel);

        // Act
        channelService.delete("TEST-CHANNEL");

        // Assert
        verify(channelRepo).findByChannelId("TEST-CHANNEL");
        verify(channelRepo).delete(channel);
    }

    @Test
    void delete_WhenChannelDoesNotExist_ShouldThrowException() {
        // Arrange
        when(channelRepo.findByChannelId("NON-EXISTENT")).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(NoSuchElementException.class,
                () -> channelService.delete("NON-EXISTENT"));
        verify(channelRepo).findByChannelId("NON-EXISTENT");
        verify(channelRepo, never()).delete(any(Channel.class));
    }
}