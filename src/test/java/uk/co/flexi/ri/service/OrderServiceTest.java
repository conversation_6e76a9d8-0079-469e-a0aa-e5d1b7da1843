package uk.co.flexi.ri.service;

import jakarta.persistence.EntityNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import uk.co.flexi.ri.dto.OrderDetailDTO;
import uk.co.flexi.ri.exception.custom.OrderNotFoundException;
import uk.co.flexi.ri.mapper.OrderDetailMapper;
import uk.co.flexi.ri.mapper.factory.OrderDetailMapperFactory;
import uk.co.flexi.ri.model.OMSProvider;
import uk.co.flexi.ri.model.Order;
import uk.co.flexi.ri.repository.OrderRepo;
import uk.co.flexi.sdk.oms.model.OrderData;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

class OrderServiceTest {

    private OrderRepo orderRepo;
    private OMSProviderService omsProviderService;
    private OrderDetailMapperFactory mapperFactory;
    private OrderLookUpService lookUpService;
    private OrderService orderService;

    private final String REFERENCE_ID = "ABC123";
    private final String INSPECTION_ID = "INSPECT-001";

    @BeforeEach
    void setUp() {
        orderRepo = mock(OrderRepo.class);
        omsProviderService = mock(OMSProviderService.class);
        mapperFactory = mock(OrderDetailMapperFactory.class);
        lookUpService = mock(OrderLookUpService.class);

        orderService = new OrderService(orderRepo, omsProviderService, mapperFactory, lookUpService);
    }

    @Test
    void testFindByReferenceIdPresent() {
        Order order = new Order();
        when(orderRepo.findByReferenceId(REFERENCE_ID)).thenReturn(Optional.of(order));
        Optional<Order> result = orderService.findByReferenceId(REFERENCE_ID);
        assertTrue(result.isPresent());
        assertEquals(order, result.get());
    }

    @Test
    void testFindByReferenceIdNotPresent() {
        when(orderRepo.findByReferenceId(REFERENCE_ID)).thenReturn(Optional.empty());
        Optional<Order> result = orderService.findByReferenceId(REFERENCE_ID);
        assertFalse(result.isPresent());
    }

    @Test
    void testGetOrderDetails_WithExistingOrder() {
        // Mocks
        OMSProvider provider = new OMSProvider();
        provider.setProviderName("MAO");

        OMSProviderService.OMSProviderInfo providerInfo = mock(OMSProviderService.OMSProviderInfo.class);
        when(providerInfo.omsProvider()).thenReturn(provider);
        when(providerInfo.searchStrategy()).thenReturn(List.of("ORDER_ID"));
        when(omsProviderService.getOMSProviderInfo()).thenReturn(providerInfo);

        OrderDetailMapper mapper = mock(OrderDetailMapper.class);
        when(mapperFactory.getMapper("maoOrderDetailMapper")).thenReturn(mapper);

        OrderData orderData = new OrderData();
        when(lookUpService.orderLookUp(any(), eq(provider))).thenReturn(orderData);

        OrderDetailDTO mappedDTO = new OrderDetailDTO();
        when(mapper.map(orderData, REFERENCE_ID)).thenReturn(mappedDTO);

        Order existingOrder = new Order();
        when(orderRepo.findByReferenceId(REFERENCE_ID)).thenReturn(Optional.of(existingOrder));

        OrderDetailDTO finalDTO = new OrderDetailDTO();
        when(mapper.mapExistingOrderDetailDTO(existingOrder, mappedDTO, INSPECTION_ID)).thenReturn(finalDTO);

        OrderDetailDTO result = orderService.getOrderDetails(REFERENCE_ID, INSPECTION_ID);

        assertNotNull(result);
        assertEquals(finalDTO, result);
    }

    @Test
    void testGetOrderDetails_WithNoExistingOrder() {
        // Setup provider
        OMSProvider provider = new OMSProvider();
        provider.setProviderName("MAO");

        OMSProviderService.OMSProviderInfo providerInfo = mock(OMSProviderService.OMSProviderInfo.class);
        when(providerInfo.omsProvider()).thenReturn(provider);
        when(providerInfo.searchStrategy()).thenReturn(List.of("ORDER_ID"));
        when(omsProviderService.getOMSProviderInfo()).thenReturn(providerInfo);

        // Setup mapper
        OrderDetailMapper mapper = mock(OrderDetailMapper.class);
        when(mapperFactory.getMapper("maoOrderDetailMapper")).thenReturn(mapper);

        // Setup orderData
        OrderData orderData = new OrderData();
        when(lookUpService.orderLookUp(any(), eq(provider))).thenReturn(orderData);

        OrderDetailDTO dto = new OrderDetailDTO();
        when(mapper.map(orderData, REFERENCE_ID)).thenReturn(dto);

        when(orderRepo.findByReferenceId(REFERENCE_ID)).thenReturn(Optional.empty());

        OrderDetailDTO result = orderService.getOrderDetails(REFERENCE_ID, INSPECTION_ID);

        assertNotNull(result);
        assertEquals(dto, result);
    }

    @Test
    void testGetOrderDetails_OrderNotFoundException() {
        OMSProvider provider = new OMSProvider();
        provider.setProviderName("MAO");

        OMSProviderService.OMSProviderInfo providerInfo = mock(OMSProviderService.OMSProviderInfo.class);
        when(providerInfo.omsProvider()).thenReturn(provider);
        when(providerInfo.searchStrategy()).thenReturn(List.of("ORDER_ID"));
        when(omsProviderService.getOMSProviderInfo()).thenReturn(providerInfo);

        when(mapperFactory.getMapper("maoOrderDetailMapper")).thenReturn(mock(OrderDetailMapper.class));
        when(lookUpService.orderLookUp(any(), eq(provider))).thenReturn(null);

        OrderNotFoundException ex = assertThrows(OrderNotFoundException.class, () ->
                orderService.getOrderDetails(REFERENCE_ID, INSPECTION_ID));
        assertEquals("Order not found for reference id: " + REFERENCE_ID, ex.getMessage());
    }

    @Test
    void testFindById_OrderExists() {
        Order order = new Order();
        when(orderRepo.findById(1L)).thenReturn(Optional.of(order));
        Order result = orderService.findById(1L);
        assertEquals(order, result);
    }

    @Test
    void testFindById_ThrowsEntityNotFound() {
        when(orderRepo.findById(1L)).thenReturn(Optional.empty());
        assertThrows(EntityNotFoundException.class, () -> orderService.findById(1L));
    }
}