package uk.co.flexi.ri.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.modelmapper.ModelMapper;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.exception.custom.RIAuthenticationException;
import uk.co.flexi.ri.exception.custom.OrderNotFoundException;
import uk.co.flexi.ri.model.*;
import uk.co.flexi.ri.repository.CommentRepo;
import uk.co.flexi.ri.repository.InspectionItemRepo;

import java.io.IOException;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class CommentServiceTest {
    private CommentRepo commentRepo;
    private UserService userService;
    private ModelMapper modelMapper;
    private InspectionItemRepo inspectionItemRepo;
    private MediaService mediaService;
    private AuthenticatedUserService authenticatedUserService;
    private EventPublisher eventPublisher;
    private CommentService service;

    @BeforeEach
    void setUp() {
        commentRepo = mock(CommentRepo.class);
        userService = mock(UserService.class);
        modelMapper = mock(ModelMapper.class);
        inspectionItemRepo = mock(InspectionItemRepo.class);
        mediaService = mock(MediaService.class);
        authenticatedUserService = mock(AuthenticatedUserService.class);
        eventPublisher = mock(EventPublisher.class);
        service = new CommentService(commentRepo, userService, modelMapper, inspectionItemRepo, mediaService, authenticatedUserService, eventPublisher);
    }

    @Test
    void save_success() throws IOException {
        CommentMediaDTO dto = new CommentMediaDTO();
        dto.setContent("c");
        dto.setCommentType(Comment.CommentType.GENERAL);
        dto.setVisibility(Comment.CommentVisibility.EXTERNAL);
        dto.setInspectionItemId("iid");
        dto.setMediaFiles(List.of(mock(MultipartFile.class)));
        dto.setMediaIds(List.of("mid"));
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        UserGroup group = new UserGroup();
        group.setVisibility(Comment.CommentVisibility.EXTERNAL);
        userDTO.setUserGroup(group);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        Comment comment = new Comment();
        comment.setCommentId("cid");
        InspectionItem item = new InspectionItem();
        item.setInspectionItemId("iid");
        comment.setInspectionItem(item);
        User user = new User();
        user.setId(1L);
        comment.setAuthor(user);
        CommentDTO commentDTO = new CommentDTO();
        commentDTO.setInspectionItemId("iid");
        when(modelMapper.map(any(CommentMediaDTO.class), eq(CommentDTO.class))).thenReturn(commentDTO);
        when(userService.findById(any())).thenReturn(user);
        when(inspectionItemRepo.findByInspectionItemId("iid")).thenReturn(Optional.of(item));
        when(modelMapper.map(any(CommentDTO.class), eq(Comment.class))).thenReturn(comment);
        when(commentRepo.save(any())).thenReturn(comment);
        when(mediaService.uploadFile(anyList(), any())).thenReturn(List.of(new MediaDTO()));
        when(mediaService.findByMediaId(any())).thenReturn(new Media());
        when(mediaService.save(any())).thenReturn(new Media());
        when(modelMapper.map(any(Media.class), eq(MediaDTO.class))).thenReturn(new MediaDTO());
        when(modelMapper.map(any(Comment.class), eq(CommentDTO.class))).thenReturn(commentDTO);
        doNothing().when(eventPublisher).publish(any(), any(), any());
        CommentDTO result = service.save(dto);
        assertNotNull(result);
        assertEquals("iid", result.getInspectionItemId());
    }

    @Test
    void save_setsVisibilityFromUserGroup() throws IOException {
        CommentMediaDTO dto = new CommentMediaDTO();
        dto.setInspectionItemId("iid");
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        UserGroup group = new UserGroup();
        group.setVisibility(Comment.CommentVisibility.EXTERNAL);
        userDTO.setUserGroup(group);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        Comment comment = new Comment();
        comment.setCommentId("cid");
        InspectionItem item = new InspectionItem();
        item.setInspectionItemId("iid");
        comment.setInspectionItem(item);
        User user = new User();
        user.setId(1L);
        comment.setAuthor(user);
        CommentDTO commentDTO = new CommentDTO();
        commentDTO.setInspectionItemId("iid");
        when(modelMapper.map(any(CommentMediaDTO.class), eq(CommentDTO.class))).thenReturn(commentDTO);
        when(userService.findById(any())).thenReturn(user);
        when(inspectionItemRepo.findByInspectionItemId("iid")).thenReturn(Optional.of(item));
        when(modelMapper.map(any(CommentDTO.class), eq(Comment.class))).thenReturn(comment);
        when(commentRepo.save(any())).thenReturn(comment);
        when(mediaService.uploadFile(anyList(), any())).thenReturn(List.of());
        when(modelMapper.map(any(Comment.class), eq(CommentDTO.class))).thenReturn(commentDTO);
        doNothing().when(eventPublisher).publish(any(), any(), any());
        CommentDTO result = service.save(dto);
        assertEquals("iid", result.getInspectionItemId());
    }

    @Test
    void update_success() throws IOException {
        String commentId = "cid";
        CommentMediaUpdateDTO updateDTO = new CommentMediaUpdateDTO();
        updateDTO.setContent("new content");
        updateDTO.setVisibility(Comment.CommentVisibility.EXTERNAL);
        updateDTO.setAddMediaFiles(List.of(mock(MultipartFile.class)));
        updateDTO.setRemoveMediaIds(List.of("mid"));
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        userDTO.setId(1L);
        UserGroup group = new UserGroup();
        group.setVisibility(Comment.CommentVisibility.EXTERNAL);
        userDTO.setUserGroup(group);
        Comment comment = new Comment();
        comment.setAuthor(new User());
        comment.getAuthor().setId(1L);
        InspectionItem item = new InspectionItem();
        item.setInspectionItemId("iid");
        comment.setInspectionItem(item);
        comment.setMedia(new ArrayList<>());
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        when(commentRepo.findByCommentId(commentId)).thenReturn(Optional.of(comment));
        when(commentRepo.save(any())).thenReturn(comment);
        when(modelMapper.map(any(Comment.class), eq(CommentDTO.class))).thenReturn(new CommentDTO());
        doNothing().when(eventPublisher).publish(any(), any(), any());
        CommentDTO result = service.update(commentId, updateDTO);
        assertNotNull(result);
    }

    @Test
    void update_unauthorized_throws() {
        String commentId = "cid";
        CommentMediaUpdateDTO updateDTO = new CommentMediaUpdateDTO();
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        userDTO.setId(2L);
        Comment comment = new Comment();
        comment.setAuthor(new User());
        comment.getAuthor().setId(1L);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        when(commentRepo.findByCommentId(commentId)).thenReturn(Optional.of(comment));
        assertThrows(RIAuthenticationException.class, () -> service.update(commentId, updateDTO));
    }

    @Test
    void update_notFound_throws() {
        when(commentRepo.findByCommentId("cid")).thenReturn(Optional.empty());
        assertThrows(OrderNotFoundException.class, () -> service.update("cid", new CommentMediaUpdateDTO()));
    }

    @Test
    void deleteComment_success() {
        String commentId = "cid";
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        userDTO.setId(1L);
        Comment comment = new Comment();
        comment.setId(10L);
        comment.setAuthor(new User());
        comment.getAuthor().setId(1L);
        comment.setMedia(new ArrayList<>());
        InspectionItem item = new InspectionItem();
        item.setInspectionItemId("iid");
        comment.setInspectionItem(item);
        CommentDTO commentDTO = new CommentDTO();
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        when(commentRepo.findByCommentId(commentId)).thenReturn(Optional.of(comment));
        when(modelMapper.map(any(Comment.class), eq(CommentDTO.class))).thenReturn(commentDTO);
        doNothing().when(eventPublisher).publish(any(), any(), any());
        doNothing().when(commentRepo).deleteById(10L);
        service.deleteComment(commentId);
        verify(commentRepo).deleteById(10L);
    }

    @Test
    void deleteComment_unauthorized_throws() {
        String commentId = "cid";
        AuthenticatedUserDTO userDTO = new AuthenticatedUserDTO();
        userDTO.setId(2L);
        Comment comment = new Comment();
        comment.setId(10L);
        comment.setAuthor(new User());
        comment.getAuthor().setId(1L);
        comment.setMedia(new ArrayList<>());
        InspectionItem item = new InspectionItem();
        item.setInspectionItemId("iid");
        comment.setInspectionItem(item);
        when(authenticatedUserService.getAuthenticatedUserDetails()).thenReturn(userDTO);
        when(commentRepo.findByCommentId(commentId)).thenReturn(Optional.of(comment));
        when(modelMapper.map(any(Comment.class), eq(CommentDTO.class))).thenReturn(new CommentDTO());
        assertThrows(RIAuthenticationException.class, () -> service.deleteComment(commentId));
    }

    @Test
    void deleteComment_notFound_throws() {
        when(commentRepo.findByCommentId("cid")).thenReturn(Optional.empty());
        assertThrows(OrderNotFoundException.class, () -> service.deleteComment("cid"));
    }
} 