package uk.co.flexi.ri.service;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.model.Merchant;
import uk.co.flexi.ri.model.User;
import uk.co.flexi.ri.model.UserGroup;
import uk.co.flexi.ri.repository.UserRepo;
import uk.co.flexi.ri.security.model.CustomAuthDetails;

import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class AuthenticatedUserServiceTest {
    private UserRepo userRepo;
    private AuthenticatedUserService service;
    private MockedStatic<RequestContextHolder> requestContextHolderMock;
    private MockedStatic<SecurityContextHolder> securityContextHolderMock;
    private RequestAttributes requestAttributes;
    private SecurityContext securityContext;
    private Authentication authentication;

    @BeforeEach
    void setUp() {
        userRepo = mock(UserRepo.class);
        service = new AuthenticatedUserService(userRepo);
        requestAttributes = mock(RequestAttributes.class);
        securityContext = mock(SecurityContext.class);
        authentication = mock(Authentication.class);
        requestContextHolderMock = Mockito.mockStatic(RequestContextHolder.class);
        securityContextHolderMock = Mockito.mockStatic(SecurityContextHolder.class);
    }

    @AfterEach
    void tearDown() {
        requestContextHolderMock.close();
        securityContextHolderMock.close();
    }

    @Test
    void getAuthenticatedUserDetails_cachedUser() {
        AuthenticatedUserDTO cached = new AuthenticatedUserDTO();
        when(requestAttributes.getAttribute("authenticatedUser", RequestAttributes.SCOPE_REQUEST)).thenReturn(cached);
        requestContextHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(requestAttributes);
        AuthenticatedUserDTO result = service.getAuthenticatedUserDetails();
        assertSame(cached, result);
    }

    @Test
    void getAuthenticatedUserDetails_noAuthentication() {
        when(requestAttributes.getAttribute("authenticatedUser", RequestAttributes.SCOPE_REQUEST)).thenReturn(null);
        requestContextHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(requestAttributes);
        securityContextHolderMock.when(SecurityContextHolder::getContext).thenReturn(securityContext);
        when(securityContext.getAuthentication()).thenReturn(null);
        AuthenticatedUserDTO result = service.getAuthenticatedUserDetails();
        assertNull(result);
    }

    @Test
    void getAuthenticatedUserDetails_notUserDetails() {
        when(requestAttributes.getAttribute("authenticatedUser", RequestAttributes.SCOPE_REQUEST)).thenReturn(null);
        requestContextHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(requestAttributes);
        securityContextHolderMock.when(SecurityContextHolder::getContext).thenReturn(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.getPrincipal()).thenReturn("notUserDetails");
        AuthenticatedUserDTO result = service.getAuthenticatedUserDetails();
        assertNull(result);
    }

    @Test
    void getAuthenticatedUserDetails_notCustomAuthDetails() {
        when(requestAttributes.getAttribute("authenticatedUser", RequestAttributes.SCOPE_REQUEST)).thenReturn(null);
        requestContextHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(requestAttributes);
        securityContextHolderMock.when(SecurityContextHolder::getContext).thenReturn(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        UserDetails userDetails = mock(UserDetails.class);
        when(authentication.getPrincipal()).thenReturn(userDetails);
        when(authentication.getDetails()).thenReturn("notCustomAuthDetails");
        AuthenticatedUserDTO result = service.getAuthenticatedUserDetails();
        assertNull(result);
    }

    @Test
    void getAuthenticatedUserDetails_userFound() {
        when(requestAttributes.getAttribute("authenticatedUser", RequestAttributes.SCOPE_REQUEST)).thenReturn(null);
        requestContextHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(requestAttributes);
        securityContextHolderMock.when(SecurityContextHolder::getContext).thenReturn(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        UserDetails userDetails = mock(UserDetails.class);
        when(authentication.getPrincipal()).thenReturn(userDetails);
        CustomAuthDetails customDetails = mock(CustomAuthDetails.class);
        when(authentication.getDetails()).thenReturn(customDetails);
        when(customDetails.getUserGroup()).thenReturn("ugid");
        when(customDetails.getTenant()).thenReturn(1);
        when(userDetails.getUsername()).thenReturn("uname");
        User user = new User();
        user.setId(1L);
        user.setUserId("uid");
        Merchant merchant = new Merchant();
        merchant.setId(2L);
        merchant.setTenant(3L);
        merchant.setMerchantName("mname");
        merchant.setReportingCurrency("USD");
        merchant.setHmacKey("hmac");
        user.setMerchant(merchant);
        UserGroup group = new UserGroup();
        group.setUserGroupId("ugid");
        user.setUserGroups(Set.of(group));
        when(userRepo.findByUserNameAndIsActive(eq("uname"), eq(1L), eq(true))).thenReturn(Optional.of(user));
        doAnswer(invocation -> {
            // Simulate setting the attribute in the request context
            return null;
        }).when(requestAttributes).setAttribute(eq("authenticatedUser"), any(), eq(RequestAttributes.SCOPE_REQUEST));
        AuthenticatedUserDTO result = service.getAuthenticatedUserDetails();
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("uid", result.getUserId());
        assertEquals(2L, result.getMerchantId());
        assertEquals(3L, result.getTenant());
        assertEquals("mname", result.getMerchantName());
        assertEquals("USD", result.getReportingCurrency());
        assertEquals("hmac", result.getHmacKey());
        assertEquals(group, result.getUserGroup());
    }

    @Test
    void getAuthenticatedUserDetails_userNotFound() {
        when(requestAttributes.getAttribute("authenticatedUser", RequestAttributes.SCOPE_REQUEST)).thenReturn(null);
        requestContextHolderMock.when(RequestContextHolder::currentRequestAttributes).thenReturn(requestAttributes);
        securityContextHolderMock.when(SecurityContextHolder::getContext).thenReturn(securityContext);
        when(securityContext.getAuthentication()).thenReturn(authentication);
        UserDetails userDetails = mock(UserDetails.class);
        when(authentication.getPrincipal()).thenReturn(userDetails);
        CustomAuthDetails customDetails = mock(CustomAuthDetails.class);
        when(authentication.getDetails()).thenReturn(customDetails);
        when(customDetails.getUserGroup()).thenReturn("ugid");
        when(customDetails.getTenant()).thenReturn(1);
        when(userDetails.getUsername()).thenReturn("uname");
        when(userRepo.findByUserNameAndIsActive(eq("uname"), eq(1L), eq(true))).thenReturn(Optional.empty());
        AuthenticatedUserDTO result = service.getAuthenticatedUserDetails();
        assertNull(result);
    }
} 