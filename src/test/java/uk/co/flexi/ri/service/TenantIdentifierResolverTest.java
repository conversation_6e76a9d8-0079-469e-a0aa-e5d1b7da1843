package uk.co.flexi.ri.service;

import org.hibernate.cfg.MultiTenancySettings;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class TenantIdentifierResolverTest {
    private final TenantIdentifierResolver resolver = new TenantIdentifierResolver();

    @AfterEach
    void tearDown() {
        resolver.clear();
    }

    @Test
    void setAndResolveCurrentTenantIdentifier() {
        resolver.setCurrentTenant(42L);
        assertEquals(42L, resolver.resolveCurrentTenantIdentifier());
    }

    @Test
    void resolveCurrentTenantIdentifier_default() {
        assertEquals(-1L, resolver.resolveCurrentTenantIdentifier());
    }

    @Test
    void clear_removesTenant() {
        resolver.setCurrentTenant(99L);
        resolver.clear();
        assertEquals(-1L, resolver.resolveCurrentTenantIdentifier());
    }

    @Test
    void validateExistingCurrentSessions_returnsFalse() {
        assertFalse(resolver.validateExistingCurrentSessions());
    }

    @Test
    void customize_setsResolverInProperties() {
        Map<String, Object> props = new HashMap<>();
        resolver.customize(props);
        assertSame(resolver, props.get(MultiTenancySettings.MULTI_TENANT_IDENTIFIER_RESOLVER));
    }
} 