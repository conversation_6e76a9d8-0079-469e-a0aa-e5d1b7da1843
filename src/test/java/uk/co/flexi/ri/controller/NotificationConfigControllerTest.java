package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.NotificationConfigDTO;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.service.NotificationConfigService;

import java.util.List;
import java.util.NoSuchElementException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class NotificationConfigControllerTest {

    @Mock
    private NotificationConfigService notificationConfigService;

    @InjectMocks
    private NotificationConfigController notificationConfigController;

    private NotificationConfigDTO notificationConfigDTO;


    @Test
    void create_ShouldCreateNotification() throws IllegalAccessException {
        //Arrange
        notificationConfigDTO = new NotificationConfigDTO();
        when(notificationConfigService.save(any(NotificationConfigDTO.class))).thenReturn(notificationConfigDTO);

        //Act
        ResponseEntity<NotificationConfigDTO> response = notificationConfigController.create(notificationConfigDTO);

        //Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(notificationConfigDTO, response.getBody());
        verify(notificationConfigService).save(notificationConfigDTO);
    }


    @Test
    void findById_ShouldReturn_NotificationDTO() {
        // Arrange
        notificationConfigDTO = new NotificationConfigDTO();
        when(notificationConfigService.findById(anyLong())).thenReturn(notificationConfigDTO);

        // Act
        ResponseEntity<NotificationConfigDTO> response = notificationConfigController.findById(1L);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(notificationConfigDTO, response.getBody());
        verify(notificationConfigService).findById(1L);
    }

    @Test
    void findById_ShouldThrowException_WhenNotificationConfigNotFound() {
        // Arrange
        when(notificationConfigService.findById(anyLong())).thenThrow(new NoSuchElementException("NotificationConfig not found with id"));

        // Act & Assert
        assertThrows(NoSuchElementException.class, () -> notificationConfigController.findById(1L));
    }

    @Test
    void updateNotificationConfig_ShouldReturn_NotificationDTO() {
        //Arrange
        notificationConfigDTO = new NotificationConfigDTO();
        when(notificationConfigService.updateConfig(anyLong(),any(NotificationConfigDTO.class))).thenReturn(notificationConfigDTO);

        //Act
        ResponseEntity<NotificationConfigDTO> response = notificationConfigController.updateNotificationConfig(1L, notificationConfigDTO);

        //Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(notificationConfigDTO, response.getBody());
        verify(notificationConfigService).updateConfig(1L, notificationConfigDTO);
    }

    @Test
    void deleteConfig() {
        // Arrange
        Long id = 1L;

        // Act
        notificationConfigController.deleteConfig(id);

        // Assert
        verify(notificationConfigService).deleteById(id);
    }

    @Test
    void getNotificationTypesReturnsAllNotificationTypes() {
        // Arrange
        List<NotificationConfig.NotificationType> notificationTypes = List.of(NotificationConfig.NotificationType.values());
        when(notificationConfigService.getNotificationTypes()).thenReturn(notificationTypes);

        // Act
        ResponseEntity<List<NotificationConfig.NotificationType>> response = notificationConfigController.getNotificationTypes();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(notificationTypes, response.getBody());
        verify(notificationConfigService).getNotificationTypes();
    }


    @Test
    void getFrequenciesReturnsAllFrequencies() {
        // Arrange
        List<NotificationConfigDTO.Frequency> frequencies = List.of(NotificationConfigDTO.Frequency.values());
        when(notificationConfigService.getFrequencies()).thenReturn(frequencies);

        // Act
        ResponseEntity<List<NotificationConfigDTO.Frequency>> response = notificationConfigController.getFrequencies();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(frequencies, response.getBody());
        verify(notificationConfigService).getFrequencies();
    }


}