package uk.co.flexi.ri.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.service.InspectionItemService;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InspectionItemControllerTest {

    @Mock
    private InspectionItemService inspectionItemService;

    @InjectMocks
    private InspectionItemController inspectionItemController;

    private InspectionItemDTO inspectionItemDTO;
    private InspectionItemUpdateReqDTO updateReqDTO;
    private ItemStatusUpdateReqDTO statusUpdateReqDTO;
    private InstructionDTO instructionDTO;
    private TempMediaDTO tempMediaDTO;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        inspectionItemController = new InspectionItemController(inspectionItemService);
        objectMapper = new ObjectMapper();

        // Initialize ProductDTO
        ProductDTO productDTO = new ProductDTO();
        productDTO.setId(1L);
        productDTO.setSku("TEST-SKU-001");
        productDTO.setProductName("Test Product");
        productDTO.setProductImage("test-image.jpg");
        productDTO.setDescription("Test Description");
        productDTO.setReturnReason("Test Return Reason");

        // Initialize InspectionItemDTO
        inspectionItemDTO = new InspectionItemDTO();
        inspectionItemDTO.setInspectionItemId("test-item-id");
        inspectionItemDTO.setProduct(productDTO);
        inspectionItemDTO.setExpectedQuantity(BigDecimal.ONE);
        inspectionItemDTO.setReceivedQuantity(BigDecimal.ONE);
        inspectionItemDTO.setItemCondition("GOOD");
        inspectionItemDTO.setReturnReason("Test Return Reason");
        inspectionItemDTO.setExpectedItemCondition("GOOD");
        inspectionItemDTO.setEnableReturnReasonEdit(true);

        // Initialize InspectionItemUpdateReqDTO with JsonNode data
        updateReqDTO = new InspectionItemUpdateReqDTO();
        updateReqDTO.setInspectionItemId("test-item-id");
        try {
            JsonNode data = objectMapper.createObjectNode()
                .put("returnReason", "Updated Return Reason")
                .put("itemCondition", "DAMAGED");
            updateReqDTO.setData(data);
        } catch (Exception e) {
            throw new RuntimeException("Error creating JsonNode", e);
        }

        // Initialize ItemStatusUpdateReqDTO
        statusUpdateReqDTO = new ItemStatusUpdateReqDTO();
        statusUpdateReqDTO.setStatus(InspectionItem.InspectionItemStatus.APPROVED);
        statusUpdateReqDTO.setItemConditionId("test-condition-id");
        statusUpdateReqDTO.setReview("Test review");

        // Initialize InstructionDTO
        instructionDTO = new InstructionDTO();
        instructionDTO.setLanguageCode("en");
        instructionDTO.setContent("Test Instruction Content");

        // Initialize TempMediaDTO
        tempMediaDTO = new TempMediaDTO();
        tempMediaDTO.setFileName("test-image.jpg");
        tempMediaDTO.setMediaType("image/jpeg");
        tempMediaDTO.setUrl("http://test-url.com/test-image.jpg");
    }

    @Test
    void testUpdate() throws IOException {
        String inspectionItemId = "test-item-id";
        when(inspectionItemService.update(eq(inspectionItemId), any(InspectionItemUpdateReqDTO.class)))
            .thenReturn(inspectionItemDTO);

        ResponseEntity<InspectionItemDTO> response = inspectionItemController.update(inspectionItemId, updateReqDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(inspectionItemDTO, response.getBody());
        verify(inspectionItemService).update(inspectionItemId, updateReqDTO);
    }

    @Test
    void testUpdateStatus() {
        String inspectionItemId = "test-item-id";
        when(inspectionItemService.updateStatus(eq(inspectionItemId), any(ItemStatusUpdateReqDTO.class)))
            .thenReturn(inspectionItemDTO);

        ResponseEntity<InspectionItemDTO> response = inspectionItemController.updateStatus(inspectionItemId, statusUpdateReqDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(inspectionItemDTO, response.getBody());
        verify(inspectionItemService).updateStatus(inspectionItemId, statusUpdateReqDTO);
    }

    @Test
    void testGetReturnReasons() {
        List<String> expectedReasons = Arrays.asList("Reason 1", "Reason 2");
        when(inspectionItemService.getReturnReasons()).thenReturn(expectedReasons);

        ResponseEntity<List<String>> response = inspectionItemController.getReturnReasons();

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedReasons, response.getBody());
        verify(inspectionItemService).getReturnReasons();
    }

    @Test
    void testFindByProductInstruction() {
        String inspectionItemId = "test-item-id";
        String languageCode = "en";
        when(inspectionItemService.findByProductInstruction(inspectionItemId, languageCode))
            .thenReturn(instructionDTO);

        ResponseEntity<InstructionDTO> response = inspectionItemController.findByProductInstruction(inspectionItemId, languageCode);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(instructionDTO, response.getBody());
        verify(inspectionItemService).findByProductInstruction(inspectionItemId, languageCode);
    }

    @Test
    void testGetTempImages() {
        String inspectionItemId = "test-item-id";
        List<TempMediaDTO> expectedImages = Arrays.asList(tempMediaDTO);
        when(inspectionItemService.getTempImages(inspectionItemId)).thenReturn(expectedImages);

        ResponseEntity<List<TempMediaDTO>> response = inspectionItemController.getTempImages(inspectionItemId);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedImages, response.getBody());
        verify(inspectionItemService).getTempImages(inspectionItemId);
    }
} 