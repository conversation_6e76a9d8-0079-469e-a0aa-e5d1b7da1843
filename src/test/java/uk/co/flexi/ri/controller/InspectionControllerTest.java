package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItemCondition;
import uk.co.flexi.ri.service.InspectionService;
import uk.co.flexi.ri.service.UserGroupService;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InspectionControllerTest {

    @Mock
    private InspectionService inspectionService;

    @Mock
    private UserGroupService userGroupService;

    @InjectMocks
    private InspectionController inspectionController;

    private InspectionDTO inspectionDTO;
    private String inspectionId;
    private String referenceId;
    private List<InspectionQtyUpdateReqDTO> qtyUpdateReqDTOList;
    private InspectionStatusUpdateReqDTO statusUpdateReqDTO;
    private AssignedPackagesFilterDTO filterDTO;
    private Pageable pageable;

    @BeforeEach
    void setUp() {
        inspectionId = "test-inspection-id";
        referenceId = "test-reference-id";
        
        inspectionDTO = new InspectionDTO();
        inspectionDTO.setInspectionId(inspectionId);
        inspectionDTO.setReferenceId(referenceId);
        inspectionDTO.setStatus(Inspection.InspectionStatus.IN_PROGRESS);

        qtyUpdateReqDTOList = Arrays.asList(new InspectionQtyUpdateReqDTO());
        statusUpdateReqDTO = new InspectionStatusUpdateReqDTO();
        filterDTO = new AssignedPackagesFilterDTO();
        pageable = PageRequest.of(0, 10);
    }

    @Test
    void create_ShouldReturnCreatedInspection() {
        // Arrange
        when(inspectionService.save(any(InspectionDTO.class))).thenReturn(inspectionDTO);

        // Act
        ResponseEntity<InspectionDTO> response = inspectionController.create(inspectionDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(inspectionDTO, response.getBody());
        verify(inspectionService).save(inspectionDTO);
    }

    @Test
    void findById_ShouldReturnInspection() {
        // Arrange
        when(inspectionService.findById(inspectionId)).thenReturn(inspectionDTO);

        // Act
        ResponseEntity<InspectionDTO> response = inspectionController.findById(inspectionId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(inspectionDTO, response.getBody());
        verify(inspectionService).findById(inspectionId);
    }

    @Test
    void findById_ShouldThrowException_WhenInspectionNotFound() {
        // Arrange
        when(inspectionService.findById(inspectionId))
            .thenThrow(new NoSuchElementException("Inspection not found"));

        // Act & Assert
        assertThrows(NoSuchElementException.class, () -> inspectionController.findById(inspectionId));
    }

    @Test
    void findByReferenceIdId_ShouldReturnInspections() {
        // Arrange
        List<InspectionDTO> expectedInspections = Arrays.asList(inspectionDTO);
        when(inspectionService.findByReferenceIdId(referenceId)).thenReturn(expectedInspections);

        // Act
        ResponseEntity<List<InspectionDTO>> response = inspectionController.findByReferenceIdId(referenceId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedInspections, response.getBody());
        verify(inspectionService).findByReferenceIdId(referenceId);
    }

    @Test
    void updateInspectionQty_ShouldReturnUpdatedInspection() {
        // Arrange
        when(inspectionService.updateInspectionQty(eq(inspectionId), anyList())).thenReturn(inspectionDTO);

        // Act
        ResponseEntity<InspectionDTO> response = inspectionController.updateInspectionQty(inspectionId, qtyUpdateReqDTOList);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(inspectionDTO, response.getBody());
        verify(inspectionService).updateInspectionQty(inspectionId, qtyUpdateReqDTOList);
    }

    @Test
    void updateInspectionQtyCheck_ShouldReturnCheckResult() {
        // Arrange
        Map<String, Object> expectedResult = Map.of("status", "success");
        when(inspectionService.updateInspectionQtyCheck(eq(inspectionId), anyList())).thenReturn(expectedResult);

        // Act
        ResponseEntity<Map<String, Object>> response = inspectionController.updateInspectionQtyCheck(inspectionId, qtyUpdateReqDTOList);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedResult, response.getBody());
        verify(inspectionService).updateInspectionQtyCheck(inspectionId, qtyUpdateReqDTOList);
    }

    @Test
    void updateStatus_ShouldReturnUpdatedInspection() {
        // Arrange
        when(inspectionService.updateStatus(eq(inspectionId), any(InspectionStatusUpdateReqDTO.class)))
            .thenReturn(inspectionDTO);

        // Act
        ResponseEntity<InspectionDTO> response = inspectionController.updateStatus(inspectionId, statusUpdateReqDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(inspectionDTO, response.getBody());
        verify(inspectionService).updateStatus(inspectionId, statusUpdateReqDTO);
    }

    @Test
    void deleteInspection_ShouldReturnSuccessMessage() {
        // Arrange
        doNothing().when(inspectionService).deleteInspection(referenceId);

        // Act
        ResponseEntity<String> response = inspectionController.deleteInspection(referenceId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("Deleted Inspections with reference ID: " + referenceId, response.getBody());
        verify(inspectionService).deleteInspection(referenceId);
    }

    @Test
    void deleteAllInspection_ShouldReturnSuccessMessage() {
        // Arrange
        String merchantName = "test-merchant";
        doNothing().when(inspectionService).deleteAllInspection(merchantName);

        // Act
        ResponseEntity<String> response = inspectionController.deleteAllInspection(merchantName);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("Deleted All Inspections", response.getBody());
        verify(inspectionService).deleteAllInspection(merchantName);
    }

    @Test
    void getAssignedPackages_ShouldReturnPageOfPackages() {
        // Arrange
        List<AssignedPackagesDTO> packages = Arrays.asList(new AssignedPackagesDTO());
        Page<AssignedPackagesDTO> expectedPage = new PageImpl<>(packages);
        when(inspectionService.getAllAssignedPackages(eq(filterDTO), any(Pageable.class)))
            .thenReturn(expectedPage);

        // Act
        ResponseEntity<Page<AssignedPackagesDTO>> response = 
            inspectionController.getAssignedPackages(filterDTO, pageable);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedPage, response.getBody());
        verify(inspectionService).getAllAssignedPackages(filterDTO, pageable);
    }

    @Test
    void getAllReviewers_ShouldReturnReviewers() {
        // Arrange
        List<UserGroupDTO> expectedReviewers = Arrays.asList(new UserGroupDTO());
        when(userGroupService.getUserGroupReviewers()).thenReturn(expectedReviewers);

        // Act
        ResponseEntity<List<UserGroupDTO>> response = inspectionController.getAllReviewers(inspectionId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedReviewers, response.getBody());
        verify(userGroupService).getUserGroupReviewers();
    }

    @Test
    void getInspectionItemCondition_ShouldReturnConditions() {
        // Arrange
        List<InspectionItemConditionDTO> expectedConditions = Arrays.asList(new InspectionItemConditionDTO());
        when(inspectionService.getInspectionItemCondition(InspectionItemCondition.ConditionType.APPROVED))
            .thenReturn(expectedConditions);

        // Act
        ResponseEntity<List<InspectionItemConditionDTO>> response = 
            inspectionController.getInspectionItemCondition(inspectionId, InspectionItemCondition.ConditionType.APPROVED);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedConditions, response.getBody());
        verify(inspectionService).getInspectionItemCondition(InspectionItemCondition.ConditionType.APPROVED);
    }
} 