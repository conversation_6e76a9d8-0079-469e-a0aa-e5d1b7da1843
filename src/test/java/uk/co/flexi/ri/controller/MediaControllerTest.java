package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.MediaDTO;
import uk.co.flexi.ri.service.MediaService;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

class MediaControllerTest {

    @Mock
    private MediaService mediaService;

    @InjectMocks
    private MediaController mediaController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void uploadFile_ShouldReturnUploadedMedia() throws IOException {
        // Arrange
        MockMultipartFile mockFile = new MockMultipartFile(
            "file",
            "test.jpg",
            "image/jpeg",
            "test image content".getBytes()
        );
        List<MultipartFile> files = Arrays.asList(mockFile);
        String inspectionItemId = "test-inspection-id";
        String commentId = "test-comment-id";
        List<MediaDTO> mediaDTOs = Arrays.asList(new MediaDTO());
        when(mediaService.uploadFile(anyList(), any(MediaDTO.class))).thenReturn(mediaDTOs);

        // Act
        ResponseEntity<List<MediaDTO>> response = mediaController.uploadFile(files, inspectionItemId, commentId);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(mediaDTOs, response.getBody());
    }

    @Test
    void findById_ShouldReturnMediaResource() {
        // Arrange
        String fileName = "test.jpg";
        String mediaType = "image/jpeg";
        ResponseEntity<InputStreamResource> expectedResponse = ResponseEntity.ok().build();
        when(mediaService.downloadFile(fileName, mediaType)).thenReturn(expectedResponse);

        // Act
        ResponseEntity<InputStreamResource> response = mediaController.findById(fileName, mediaType);

        // Assert
        assertEquals(expectedResponse, response);
    }

    @Test
    void uploadTempFile_ShouldUploadTemporaryFile() {
        // Arrange
        MockMultipartFile mockFile = new MockMultipartFile(
            "file",
            "test.jpg",
            "image/jpeg",
            "test image content".getBytes()
        );
        List<MultipartFile> files = Arrays.asList(mockFile);
        String token = "test-token";

        // Act & Assert
        mediaController.uploadTempFile(token, files);
        // No assertion needed as the method is void
    }
}