package uk.co.flexi.ri.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.AuthProviderConfigDTO;
import uk.co.flexi.ri.model.AuthProvider;
import uk.co.flexi.ri.service.AuthProviderService;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthProviderControllerTest {

    @Mock
    private AuthProviderService authProviderService;

    @InjectMocks
    private AuthProviderController authProviderController;

    private ObjectMapper objectMapper;
    private List<String> authProviders;
    private AuthProviderConfigDTO authProviderConfigDTO;
    private JsonNode configJson;

    @BeforeEach
    void setUp() throws IOException {
        objectMapper = new ObjectMapper();
        
        // Create test auth providers list
        authProviders = Arrays.asList("GOOGLE", "AZURE", "OKTA", "MANHATTAN");

        // Create test auth provider config
        authProviderConfigDTO = AuthProviderConfigDTO.builder()
                .clientId("test-client-id")
                .clientSecret("test-client-secret")
                .redirectUri("http://test.com/code/test")
                .tenantId("test-tenant-id")
                .domain("test-domain.com")
                .build();

        // Create test JSON config
        String configJsonString = """
            {
                "clientId": "test-client-id",
                "clientSecret": "test-client-secret",
                "redirectUri": "http://test.com/code/test",
                "tenantId": "test-tenant-id",
                "domain": "test-domain.com"
            }
            """;
        configJson = objectMapper.readTree(configJsonString);
    }

    @Test
    void testGetAuthProviders() {
        // Arrange
        when(authProviderService.getAuthProviders()).thenReturn(authProviders);

        // Act
        ResponseEntity<List<String>> response = authProviderController.getAuthProviders();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(authProviders, response.getBody());
        verify(authProviderService, times(1)).getAuthProviders();
    }

    @Test
    void testAddSSOConfig() throws IOException {
        // Arrange
        when(authProviderService.addSSOConfig(any(AuthProvider.Provider.class), any(JsonNode.class)))
                .thenReturn(authProviderConfigDTO);

        // Act
        ResponseEntity<AuthProviderConfigDTO> response = authProviderController.addSSOConfig(
                AuthProvider.Provider.GOOGLE, configJson);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(authProviderConfigDTO, response.getBody());
        verify(authProviderService, times(1)).addSSOConfig(AuthProvider.Provider.GOOGLE, configJson);
    }

    @Test
    void testGetSSOConfig() {
        // Arrange
        when(authProviderService.getSSOConfig(any(AuthProvider.Provider.class)))
                .thenReturn(authProviderConfigDTO);

        // Act
        ResponseEntity<AuthProviderConfigDTO> response = authProviderController.getSSOConfig(
                AuthProvider.Provider.GOOGLE);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(authProviderConfigDTO, response.getBody());
        verify(authProviderService, times(1)).getSSOConfig(AuthProvider.Provider.GOOGLE);
    }
} 