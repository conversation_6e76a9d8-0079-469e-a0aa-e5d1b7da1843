package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.redirectedUrl;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class DefaultControllerTest {

    @InjectMocks
    private DefaultController defaultController;

    private MockMvc mockMvc;

    @Test
    void redirectToSwagger_ShouldRedirectToSwaggerUI() throws Exception {
        // Arrange
        mockMvc = MockMvcBuilders.standaloneSetup(defaultController).build();

        // Act & Assert
        mockMvc.perform(get("/"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/swagger-ui/index.html"));
    }

    @Test
    void redirectServerToSwagger_ShouldRedirectToSwaggerUI() throws Exception {
        // Arrange
        mockMvc = MockMvcBuilders.standaloneSetup(defaultController).build();

        // Act & Assert
        mockMvc.perform(get("/api/v1/swagger"))
                .andExpect(status().is3xxRedirection())
                .andExpect(redirectedUrl("/swagger-ui/index.html"));
    }
} 