package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.ChannelDTO;
import uk.co.flexi.ri.service.ChannelService;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ChannelControllerTest {

    @Mock
    private ChannelService channelService;

    @InjectMocks
    private ChannelController channelController;

    private ChannelDTO channelDTO;

    @BeforeEach
    void setUp() {
        channelDTO = new ChannelDTO();
        channelDTO.setId(1L);
        channelDTO.setChannelId("test-channel-id");
        channelDTO.setName("Test Channel");
        channelDTO.setServiceLevelAgreement(24); // 24 hours SLA
    }

    @Test
    void testCreate() {
        when(channelService.save(any(ChannelDTO.class))).thenReturn(channelDTO);

        ResponseEntity<ChannelDTO> response = channelController.create(channelDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(channelDTO, response.getBody());
        verify(channelService).save(channelDTO);
    }

    @Test
    void testUpdate() {
        String channelId = "test-channel-id";
        when(channelService.update(eq(channelId), any(ChannelDTO.class))).thenReturn(channelDTO);

        ResponseEntity<ChannelDTO> response = channelController.update(channelId, channelDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(channelDTO, response.getBody());
        verify(channelService).update(channelId, channelDTO);
    }

    @Test
    void testFindAll() {
        List<ChannelDTO> expectedChannels = Arrays.asList(channelDTO);
        when(channelService.findAll()).thenReturn(expectedChannels);

        ResponseEntity<List<ChannelDTO>> response = channelController.findAll();

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedChannels, response.getBody());
        verify(channelService).findAll();
    }

    @Test
    void testFindById() {
        String channelId = "test-channel-id";
        when(channelService.findById(channelId)).thenReturn(channelDTO);

        ResponseEntity<ChannelDTO> response = channelController.findById(channelId);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(channelDTO, response.getBody());
        verify(channelService).findById(channelId);
    }

    @Test
    void testDelete() {
        String channelId = "test-channel-id";
        doNothing().when(channelService).delete(channelId);

        channelController.delete(channelId);

        verify(channelService).delete(channelId);
    }
} 