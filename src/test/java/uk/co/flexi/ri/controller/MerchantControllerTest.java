package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.MerchantDTO;
import uk.co.flexi.ri.service.MerchantService;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MerchantControllerTest {

    @Mock
    private MerchantService merchantService;

    @InjectMocks
    private MerchantController merchantController;

    private MerchantDTO merchantDTO;

    @BeforeEach
    void setUp() {
        merchantDTO = new MerchantDTO();
        merchantDTO.setId(1L);
        merchantDTO.setMerchantName("Test Merchant");
        merchantDTO.setMerchantEmail("<EMAIL>");
        merchantDTO.setServiceLevelAgreement(24);
        merchantDTO.setReturnReason(Arrays.asList("Reason 1", "Reason 2"));
        merchantDTO.setTenant(1001L);
    }

    @Test
    void testGetMerchantDetails() {
        when(merchantService.getMerchantDetails()).thenReturn(merchantDTO);

        ResponseEntity<MerchantDTO> response = merchantController.getMerchantDetails();

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(merchantDTO, response.getBody());
        verify(merchantService).getMerchantDetails();
    }
} 