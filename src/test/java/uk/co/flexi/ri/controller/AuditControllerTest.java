package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.HistoryResDTO;
import uk.co.flexi.ri.service.AuditService;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class AuditControllerTest {

    @Mock
    private AuditService auditService;

    @InjectMocks
    private AuditController auditController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void categories_ShouldReturnCategoriesList() {
        // Arrange
        List<String> categories = Arrays.asList("CATEGORY1", "CATEGORY2");
        when(auditService.getCategories()).thenReturn(categories);

        // Act
        ResponseEntity<List<String>> response = auditController.categories();

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(categories, response.getBody());
    }

    @Test
    void history_ShouldReturnAuditLogs() {
        // Arrange
        List<HistoryResDTO> historyList = Arrays.asList(new HistoryResDTO(), new HistoryResDTO());
        Page<HistoryResDTO> historyPage = new PageImpl<>(historyList);
        when(auditService.getAuditLogs(
            any(AuditService.AuditCategory.class),
            any(),
            any(),
            any(),
            any(),
            any(),
            any(Pageable.class)
        )).thenReturn(historyPage);

        // Act
        ResponseEntity<Page<HistoryResDTO>> response = auditController.history(
            AuditService.AuditCategory.INSPECTION,
            null,
            null,
            null,
            null,
            null,
            Pageable.unpaged()
        );

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(historyPage, response.getBody());
    }
} 