package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.OrderDetailDTO;
import uk.co.flexi.ri.dto.ProductDetailDTO;
import uk.co.flexi.ri.service.OrderService;

import java.math.BigDecimal;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OrderControllerTest {

    @Mock
    private OrderService orderService;

    @InjectMocks
    private OrderController orderController;

    private OrderDetailDTO orderDetailDTO;
    private ProductDetailDTO productDetailDTO;

    @BeforeEach
    void setUp() {
        // Create test product detail
        productDetailDTO = new ProductDetailDTO();
        productDetailDTO.setProductName("Test Product");
        productDetailDTO.setSku("TEST-SKU-001");
        productDetailDTO.setDescription("Test Description");
        productDetailDTO.setExpectedQuantity(BigDecimal.valueOf(2));
        productDetailDTO.setReceivedQuantity(BigDecimal.ZERO);
        productDetailDTO.setColour("Red");
        productDetailDTO.setSize("M");
        productDetailDTO.setReturnReason("Wrong Size");
        productDetailDTO.setProductClass("Clothing");
        productDetailDTO.setProductSubclass("T-Shirt");
        productDetailDTO.setSeason("Summer");
        productDetailDTO.setBrand("Test Brand");
        productDetailDTO.setDepartmentName("Men's Wear");
        productDetailDTO.setStyle("Casual");
        productDetailDTO.setCountry("UK");
        productDetailDTO.setRegion("London");
        productDetailDTO.setCity("London");
        productDetailDTO.setExpectedItemCondition("New");
        productDetailDTO.setCurrencyCode("GBP");
        productDetailDTO.setUnitPrice(BigDecimal.valueOf(29.99));

        // Create test order detail
        orderDetailDTO = new OrderDetailDTO();
        orderDetailDTO.setInspectionId("INSP-001");
        orderDetailDTO.setOrderId("ORD-001");
        orderDetailDTO.setReturnOrderId("RET-001");
        orderDetailDTO.setReturnTrackingId("TRK-001");
        orderDetailDTO.setReferenceId("REF-001");
        orderDetailDTO.setPartnerName("Test Partner");
        orderDetailDTO.setSellingChannel("Online");
        orderDetailDTO.setCustomerFirstName("John");
        orderDetailDTO.setCustomerLastName("Doe");
        orderDetailDTO.setCustomerEmail("<EMAIL>");
        orderDetailDTO.setProductDetails(Arrays.asList(productDetailDTO));
    }

    @Test
    void testGetOrderDetails() {
        // Arrange
        String input = "REF-001";
        String inspectionId = "INSP-001";
        when(orderService.getOrderDetails(eq(input), eq(inspectionId))).thenReturn(orderDetailDTO);

        // Act
        ResponseEntity<OrderDetailDTO> response = orderController.getOrderDetails(input, inspectionId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(orderDetailDTO, response.getBody());
        verify(orderService).getOrderDetails(input, inspectionId);
    }

    @Test
    void testGetOrderDetailsWithoutInspectionId() {
        // Arrange
        String input = "REF-001";
        when(orderService.getOrderDetails(eq(input), eq(null))).thenReturn(orderDetailDTO);

        // Act
        ResponseEntity<OrderDetailDTO> response = orderController.getOrderDetails(input, null);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(orderDetailDTO, response.getBody());
        verify(orderService).getOrderDetails(input, null);
    }
} 