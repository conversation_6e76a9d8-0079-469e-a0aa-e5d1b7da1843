package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.SupportedLanguageDTO;
import uk.co.flexi.ri.service.LanguageService;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LanguageControllerTest {

    @Mock
    private LanguageService languageService;

    @InjectMocks
    private LanguageController languageController;

    private SupportedLanguageDTO languageDTO1;
    private SupportedLanguageDTO languageDTO2;

    @BeforeEach
    void setUp() {
        languageDTO1 = new SupportedLanguageDTO();
        languageDTO1.setCode("en");
        languageDTO1.setName("English");

        languageDTO2 = new SupportedLanguageDTO();
        languageDTO2.setCode("fr");
        languageDTO2.setName("French");
    }

    @Test
    void testFindByConditionType() {
        List<SupportedLanguageDTO> expectedLanguages = Arrays.asList(languageDTO1, languageDTO2);
        when(languageService.findAllSupportedLanguages()).thenReturn(expectedLanguages);

        ResponseEntity<List<SupportedLanguageDTO>> response = languageController.findByConditionType();

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedLanguages, response.getBody());
        verify(languageService).findAllSupportedLanguages();
    }
} 