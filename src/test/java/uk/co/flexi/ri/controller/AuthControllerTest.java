package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.exception.custom.RIAuthenticationException;
import uk.co.flexi.ri.service.AuthService;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AuthControllerTest {

    @Mock
    private AuthService authService;

    @InjectMocks
    private AuthController authController;

    private AuthReqDTO authReqDTO;
    private AuthResDTO authResDTO;
    private RefreshTokenReqDTO refreshTokenReqDTO;
    private UpdateTokenReqDTO updateTokenReqDTO;
    private ImageTokenReqDTO imageTokenReqDTO;

    @BeforeEach
    void setUp() {
        // Create test auth request
        authReqDTO = new AuthReqDTO("testuser", "password123");

        // Create test auth response
        authResDTO = new AuthResDTO("test-access-token", "test-refresh-token");

        // Create test refresh token request
        refreshTokenReqDTO = new RefreshTokenReqDTO();
        refreshTokenReqDTO.setRefreshToken("test-refresh-token");

        // Create test update token request
        updateTokenReqDTO = new UpdateTokenReqDTO();
        updateTokenReqDTO.setUserName("testuser");
        updateTokenReqDTO.setTenant(1L);

        // Create test image token request
        imageTokenReqDTO = new ImageTokenReqDTO();
        imageTokenReqDTO.setUserId("test-user-id");
        imageTokenReqDTO.setInspectionItemId("test-inspection-id");
    }

    @Test
    void testAuthenticate() throws RIAuthenticationException {
        // Arrange
        when(authService.authenticate(any(AuthReqDTO.class))).thenReturn(authResDTO);

        // Act
        ResponseEntity<AuthResDTO> response = authController.authenticate(authReqDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(authResDTO, response.getBody());
        verify(authService, times(1)).authenticate(authReqDTO);
    }

    @Test
    void testGetRefreshToken() throws RIAuthenticationException {
        // Arrange
        when(authService.generateRefreshToken(any(RefreshTokenReqDTO.class))).thenReturn(authResDTO);

        // Act
        ResponseEntity<AuthResDTO> response = authController.getRefreshToken(refreshTokenReqDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(authResDTO, response.getBody());
        verify(authService, times(1)).generateRefreshToken(refreshTokenReqDTO);
    }

    @Test
    void testUpdateToken() throws RIAuthenticationException {
        // Arrange
        when(authService.regenerateToken(any(UpdateTokenReqDTO.class))).thenReturn(authResDTO);

        // Act
        ResponseEntity<AuthResDTO> response = authController.getRefreshToken(updateTokenReqDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(authResDTO, response.getBody());
        verify(authService, times(1)).regenerateToken(updateTokenReqDTO);
    }

    @Test
    void testGenerateImageToken() throws RIAuthenticationException {
        // Arrange
        String expectedToken = "test-image-token";
        when(authService.generateImageToken(any(ImageTokenReqDTO.class))).thenReturn(expectedToken);

        // Act
        ResponseEntity<String> response = authController.generateImageToken(imageTokenReqDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(expectedToken, response.getBody());
        verify(authService, times(1)).generateImageToken(imageTokenReqDTO);
    }
} 