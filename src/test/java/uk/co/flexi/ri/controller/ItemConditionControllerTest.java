package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.InspectionItemConditionDTO;
import uk.co.flexi.ri.model.InspectionItemCondition;
import uk.co.flexi.ri.service.ItemConditionService;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ItemConditionControllerTest {

    @Mock
    private ItemConditionService itemConditionService;

    @InjectMocks
    private ItemConditionController itemConditionController;

    private InspectionItemConditionDTO itemConditionDTO;
    private String itemConditionId;

    @BeforeEach
    void setUp() {
        itemConditionId = "test-condition-id";
        itemConditionDTO = new InspectionItemConditionDTO();
        itemConditionDTO.setItemConditionId(itemConditionId);
        itemConditionDTO.setName("Test Condition");
        itemConditionDTO.setConditionType(InspectionItemCondition.ConditionType.APPROVED);
    }

    @Test
    void create_ShouldReturnCreatedItemCondition() {
        // Arrange
        when(itemConditionService.save(any(InspectionItemConditionDTO.class))).thenReturn(itemConditionDTO);

        // Act
        ResponseEntity<InspectionItemConditionDTO> response = itemConditionController.create(itemConditionDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(itemConditionDTO, response.getBody());
        verify(itemConditionService).save(itemConditionDTO);
    }

    @Test
    void update_ShouldReturnUpdatedItemCondition() {
        // Arrange
        when(itemConditionService.update(eq(itemConditionId), any(InspectionItemConditionDTO.class)))
            .thenReturn(itemConditionDTO);

        // Act
        ResponseEntity<InspectionItemConditionDTO> response = 
            itemConditionController.update(itemConditionId, itemConditionDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(itemConditionDTO, response.getBody());
        verify(itemConditionService).update(itemConditionId, itemConditionDTO);
    }

    @Test
    void update_ShouldThrowException_WhenItemConditionNotFound() {
        // Arrange
        when(itemConditionService.update(eq(itemConditionId), any(InspectionItemConditionDTO.class)))
            .thenThrow(new NoSuchElementException("Invalid Item Condition"));

        // Act & Assert
        assertThrows(NoSuchElementException.class, () -> 
            itemConditionController.update(itemConditionId, itemConditionDTO));
    }

    @Test
    void findByConditionType_ShouldReturnItemConditions() {
        // Arrange
        List<InspectionItemConditionDTO> expectedConditions = Arrays.asList(itemConditionDTO);
        when(itemConditionService.findByConditionType(InspectionItemCondition.ConditionType.APPROVED))
            .thenReturn(expectedConditions);

        // Act
        ResponseEntity<List<InspectionItemConditionDTO>> response = 
            itemConditionController.findByConditionType(InspectionItemCondition.ConditionType.APPROVED);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedConditions, response.getBody());
        verify(itemConditionService).findByConditionType(InspectionItemCondition.ConditionType.APPROVED);
    }

    @Test
    void findById_ShouldReturnItemCondition() {
        // Arrange
        when(itemConditionService.findById(itemConditionId)).thenReturn(itemConditionDTO);

        // Act
        ResponseEntity<InspectionItemConditionDTO> response = itemConditionController.findById(itemConditionId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(itemConditionDTO, response.getBody());
        verify(itemConditionService).findById(itemConditionId);
    }

    @Test
    void findById_ShouldThrowException_WhenItemConditionNotFound() {
        // Arrange
        when(itemConditionService.findById(itemConditionId))
            .thenThrow(new NoSuchElementException("Invalid Item Condition"));

        // Act & Assert
        assertThrows(NoSuchElementException.class, () -> itemConditionController.findById(itemConditionId));
    }

    @Test
    void delete_ShouldDeleteItemCondition() {
        // Arrange
        doNothing().when(itemConditionService).delete(itemConditionId);

        // Act
        itemConditionController.delete(itemConditionId);

        // Assert
        verify(itemConditionService).delete(itemConditionId);
    }

    @Test
    void delete_ShouldThrowException_WhenItemConditionNotFound() {
        // Arrange
        doThrow(new NoSuchElementException("Invalid Item Condition"))
            .when(itemConditionService).delete(itemConditionId);

        // Act & Assert
        assertThrows(NoSuchElementException.class, () -> itemConditionController.delete(itemConditionId));
    }
} 