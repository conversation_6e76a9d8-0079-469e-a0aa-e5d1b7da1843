package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.InstructionDTO;
import uk.co.flexi.ri.dto.ProductInstructionDTO;
import uk.co.flexi.ri.dto.ProductInstructionUpdateDTO;
import uk.co.flexi.ri.service.ProductService;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProductControllerTest {

    @Mock
    private ProductService productService;

    @InjectMocks
    private ProductController productController;

    private ProductInstructionDTO productInstructionDTO;
    private String productInstructionId;
    private List<ProductInstructionUpdateDTO> updateDTOs;
    private List<String> instructionIds;
    private MockMultipartFile multipartFile;

    @BeforeEach
    void setUp() {
        productInstructionId = "test-instruction-id";
        
        // Setup ProductInstructionDTO
        productInstructionDTO = new ProductInstructionDTO();
        productInstructionDTO.setProductInstructionId(productInstructionId);
        productInstructionDTO.setProductClass("TestClass");
        productInstructionDTO.setProductSubclass("TestSubclass");
        
        InstructionDTO instructionDTO = new InstructionDTO();
        instructionDTO.setLanguageCode("en");
        instructionDTO.setContent("Test content");
        productInstructionDTO.setInstructions(Arrays.asList(instructionDTO));

        // Setup update DTOs
        ProductInstructionUpdateDTO updateDTO = new ProductInstructionUpdateDTO();
        updateDTO.setLanguageCode("en");
        updateDTO.setContent("Updated content");
        updateDTOs = Arrays.asList(updateDTO);

        // Setup instruction IDs
        instructionIds = Arrays.asList("id1", "id2");

        // Setup multipart file
        multipartFile = new MockMultipartFile(
            "file",
            "test.csv",
            MediaType.TEXT_PLAIN_VALUE,
            "test content".getBytes()
        );
    }

    @Test
    void createProductInstruction_ShouldReturnCreatedInstruction() {
        // Arrange
        when(productService.createProductInstruction(any(ProductInstructionDTO.class)))
            .thenReturn(productInstructionDTO);

        // Act
        ResponseEntity<ProductInstructionDTO> response = productController.createProductInstruction(productInstructionDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(productInstructionDTO, response.getBody());
        verify(productService).createProductInstruction(productInstructionDTO);
    }

    @Test
    void findAllProductInstruction_ShouldReturnAllInstructions() {
        // Arrange
        List<ProductInstructionDTO> expectedInstructions = Arrays.asList(productInstructionDTO);
        when(productService.findAllProductInstruction()).thenReturn(expectedInstructions);

        // Act
        ResponseEntity<List<ProductInstructionDTO>> response = productController.findAllProductInstruction();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedInstructions, response.getBody());
        verify(productService).findAllProductInstruction();
    }

    @Test
    void findByIdAndLanguage_ShouldReturnInstruction() {
        // Arrange
        String languageCode = "en";
        when(productService.findByIdAndLanguage(productInstructionId, languageCode))
            .thenReturn(productInstructionDTO);

        // Act
        ResponseEntity<ProductInstructionDTO> response = 
            productController.findByIdAndLanguage(productInstructionId, languageCode);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(productInstructionDTO, response.getBody());
        verify(productService).findByIdAndLanguage(productInstructionId, languageCode);
    }

    @Test
    void findByIdAndLanguage_ShouldThrowException_WhenInstructionNotFound() {
        // Arrange
        String languageCode = "en";
        when(productService.findByIdAndLanguage(productInstructionId, languageCode))
            .thenThrow(new NoSuchElementException("Product Instruction not found"));

        // Act & Assert
        assertThrows(NoSuchElementException.class, 
            () -> productController.findByIdAndLanguage(productInstructionId, languageCode));
    }

    @Test
    void updateProductInstruction_ShouldReturnUpdatedInstruction() {
        // Arrange
        when(productService.updateProductInstruction(eq(productInstructionId), anyList()))
            .thenReturn(productInstructionDTO);

        // Act
        ResponseEntity<ProductInstructionDTO> response = 
            productController.updateProductInstruction(productInstructionId, updateDTOs);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(productInstructionDTO, response.getBody());
        verify(productService).updateProductInstruction(productInstructionId, updateDTOs);
    }

    @Test
    void deleteById_ShouldDeleteInstruction() {
        // Arrange
        doNothing().when(productService).deleteById(productInstructionId);

        // Act
        productController.deleteById(productInstructionId);

        // Assert
        verify(productService).deleteById(productInstructionId);
    }

    @Test
    void deleteByIds_ShouldDeleteMultipleInstructions() {
        // Arrange
        doNothing().when(productService).deleteByIds(instructionIds);

        // Act
        productController.deleteByIds(instructionIds);

        // Assert
        verify(productService).deleteByIds(instructionIds);
    }

    @Test
    void importProductInstruction_ShouldReturnImportedInstructions() throws IOException {
        // Arrange
        String format = "csv";
        List<ProductInstructionDTO> expectedInstructions = Arrays.asList(productInstructionDTO);
        when(productService.importProductInstruction(eq(format), any(MultipartFile.class)))
            .thenReturn(expectedInstructions);

        // Act
        ResponseEntity<List<ProductInstructionDTO>> response = 
            productController.importProductInstruction(format, multipartFile);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedInstructions, response.getBody());
        verify(productService).importProductInstruction(format, multipartFile);
    }

    @Test
    void exportProductInstruction_ShouldReturnFileBytes() {
        // Arrange
        String extension = "csv";
        byte[] expectedBytes = "test content".getBytes();
        when(productService.exportProductInstruction(eq(extension), anyList()))
            .thenReturn(expectedBytes);

        // Act
        ResponseEntity<byte[]> response = productController.exportProductInstruction(extension, instructionIds);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedBytes, response.getBody());
        assertEquals("attachment; filename=product_instruction." + extension, 
            response.getHeaders().getFirst(HttpHeaders.CONTENT_DISPOSITION));
        assertEquals(MediaType.APPLICATION_OCTET_STREAM, response.getHeaders().getContentType());
        verify(productService).exportProductInstruction(extension, instructionIds);
    }

    @Test
    void exportProductInstruction_ShouldHandleNullIds() {
        // Arrange
        String extension = "csv";
        byte[] expectedBytes = "test content".getBytes();
        when(productService.exportProductInstruction(eq(extension), eq(null)))
            .thenReturn(expectedBytes);

        // Act
        ResponseEntity<byte[]> response = productController.exportProductInstruction(extension, null);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedBytes, response.getBody());
        assertEquals("attachment; filename=product_instruction." + extension, 
            response.getHeaders().getFirst(HttpHeaders.CONTENT_DISPOSITION));
        assertEquals(MediaType.APPLICATION_OCTET_STREAM, response.getHeaders().getContentType());
        verify(productService).exportProductInstruction(extension, null);
    }
} 