package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.RoleDTO;
import uk.co.flexi.ri.dto.RoleGroupDTO;
import uk.co.flexi.ri.service.RoleGroupService;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RoleGroupControllerTest {

    @Mock
    private RoleGroupService roleGroupService;

    @InjectMocks
    private RoleGroupController roleGroupController;

    private RoleDTO roleDTO;
    private RoleGroupDTO roleGroupDTO;

    @BeforeEach
    void setUp() {
        // Create test role
        roleDTO = new RoleDTO();
        roleDTO.setId(1L);
        roleDTO.setName("TEST_ROLE");

        // Create test role group
        roleGroupDTO = new RoleGroupDTO();
        roleGroupDTO.setRoleGroupId("test-role-group-id");
        roleGroupDTO.setName("Test Role Group");
        Set<RoleDTO> roles = new HashSet<>();
        roles.add(roleDTO);
        roleGroupDTO.setRoles(roles);
    }

    @Test
    void testGetRoles() {
        // Arrange
        List<RoleDTO> expectedRoles = Arrays.asList(roleDTO);
        when(roleGroupService.getRoles()).thenReturn(expectedRoles);

        // Act
        ResponseEntity<List<RoleDTO>> response = roleGroupController.getRoles();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(expectedRoles, response.getBody());
        verify(roleGroupService, times(1)).getRoles();
    }

    @Test
    void testGetRoleGroups() {
        // Arrange
        List<RoleGroupDTO> expectedRoleGroups = Arrays.asList(roleGroupDTO);
        when(roleGroupService.getRoleGroups()).thenReturn(expectedRoleGroups);

        // Act
        ResponseEntity<List<RoleGroupDTO>> response = roleGroupController.getRoleGroups();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(expectedRoleGroups, response.getBody());
        verify(roleGroupService, times(1)).getRoleGroups();
    }

    @Test
    void testFindById() {
        // Arrange
        String roleGroupId = "test-role-group-id";
        when(roleGroupService.findRoleGroupDTOById(roleGroupId)).thenReturn(roleGroupDTO);

        // Act
        ResponseEntity<RoleGroupDTO> response = roleGroupController.findById(roleGroupId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(roleGroupDTO, response.getBody());
        verify(roleGroupService, times(1)).findRoleGroupDTOById(roleGroupId);
    }

    @Test
    void testCreateRoleGroup() {
        // Arrange
        when(roleGroupService.createRoleGroup(any(RoleGroupDTO.class))).thenReturn(roleGroupDTO);

        // Act
        ResponseEntity<RoleGroupDTO> response = roleGroupController.createRoleGroup(roleGroupDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(roleGroupDTO, response.getBody());
        verify(roleGroupService, times(1)).createRoleGroup(roleGroupDTO);
    }

    @Test
    void testUpdateRoleGroup() {
        // Arrange
        String roleGroupId = "test-role-group-id";
        when(roleGroupService.updateRoleGroup(eq(roleGroupId), any(RoleGroupDTO.class))).thenReturn(roleGroupDTO);

        // Act
        ResponseEntity<RoleGroupDTO> response = roleGroupController.updateRoleGroup(roleGroupId, roleGroupDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(roleGroupDTO, response.getBody());
        verify(roleGroupService, times(1)).updateRoleGroup(roleGroupId, roleGroupDTO);
    }

    @Test
    void testDeleteRoleGroups() {
        // Arrange
        String roleGroupId = "test-role-group-id";
        doNothing().when(roleGroupService).deleteRoleGroups(roleGroupId);

        // Act
        roleGroupController.deleteRoleGroups(roleGroupId);

        // Assert
        verify(roleGroupService, times(1)).deleteRoleGroups(roleGroupId);
    }
} 