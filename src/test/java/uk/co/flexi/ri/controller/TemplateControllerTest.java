package uk.co.flexi.ri.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.TemplateDTO;
import uk.co.flexi.ri.service.TemplateService;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TemplateControllerTest {

    @Mock
    private TemplateService templateService;

    @InjectMocks
    private TemplateController templateController;

    private TemplateDTO templateDTO1;
    private TemplateDTO templateDTO2;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() throws Exception {
        objectMapper = new ObjectMapper();
        JsonNode templateData1 = objectMapper.readTree("{\"key1\": \"value1\", \"key2\": \"value2\"}");
        JsonNode templateData2 = objectMapper.readTree("{\"key3\": \"value3\", \"key4\": \"value4\"}");

        templateDTO1 = new TemplateDTO();
        templateDTO1.setTemplateId("TEMPLATE-001");
        templateDTO1.setTemplateName("Test Template 1");
        templateDTO1.setTemplateData(templateData1);

        templateDTO2 = new TemplateDTO();
        templateDTO2.setTemplateId("TEMPLATE-002");
        templateDTO2.setTemplateName("Test Template 2");
        templateDTO2.setTemplateData(templateData2);
    }

    @Test
    void testFindAll() {
        List<TemplateDTO> expectedTemplates = Arrays.asList(templateDTO1, templateDTO2);
        when(templateService.getTemplates()).thenReturn(expectedTemplates);

        ResponseEntity<List<TemplateDTO>> response = templateController.findAll();

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedTemplates, response.getBody());
        verify(templateService).getTemplates();
    }
} 