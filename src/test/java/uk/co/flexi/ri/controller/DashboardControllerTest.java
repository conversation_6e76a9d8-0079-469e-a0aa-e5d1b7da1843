package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.InspectionCompletedDTO;
import uk.co.flexi.ri.dto.ItemConditionResponseDTO;
import uk.co.flexi.ri.dto.TreeMapResponseDTO;
import uk.co.flexi.ri.service.DashboardService;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class DashboardControllerTest {

    @Mock
    private DashboardService dashboardService;

    @InjectMocks
    private DashboardController dashboardController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void getCommonReturnReasons_ShouldReturnTreeMapResponse() {
        // Arrange
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();
        Integer level = 1;
        String filterValue = "test-filter";
        TreeMapResponseDTO expectedResponse = new TreeMapResponseDTO(100L, List.of());
        when(dashboardService.getCommonReturnReasons(any(LocalDate.class), any(LocalDate.class), anyString(), any(Integer.class)))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<TreeMapResponseDTO> response = dashboardController.getCommonReturnReasons(
            startDate, endDate, level, filterValue);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody());
    }

    @Test
    void getCommonItemCondition_ShouldReturnItemConditions() {
        // Arrange
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();
        List<ItemConditionResponseDTO> expectedResponse = List.of(new ItemConditionResponseDTO());
        when(dashboardService.getCommonItemCondition(any(LocalDate.class), any(LocalDate.class)))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<List<ItemConditionResponseDTO>> response = dashboardController.getCommonItemCondition(
            startDate, endDate);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody());
    }

    @Test
    void getItemConditionGroupedByChannelAndCategory_ShouldReturnGroupedData() throws Exception {
        // Arrange
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();
        String groupBy = "productClass";
        Map<String, Map<String, Map<String, Map<String, Object>>>> expectedResponse = Map.of();
        when(dashboardService.getItemConditionGroupedByChannelAndCategory(any(LocalDate.class), any(LocalDate.class), anyString()))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<Map<String, Map<String, Map<String, Map<String, Object>>>>> response = 
            dashboardController.getItemConditionGroupedByChannelAndCategory(startDate, endDate, groupBy);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody());
    }

    @Test
    void getInspectionStatus_ShouldReturnStatusWithPercentage() {
        // Arrange
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();
        Map<String, Map<String, Object>> expectedResponse = Map.of(
            "created", Map.of("count", 10L, "percentage", "33.33"),
            "completed", Map.of("count", 10L, "percentage", "33.33"),
            "sendForReview", Map.of("count", 10L, "percentage", "33.33")
        );
        when(dashboardService.getInspectionStatusWithPercentage(any(LocalDate.class), any(LocalDate.class)))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<Map<String, Map<String, Object>>> response = 
            dashboardController.getInspectionStatus(startDate, endDate);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody());
    }

    @Test
    void getTopProductsReturns_ShouldReturnTreeMapResponse() {
        // Arrange
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();
        String groupBy = "style";
        Integer level = 0;
        String styleFilter = "test-style";
        String skuFilter = "test-sku";
        TreeMapResponseDTO expectedResponse = new TreeMapResponseDTO(100L, List.of());
        when(dashboardService.getTopProductsReturns(any(LocalDate.class), any(LocalDate.class), anyString(), 
            anyString(), anyString(), any(Integer.class)))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<TreeMapResponseDTO> response = dashboardController.getTopProductsReturns(
            startDate, endDate, groupBy, level, styleFilter, skuFilter);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody());
    }

    @Test
    void getInspectionTimeTracker_ShouldReturnTimeTrackerData() {
        // Arrange
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();
        Map<String, Object> expectedResponse = Map.of(
            "total", 100,
            "data", List.of()
        );
        when(dashboardService.getInspectionTimeTracker(any(LocalDate.class), any(LocalDate.class)))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<Map<String, Object>> response = 
            dashboardController.getInspectionTimeTracker(startDate, endDate);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody());
    }

    @Test
    void getInspectionCompletedStatus_ShouldReturnCompletedStatus() {
        // Arrange
        LocalDate startDate = LocalDate.now().minusDays(7);
        LocalDate endDate = LocalDate.now();
        String granularity = "daily";
        InspectionCompletedDTO expectedResponse = new InspectionCompletedDTO();
        when(dashboardService.getInspectionCompletedStatus(any(LocalDate.class), any(LocalDate.class), anyString()))
            .thenReturn(expectedResponse);

        // Act
        ResponseEntity<InspectionCompletedDTO> response = 
            dashboardController.getInspectionCompletedStatus(startDate, endDate, granularity);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedResponse, response.getBody());
    }
} 