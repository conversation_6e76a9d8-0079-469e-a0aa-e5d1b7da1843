package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import uk.co.flexi.ri.dto.CommentDTO;
import uk.co.flexi.ri.dto.CommentMediaDTO;
import uk.co.flexi.ri.dto.CommentMediaUpdateDTO;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.service.CommentService;

import java.io.IOException;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class CommentControllerTest {

    @Mock
    private CommentService commentService;

    @InjectMocks
    private CommentController commentController;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void createComment_ShouldReturnCreatedComment() throws IOException {
        // Arrange
        CommentMediaDTO commentMediaDTO = new CommentMediaDTO();
        commentMediaDTO.setContent("Test comment");
        commentMediaDTO.setInspectionItemId("test-item-id");
        commentMediaDTO.setVisibility(Comment.CommentVisibility.INTERNAL);
        commentMediaDTO.setCommentType(Comment.CommentType.GENERAL);
        commentMediaDTO.setMediaFiles(Arrays.asList(
            new MockMultipartFile("file", "test.jpg", MediaType.IMAGE_JPEG_VALUE, "test image content".getBytes())
        ));

        CommentDTO expectedComment = new CommentDTO();
        expectedComment.setContent("Test comment");
        expectedComment.setInspectionItemId("test-item-id");
        when(commentService.save(any(CommentMediaDTO.class))).thenReturn(expectedComment);

        // Act
        ResponseEntity<CommentDTO> response = commentController.createComment(commentMediaDTO);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedComment, response.getBody());
    }

    @Test
    void updateComment_ShouldReturnUpdatedComment() throws IOException {
        // Arrange
        String commentId = "test-comment-id";
        CommentMediaUpdateDTO updateDTO = new CommentMediaUpdateDTO();
        updateDTO.setContent("Updated comment");
        updateDTO.setVisibility(Comment.CommentVisibility.EXTERNAL);
        updateDTO.setAddMediaFiles(Arrays.asList(
            new MockMultipartFile("file", "test.jpg", MediaType.IMAGE_JPEG_VALUE, "test image content".getBytes())
        ));

        CommentDTO expectedComment = new CommentDTO();
        expectedComment.setContent("Updated comment");
        expectedComment.setVisibility(Comment.CommentVisibility.EXTERNAL);
        when(commentService.update(anyString(), any(CommentMediaUpdateDTO.class))).thenReturn(expectedComment);

        // Act
        ResponseEntity<CommentDTO> response = commentController.updateComment(commentId, updateDTO);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedComment, response.getBody());
    }

    @Test
    void deleteComments_ShouldReturnSuccessMessage() {
        // Arrange
        String commentId = "test-comment-id";
        String expectedMessage = "Comment Deleted Cheers!! 👍 ";

        // Act
        ResponseEntity<String> response = commentController.deleteComments(commentId);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(expectedMessage, response.getBody());
    }
} 