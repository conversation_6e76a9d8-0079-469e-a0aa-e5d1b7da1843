package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.NotificationChannelDTO;
import uk.co.flexi.ri.model.NotificationChannel;
import uk.co.flexi.ri.service.NotificationChannelService;

import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;


import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NotificationChannelControllerTest {

    @Mock
    private NotificationChannelService notificationChannelService;

    @InjectMocks
    private NotificationChannelController notificationChannelController;


    @Test
    void getNotificationChannel_ShouldGetNotificationChannel() {

        // Arrange
        NotificationChannelDTO notificationChannelDTO = new NotificationChannelDTO();
        when(notificationChannelService.getNotificationChannelById(anyLong())).thenReturn(notificationChannelDTO);

        // Act
        ResponseEntity<NotificationChannelDTO> response = notificationChannelController.getNotificationChannel(1l);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(notificationChannelDTO, response.getBody());
        verify(notificationChannelService).getNotificationChannelById(1L);
    }

    @Test
    void getNotificationChannel_ShouldThrowException_WhenNotificationChannelNotFound() {

        // Arrange
        when(notificationChannelService.getNotificationChannelById(anyLong())).thenThrow(new NoSuchElementException());

        // Act & Assert
        assertThrows(NoSuchElementException.class,() -> notificationChannelService.getNotificationChannelById(1L));
    }

    @Test
    void getChannels_ShouldReturnsListOfChannels() {
        // Arrange
        List<NotificationChannel.Channel> channels = List.of(NotificationChannel.Channel.values());
        when(notificationChannelService.getChannelList()).thenReturn(channels);

        // Act
        ResponseEntity<List<NotificationChannel.Channel>> response = notificationChannelController.getChannels();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(channels, response.getBody());
        verify(notificationChannelService).getChannelList();
    }

    @Test
    void getChannelsReturns_ShouldReturnEmptyList_WhenNoChannelsAvailable() {
        when(notificationChannelService.getChannelList()).thenReturn(List.of());

        ResponseEntity<List<NotificationChannel.Channel>> response = notificationChannelController.getChannels();

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        verify(notificationChannelService).getChannelList();
    }

    @Test
    void getChannelConfigKeysReturnsConfigKeysForValidChannel() {
        // Arrange
        NotificationChannel.Channel channel = NotificationChannel.Channel.EMAIL;
        Map<String, Object> configKeys = Map.of("key1", "value1");
        when(notificationChannelService.getChannelConfigKeys(channel)).thenReturn(configKeys);

        // Act
        ResponseEntity<Map<String, Object>> response = notificationChannelController.getChannelConfigKeys(channel);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(configKeys, response.getBody());
        verify(notificationChannelService).getChannelConfigKeys(channel);
    }

    @Test
    void getChannelConfigKeysThrowsExceptionForInvalidChannel() {
        NotificationChannel.Channel invalidChannel = null;
        when(notificationChannelService.getChannelConfigKeys(invalidChannel)).thenThrow(IllegalArgumentException.class);

        assertThrows(IllegalArgumentException.class, () -> notificationChannelController.getChannelConfigKeys(invalidChannel));
        verify(notificationChannelService).getChannelConfigKeys(invalidChannel);
    }

    @Test
    void createNotificationChannel_ShouldCreateNotificationChannel() {

        //Arrange
        NotificationChannelDTO notificationChannelDTO = new NotificationChannelDTO();
        when(notificationChannelService.createNotificationChannel(any(NotificationChannelDTO.class))).thenReturn(notificationChannelDTO);

        //Act
        ResponseEntity<NotificationChannelDTO> response = notificationChannelController.createNotificationChannel(notificationChannelDTO);

        //Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(notificationChannelDTO, response.getBody());
        verify(notificationChannelService).createNotificationChannel(notificationChannelDTO);
    }

    @Test
    void updateNotificationChannel_ShouldUpdateNotificationChannel() {

        //Arrange
        NotificationChannelDTO notificationChannelDTO = new NotificationChannelDTO();
        when(notificationChannelService.updateNotificationChannel(anyLong(),any(NotificationChannelDTO.class)))
                .thenReturn(notificationChannelDTO);

        //Act
        ResponseEntity<NotificationChannelDTO> response = notificationChannelController.updateNotificationChannel(1l, notificationChannelDTO);

        //Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(notificationChannelDTO, response.getBody());
        verify(notificationChannelService).updateNotificationChannel(1l,notificationChannelDTO);
    }

    @Test
    void updateNotificationChannel_ShouldThrowException_WhenNotificationChannelNotFound() {

        // Arrange
        NotificationChannelDTO notificationChannelDTO = new NotificationChannelDTO();
        when(notificationChannelService.updateNotificationChannel(anyLong(),any(NotificationChannelDTO.class))).thenThrow(new NoSuchElementException());

        // Act & Assert
        assertThrows(NoSuchElementException.class,() -> notificationChannelService.updateNotificationChannel(1L,notificationChannelDTO));
    }

    @Test
    void deleteNotificationChannel() {
        //Arrange
        Long id = 1l;

        //Act
        notificationChannelController.deleteNotificationChannel(id);

        //Assert
        verify(notificationChannelService).deleteNotificationChannel(1L);
    }

    @Test
    void getChannelConfigReturnsListOfNotificationChannelDTOs() {
        List<NotificationChannelDTO> channelDTOs = List.of(new NotificationChannelDTO(), new NotificationChannelDTO());
        when(notificationChannelService.getAllNotificationChannels()).thenReturn(channelDTOs);

        ResponseEntity<List<NotificationChannelDTO>> response = notificationChannelController.getChannelConfig();

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(channelDTOs, response.getBody());
        verify(notificationChannelService).getAllNotificationChannels();
    }
}