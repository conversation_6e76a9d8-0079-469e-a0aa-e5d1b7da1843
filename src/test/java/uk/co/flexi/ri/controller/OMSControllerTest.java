package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.OMSConfigDTO;
import uk.co.flexi.ri.service.OMSConfigService;
import uk.co.flexi.ri.service.OMSProviderService;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OMSControllerTest {

    @Mock
    private OMSConfigService omsConfigService;

    @Mock
    private OMSProviderService omsProviderService;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    @InjectMocks
    private OMSController omsController;

    private List<String> omsList;
    private Map<String, String> configs;
    private List<String> strategies;
    private OMSConfigDTO omsConfigDTO;

    @BeforeEach
    void setUp() {
        // Create test OMS list
        omsList = Arrays.asList("MAO", "MOCK");

        // Create test configs
        configs = new HashMap<>();
        configs.put("auth_url", "https://test.com/auth");
        configs.put("base_path", "/api/v1");
        configs.put("username", "test_user");
        configs.put("password", "test_pass");

        // Create test strategies
        strategies = Arrays.asList("ORDER_ID", "TRACKING_ID", "REFERENCE_ID");

        // Create test OMS config DTO
        omsConfigDTO = new OMSConfigDTO();
        omsConfigDTO.setConfigs(configs);
        omsConfigDTO.setStrategies(strategies);
    }

    @Test
    void testGetOMSList() {
        // Arrange
        when(omsProviderService.getOMSList()).thenReturn(omsList);

        // Act
        ResponseEntity<List<String>> response = omsController.getOMSList();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(omsList, response.getBody());
        verify(omsProviderService).getOMSList();
    }

    @Test
    void testAddOMSConfig() {
        // Arrange
        String name = "MAO";
        when(omsConfigService.addOMSConfig(eq(name), eq(configs))).thenReturn(configs);
        when(omsProviderService.addSearchStrategies(eq(name), eq(strategies))).thenReturn(strategies);

        // Act
        ResponseEntity<OMSConfigDTO> response = omsController.addOMSConfig(name, omsConfigDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(configs, response.getBody().getConfigs());
        assertEquals(strategies, response.getBody().getStrategies());
        verify(omsConfigService).addOMSConfig(name, configs);
        verify(omsProviderService).addSearchStrategies(name, strategies);
    }

    @Test
    void testGetOMSConfig() {
        // Arrange
        String name = "MAO";
        when(omsConfigService.getOMSConfig(eq(name))).thenReturn(configs);

        // Act
        ResponseEntity<Map<String, String>> response = omsController.getOMSConfig(name);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(configs, response.getBody());
        verify(omsConfigService).getOMSConfig(name);
    }

    @Test
    void testGetSearchStrategies() {
        // Arrange
        String name = "MAO";
        when(omsProviderService.getSearchStrategies(eq(name))).thenReturn(strategies);

        // Act
        ResponseEntity<List<String>> response = omsController.getSearchStrategies(name);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(strategies, response.getBody());
        verify(omsProviderService).getSearchStrategies(name);
    }

    @Test
    void testGetAllSearchStrategies() {
        // Arrange
        String name = "MAO";
        when(omsProviderService.getAllSearchStrategies(eq(name))).thenReturn(strategies);

        // Act
        ResponseEntity<List<String>> response = omsController.getAllSearchStrategies(name);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(strategies, response.getBody());
        verify(omsProviderService).getAllSearchStrategies(name);
    }

    @Test
    void testTestConnection() {
        // Arrange
        String name = "MAO";
        Map<String, String> testResult = new HashMap<>();
        testResult.put("status", "success");
        when(omsProviderService.testConnection(eq(name))).thenReturn(testResult);

        // Act
        ResponseEntity<Map<String, String>> response = omsController.testConnection(name);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(testResult, response.getBody());
        verify(omsProviderService).testConnection(name);
    }
} 