package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.exception.custom.UserNotFoundException;
import uk.co.flexi.ri.model.AuthProvider;
import uk.co.flexi.ri.service.UserService;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserManagementControllerTest {

    @Mock
    private UserService userService;

    @InjectMocks
    private UserManagementController userManagementController;

    private UserDTO userDTO;
    private UserLoginDTO userLoginDTO;
    private UserDetailDTO userDetailDTO;
    private MerchantDTO merchantDTO;
    private String userId;
    private String userName;
    private MockMultipartFile multipartFile;

    @BeforeEach
    void setUp() {
        userId = "test-user-id";
        userName = "testuser";

        // Setup UserDTO
        userDTO = new UserDTO();
        userDTO.setUserId(userId);
        userDTO.setUserName(userName);
        userDTO.setFirstName("Test");
        userDTO.setLastName("User");
        userDTO.setIsActive(true);
        userDTO.setRoleGroup("ADMIN");
        userDTO.setUserGroup("ADMIN_GROUP");
        AuthProviderDTO authProviderDTO = new AuthProviderDTO();
        authProviderDTO.setProvider(AuthProvider.Provider.DB);
        userDTO.setAuthProvider(authProviderDTO);

        // Setup UserLoginDTO
        userLoginDTO = new UserLoginDTO();
        userLoginDTO.setUserName(userName);
        userLoginDTO.setAuthProvider(AuthProvider.Provider.DB.name());
        userLoginDTO.setUrl("http://localhost:8080/auth");

        // Setup UserDetailDTO
        userDetailDTO = new UserDetailDTO();
        userDetailDTO.setUserId(userId);
        userDetailDTO.setUserName(userName);
        userDetailDTO.setFirstName("Test");
        userDetailDTO.setLastName("User");
        userDetailDTO.setIsActive(true);
        userDetailDTO.setSupplierName("Test Supplier");
        userDetailDTO.setTimeZone("UTC");
        userDetailDTO.setLanguage("en");
        userDetailDTO.setUserGroup("ADMIN_GROUP");
        userDetailDTO.setReportingCurrency("USD");
        Set<RoleDTO> roles = new HashSet<>();
        RoleDTO roleDTO = new RoleDTO();
        roleDTO.setId(1L);
        roleDTO.setName("Administrator");
        roles.add(roleDTO);
        userDetailDTO.setRoles(roles);
        AuthProviderDTO userDetailAuthProvider = new AuthProviderDTO();
        userDetailAuthProvider.setProvider(AuthProvider.Provider.DB);
        userDetailDTO.setAuthProvider(userDetailAuthProvider);

        // Setup MerchantDTO
        merchantDTO = new MerchantDTO();
        merchantDTO.setId(1L);
        merchantDTO.setMerchantName("Test Merchant");
        merchantDTO.setMerchantEmail("<EMAIL>");
        merchantDTO.setServiceLevelAgreement(24);
        merchantDTO.setReturnReason(Arrays.asList("DAMAGED", "WRONG_ITEM"));
        merchantDTO.setTenant(1001L);

        // Setup multipart file
        multipartFile = new MockMultipartFile(
            "file",
            "test.jpg",
            "image/jpeg",
            "test image content".getBytes()
        );
    }

    @Test
    void create_ShouldReturnCreatedUser() {
        // Arrange
        when(userService.save(any(UserDTO.class))).thenReturn(userDTO);

        // Act
        ResponseEntity<UserDTO> response = userManagementController.create(userDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userDTO, response.getBody());
        verify(userService).save(userDTO);
    }

    @Test
    void update_ShouldReturnUpdatedUser() {
        // Arrange
        when(userService.update(eq(userId), any(UserDTO.class))).thenReturn(userDTO);

        // Act
        ResponseEntity<UserDTO> response = userManagementController.update(userId, userDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userDTO, response.getBody());
        verify(userService).update(userId, userDTO);
    }

    @Test
    void findAll_ShouldReturnPageOfUsers() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        List<UserDTO> users = Arrays.asList(userDTO);
        Page<UserDTO> expectedPage = new PageImpl<>(users);
        when(userService.findAll(anyBoolean(), any(Pageable.class))).thenReturn(expectedPage);

        // Act
        ResponseEntity<Page<UserDTO>> response = userManagementController.findAll(false, pageable);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedPage, response.getBody());
        verify(userService).findAll(false, pageable);
    }

    @Test
    void findById_ShouldReturnUser() {
        // Arrange
        when(userService.findByUserId(userId)).thenReturn(userDTO);

        // Act
        ResponseEntity<UserDTO> response = userManagementController.findById(userId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userDTO, response.getBody());
        verify(userService).findByUserId(userId);
    }

    @Test
    void findByUserName_ShouldReturnUser() {
        // Arrange
        when(userService.findUserByUserName(userName)).thenReturn(userDTO);

        // Act
        ResponseEntity<UserDTO> response = userManagementController.findByUserName(userName);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userDTO, response.getBody());
        verify(userService).findUserByUserName(userName);
    }

    @Test
    void activateUser_ShouldReturnActivatedUser() {
        // Arrange
        when(userService.activateUser(userId)).thenReturn(userDTO);

        // Act
        ResponseEntity<UserDTO> response = userManagementController.activateUser(userId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userDTO, response.getBody());
        verify(userService).activateUser(userId);
    }

    @Test
    void deactivateUser_ShouldReturnDeactivatedUser() {
        // Arrange
        when(userService.deactivateUser(userId)).thenReturn(userDTO);

        // Act
        ResponseEntity<UserDTO> response = userManagementController.deactivateUser(userId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userDTO, response.getBody());
        verify(userService).deactivateUser(userId);
    }

    @Test
    void findByEmail_ShouldReturnUserLogin() throws UserNotFoundException {
        // Arrange
        when(userService.findByUserName(userName)).thenReturn(userLoginDTO);

        // Act
        ResponseEntity<UserLoginDTO> response = userManagementController.findByEmail(userName);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userLoginDTO, response.getBody());
        verify(userService).findByUserName(userName);
    }

    @Test
    void findByEmail_ShouldThrowException_WhenUserNotFound() throws UserNotFoundException {
        // Arrange
        when(userService.findByUserName(userName))
            .thenThrow(new UserNotFoundException("User not found"));

        // Act & Assert
        assertThrows(UserNotFoundException.class, () -> userManagementController.findByEmail(userName));
    }

    @Test
    void getLoggedInUserDetails_ShouldReturnUserDetails() {
        // Arrange
        when(userService.getLoggedInUserDetails()).thenReturn(userDetailDTO);

        // Act
        ResponseEntity<UserDetailDTO> response = userManagementController.getLoggedInUserDetails();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userDetailDTO, response.getBody());
        verify(userService).getLoggedInUserDetails();
    }

    @Test
    void updateProfilePicture_ShouldReturnUpdatedUserDetails() throws Exception {
        // Arrange
        when(userService.updateProfilePicture(any(MultipartFile.class))).thenReturn(userDetailDTO);

        // Act
        ResponseEntity<UserDetailDTO> response = userManagementController.updateProfilePicture(multipartFile);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userDetailDTO, response.getBody());
        verify(userService).updateProfilePicture(multipartFile);
    }

    @Test
    void removeProfilePicture_ShouldReturnUpdatedUserDetails() {
        // Arrange
        when(userService.removeProfilePicture()).thenReturn(userDetailDTO);

        // Act
        ResponseEntity<UserDetailDTO> response = userManagementController.removeProfilePicture();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userDetailDTO, response.getBody());
        verify(userService).removeProfilePicture();
    }

    @Test
    void updateLoggedInUserTimeZone_ShouldReturnUpdatedUserDetails() {
        // Arrange
        String timeZone = "UTC";
        when(userService.updateLoggedInUserTimeZone(timeZone)).thenReturn(userDetailDTO);

        // Act
        ResponseEntity<UserDetailDTO> response = userManagementController.updateLoggedInUserTimeZone(timeZone);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userDetailDTO, response.getBody());
        verify(userService).updateLoggedInUserTimeZone(timeZone);
    }

    @Test
    void updateLoggedInUserLanguage_ShouldReturnUpdatedUserDetails() {
        // Arrange
        String language = "en";
        when(userService.updateLoggedInUserLanguage(language)).thenReturn(userDetailDTO);

        // Act
        ResponseEntity<UserDetailDTO> response = userManagementController.updateLoggedInUserLanguage(language);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(userDetailDTO, response.getBody());
        verify(userService).updateLoggedInUserLanguage(language);
    }

    @Test
    void getAllMerchantByUserName_ShouldReturnMerchants() {
        // Arrange
        List<MerchantDTO> expectedMerchants = Arrays.asList(merchantDTO);
        when(userService.getAllMerchantByUserName(userName)).thenReturn(expectedMerchants);

        // Act
        ResponseEntity<List<MerchantDTO>> response = userManagementController.getAllMerchantByUserName(userName);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedMerchants, response.getBody());
        verify(userService).getAllMerchantByUserName(userName);
    }

    @Test
    void searchUserName_ShouldReturnListOfUserNames() {
        // Arrange
        List<String> expectedUserNames = Arrays.asList("alice", "bob", "charlie");
        when(userService.searchUserName(userName)).thenReturn(expectedUserNames);

        // Act
        ResponseEntity<List<String>> response = userManagementController.searchUserName(userName);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedUserNames, response.getBody());
        verify(userService).searchUserName(userName);
    }
} 