package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.ReturnReasonDTO;
import uk.co.flexi.ri.service.ReturnReasonService;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReturnReasonControllerTest {

    @Mock
    private ReturnReasonService returnReasonService;

    @InjectMocks
    private ReturnReasonController returnReasonController;

    private ReturnReasonDTO returnReasonDTO;
    private String returnReasonId;

    @BeforeEach
    void setUp() {
        returnReasonId = "test-reason-id";
        returnReasonDTO = new ReturnReasonDTO();
        returnReasonDTO.setId(1L);
        returnReasonDTO.setReturnReasonId(returnReasonId);
        returnReasonDTO.setReason("Test Return Reason");
    }

    @Test
    void create_ShouldReturnCreatedReturnReason() {
        // Arrange
        when(returnReasonService.save(any(ReturnReasonDTO.class))).thenReturn(returnReasonDTO);

        // Act
        ResponseEntity<ReturnReasonDTO> response = returnReasonController.create(returnReasonDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(returnReasonDTO, response.getBody());
        verify(returnReasonService).save(returnReasonDTO);
    }

    @Test
    void update_ShouldReturnUpdatedReturnReason() {
        // Arrange
        when(returnReasonService.update(eq(returnReasonId), any(ReturnReasonDTO.class)))
            .thenReturn(returnReasonDTO);

        // Act
        ResponseEntity<ReturnReasonDTO> response = 
            returnReasonController.update(returnReasonId, returnReasonDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(returnReasonDTO, response.getBody());
        verify(returnReasonService).update(returnReasonId, returnReasonDTO);
    }

    @Test
    void update_ShouldThrowException_WhenReturnReasonNotFound() {
        // Arrange
        when(returnReasonService.update(eq(returnReasonId), any(ReturnReasonDTO.class)))
            .thenThrow(new NoSuchElementException("Invalid Return Reason"));

        // Act & Assert
        assertThrows(NoSuchElementException.class, () -> 
            returnReasonController.update(returnReasonId, returnReasonDTO));
    }

    @Test
    void findAll_ShouldReturnAllReturnReasons() {
        // Arrange
        List<ReturnReasonDTO> expectedReasons = Arrays.asList(returnReasonDTO);
        when(returnReasonService.findAll()).thenReturn(expectedReasons);

        // Act
        ResponseEntity<List<ReturnReasonDTO>> response = returnReasonController.findAll();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(expectedReasons, response.getBody());
        verify(returnReasonService).findAll();
    }

    @Test
    void findById_ShouldReturnReturnReason() {
        // Arrange
        when(returnReasonService.findById(returnReasonId)).thenReturn(returnReasonDTO);

        // Act
        ResponseEntity<ReturnReasonDTO> response = returnReasonController.findById(returnReasonId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(returnReasonDTO, response.getBody());
        verify(returnReasonService).findById(returnReasonId);
    }

    @Test
    void findById_ShouldThrowException_WhenReturnReasonNotFound() {
        // Arrange
        when(returnReasonService.findById(returnReasonId))
            .thenThrow(new NoSuchElementException("Invalid Return Reason"));

        // Act & Assert
        assertThrows(NoSuchElementException.class, () -> returnReasonController.findById(returnReasonId));
    }

    @Test
    void delete_ShouldDeleteReturnReason() {
        // Arrange
        doNothing().when(returnReasonService).delete(returnReasonId);

        // Act
        returnReasonController.delete(returnReasonId);

        // Assert
        verify(returnReasonService, times(1)).delete(returnReasonId);
        verifyNoMoreInteractions(returnReasonService);
    }

    @Test
    void delete_ShouldThrowException_WhenReturnReasonNotFound() {
        // Arrange
        doThrow(new NoSuchElementException("Invalid Return Reason"))
            .when(returnReasonService).delete(returnReasonId);

        // Act & Assert
        NoSuchElementException exception = assertThrows(NoSuchElementException.class, 
            () -> returnReasonController.delete(returnReasonId));
        assertEquals("Invalid Return Reason", exception.getMessage());
        verify(returnReasonService, times(1)).delete(returnReasonId);
        verifyNoMoreInteractions(returnReasonService);
    }

    @Test
    void delete_ShouldThrowException_WhenReturnReasonIdIsNull() {
        // Arrange
        doThrow(new IllegalArgumentException("Return Reason ID cannot be null"))
            .when(returnReasonService).delete(null);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
            () -> returnReasonController.delete(null));
        assertEquals("Return Reason ID cannot be null", exception.getMessage());
        verify(returnReasonService, times(1)).delete(null);
        verifyNoMoreInteractions(returnReasonService);
    }
} 