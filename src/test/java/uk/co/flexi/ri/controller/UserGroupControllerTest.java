package uk.co.flexi.ri.controller;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import uk.co.flexi.ri.dto.UserGroupDTO;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.service.UserGroupService;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserGroupControllerTest {

    @Mock
    private UserGroupService userGroupService;

    @InjectMocks
    private UserGroupController userGroupController;

    private UserGroupDTO userGroupDTO;

    @BeforeEach
    void setUp() {
        // Create test user group
        userGroupDTO = new UserGroupDTO();
        userGroupDTO.setId(1L);
        userGroupDTO.setUserGroupId("test-user-group-id");
        userGroupDTO.setName("Test User Group");
        userGroupDTO.setVisibility(Comment.CommentVisibility.INTERNAL);
        userGroupDTO.setPartnerName("Test Partner");
        userGroupDTO.setIsActive(true);
    }

    @Test
    void testCreate() {
        // Arrange
        when(userGroupService.save(any(UserGroupDTO.class))).thenReturn(userGroupDTO);

        // Act
        ResponseEntity<UserGroupDTO> response = userGroupController.create(userGroupDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(userGroupDTO, response.getBody());
        verify(userGroupService, times(1)).save(userGroupDTO);
    }

    @Test
    void testUpdate() {
        // Arrange
        String userGroupId = "test-user-group-id";
        when(userGroupService.update(eq(userGroupId), any(UserGroupDTO.class))).thenReturn(userGroupDTO);

        // Act
        ResponseEntity<UserGroupDTO> response = userGroupController.update(userGroupId, userGroupDTO);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(userGroupDTO, response.getBody());
        verify(userGroupService, times(1)).update(userGroupId, userGroupDTO);
    }

    @Test
    void testFindByUserGroupId() {
        // Arrange
        String userGroupId = "test-user-group-id";
        when(userGroupService.findUserGroupDTOById(userGroupId)).thenReturn(userGroupDTO);

        // Act
        ResponseEntity<UserGroupDTO> response = userGroupController.findByUserGroupId(userGroupId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(userGroupDTO, response.getBody());
        verify(userGroupService, times(1)).findUserGroupDTOById(userGroupId);
    }

    @Test
    void testActivateUserGroup() {
        // Arrange
        String userGroupId = "test-user-group-id";
        when(userGroupService.activateUserGroup(userGroupId)).thenReturn(userGroupDTO);

        // Act
        ResponseEntity<UserGroupDTO> response = userGroupController.activateUserGroup(userGroupId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(userGroupDTO, response.getBody());
        verify(userGroupService, times(1)).activateUserGroup(userGroupId);
    }

    @Test
    void testDeactivateUserGroup() {
        // Arrange
        String userGroupId = "test-user-group-id";
        when(userGroupService.deactivateUserGroup(userGroupId)).thenReturn(userGroupDTO);

        // Act
        ResponseEntity<UserGroupDTO> response = userGroupController.deactivateUserGroup(userGroupId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(userGroupDTO, response.getBody());
        verify(userGroupService, times(1)).deactivateUserGroup(userGroupId);
    }

    @Test
    void testFindAll() {
        // Arrange
        List<UserGroupDTO> expectedUserGroups = Arrays.asList(userGroupDTO);
        when(userGroupService.findAll(anyBoolean())).thenReturn(expectedUserGroups);

        // Act
        ResponseEntity<List<UserGroupDTO>> response = userGroupController.findAll(false);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(expectedUserGroups, response.getBody());
        verify(userGroupService, times(1)).findAll(false);
    }

    @Test
    void testFindAllGroups() {
        // Arrange
        List<UserGroupDTO> expectedUserGroups = Arrays.asList(userGroupDTO);
        when(userGroupService.findAllGroups()).thenReturn(expectedUserGroups);

        // Act
        ResponseEntity<List<UserGroupDTO>> response = userGroupController.findAllGroups();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(expectedUserGroups, response.getBody());
        verify(userGroupService, times(1)).findAllGroups();
    }

    @Test
    void testGetAllReviewers() {
        // Arrange
        List<UserGroupDTO> expectedUserGroups = Arrays.asList(userGroupDTO);
        when(userGroupService.getAllReviewers()).thenReturn(expectedUserGroups);

        // Act
        ResponseEntity<List<UserGroupDTO>> response = userGroupController.getAllReviewers();

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(expectedUserGroups, response.getBody());
        verify(userGroupService, times(1)).getAllReviewers();
    }

    @Test
    void testGetByUserGroupId() {
        // Arrange
        String userGroupId = "test-user-group-id";
        when(userGroupService.getByUserGroupId(userGroupId)).thenReturn(userGroupDTO);

        // Act
        ResponseEntity<UserGroupDTO> response = userGroupController.getByUserGroupId(userGroupId);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(userGroupDTO, response.getBody());
        verify(userGroupService, times(1)).getByUserGroupId(userGroupId);
    }

    @Test
    void testAddReviewers() {
        // Arrange
        String userGroupId = "test-user-group-id";
        List<String> reviewers = Arrays.asList("reviewer1", "reviewer2");
        when(userGroupService.addReviewers(eq(userGroupId), eq(reviewers))).thenReturn(userGroupDTO);

        // Act
        ResponseEntity<UserGroupDTO> response = userGroupController.addReviewers(userGroupId, reviewers);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(userGroupDTO, response.getBody());
        verify(userGroupService, times(1)).addReviewers(userGroupId, reviewers);
    }

    @Test
    void testUpdateReviewers() {
        // Arrange
        String userGroupId = "test-user-group-id";
        List<String> reviewers = Arrays.asList("reviewer1", "reviewer2");
        when(userGroupService.updateReviewers(eq(userGroupId), eq(reviewers))).thenReturn(userGroupDTO);

        // Act
        ResponseEntity<UserGroupDTO> response = userGroupController.updateReviewers(userGroupId, reviewers);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(userGroupDTO, response.getBody());
        verify(userGroupService, times(1)).updateReviewers(userGroupId, reviewers);
    }

    @Test
    void testDeleteReviewers() {
        // Arrange
        String userGroupId = "test-user-group-id";
        doNothing().when(userGroupService).deleteReviewers(userGroupId);

        // Act
        userGroupController.deleteReviewers(userGroupId);

        // Assert
        verify(userGroupService, times(1)).deleteReviewers(userGroupId);
    }
} 