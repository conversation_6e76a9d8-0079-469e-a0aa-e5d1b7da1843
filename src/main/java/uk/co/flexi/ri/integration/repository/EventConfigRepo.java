package uk.co.flexi.ri.integration.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.integration.model.EventConfig;

import java.util.List;

public interface EventConfigRepo extends JpaRepository<EventConfig, Long> {

    @Query(value = "SELECT e.id FROM event_config e WHERE e.tenant = :tenantId AND e.event_type " +
            "LIKE %:type% ",
            nativeQuery = true)
    List<Long> findIdByTenantId(@Param("tenantId") Long tenantId, @Param("type") String type);
}
