package uk.co.flexi.ri.integration.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.ProducerTemplate;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.exception.custom.IntegrationException;
import uk.co.flexi.ri.exception.custom.ScriptExecutionException;
import uk.co.flexi.ri.integration.dto.EventConfigDTO;
import uk.co.flexi.ri.integration.dto.ScriptReqDTO;
import uk.co.flexi.ri.integration.model.EventConfig;
import uk.co.flexi.ri.integration.repository.EventConfigRepo;
import uk.co.flexi.ri.model.EventQueue;
import uk.co.flexi.ri.service.AuthenticatedUserService;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class EventConfigService {

    private static final String DESTINATION = "destination";

    private final EventConfigRepo eventConfigRepo;

    private final ModelMapper modelMapper;

    private final ObjectMapper objectMapper;

    private final ProducerTemplate producerTemplate;

    private final EventService eventService;

    private final AuthenticatedUserService authenticatedUserService;

    private final ScriptExecutor scriptExecutor;

    public List<EventConfig.Destination> getDestinationList() {
        return Arrays.stream(EventConfig.Destination.values()).toList();
    }

    public EventConfigDTO getEventConfigKeys(EventConfig.Destination name) {
        String hmacKey = authenticatedUserService.getAuthenticatedUserDetails().getHmacKey();
        EventConfigDTO eventConfigDTO = new EventConfigDTO();
        eventConfigDTO.setConfig(buildDestinationConfig(name, hmacKey));
        return eventConfigDTO;
    }

    public List<String> getEventType() {
        return Arrays.stream(EventQueue.EventType.values())
                .map(Enum::name)
                .toList();
    }

    public List<EventConfigDTO> getEventConfig() {
        return eventConfigRepo.findAll().stream().map(c -> modelMapper.map(c, EventConfigDTO.class)).toList();
    }

    public EventConfigDTO findById(Long id) {
        return eventConfigRepo.findById(id)
                .map(c -> modelMapper.map(c, EventConfigDTO.class))
                .orElseThrow(() -> new NoSuchElementException("EventConfig not found with id: " + id));
    }

    public EventConfigDTO addEventConfig(EventConfigDTO eventConfigDTO) {
        EventConfig eventConfig = modelMapper.map(eventConfigDTO, EventConfig.class);
        eventConfig = eventConfigRepo.save(eventConfig);
        return modelMapper.map(eventConfig, EventConfigDTO.class);
    }

    public EventConfigDTO updateEventConfig(Long id, EventConfigDTO eventConfigDTO) {
        EventConfig existing = eventConfigRepo.findById(id)
                .orElseThrow(() -> new NoSuchElementException("EventConfig not found with id: " + id));
        EventConfig eventConfig = modelMapper.map(eventConfigDTO, EventConfig.class);
        eventConfig.setId(existing.getId());
        eventConfig = eventConfigRepo.save(eventConfig);
        return modelMapper.map(eventConfig, EventConfigDTO.class);
    }

    public void deleteEventConfig(Long id) {
        eventConfigRepo.deleteById(id);
    }

    private Map<String, Object> buildDestinationConfig(EventConfig.Destination name, String hmacKey) {
        return switch (name) {
            case WEBHOOK -> {
                Map<String, Object> config = new HashMap<>();
                config.put(DESTINATION, "");
                config.put("url", "");
                config.put("authKey", "");
                config.put("authType", "");
                config.put("authValue", "");
                if (hmacKey != null && !hmacKey.isBlank()) {
                    config.put("hmacKey", hmacKey);
                }
                yield config;
            }
            case KAFKA -> Map.of(
                    DESTINATION, "",
                    "topic", "",
                    "brokers", "",
                    "userName", "",
                    "password", ""
            );
            case SQS -> Map.of(
                    DESTINATION, "",
                    "queueName", "",
                    "accessKey", "",
                    "secretKey", "",
                    "region", ""
            );
            case GOOGLEPUBSUB -> Map.of(
                    DESTINATION, "",
                    "projectId", "",
                    "topic", "",
                    "credPath", ""
            );
            case AZUREQUEUE -> Map.of(
                    DESTINATION, "",
                    "accountName", "",
                    "client", ""
            );
        };
    }


    public Map<String, String> testConnection(EventConfigDTO eventConfigDTO) {
        eventService.addHmacConfig(eventConfigDTO.getDestination(), eventConfigDTO.getConfig());
        Map<String, Object> headers = eventService.buildHeaders(eventConfigDTO.getDestination(),
                eventConfigDTO.getConfig());
        Map<String, String> body = new HashMap<>();
        Exchange exchange = producerTemplate.request("direct:processEvent", e -> {
            e.getIn().setBody(body);
            e.getIn().setHeaders(headers);
        });
        String errorMessage = exchange.getException() != null ? exchange.getException().getMessage() : null;
        if (errorMessage != null) {
            throw new IntegrationException("");
        } else {
            return Map.of("status", "success", "message", "Connection successful");
        }
    }

    public JsonNode executeScript(ScriptReqDTO reqDTO) throws JsonProcessingException, ScriptExecutionException {
        String input = objectMapper.writeValueAsString(reqDTO.getInputJson());
        String jsonString = scriptExecutor.execute(input, reqDTO.getScript());
        return objectMapper.readTree(jsonString);
    }
}
