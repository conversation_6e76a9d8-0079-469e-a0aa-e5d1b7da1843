package uk.co.flexi.ri.integration.model;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;
import uk.co.flexi.ri.model.DateAudit;
import uk.co.flexi.ri.util.covertor.MapToJsonConverter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "event_config")
public class EventConfig extends DateAudit {

    public enum Destination {
        WEBHOOK,
        KAFKA,
        SQS,
        GOOGLEPUBSUB,
        AZUREQUEUE
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "config_name", nullable = false)
    private String configName;

    @Enumerated(EnumType.STRING)
    @Column(name = "destination", nullable = false)
    private Destination destination;

    @Column(name = "event_type", nullable = false)
    private String eventType;

    @Lob
    @Column(name = "mapping_script", length = 10000)
    private String mappingScript;

    @Column(name = "config", columnDefinition = "json")
    @Convert(converter = MapToJsonConverter.class)
    private Map<String, Object> config = new HashMap<>();

    @Column(name = "retry_count")
    private Integer retryCount = 5;

    @TenantId
    private Long tenant;
}
