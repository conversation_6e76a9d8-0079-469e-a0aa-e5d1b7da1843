package uk.co.flexi.ri.integration.controller;

import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;

@RestController
@RequestMapping(value = "/api/v1/simulator")
public class SimulatorController {

    private static final String STATIC_AUTH_TOKEN = "Bearer eyJhbGciOiJIUzI1NiJ9";
    private static final AtomicInteger requestCounter = new AtomicInteger(0);
    private static final Random random = new Random();
    private static final String STATUS = "status";
    private static final String MESSAGE = "message";

    @PostMapping("/post-event")
    public ResponseEntity<Map<String, Object>> postEvent(@RequestHeader(value = "CUS-AUTH") String authToken, @RequestBody JsonNode jsonNode) {
        Map<String, Object> response = new HashMap<>();
        if (authToken == null || !authToken.equals(STATIC_AUTH_TOKEN)) {
            response.put(STATUS, "error");
            response.put(MESSAGE, "Unauthorized: Invalid token");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
        int currentCount = requestCounter.incrementAndGet();
        if (currentCount % 25 == 0 || random.nextInt(25) == 0) {
            response.put(STATUS, "error");
            response.put(MESSAGE, "Internal Server Error: Simulated failure");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
        response.put(STATUS, "success");
        response.put(MESSAGE, "Event processed successfully");
        return ResponseEntity.ok(response);
    }
}
