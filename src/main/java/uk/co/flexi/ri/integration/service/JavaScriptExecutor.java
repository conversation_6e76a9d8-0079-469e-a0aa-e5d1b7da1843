package uk.co.flexi.ri.integration.service;

import org.mozilla.javascript.Context;
import org.mozilla.javascript.Scriptable;
import org.springframework.stereotype.Service;
import uk.co.flexi.ri.exception.custom.ScriptExecutionException;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Service
public class JavaScriptExecutor implements ScriptExecutor {

    private static final long MAX_SCRIPT_SIZE = 10000;
    private static final long TIMEOUT_MS = 5000;

    @Override
    public String execute(String inputJson, String script) throws ScriptExecutionException {
        try (ExecutorService executor = Executors.newSingleThreadExecutor()) {
            Future<String> future = executor.submit(() -> executeScript(inputJson, script));
            return future.get(TIMEOUT_MS, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ScriptExecutionException("Script execution was interrupted", e);
        } catch (TimeoutException e) {
            throw new ScriptExecutionException("Script execution timed out", e);
        } catch (Exception e) {
            throw new ScriptExecutionException("Script execution failed", e);
        }
    }

    private String executeScript(String inputJson, String script) throws ScriptExecutionException {
        if (inputJson == null || script == null) {
            throw new ScriptExecutionException("Input parameters cannot be null");
        }

        if (script.length() > MAX_SCRIPT_SIZE) {
            throw new ScriptExecutionException("Script exceeds maximum allowed size");
        }

        validateScript(script);

        try (Context context = Context.enter()) {
            context.setClassShutter(className -> false);

            Scriptable scope = context.initSafeStandardObjects();
            scope.put("inputJson", scope, inputJson);

            // Block dangerous JavaScript functions
            for (String key : new String[]{"load", "loadWithNewGlobal", "exit", "quit", "java", "Packages", "getClass"}) {
                scope.delete(key);
            }

            context.evaluateString(scope,
                    "(function() {" + script + "})()",
                    "UserScript",
                    1,
                    null);

            Object result = scope.get("result", scope);
            if (result == null || result == Context.getUndefinedValue()) {
                throw new ScriptExecutionException("Script did not return a valid result");
            }

            return Context.toString(result);

        } catch (Exception e) {
            throw new ScriptExecutionException("Failed to execute script: " + e.getMessage(), e);
        }
    }

    private void validateScript(String script) throws ScriptExecutionException {
        if (script.length() > MAX_SCRIPT_SIZE) {
            throw new ScriptExecutionException("Script too large");
        }

        String[] forbiddenPatterns = {
                "java.lang", "Runtime", "Process", "File",
                "importPackage", "importClass", "\\brequire\\b",
                "\\beval\\b", "\\bXMLHttpRequest\\b", "\\bfetch\\b"
        };

        for (String pattern : forbiddenPatterns) {
            if (script.matches("(?i).*" + pattern + ".*")) {
                throw new ScriptExecutionException("Script contains forbidden pattern: " + pattern);
            }
        }
    }
}
