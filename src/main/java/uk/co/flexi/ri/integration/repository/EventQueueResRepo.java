package uk.co.flexi.ri.integration.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import uk.co.flexi.ri.integration.model.EventQueueRes;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;

public interface EventQueueResRepo extends JpaRepository<EventQueueRes, Long> {

    List<EventQueueRes> findByRequestIdIn(List<Long> requestIds);

    Optional<EventQueueRes> findByRequestId(Long requestIds);

    @Query(value = "select event_id from event_queue_res eqr where eqr.status = 'FAILED' " +
            "and response_time >= :lastExecTime and eqr.tenant = :tenant",
            nativeQuery = true)
    List<String> findFailedResponseForNotification(Long tenant, OffsetDateTime lastExecTime);
}
