package uk.co.flexi.ri.integration.notification.handler;

import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.Mustache;
import com.github.mustachejava.MustacheFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.ProducerTemplate;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.integration.repository.EventQueueResRepo;
import uk.co.flexi.ri.integration.service.JobExecutionService;
import uk.co.flexi.ri.model.NotificationChannel;
import uk.co.flexi.ri.model.NotificationConfig;

import java.io.IOException;
import java.io.StringWriter;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class IntegrationFailureNotificationHandler implements NotificationHandler {

    private final EventQueueResRepo eventQueueResRepo;

    private final ProducerTemplate producerTemplate;

    private final JobExecutionService jobExecutionService;

    @Override
    public Map<NotificationConfig, Object> handle(NotificationConfig notificationConfig) throws Exception {
        log.info("Handling INTEGRATION_FAILURE notification for user group: {}", notificationConfig.getUserGroupId());
        try {
            OffsetDateTime lastTime = jobExecutionService.getLastJobExecutionTime("notificationSendingJob");
            List<String> eventIds =
                    eventQueueResRepo.findFailedResponseForNotification(notificationConfig.getTenant(), lastTime);

            if (!eventIds.isEmpty()) {
                Map<String, Object> headers = buildHeader(notificationConfig, notificationConfig.getChannel().getConfig());
                Object messageBody = buildBody(eventIds, notificationConfig.getChannel().getChannel());

                producerTemplate.sendBodyAndHeaders("direct:sendNotification", messageBody, headers);

                log.info("INTEGRATION_FAILURE notification sent successfully to: {}", notificationConfig.getDestination());

                return Map.of(notificationConfig, messageBody);
            }

        } catch (Exception e) {
            log.error("Failed to send INTEGRATION_FAILURE notification to: {}", notificationConfig.getDestination(), e);
            throw e;
        }
        return null;
    }

    private Map<String, Object> buildHeader(NotificationConfig notificationConfig, Map<String, Object> config) {
        return switch (notificationConfig.getChannel().getChannel()) {
            case EMAIL -> Map.of(
                    "smtpHost", config.getOrDefault("smtpHost", ""),
                    "smtpPort", config.getOrDefault("smtpPort", ""),
                    "smtpUsername", config.getOrDefault("smtpUsername", ""),
                    "smtpPassword", config.getOrDefault("smtpPassword", ""),
                    "emailFrom", config.getOrDefault("emailFrom", ""),
                    "emailSubject", "Integration Failure",
                    "notificationType", "EMAIL",
                    "emailTo", notificationConfig.getDestination()
            );
            case SLACK -> Map.of(
                    "notificationType", "SLACK",
                    "slackWebhookUrl", (notificationConfig.getDestination() != null) ?
                            notificationConfig.getDestination().replace("@", "%40")
                            : config.get("slackWebhookUrl").toString().replace("@", "%40")
            );
            case TEAMS -> Map.of(
                    "notificationType", "TEAMS",
                    "teamsWebhookUrl", (notificationConfig.getDestination() != null) ?
                            notificationConfig.getDestination() : config.get("teamsWebhookUrl")
            );
        };
    }

    private Object buildBody(List<String> eventIds, NotificationChannel.Channel channel) throws IOException {
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile(getTemplateName(channel));

        Map<String, Object> data = new HashMap<>();
        data.put("eventIds", eventIds);

        StringWriter writer = new StringWriter();
        mustache.execute(writer, data).flush();
        String renderedText = writer.toString();

        if (channel.equals(NotificationChannel.Channel.EMAIL)) {
            return renderedText;
        } else if (channel.equals(NotificationChannel.Channel.SLACK)) {
            return Map.of("text", renderedText);
        } else if (channel.equals(NotificationChannel.Channel.TEAMS)) {
            return Map.of(
                    "@type", "MessageCard",
                    "@context", "http://schema.org/extensions",
                    "summary", "Integration Failure",
                    "text", renderedText
            );
        } else {
            throw new IllegalArgumentException("Unsupported channel: " + channel);
        }
    }

    private String getTemplateName(NotificationChannel.Channel channel) {
        return switch (channel) {
            case EMAIL -> "templates/integration_failure_email_template.html";
            case SLACK -> "templates/integration_failure_slack_template.mustache";
            case TEAMS -> "templates/integration_failure_teams_template.mustache";
        };
    }

    @Override
    public NotificationConfig.NotificationType getSupportedType() {
        return NotificationConfig.NotificationType.INTEGRATION_FAILURE;
    }
}
