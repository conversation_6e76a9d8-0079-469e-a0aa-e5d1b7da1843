package uk.co.flexi.ri.integration.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.integration.model.EventQueueRes;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventQueueResDTO {

    //queue id
    private Long id;

    private String eventId;

    private Long requestId;

    private Integer statusCode;

    private String body;

    private EventQueueRes.Status status = EventQueueRes.Status.PENDING;

    private String errorMessage;

    private Long tenant;
}
