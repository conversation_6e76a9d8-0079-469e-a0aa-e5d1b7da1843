package uk.co.flexi.ri.integration.batch.route;

import lombok.RequiredArgsConstructor;
import org.apache.camel.Exchange;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.dataformat.JsonLibrary;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.util.HMACUtils;

@Component
@RequiredArgsConstructor
public class EventRoutingRoute extends RouteBuilder {

    private static final String DESTINATION = "destination";

    @Override
    public void configure() throws Exception {

        onException(Exception.class)
                .log("Exception occurred: ${exception.message}")
                .handled(false)
                .end();

        // Main route dispatcher
        from("direct:processEvent")
                .routeId("eventProcessingRoute")
                .log("Received event with destination: ${header.destination}")
                .choice()
                .when(header(DESTINATION).isEqualTo("WEBHOOK")).to("direct:webhookRoute")
                .when(header(DESTINATION).isEqualTo("KAFKA")).to("direct:kafkaRoute")
                .when(header(DESTINATION).isEqualTo("SQS")).to("direct:sqsRoute")
                .when(header(DESTINATION).isEqualTo("GOOGLEPUBSUB")).to("direct:googlePubSubRoute")
                .when(header(DESTINATION).isEqualTo("AZUREQUEUE")).to("direct:azureQueueRoute")
                .otherwise().throwException(new IllegalArgumentException("Unsupported event destination"))
                .end();

        // Webhook
        from("direct:webhookRoute")
                .routeId("webhookRoute")
                .log("Preparing webhook call to: ${header.url}")
                .marshal().json(JsonLibrary.Jackson)
                .setHeader(Exchange.HTTP_METHOD, constant("POST"))
                .setHeader("Content-Type", constant("application/json"))
                .process(exchange -> {
                    String body = exchange.getIn().getBody(String.class);
                    String secret = exchange.getIn().getHeader("hmacKey", String.class);
                    Long merchant = exchange.getIn().getHeader("tenant", Long.class);
                    if (secret != null && !secret.trim().isEmpty()) {
                        String signature = HMACUtils.generate(body, secret);
                        exchange.getIn().setHeader("X-HMAC-Signature", signature);
                        exchange.getIn().setHeader("X-Merchant-Id", merchant);
                    }
                })
                .setHeader("${header.authKey}", simple("${header.authType} ${header.authValue}"))
                .toD("${header.url}?throwExceptionOnFailure=true");

        // Kafka
        from("direct:kafkaRoute")
                .routeId("kafkaRoute")
                .log("Sending to Kafka topic: ${header.topic}")
                .marshal().json(JsonLibrary.Jackson)
                .toD("kafka:${header.url}?" +
                        "brokers=${header.brokers}" +
                        "&securityProtocol=SASL_SSL" +
                        "&saslMechanism=PLAIN" +
                        "&saslJaasConfig=org.apache.kafka.common.security.plain.PlainLoginModule required " +
                        "username='${header.userName}' password='${header.password}';" +
                        "&keySerializer=org.apache.kafka.common.serialization.StringSerializer" +
                        "&valueSerializer=org.apache.kafka.common.serialization.StringSerializer");

        // AWS SQS
        from("direct:sqsRoute")
                .routeId("sqsRoute")
                .marshal().json(JsonLibrary.Jackson)
                .toD("aws2-sqs://${header.queueName}" +
                        "?accessKey=${header.accessKey}" +
                        "&secretKey=${header.secretKey}" +
                        "&region=${header.region}" +
                        "&autoCreateQueue=false" +
                        "&deleteAfterRead=true");

        // Google Pub/Sub
        from("direct:googlePubSubRoute")
                .routeId("googlePubSubRoute")
                .marshal().json(JsonLibrary.Jackson)
                .toD("google-pubsub://${header.projectId}:${header.topic}" +
                        "?serviceAccountKeyFile=${header.credPath}" +
                        "&publisher=true");

        // Azure Storage Queue
        from("direct:azureQueueRoute")
                .routeId("azureQueueRoute")
                .marshal().json(JsonLibrary.Jackson)
                .toD("azure-storage-queue://${header.accountName}/test?serviceClient=#${header.client}");
    }
}