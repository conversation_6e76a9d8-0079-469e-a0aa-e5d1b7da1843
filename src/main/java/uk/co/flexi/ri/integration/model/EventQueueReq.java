package uk.co.flexi.ri.integration.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import uk.co.flexi.ri.util.covertor.JsonNodeConverter;

import java.time.OffsetDateTime;


@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "event_queue_req")
public class EventQueueReq {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "event_queue_id", nullable = false)
    private Long eventQueueId;

    @Column(name = "event_type", nullable = false)
    private String eventType;

    @Column(name = "unique_id", nullable = false)
    private String uniqueId;

    @Column(name = "event_id", nullable = false)
    private String eventId;

    @Column(name = "config_id", nullable = false)
    private Long configId;

    @Column(name = "target", nullable = false)
    private String target;

    @Column(name = "request_time", nullable = false)
    private OffsetDateTime requestTime = OffsetDateTime.now();

    @Column(name = "original_payload", columnDefinition = "LONGTEXT")
    @Convert(converter = JsonNodeConverter.class)
    private JsonNode originalPayload;

    @Column(name = "payload", columnDefinition = "LONGTEXT")
    @Convert(converter = JsonNodeConverter.class)
    private JsonNode payload;

    @Column(name = "tenant", nullable = false)
    private Long tenant;
}
