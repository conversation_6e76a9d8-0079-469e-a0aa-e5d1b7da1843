package uk.co.flexi.ri.integration.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.Exchange;
import org.apache.camel.ProducerTemplate;
import org.apache.camel.http.base.HttpOperationFailedException;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.event.dto.Comment__2;
import uk.co.flexi.ri.event.dto.Inspection;
import uk.co.flexi.ri.event.dto.InspectionItem;
import uk.co.flexi.ri.event.dto.Payload;
import uk.co.flexi.ri.event.dto.RiEventDto;
import uk.co.flexi.ri.exception.custom.ScriptExecutionException;
import uk.co.flexi.ri.integration.dto.EventLogDTO;
import uk.co.flexi.ri.integration.dto.EventQueueResDTO;
import uk.co.flexi.ri.integration.model.EventConfig;
import uk.co.flexi.ri.integration.model.EventQueueReq;
import uk.co.flexi.ri.integration.model.EventQueueRes;
import uk.co.flexi.ri.integration.model.view.EventQueueView;
import uk.co.flexi.ri.integration.repository.EventConfigRepo;
import uk.co.flexi.ri.integration.repository.EventQueueReqRepo;
import uk.co.flexi.ri.integration.repository.EventQueueResRepo;
import uk.co.flexi.ri.repository.MerchantRepo;
import uk.co.flexi.ri.service.AuthenticatedUserService;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
@Slf4j
public class EventService {

    private static final String HMACK_KEY = "hmacKey";

    private static final String TOPIC = "topic";

    private static final String TENANT = "tenant";

    private static final String DESTINATION = "destination";

    private static final String ORIGINAL_PAYLOAD = "originalPayload";

    private final EventQueueReqRepo eventQueueReqRepo;

    private final EventQueueResRepo eventQueueResRepo;

    private final EventConfigRepo eventConfigRepo;

    private final AuthenticatedUserService authenticatedUserService;

    private final ProducerTemplate producerTemplate;

    private final ObjectMapper objectMapper;

    private final ModelMapper modelMapper;

    private final MerchantRepo merchantRepo;

    private final ScriptExecutor scriptExecutor;

    public EventQueueResDTO processEvent(Map<String, Object> headers, Object body) {
        EventQueueResDTO resDTO = new EventQueueResDTO();
        try {
            Exchange exchange = producerTemplate.request("direct:processEvent", e -> {
                e.getIn().setBody(body);
                e.getIn().setHeaders(headers);
            });
            String responseBody = exchange.getMessage().getBody(String.class);
            Integer statusCode = exchange.getMessage().getHeader(Exchange.HTTP_RESPONSE_CODE, Integer.class);
            String errorMessage = exchange.getException() != null ? exchange.getException().getMessage() : null;
            if (errorMessage != null) {
                statusCode = extractStatusCode(exchange.getException());
                resDTO.setStatusCode(statusCode);
                resDTO.setErrorMessage(errorMessage);
                resDTO.setBody(extractBodyFromException(exchange.getException()));
                resDTO.setStatus(EventQueueRes.Status.FAILED);
            } else {
                resDTO.setStatusCode(statusCode);
                resDTO.setBody(responseBody);
                resDTO.setStatus(EventQueueRes.Status.SENT);
            }
        } catch (Exception e) {
            log.error("Error occurred while processing event with header: {}", headers);
            resDTO.setErrorMessage(e.getMessage());
            resDTO.setStatus(EventQueueRes.Status.FAILED);
        }
        return resDTO;
    }

    public List<EventLogDTO> getAllInspectionEvents(String id) {
        Long tenant = authenticatedUserService.getAuthenticatedUserDetails().getTenant();
        List<EventQueueReq> eventRequests = eventQueueReqRepo.findByUniqueIdAndTenant(id, tenant);
        List<EventConfig> configs = eventConfigRepo.findAll();

        Map<String, Long> eventCountMap = eventRequests.stream()
                .collect(Collectors.groupingBy(EventQueueReq::getEventId, Collectors.counting()));

        Map<Long, Integer> configMap = configs.stream()
                .collect(Collectors.toMap(EventConfig::getId, EventConfig::getRetryCount));

        Map<String, Long> latestEventRequestMap = eventRequests.stream()
                .collect(Collectors.toMap(
                        EventQueueReq::getEventId,
                        req -> req,
                        (r1, r2) -> r1.getRequestTime().isAfter(r2.getRequestTime()) ? r1 : r2
                ))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().getId()
                ));

        Map<Long, EventQueueRes> responseMap = eventQueueResRepo.findByRequestIdIn(
                eventRequests.stream().map(EventQueueReq::getId).toList()
        ).stream().collect(Collectors.toMap(
                res -> res.getRequest().getId(),
                res -> res,
                (r1, r2) -> r1
        ));

        return eventRequests.stream().map(request -> {
            EventLogDTO dto = new EventLogDTO();
            dto.setEventId(request.getEventId());
            dto.setEventType(request.getEventType());
            dto.setTarget(request.getTarget());
            dto.setRequestTime(request.getRequestTime());

            try {
                if (request.getOriginalPayload() != null && !request.getOriginalPayload().isEmpty()) {
                    dto.setOriginalBody(objectMapper.writeValueAsString(request.getOriginalPayload()));
                }
                dto.setRequestBody(objectMapper.writeValueAsString(request.getPayload()));
            } catch (JsonProcessingException e) {
                dto.setRequestBody("Error serializing request body");
            }

            EventQueueRes response = responseMap.get(request.getId());
            if (response != null) {
                dto.setStatus(response.getStatus().name());
                dto.setStatusCode(response.getStatusCode());
                dto.setResponseTime(response.getResponseTime());
                dto.setResponseBody(response.getBody());
                dto.setErrorMessage(response.getErrorMessage());

                dto.setResponseDelay(Duration.between(request.getRequestTime(), response.getResponseTime()).toMillis());

                boolean isLatest = request.getId().equals(latestEventRequestMap.get(request.getEventId()));
                long count = eventCountMap.getOrDefault(request.getEventId(), 0L);
                int retryLimit = configMap.getOrDefault(request.getConfigId(), Integer.MAX_VALUE);

                if (isLatest && count >= retryLimit && response.getStatus().equals(EventQueueRes.Status.FAILED)) {
                    dto.setIsRetryEnable(true);
                }
            } else {
                dto.setStatus("PENDING");
            }

            return dto;
        }).toList();
    }

    public EventLogDTO retryEventPosting(String eventId) {
        EventQueueReq request = eventQueueReqRepo.findFirstByEventIdOrderByRequestTimeDesc(eventId)
                .orElseThrow(() -> new NoSuchElementException("Event Queue Request not found with Event id: " + eventId));
        request.setRequestTime(OffsetDateTime.now());
        request = eventQueueReqRepo.save(request);
        EventConfig eventConfig = eventConfigRepo.findById(request.getConfigId())
                .orElseThrow(() -> new NoSuchElementException("Event Config is not found"));
        Map<String, Object> headers = buildHeaders(eventConfig.getDestination(), eventConfig.getConfig());
        EventQueueResDTO resDTO = processEvent(headers, request.getPayload());
        resDTO.setRequestId(request.getId());
        EventQueueRes response = updateEventResponse(resDTO);
        EventLogDTO eventLog = new EventLogDTO();
        eventLog.setEventId(request.getEventId());
        eventLog.setEventType(request.getEventType());
        eventLog.setTarget(request.getTarget());
        eventLog.setRequestTime(request.getRequestTime());
        try {
            eventLog.setRequestBody(objectMapper.writeValueAsString(request.getPayload()));
        } catch (JsonProcessingException e) {
            eventLog.setRequestBody("Error serializing request body");
        }
        eventLog.setStatus(response.getStatus().name());
        eventLog.setStatusCode(response.getStatusCode());
        eventLog.setResponseTime(response.getResponseTime());
        eventLog.setResponseDelay(Duration.between(request.getRequestTime(), response.getResponseTime()).toMillis());
        eventLog.setResponseBody(response.getBody());
        eventLog.setErrorMessage(response.getErrorMessage());
        if (response.getStatus().equals(EventQueueRes.Status.FAILED)) {
            eventLog.setIsRetryEnable(Boolean.TRUE);
        }
        return eventLog;
    }

    public EventQueueRes updateEventResponse(EventQueueResDTO event) {
        EventQueueRes eventQueueRes = eventQueueResRepo.findByRequestId(event.getRequestId())
                .orElseThrow(() -> new NoSuchElementException("Event Queue Response not found with request id: " + event.getRequestId()));
        eventQueueRes.setStatusCode(event.getStatusCode());
        eventQueueRes.setBody(event.getBody());
        eventQueueRes.setErrorMessage(event.getErrorMessage());
        eventQueueRes.setStatus(event.getStatus());
        eventQueueRes.setResponseTime(OffsetDateTime.now());
        return eventQueueResRepo.save(eventQueueRes);
    }

    public Map<String, Object> buildHeaders(EventConfig.Destination destination, Map<String, Object> config) {
        switch (destination) {
            case WEBHOOK -> {
                return Map.of(DESTINATION, destination.name(),
                        "url", config.getOrDefault("url", ""),
                        "authKey", config.getOrDefault("authKey", ""),
                        "authType", config.getOrDefault("authType", ""),
                        "authValue", config.getOrDefault("authValue", ""),
                        HMACK_KEY, config.getOrDefault(HMACK_KEY, ""),
                        TENANT, config.getOrDefault(TENANT, ""));
            }
            case KAFKA -> {
                return Map.of(DESTINATION, destination.name(),
                        TOPIC, config.getOrDefault(TOPIC, ""),
                        "brokers", config.getOrDefault("brokers", ""),
                        "userName", config.getOrDefault("userName", ""),
                        "password", config.getOrDefault("password", ""));
            }
            case SQS -> {
                return Map.of(DESTINATION, destination.name(),
                        "queueName", config.getOrDefault("queueName", ""),
                        "accessKey", config.getOrDefault("accessKey", ""),
                        "secretKey", config.getOrDefault("secretKey", ""),
                        "region", config.getOrDefault("region", ""));
            }
            case GOOGLEPUBSUB -> {
                return Map.of(DESTINATION, destination.name(),
                        "projectId", config.getOrDefault("projectId", ""),
                        TOPIC, config.getOrDefault(TOPIC, ""),
                        "credPath", config.getOrDefault("credPath", ""));
            }
            case AZUREQUEUE -> {
                return Map.of(DESTINATION, destination.name(),
                        "accountName", config.getOrDefault("accountName", ""),
                        "client", config.getOrDefault("client", ""));
            }
        }
        return Map.of();
    }

    public Object buildBody(EventQueueView event) {
        RiEventDto eventDto = createRiEventDto(event);
        ObjectNode result = objectMapper.createObjectNode();
        JsonNode originalPayload = objectMapper.valueToTree(eventDto);
        result.set(ORIGINAL_PAYLOAD, originalPayload);
        if (event.getMappingScript() != null && !event.getMappingScript().isBlank()) {
            try {
                String payloadJson = objectMapper.writeValueAsString(eventDto);
                String jsonOutput = scriptExecutor.execute(payloadJson, event.getMappingScript());
                JsonNode convertedPayload = objectMapper.readTree(jsonOutput);
                result.set("convertedPayload", convertedPayload);
                return result;
            } catch (ScriptExecutionException | JsonProcessingException e) {
                log.error("Error converting payload using scripting. Falling back to default payload. Event ID: {}",
                        event.getEventId(), e);
            }
        }
        return result;
    }

    public JsonNode getRequestPayload(Object body) {
        ObjectNode resultNode = (ObjectNode) body;
        JsonNode originalPayload = resultNode.get(ORIGINAL_PAYLOAD);
        JsonNode convertedPayload = resultNode.get("convertedPayload");
        if (convertedPayload != null && !convertedPayload.isEmpty()) {
            return convertedPayload;
        } else {
            return originalPayload;
        }
    }

    public JsonNode getOriginalPayload(Object body) {
        ObjectNode resultNode = (ObjectNode) body;
        return resultNode.get(ORIGINAL_PAYLOAD);
    }

    private RiEventDto createRiEventDto(EventQueueView event) {
        RiEventDto dto = new RiEventDto();
        dto.setEventId(event.getEventId());
        dto.setEventType(event.getEventType().name());
        dto.setReferenceType(event.getReferenceType().name());
        dto.setEventTime(event.getEventTime().toString());
        dto.setPerformedByUser(event.getPerformedByUser());
        dto.setPerformedByGroup(event.getPerformedByGroup());
        dto.setPayload(buildPayload(event));
        return dto;
    }

    public Payload buildPayload(EventQueueView event) {
        Payload payload = new Payload();
        Map<String, Object> payloadData = event.getPayload();
        switch (event.getReferenceType()) {
            case INSPECTION -> {
                Inspection inspection = modelMapper.map(payloadData, Inspection.class);
                payload.setInspection(inspection);
            }
            case INSPECTION_ITEM -> {
                InspectionItem inspectionItem = modelMapper.map(payloadData, InspectionItem.class);
                payload.setInspectionItem(inspectionItem);
            }
            case COMMENT -> {
                Comment__2 comment = modelMapper.map(payloadData, Comment__2.class);
                payload.setComment(comment);
            }
        }
        return payload;
    }

    public void insertEventRequest(EventQueueView item, Object body, Object originalPayload) throws JsonProcessingException {
        EventQueueReq eventQueueReq = new EventQueueReq();
        eventQueueReq.setEventQueueId(item.getId());
        eventQueueReq.setEventId(item.getEventId());
        eventQueueReq.setConfigId(item.getConfigId());
        eventQueueReq.setEventType(item.getEventType().name());
        eventQueueReq.setUniqueId(item.getUniqueId());
        eventQueueReq.setTarget(item.getDestination().name());
        eventQueueReq.setRequestTime(OffsetDateTime.now());
        eventQueueReq.setPayload((JsonNode) body);
        eventQueueReq.setOriginalPayload(originalPayload != null ? (JsonNode) originalPayload : null);
        eventQueueReq.setTenant(item.getTenant());
        log.info("Payload before DB insert: {}", objectMapper.writeValueAsString(eventQueueReq.getPayload()));
        eventQueueReq = eventQueueReqRepo.save(eventQueueReq);
        item.setRequestId(eventQueueReq.getId());
    }

    private Integer extractStatusCode(Exception e) {
        if (e instanceof HttpOperationFailedException httpOperationFailedException) {
            return httpOperationFailedException.getStatusCode();
        }
        return null;
    }

    private String extractBodyFromException(Exception e) {
        if (e instanceof HttpOperationFailedException httpOperationFailedException) {
            return httpOperationFailedException.getResponseBody();
        }
        return null;
    }

    public void addHmacConfig(EventConfig.Destination destination, Map<String, Object> config, Long tenant, String hmacKey) {
        if (destination == EventConfig.Destination.WEBHOOK) {
            config.put(HMACK_KEY, hmacKey);
            config.put(TENANT, tenant);
        }
    }

    public void addHmacConfig(EventConfig.Destination destination, Map<String, Object> config) {
        if (destination == EventConfig.Destination.WEBHOOK) {
            Long tenant = authenticatedUserService.getAuthenticatedUserDetails().getTenant();
            String hmacKey = merchantRepo.findHmacKeyByTenant(tenant);
            config.put(HMACK_KEY, hmacKey);
            config.put(TENANT, tenant);
        }
    }
}
