package uk.co.flexi.ri.integration.batch.processor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.integration.dto.EventQueueResDTO;
import uk.co.flexi.ri.integration.model.view.EventQueueView;
import uk.co.flexi.ri.integration.service.EventService;

import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class EventQueueProcessor implements ItemProcessor<EventQueueView, EventQueueResDTO> {

    private final EventService eventService;

    @Override
    public EventQueueResDTO process(EventQueueView event) throws Exception {
        Object body = eventService.buildBody(event);
        JsonNode reqPayload = eventService.getRequestPayload(body);
        JsonNode originalPayload = eventService.getOriginalPayload(body);
        eventService.addHmacConfig(event.getDestination(), event.getConfig(),event.getTenant(), event.getHmacKey());
        Map<String, Object> headers = eventService.buildHeaders(event.getDestination(), event.getConfig());
        log.info("Inserting event request for event ID: {}", event.getEventId());
        eventService.insertEventRequest(event, reqPayload, originalPayload);
        log.info("Processing event with ID: {}, with payload: {}",
                event.getEventId(), new ObjectMapper().writeValueAsString(reqPayload));
        EventQueueResDTO resDTO = eventService.processEvent(headers, reqPayload);
        resDTO.setId(event.getId());
        resDTO.setEventId(event.getEventId());
        resDTO.setRequestId(event.getRequestId());
        resDTO.setTenant(event.getTenant());
        return resDTO;
    }
}