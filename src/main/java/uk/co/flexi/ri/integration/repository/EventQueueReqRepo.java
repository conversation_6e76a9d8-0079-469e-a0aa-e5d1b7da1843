package uk.co.flexi.ri.integration.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import uk.co.flexi.ri.integration.model.EventQueueReq;

import java.util.List;
import java.util.Optional;

public interface EventQueueReqRepo extends JpaRepository<EventQueueReq, Long> {

    List<EventQueueReq> findByUniqueIdAndTenant(String uniqueId, Long tenant);

    Optional<EventQueueReq> findFirstByEventIdOrderByRequestTimeDesc(String eventId);

    long countByEventId(String eventId);
}
