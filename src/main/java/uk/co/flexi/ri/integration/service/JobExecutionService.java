package uk.co.flexi.ri.integration.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobInstance;
import org.springframework.batch.core.explore.JobExplorer;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class JobExecutionService {

    private final JobExplorer jobExplorer;

    public OffsetDateTime getLastJobExecutionTime(String jobName) {
        List<JobInstance> jobInstances = jobExplorer.getJobInstances(jobName, 0, 1);

        if (jobInstances.isEmpty()) {
            return OffsetDateTime.now(ZoneOffset.UTC).minusMinutes(1);
        }

        JobInstance latestInstance = jobInstances.get(0);
        List<JobExecution> executions = jobExplorer.getJobExecutions(latestInstance);

        return executions.stream()
                .map(JobExecution::getEndTime)
                .filter(Objects::nonNull)
                .max(Comparator.naturalOrder())
                .map(endTime -> endTime.atOffset(ZoneOffset.UTC))
                .orElse(OffsetDateTime.now(ZoneOffset.UTC).minusMinutes(1));
    }
}
