package uk.co.flexi.ri.integration.batch.route;

import lombok.RequiredArgsConstructor;
import org.apache.camel.builder.RouteBuilder;
import org.apache.camel.model.dataformat.JsonLibrary;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class NotificationRoute extends RouteBuilder {

    private static final String NOTIFICATION_TYPE = "notificationType";

    @Override
    public void configure() throws Exception {

        onException(Exception.class)
                .log("Notification Error: ${exception.message}")
                .handled(true);

        // Dispatcher
        from("direct:sendNotification")
                .routeId("notificationDispatcher")
                .log("Received notification type: ${header.notificationType}")
                .choice()
                .when(header(NOTIFICATION_TYPE).isEqualTo("EMAIL")).to("direct:emailNotification")
                .when(header(NOTIFICATION_TYPE).isEqualTo("SLACK")).to("direct:slackNotification")
                .when(header(NOTIFICATION_TYPE).isEqualTo("TEAMS")).to("direct:teamsNotification")
                .otherwise().log("Unsupported notification type: ${header.notificationType}")
                .end();

        // Email Notification
        from("direct:emailNotification")
                .routeId("emailNotificationRoute")
                .log("Sending email to: ${header.emailTo}")
                .toD("smtp://${header.smtpHost}:${header.smtpPort}" +
                        "?username=${header.smtpUsername}" +
                        "&password=${header.smtpPassword}" +
                        "&to=${header.emailTo}" +
                        "&subject=${header.emailSubject}" +
                        "&from=${header.emailFrom}" +
                        "&contentType=text/html" +
                        "&mail.smtp.auth=true" +
                        "&mail.smtp.starttls.enable=true" +
                        "&mail.smtp.starttls.required=true")
                .log("Email sent to ${header.emailTo}");

        // Slack Notification
        from("direct:slackNotification")
                .routeId("slackNotificationRoute")
                .marshal().json(JsonLibrary.Jackson)
                .log("Posting message to Slack webhook: ${header.slackWebhookUrl}")
                .toD("${header.slackWebhookUrl}?bridgeEndpoint=true&throwExceptionOnFailure=true")
                .log("Slack message posted");

        // Teams Notification
        from("direct:teamsNotification")
                .routeId("teamsNotificationRoute")
                .marshal().json(JsonLibrary.Jackson)
                .log("Posting message to Teams webhook: ${header.teamsWebhookUrl}")
                .toD("${header.teamsWebhookUrl}?bridgeEndpoint=true&throwExceptionOnFailure=true")
                .log("Teams message posted");
    }
}
