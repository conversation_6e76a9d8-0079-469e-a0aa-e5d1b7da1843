package uk.co.flexi.ri.integration.notification.factory;

import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.integration.notification.handler.NotificationHandler;
import uk.co.flexi.ri.model.NotificationConfig;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class NotificationHandlerFactory {

    private final List<NotificationHandler> notificationHandlers;
    private final Map<NotificationConfig.NotificationType, NotificationHandler> handlerMap;

    public NotificationHandlerFactory(List<NotificationHandler> notificationHandlers) {
        this.notificationHandlers = notificationHandlers;
        this.handlerMap = notificationHandlers.stream()
                .collect(Collectors.toMap(
                        NotificationHandler::getSupportedType,
                        Function.identity()
                ));
        log.info("Initialized NotificationHandlerFactory with {} handlers", handlerMap.size());
    }

    public NotificationHandler getHandler(NotificationConfig.NotificationType notificationType) {
        NotificationHandler handler = handlerMap.get(notificationType);
        if (handler == null) {
            throw new IllegalArgumentException("No handler found for notification type: " + notificationType);
        }
        return handler;
    }
}
