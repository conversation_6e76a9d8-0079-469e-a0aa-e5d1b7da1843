package uk.co.flexi.ri.integration.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.integration.model.EventConfig;

import java.util.HashMap;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventConfigDTO {

    private Long id;

    private String configName;

    private EventConfig.Destination destination;

    private String eventType;

    private String mappingScript;

    private Map<String, Object> config = new HashMap<>();

    private Integer retryCount;
}
