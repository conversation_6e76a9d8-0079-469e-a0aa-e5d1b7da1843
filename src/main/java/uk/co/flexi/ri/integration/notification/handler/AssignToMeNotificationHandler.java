package uk.co.flexi.ri.integration.notification.handler;

import com.github.mustachejava.DefaultMustacheFactory;
import com.github.mustachejava.Mustache;
import com.github.mustachejava.MustacheFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.camel.ProducerTemplate;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.integration.service.JobExecutionService;
import uk.co.flexi.ri.model.NotificationChannel;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.repository.InspectionRepo;

import java.io.IOException;
import java.io.StringWriter;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class AssignToMeNotificationHandler implements NotificationHandler {

    private final InspectionRepo inspectionRepo;

    private final ProducerTemplate producerTemplate;

    private final JobExecutionService jobExecutionService;

    @Override
    public Map<NotificationConfig, Object> handle(NotificationConfig notificationConfig) throws Exception {
        log.info("Handling ASSIGN_TO_ME notification for user group: {}", notificationConfig.getUserGroupId());
        try {
            List<String> referenceIds;
            if (notificationConfig.getFrequency().equals("IMMEDIATELY")) {
                OffsetDateTime lastTime = jobExecutionService.getLastJobExecutionTime("notificationSendingJob");
                referenceIds =
                        inspectionRepo.findNotificationEligibleInspection(notificationConfig.getUserGroupId(),
                                lastTime, notificationConfig.getTenant());
            } else {
                referenceIds =
                        inspectionRepo.findNotificationEligibleInspection(notificationConfig.getUserGroupId(), notificationConfig.getTenant());
            }

            if (!referenceIds.isEmpty()) {
                Map<String, Object> headers = buildHeader(notificationConfig, notificationConfig.getChannel().getConfig());
                Object messageBody = buildBody(referenceIds, notificationConfig.getChannel().getChannel());

                producerTemplate.sendBodyAndHeaders("direct:sendNotification", messageBody, headers);

                log.info("ASSIGN_TO_ME notification sent successfully to: {}", notificationConfig.getDestination());

                return Map.of(notificationConfig, messageBody);
            }

            log.info("No Eligible ASSIGN_TO_ME notifications");

        } catch (Exception e) {
            log.error("Failed to send ASSIGN_TO_ME notification to: {}", notificationConfig.getDestination(), e);
            throw e;
        }
        return null;
    }

    private Map<String, Object> buildHeader(NotificationConfig notificationConfig, Map<String, Object> config) {
        return switch (notificationConfig.getChannel().getChannel()) {
            case EMAIL -> Map.of(
                    "smtpHost", config.getOrDefault("smtpHost", ""),
                    "smtpPort", config.getOrDefault("smtpPort", ""),
                    "smtpUsername", config.getOrDefault("smtpUsername", ""),
                    "smtpPassword", config.getOrDefault("smtpPassword", ""),
                    "emailFrom", config.getOrDefault("emailFrom", ""),
                    "emailSubject", "Packages Assigned for Your Review",
                    "notificationType", "EMAIL",
                    "emailTo", notificationConfig.getDestination()
            );
            case SLACK -> Map.of(
                    "notificationType", "SLACK",
                    "slackWebhookUrl", (notificationConfig.getDestination() != null) ?
                            notificationConfig.getDestination().replace("@", "%40")
                            : config.get("slackWebhookUrl").toString().replace("@", "%40")
            );
            case TEAMS -> Map.of(
                    "notificationType", "TEAMS",
                    "teamsWebhookUrl", (notificationConfig.getDestination() != null) ?
                            notificationConfig.getDestination() : config.get("teamsWebhookUrl")
            );
        };
    }

    private Object buildBody(List<String> referenceIds, NotificationChannel.Channel channel) throws IOException {
        MustacheFactory mf = new DefaultMustacheFactory();
        Mustache mustache = mf.compile(getTemplateName(channel));

        Map<String, Object> data = new HashMap<>();
        data.put("assigned", referenceIds);

        StringWriter writer = new StringWriter();
        mustache.execute(writer, data).flush();
        String renderedText = writer.toString();

        if (channel.equals(NotificationChannel.Channel.EMAIL)) {
            return renderedText;
        } else if (channel.equals(NotificationChannel.Channel.SLACK)) {
            return Map.of("text", renderedText);
        } else if (channel.equals(NotificationChannel.Channel.TEAMS)) {
            return Map.of(
                    "@type", "MessageCard",
                    "@context", "http://schema.org/extensions",
                    "summary", "Package Assignment",
                    "text", renderedText
            );
        } else {
            throw new IllegalArgumentException("Unsupported channel: " + channel);
        }
    }

    private String getTemplateName(NotificationChannel.Channel channel) {
        return switch (channel) {
            case EMAIL -> "templates/assign_to_me_email_template.html";
            case SLACK -> "templates/assign_to_me_slack_template.mustache";
            case TEAMS -> "templates/assign_to_me_teams_template.mustache";
        };
    }

    @Override
    public NotificationConfig.NotificationType getSupportedType() {
        return NotificationConfig.NotificationType.ASSIGN_TO_ME;
    }
}
