package uk.co.flexi.ri.integration.batch.writer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.model.NotificationResponse;
import uk.co.flexi.ri.repository.NotificationResponseRepo;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class NotificationWriter implements ItemWriter<Map<NotificationConfig, Object>> {

    private final NotificationResponseRepo notificationResponseRepo;

    @Override
    public void write(Chunk<? extends Map<NotificationConfig, Object>> items) throws Exception {
        List<NotificationResponse> list = new ArrayList<>();

        for (Map<NotificationConfig, Object> item : items) {
            for (Map.Entry<NotificationConfig, Object> entry : item.entrySet()) {
                NotificationResponse response = new NotificationResponse();
                response.setConfigId(entry.getKey().getId());
                response.setContent((String) entry.getValue());
                response.setSendDate(OffsetDateTime.now());
                response.setTenant(entry.getKey().getTenant());
                list.add(response);
            }
        }
        if (!list.isEmpty()) {
            notificationResponseRepo.saveAll(list);
        }
    }
}