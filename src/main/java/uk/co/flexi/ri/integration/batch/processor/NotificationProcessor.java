package uk.co.flexi.ri.integration.batch.processor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.integration.notification.handler.NotificationHandler;
import uk.co.flexi.ri.integration.notification.factory.NotificationHandlerFactory;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.util.Util;

import java.util.Map;


@Slf4j
@Component
@RequiredArgsConstructor
public class NotificationProcessor implements ItemProcessor<NotificationConfig, Map<NotificationConfig, Object>> {

    private final NotificationHandlerFactory notificationHandlerFactory;

    @Override
    public Map<NotificationConfig, Object> process(NotificationConfig notificationConfig) throws Exception {
        if (!notificationConfig.getFrequency().equals(
                "IMMEDIATELY") && !Util.shouldTriggerNow(notificationConfig.getFrequency())) {
            return null;
        }
        try {
            NotificationHandler handler = notificationHandlerFactory.getHandler(notificationConfig.getNotificationType());
            Map<NotificationConfig, Object> response = handler.handle(notificationConfig);
            log.info("Successfully processed notification config ID: {}", notificationConfig.getId());
            return response;
        } catch (Exception e) {
            log.error("Error processing notification config ID: {}", notificationConfig.getId(), e);
            throw e;
        }
    }
}