package uk.co.flexi.ri.integration.batch.processor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.integration.service.EventService;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.util.Util;


@Slf4j
@Component
@RequiredArgsConstructor
public class NotificationProcessor implements ItemProcessor<NotificationConfig, NotificationConfig> {

    private final EventService eventService;

    @Override
    public NotificationConfig process(NotificationConfig event) throws Exception {

        if(Util.shouldTriggerNow(event.getFrequency())){

        }
        System.err.println("this is the notifcatio config : "+event.getDestination());
        return null;
    }
}