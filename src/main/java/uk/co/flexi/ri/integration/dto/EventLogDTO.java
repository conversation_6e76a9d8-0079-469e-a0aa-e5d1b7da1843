package uk.co.flexi.ri.integration.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventLogDTO {

    private String eventId;

    private String eventType;

    private String target;

    private OffsetDateTime requestTime;

    private String originalBody;

    private String requestBody;

    private String status;

    private Integer statusCode;

    private OffsetDateTime responseTime;

    private String responseBody;

    private String errorMessage;

    private Long responseDelay;

    private Boolean isRetryEnable = Boolean.FALSE;
}