package uk.co.flexi.ri.integration.model.view;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Immutable;
import uk.co.flexi.ri.integration.model.EventConfig;
import uk.co.flexi.ri.model.EventQueue;
import uk.co.flexi.ri.util.covertor.MapToJsonConverter;

import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Entity
@Immutable
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "event_queue_view")
public class EventQueueView {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "event_id")
    private String eventId;

    @Column(name = "unique_id")
    private String uniqueId;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_type")
    private EventQueue.EventType eventType;

    @Enumerated(EnumType.STRING)
    @Column(name = "reference_type")
    private EventQueue.ReferenceType referenceType;

    @Column(name = "event_time")
    private OffsetDateTime eventTime;

    @Column(name = "performed_by_user")
    private String performedByUser;

    @Column(name = "performed_by_group")
    private String performedByGroup;

    @Column(name = "config_id")
    private Long configId;

    @Column(name = "hmac_key")
    private String hmacKey;

    @Column(name = "payload", columnDefinition = "json")
    @Convert(converter = MapToJsonConverter.class)
    private Map<String, Object> payload = new HashMap<>();

    @Enumerated(EnumType.STRING)
    @Column(name = "destination")
    private EventConfig.Destination destination;

    @Column(name = "mapping_script")
    private String mappingScript;

    @Column(name = "config", columnDefinition = "json")
    @Convert(converter = MapToJsonConverter.class)
    private Map<String, Object> config = new HashMap<>();

    @Column(name = "tenant")
    private Long tenant;

    @Transient
    private Long requestId;
}
