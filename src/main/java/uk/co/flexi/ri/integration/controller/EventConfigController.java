package uk.co.flexi.ri.integration.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.exception.custom.ScriptExecutionException;
import uk.co.flexi.ri.integration.dto.EventConfigDTO;
import uk.co.flexi.ri.integration.dto.ScriptReqDTO;
import uk.co.flexi.ri.integration.model.EventConfig;
import uk.co.flexi.ri.integration.service.EventConfigService;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api/v1/event-configs")
@PreAuthorize("hasAuthority('RI_INSP_INTGRN_LOG')")
@RequiredArgsConstructor
public class EventConfigController {

    private final EventConfigService eventConfigService;

    @GetMapping("/destination")
    public ResponseEntity<List<EventConfig.Destination>> getDestinationList() {
        return ResponseEntity.ok(eventConfigService.getDestinationList());
    }

    @GetMapping("/destination/{name}/keys")
    public ResponseEntity<EventConfigDTO> getEventConfigKeys(@PathVariable(value = "name") EventConfig.Destination name) {
        return ResponseEntity.ok(eventConfigService.getEventConfigKeys(name));
    }

    @GetMapping("/event-type")
    public ResponseEntity<List<String>> getEventType() {
        return ResponseEntity.ok(eventConfigService.getEventType());
    }

    @GetMapping
    public ResponseEntity<List<EventConfigDTO>> getEventConfig() {
        return ResponseEntity.ok(eventConfigService.getEventConfig());
    }

    @GetMapping("/{id}")
    public ResponseEntity<EventConfigDTO> findById(@PathVariable(value = "id") Long id) {
        return ResponseEntity.ok(eventConfigService.findById(id));
    }

    @PostMapping
    public ResponseEntity<EventConfigDTO> addEventConfig(@RequestBody EventConfigDTO eventConfigDTO) {
        return ResponseEntity.ok(eventConfigService.addEventConfig(eventConfigDTO));
    }

    @PutMapping("/{id}")
    public ResponseEntity<EventConfigDTO> updateEventConfig(@PathVariable(value = "id") Long id,
                                                            @RequestBody EventConfigDTO eventConfigDTO) {
        return ResponseEntity.ok(eventConfigService.updateEventConfig(id, eventConfigDTO));
    }

    @DeleteMapping("/{id}")
    public void deleteEventConfig(@PathVariable(value = "id") Long id) {
        eventConfigService.deleteEventConfig(id);
    }

    @PostMapping("/test-connection")
    public ResponseEntity<Map<String, String>> testConnection(@RequestBody EventConfigDTO eventConfigDTO) {
        return ResponseEntity.ok(eventConfigService.testConnection(eventConfigDTO));
    }

    @PostMapping("/execute-script")
    public ResponseEntity<JsonNode> executeScript(@RequestBody ScriptReqDTO reqDTO) throws JsonProcessingException, ScriptExecutionException {
        return ResponseEntity.ok(eventConfigService.executeScript(reqDTO));
    }
}
