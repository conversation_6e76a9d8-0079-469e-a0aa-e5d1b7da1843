package uk.co.flexi.ri.integration.batch.reader;

import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemReader;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.repository.NotificationConfigRepo;

import java.util.Iterator;
import java.util.List;

@Component
@RequiredArgsConstructor
@StepScope
public class NotificationConfigReader implements ItemReader<NotificationConfig> {

    private final NotificationConfigRepo notificationConfigRepo;
    private Iterator<NotificationConfig> eventQueueIterator;

    @Override
    public NotificationConfig read() {
        if (eventQueueIterator == null) {
            List<NotificationConfig> events = notificationConfigRepo.findAllNotifications();
            eventQueueIterator = events.iterator();
        }
        if (eventQueueIterator.hasNext()) {
            return eventQueueIterator.next();
        }
        return null;
    }
}