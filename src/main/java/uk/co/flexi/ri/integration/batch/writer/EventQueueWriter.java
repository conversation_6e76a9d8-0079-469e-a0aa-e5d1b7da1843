package uk.co.flexi.ri.integration.batch.writer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.Chunk;
import org.springframework.batch.item.ItemWriter;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.integration.dto.EventQueueResDTO;
import uk.co.flexi.ri.integration.model.EventQueueReq;
import uk.co.flexi.ri.integration.model.EventQueueRes;
import uk.co.flexi.ri.integration.repository.EventQueueResRepo;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class EventQueueWriter implements ItemWriter<EventQueueResDTO> {

    private final EventQueueResRepo eventQueueResRepo;

    @Override
    public void write(Chunk<? extends EventQueueResDTO> items) throws Exception {
        log.info("Updating {} processed events", items.size());
        List<EventQueueRes> queueResList = new ArrayList<>();
        items.forEach(event -> {
            EventQueueRes eventQueueRes = new EventQueueRes();
            EventQueueReq eventQueueReq = new EventQueueReq();
            eventQueueReq.setId(event.getRequestId());
            eventQueueRes.setRequest(eventQueueReq);
            eventQueueRes.setEventId(event.getEventId());
            eventQueueRes.setResponseTime(OffsetDateTime.now());
            eventQueueRes.setStatusCode(event.getStatusCode());
            eventQueueRes.setBody(event.getBody());
            eventQueueRes.setErrorMessage(event.getErrorMessage());
            eventQueueRes.setStatus(event.getStatus());
            eventQueueRes.setTenant(event.getTenant());
            queueResList.add(eventQueueRes);
        });
        eventQueueResRepo.saveAll(queueResList);
    }
}