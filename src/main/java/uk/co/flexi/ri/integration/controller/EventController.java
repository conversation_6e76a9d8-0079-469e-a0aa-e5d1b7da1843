package uk.co.flexi.ri.integration.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.integration.dto.EventLogDTO;
import uk.co.flexi.ri.integration.service.EventService;

import java.util.List;

@RestController
@RequestMapping(value = "/api/v1/events")
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('RI_INSP_INTGRN_LOG_VIEW')")
public class EventController {

    private final EventService eventService;

    @GetMapping("/inspection/{id}/log")
    public ResponseEntity<List<EventLogDTO>> getEvents(@PathVariable(value = "id") String id) {
        return ResponseEntity.ok(eventService.getAllInspectionEvents(id));
    }

    @GetMapping("/{id}/retry")
    public ResponseEntity<EventLogDTO> retryEventPosting(@PathVariable(value = "id") String eventId) {
        return ResponseEntity.ok(eventService.retryEventPosting(eventId));
    }
}
