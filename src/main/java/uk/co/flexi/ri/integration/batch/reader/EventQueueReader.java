package uk.co.flexi.ri.integration.batch.reader;

import lombok.RequiredArgsConstructor;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.item.ItemReader;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.integration.model.view.EventQueueView;
import uk.co.flexi.ri.integration.repository.view.EventQueueViewRepo;

import java.util.Iterator;
import java.util.List;

@Component
@RequiredArgsConstructor
@StepScope
public class EventQueueReader implements ItemReader<EventQueueView> {

    private final EventQueueViewRepo eventQueueViewRepo;
    private Iterator<EventQueueView> eventQueueIterator;

    @Override
    public EventQueueView read() {
        if (eventQueueIterator == null) {
            List<EventQueueView> events = eventQueueViewRepo.findAll();
            eventQueueIterator = events.iterator();
        }
        if (eventQueueIterator.hasNext()) {
            return eventQueueIterator.next();
        }
        return null;
    }
}