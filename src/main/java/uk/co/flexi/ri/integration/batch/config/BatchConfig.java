package uk.co.flexi.ri.integration.batch.config;

import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.job.builder.JobBuilder;
import org.springframework.batch.core.repository.JobRepository;
import org.springframework.batch.core.step.builder.StepBuilder;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemReader;
import org.springframework.batch.item.ItemWriter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.PlatformTransactionManager;
import uk.co.flexi.ri.integration.dto.EventQueueResDTO;
import uk.co.flexi.ri.integration.model.view.EventQueueView;
import uk.co.flexi.ri.model.NotificationConfig;

import java.util.Map;

@Configuration
@EnableScheduling
public class BatchConfig {

    private static final Integer CHUNK_SIZE = 10;

    @Bean
    public Job eventPublisherJob(JobRepository jobRepository, Step eventPublisherStep) {
        return new JobBuilder("eventPublisherJob", jobRepository)
                .start(eventPublisherStep)
                .build();
    }

    @Bean
    public Job notificationSendingJob(JobRepository jobRepository, Step notificationSendingStep) {
        return new JobBuilder("notificationSendingJob", jobRepository)
                .start(notificationSendingStep)
                .build();
    }

    @Bean
    public Step eventPublisherStep(JobRepository jobRepository,
                                   PlatformTransactionManager transactionManager,
                                   ItemReader<EventQueueView> eventQueueReader,
                                   ItemProcessor<EventQueueView, EventQueueResDTO> eventQueueProcessor,
                                   ItemWriter<EventQueueResDTO> eventQueueWriter) {
        return new StepBuilder("eventPublisherStep", jobRepository)
                .<EventQueueView, EventQueueResDTO>chunk(CHUNK_SIZE, transactionManager)
                .reader(eventQueueReader)
                .processor(eventQueueProcessor)
                .writer(eventQueueWriter)
                .build();
    }

    @Bean
    public Step notificationSendingStep(JobRepository jobRepository,
                                        PlatformTransactionManager transactionManager,
                                        ItemReader<NotificationConfig> notificationConfigReader,
                                        ItemProcessor<NotificationConfig, Map<NotificationConfig, Object>> notificationProcessor,
                                        ItemWriter<Map<NotificationConfig, Object>> eventQueueWriter) {
        return new StepBuilder("notificationSendingStep", jobRepository)
                .<NotificationConfig, Map<NotificationConfig, Object>>chunk(CHUNK_SIZE, transactionManager)
                .reader(notificationConfigReader)
                .processor(notificationProcessor)
                .writer(eventQueueWriter)
                .build();
    }
}