package uk.co.flexi.ri.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.oauth2.client.InMemoryOAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import uk.co.flexi.ri.repository.AuthProviderRepo;
import uk.co.flexi.ri.security.repo.DBClientRegistrationRepository;

@Configuration
public class OAuth2ClientConfig {

    @Bean
    @Primary
    public ClientRegistrationRepository clientRegistrationRepository(AuthProviderRepo repository) {
        return new DBClientRegistrationRepository(repository);
    }

    @Bean
    public OAuth2AuthorizedClientService authorizedClientService(ClientRegistrationRepository clientRegistrationRepository) {
        return new InMemoryOAuth2AuthorizedClientService(clientRegistrationRepository);
    }
}
