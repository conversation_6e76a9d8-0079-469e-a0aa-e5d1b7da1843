package uk.co.flexi.ri.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MinioConfig {

    @Value("${minio.access.key}")
    String minioAccessKey;

    @Value("${minio.secret.key}")
    String minioSecretKey;

    @Value("${minio.secret.api}")
    String minioApi;

    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
                .endpoint(minioApi)
                .credentials(minioAccessKey, minioSecretKey)
                .build();
    }
}
