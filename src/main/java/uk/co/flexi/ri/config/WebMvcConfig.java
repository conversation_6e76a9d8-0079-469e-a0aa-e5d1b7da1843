package uk.co.flexi.ri.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import uk.co.flexi.ri.interceptors.TenantInterceptor;
import uk.co.flexi.ri.service.TenantIdentifierResolver;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    private final TenantIdentifierResolver tenantResolver;

    public WebMvcConfig(TenantIdentifierResolver tenantResolver) {
        this.tenantResolver = tenantResolver;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new TenantInterceptor(tenantResolver));
    }

    //TODO  - Take look before prod deplyment
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**").allowedMethods("*").allowedHeaders("*").allowedOriginPatterns("*").allowCredentials(true).exposedHeaders("Authorization");
    }
}
