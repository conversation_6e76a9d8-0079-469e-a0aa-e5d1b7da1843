package uk.co.flexi.ri.security.repo;

import lombok.AllArgsConstructor;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.model.AuthProvider;
import uk.co.flexi.ri.repository.AuthProviderRepo;

@Component
@AllArgsConstructor
public class DBClientRegistrationRepository implements ClientRegistrationRepository {

    private final AuthProviderRepo authProviderRepo;

    @Override
    public ClientRegistration findByRegistrationId(String registrationId) {
        return authProviderRepo.findByRegistrationId(registrationId)
                .map(this::convertToClientRegistration)
                .orElse(null);
    }

    private ClientRegistration convertToClientRegistration(AuthProvider client) {
        return ClientRegistration.withRegistrationId(client.getRegistrationId())
                .clientId(client.getClientId())
                .clientSecret(client.getClientSecret())
                .authorizationUri(client.getAuthorizationUri())
                .tokenUri(client.getTokenUri())
                .redirectUri(client.getRedirectUri())
                .scope(client.getScopes().split(","))
                .userInfoUri(client.getUserInfoUri())
                .userNameAttributeName(client.getNameAttr())
                .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .jwkSetUri(client.getJwkSetUri())
                .build();
    }
}
