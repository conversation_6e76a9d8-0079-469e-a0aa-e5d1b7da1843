package uk.co.flexi.ri.security.config;

import lombok.AllArgsConstructor;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import uk.co.flexi.ri.exception.custom.UserNotFoundException;
import uk.co.flexi.ri.model.Media;
import uk.co.flexi.ri.model.Merchant;
import uk.co.flexi.ri.model.User;
import uk.co.flexi.ri.repository.MerchantRepo;
import uk.co.flexi.ri.repository.UserRepo;
import uk.co.flexi.ri.security.filter.JWTAuthFilter;
import uk.co.flexi.ri.security.service.CustomAuthenticationEntryPoint;
import uk.co.flexi.ri.security.service.JWTService;
import uk.co.flexi.ri.security.service.UserInfoUserDetailService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import uk.co.flexi.ri.service.MediaService;

import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@AllArgsConstructor
public class SecurityConfig {

    private final JWTAuthFilter jwtAuthFilter;

    private final JWTService jwtService;

    private final UserRepo userRepo;

    private final MerchantRepo merchantRepo;

    private final MediaService mediaService;

    @Bean
    public UserDetailsService userDetailsService() {
        return new UserInfoUserDetailService(userRepo);
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity httpSecurity) throws Exception {
        return httpSecurity
                .csrf(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers("/swagger-ui/**",
                                "/v3/api-docs/**",
                                "/swagger-ui.html",
                                "/api/v1/swagger-ui/**",
                                "/api/v1/swagger-ui.html",
                                "/swagger-resources/**",
                                "/webjars/**").permitAll()
                        .requestMatchers("/api/v1/auth/login**").permitAll()
                        .requestMatchers("/api/v1/media/temp-upload").permitAll()
                        .requestMatchers("/api/v1/simulator/post-event").permitAll()
                        .requestMatchers("/api/v1/auth/refresh-token**").permitAll()
                        .requestMatchers("/").permitAll()
                        .requestMatchers(HttpMethod.GET, "/api/v1/users").permitAll()
                        .requestMatchers("/api/v1/swagger").permitAll()
                        .requestMatchers("/oauth2/authorization/google/**").permitAll()
                        .requestMatchers("/api/v1/oauth2/login/success**").permitAll()
                        .requestMatchers("/api/v1/oauth2/authorization/google/**").permitAll()
                        .requestMatchers("/api/v1/**").authenticated()
                )
                .oauth2Login(oauth -> oauth
                        .successHandler(authSuccessHandler())
                )
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                )
                .authenticationProvider(authenticationProvider())
                .addFilterBefore(jwtAuthFilter, UsernamePasswordAuthenticationFilter.class)
                .exceptionHandling(exception -> exception
                        .authenticationEntryPoint(new CustomAuthenticationEntryPoint())
                )
                .build();
    }

    @Bean
    public AuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authenticationProvider = new DaoAuthenticationProvider();
        authenticationProvider.setUserDetailsService(userDetailsService());
        authenticationProvider.setPasswordEncoder(passwordEncoder());
        return authenticationProvider;
    }


    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration configuration) throws Exception {
        return configuration.getAuthenticationManager();
    }


    @Bean
    public AuthenticationSuccessHandler authSuccessHandler() {
        return new SimpleUrlAuthenticationSuccessHandler() {
            @Override
            protected void handle(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException {
                OAuth2AuthenticationToken oauthToken = (OAuth2AuthenticationToken) authentication;
                DefaultOAuth2User oauthUser = (DefaultOAuth2User) oauthToken.getPrincipal();

                String userName = oauthUser.getAttribute("preferred_username");
                if (userName == null) {
                    userName = oauthUser.getAttribute("email");
                }
                String profileImage = oauthUser.getAttribute("picture");

                User user = findOrUpdateUser(userName, profileImage);
                String userGroupId = userRepo.findFirstUserGroupByUserId(user.getId());

                String accessToken = jwtService.generateToken(user, userGroupId);
                String refreshToken = jwtService.generateRefreshToken(user, userGroupId);

                redirectToSuccess(response, accessToken, refreshToken);
            }

            private User findOrUpdateUser(String userName, String profileImage) {
                return userRepo.findByUserNameAndIsActive(userName, Boolean.TRUE).stream().findFirst()
                        .map(existingUser -> updateUserProfileImage(existingUser, profileImage))
                        .orElseThrow(() -> new UserNotFoundException("User with username " + userName + " not found"));
            }

            private User updateUserProfileImage(User user, String profileImage) {
                if (user.getProfileImage() == null && profileImage != null) {
                    CompletableFuture.runAsync(() -> {
                        try {
                            Optional<Merchant> merchant = merchantRepo.findById(user.getMerchant().getId());
                            Media media = mediaService.uploadSSOProfileImage(profileImage,
                                    merchant.get().getMerchantName());
                            user.setProfileImage(media);
                            userRepo.save(user);
                        } catch (Exception e) {
                            logger.error("Error saving profile image", e);
                        }
                    });
                }
                return user;
            }

            private void redirectToSuccess(HttpServletResponse response, String accessToken, String refreshToken) throws IOException {
                String redirectUrl = ServletUriComponentsBuilder.fromCurrentRequestUri()
                        .scheme("https")
                        .replacePath("/sso/success")
                        .queryParam("access_token", accessToken)
                        .queryParam("refresh_token", refreshToken)
                        .build()
                        .toUriString();
                response.sendRedirect(redirectUrl);
            }
        };
    }
}
