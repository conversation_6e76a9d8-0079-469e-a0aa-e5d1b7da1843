package uk.co.flexi.ri.security.service;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.oauth2.jwt.JwtException;
import org.springframework.stereotype.Service;
import uk.co.flexi.ri.exception.custom.RIAuthenticationException;
import uk.co.flexi.ri.model.User;
import uk.co.flexi.ri.util.Constants;

import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

@Service
public class JWTService {

    private static final String USER_GROUP = "userGroup";

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${jwt.accessExpirationMs}")
    private long accessTokenExpirationMs;

    @Value("${jwt.refreshExpirationMs}")
    private long refreshTokenExpirationMs;

    public String generateToken(User user, String userGroup) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(USER_GROUP, userGroup);
        claims.put(Constants.TENANT, user.getTenant());
        return createToken(claims, user.getUserName(), accessTokenExpirationMs);
    }

    public String generateRefreshToken(User user, String userGroup) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(USER_GROUP, userGroup);
        claims.put(Constants.TENANT, user.getTenant());
        return createToken(claims, user.getUserName(), refreshTokenExpirationMs);
    }

    public String generateCustomToken(String subject, Map<String, Object> claims) {
        return createToken(claims, subject, accessTokenExpirationMs);
    }

    private String createToken(Map<String, Object> claims, String subject, long expiry) {
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiry))
                .signWith(getSignKey(), SignatureAlgorithm.HS256).compact();
    }

    private Key getSignKey() {
        byte[] keyBite = Decoders.BASE64.decode(jwtSecret);
        return Keys.hmacShaKeyFor(keyBite);
    }

    public String extractSubject(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public String extractUserGroup(String token) {
        return extractClaim(token, claims -> (String) claims.get(USER_GROUP));
    }

    public Integer extractTenant(String token) {
        return extractClaim(token, claims -> (Integer) claims.get(Constants.TENANT));
    }

    public String extractUserId(String token) {
        return extractClaim(token, claims -> (String) claims.get("userId"));
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    private Claims extractAllClaims(String token) {
        return Jwts
                .parserBuilder()
                .setSigningKey(getSignKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    public Boolean validateToken(String token) {
        try {
            Jwts.parserBuilder().setSigningKey(getSignKey()).build().parseClaimsJws(token);
        } catch (MalformedJwtException ex) {
            throw new RIAuthenticationException("Malformed jwt token");

        } catch (ExpiredJwtException ex) {
            throw new RIAuthenticationException("Token expired. Refresh required");

        } catch (UnsupportedJwtException ex) {
            throw new RIAuthenticationException("Unsupported JWT token");

        } catch (IllegalArgumentException | JwtException ex) {
            throw new RIAuthenticationException("Illegal argument token");
        }
        return Boolean.TRUE;
    }
}
