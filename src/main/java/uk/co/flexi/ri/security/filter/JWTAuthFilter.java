package uk.co.flexi.ri.security.filter;

import lombok.RequiredArgsConstructor;
import uk.co.flexi.ri.security.model.CustomAuthDetails;
import uk.co.flexi.ri.security.service.JWTService;
import uk.co.flexi.ri.security.service.UserInfoUserDetailService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import uk.co.flexi.ri.util.Constants;

import java.io.IOException;

@Component
@RequiredArgsConstructor
public class JWTAuthFilter extends OncePerRequestFilter {

    private final JWTService jwtService;

    private final UserInfoUserDetailService userDetailsService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String authHeader = request.getHeader("Authorization");
        String token = null;

        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            token = authHeader.substring(7);
        }

        if (StringUtils.hasText(token) && SecurityContextHolder.getContext().getAuthentication() == null
                && Boolean.TRUE.equals(jwtService.validateToken(token))) {

            String userName = jwtService.extractSubject(token);
            String userGroup = jwtService.extractUserGroup(token);
            Integer tenant = jwtService.extractTenant(token);

            UserDetails userDetails = userDetailsService.loadUserByUsername(userName);
            UsernamePasswordAuthenticationToken authenticationToken =
                    new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());

            authenticationToken.setDetails(new CustomAuthDetails(userGroup, tenant));
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            request.setAttribute(Constants.TENANT, String.valueOf(tenant));
        }

        filterChain.doFilter(request, response);
    }
}
