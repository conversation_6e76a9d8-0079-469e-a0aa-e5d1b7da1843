package uk.co.flexi.ri.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import uk.co.flexi.ri.model.InspectionItemCondition;

import java.util.List;
import java.util.Optional;

public interface InspectionItemConditionRepo extends JpaRepository<InspectionItemCondition, Long> {

    Optional<InspectionItemCondition> findByItemConditionId(String userGroupId);

    List<InspectionItemCondition> findByConditionType(InspectionItemCondition.ConditionType conditionType);

}
