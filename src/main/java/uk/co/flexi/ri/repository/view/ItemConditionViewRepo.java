package uk.co.flexi.ri.repository.view;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.dto.ItemConditionResponseDTO;
import uk.co.flexi.ri.model.view.ItemConditionView;

import java.time.LocalDate;
import java.util.List;

public interface ItemConditionViewRepo extends JpaRepository<ItemConditionView, Long> {

    @Query("""
                SELECT new uk.co.flexi.ri.dto.ItemConditionResponseDTO(
                    ic.itemStatus,
                    ic.itemCondition,
                    SUM(ic.returnCount),
                    SUM(ic.price),
                    CAST((SUM(ic.returnCount) * 100.0) /
                         (SELECT COALESCE(SUM(ic2.returnCount), 1)
                          FROM ItemConditionView ic2
                          WHERE ic2.createdAt BETWEEN :startDate AND :endDate) AS BigDecimal)
                )
                FROM ItemConditionView ic
                WHERE ic.createdAt BETWEEN :startDate AND :endDate
                GROUP BY ic.itemStatus, ic.itemCondition
            """)
    List<ItemConditionResponseDTO> findGroupedData(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    @Query("""
            SELECT ic.sellingChannel AS sellingChannel,
                   ic.itemStatus AS itemStatus,
                   JSON_OBJECTAGG(ic.itemCondition, JSON_OBJECT('count', ic.totalReturnCount, 'price', ic.price)) AS conditions,
                   SUM(ic.totalReturnCount) AS total,
                   SUM(ic.price) AS price
            FROM (
                SELECT sellingChannel AS sellingChannel,
                       itemStatus AS itemStatus,
                       itemCondition AS itemCondition,
                       SUM(returnCount) AS totalReturnCount,
                       SUM(price) AS price
                FROM ItemConditionView
                WHERE createdAt BETWEEN :startDate AND :endDate
                GROUP BY sellingChannel, itemStatus, itemCondition
            ) ic
            GROUP BY ic.sellingChannel, ic.itemStatus
            """)
    List<Object[]> findGroupedDataByChannel(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    @Query("""
            SELECT MAX(ic.departmentName) AS departmentName,
                   ic.itemStatus AS itemStatus,
                   JSON_OBJECTAGG(ic.itemCondition, JSON_OBJECT('count', ic.totalReturnCount, 'price', ic.price)) AS conditions,
                   SUM(ic.totalReturnCount) AS total,
                   SUM(ic.price) AS price
            FROM (
                SELECT departmentName AS departmentName,
                       itemStatus AS itemStatus,
                       productClass AS productClass,
                       itemCondition AS itemCondition,
                       SUM(returnCount) AS totalReturnCount,
                       SUM(price) AS price
                FROM ItemConditionView
                WHERE createdAt BETWEEN :startDate AND :endDate
                GROUP BY departmentName, productClass, itemStatus, itemCondition
            ) ic
            GROUP BY ic.productClass, ic.itemStatus
            """)
    List<Object[]> findGroupedDataByProductClass(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

}
