package uk.co.flexi.ri.repository;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import uk.co.flexi.ri.model.TempMedia;

import java.time.OffsetDateTime;
import java.util.List;

public interface TempMediaRepo extends JpaRepository<TempMedia, Long> {

    List<TempMedia> findByInspectionItemIdAndTenant(String inspectionItemId, Long tenant);

    @Transactional
    @Modifying
    @Query("DELETE FROM TempMedia tm WHERE tm.createdAt < :cutoff")
    void deleteByCreatedAtBefore(OffsetDateTime cutoff);
}
