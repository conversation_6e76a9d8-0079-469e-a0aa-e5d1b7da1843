package uk.co.flexi.ri.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.Order;

import java.util.Optional;

public interface OrderRepo extends JpaRepository<Order, Long> {

    @Query("SELECT o FROM Order o WHERE ( :referenceId = o.orderId OR :referenceId = o.returnTrackingId OR " +
            ":referenceId = o.returnOrderId) ")
    Optional<Order> findByReferenceId(@Param("referenceId") String referenceId);
}
