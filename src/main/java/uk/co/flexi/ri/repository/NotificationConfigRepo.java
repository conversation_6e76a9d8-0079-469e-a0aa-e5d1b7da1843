package uk.co.flexi.ri.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import uk.co.flexi.ri.model.NotificationConfig;

import java.util.List;
import java.util.Optional;

public interface NotificationConfigRepo extends JpaRepository<NotificationConfig, Long> {

    @Query(value = "SELECT * FROM notification_config", nativeQuery = true)
    List<NotificationConfig> findAllNotifications();
}
