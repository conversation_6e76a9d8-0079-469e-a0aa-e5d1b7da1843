package uk.co.flexi.ri.repository.view;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.view.TimeTrackerView;

import java.time.LocalDate;
import java.util.List;

public interface TimeTrackerViewRepo extends JpaRepository<TimeTrackerView, Long> {

    @Query("""
            SELECT
                CASE
                    WHEN ttv.days < 1 THEN '< 1 day'
                    WHEN ttv.days BETWEEN 1 AND 3 THEN '1-3 days'
                    WHEN ttv.days BETWEEN 3 AND 5 THEN '3-5 days'
                    ELSE '> 5 days'
                END AS label,
                SUM(ttv.returnCount) AS value
            FROM TimeTrackerView ttv
            WHERE ttv.completedAt BETWEEN :startDate AND :endDate
            GROUP BY label
            """)
    List<Object[]> getInspectionCompletionTime(@Param("startDate") LocalDate startDate,
                                               @Param("endDate") LocalDate endDate);

}
