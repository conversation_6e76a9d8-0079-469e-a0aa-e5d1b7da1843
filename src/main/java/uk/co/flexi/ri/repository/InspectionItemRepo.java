package uk.co.flexi.ri.repository;

import org.springframework.lang.NonNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItem;

import java.util.List;
import java.util.Optional;

public interface InspectionItemRepo extends JpaRepository<InspectionItem, Long> {

    List<InspectionItem> findByInspection(Inspection inspection);

    Optional<InspectionItem> findByInspectionItemId(@NonNull String inspectionItemId);

    @Modifying
    @Query("DELETE FROM InspectionItem ii WHERE ii.id = :id")
    void deleteByInspectionItemId(@Param("id") Long id);
}
