package uk.co.flexi.ri.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.Role;
import uk.co.flexi.ri.model.RoleGroup;

import java.util.List;

public interface RoleRepo extends JpaRepository<Role, Long> {

    @Query("SELECT r FROM Role r JOIN r.roleGroups rg WHERE rg = :roleGroup")
    List<Role> findByRoleGroup(@Param("roleGroup") RoleGroup roleGroup);
}
