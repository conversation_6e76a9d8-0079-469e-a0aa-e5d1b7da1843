package uk.co.flexi.ri.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.User;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface UserRepo extends JpaRepository<User, Integer> {

    @Query(value = "SELECT * FROM user u WHERE u.user_name = :userName AND u.is_active = :isActive", nativeQuery = true)
    List<User> findByUserNameAndIsActive(@Param("userName") String userName, @Param("isActive") Boolean isActive);

    @Query(value = "SELECT * FROM user u WHERE u.user_name = :userName " +
            "AND tenant = :tenant AND u.is_active = :isActive", nativeQuery = true)
    Optional<User> findByUserNameAndIsActive(@Param("userName") String userName,
                                             @Param("tenant") Long tenant, @Param("isActive") Boolean isActive);

    Optional<User> findById(Long userId);

    @Query(value = "SELECT * FROM user u where tenant = :tenant order by is_active desc, id desc",
           countQuery = "SELECT count(*) FROM user u where tenant = :tenant", 
           nativeQuery = true)
    Page<User> findAllUsers(@Param("tenant") Long tenant, Pageable pageable);

    Optional<User> findByUserId(String userId);

    @Query(value = "SELECT * FROM user u where user_id = :userId ", nativeQuery = true)
    Optional<User> findUserById(String userId);

    @Query(value = "select ug.user_group_id from user_group ug join user_group_rel ugr on ugr.group_id = ug.id" +
            " join `user` u on u.id = ugr.user_id where u.id = :userId ORDER BY ugr.id ASC LIMIT 1", nativeQuery = true)
    String findFirstUserGroupByUserId(@Param("userId") Long userId);

    @Query("SELECT u.userName FROM User u WHERE (:userName IS NULL OR LOWER(u.userName) LIKE LOWER(CONCAT(:userName, '%')))")
    List<String> findUserNamesBySearch(@Param("userName") String userName);
}
