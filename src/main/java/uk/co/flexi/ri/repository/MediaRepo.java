package uk.co.flexi.ri.repository;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.model.Media;

public interface MediaRepo extends JpaRepository<Media, Long> {

    Media findByMediaId(@NonNull String mediaId);

    long countByInspectionItemAndUrl(@NonNull InspectionItem inspectionItem, @NonNull String url);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM media WHERE id = :mediaId", nativeQuery = true)
    void deleteMedia(@Param("mediaId") Long mediaId);
}
