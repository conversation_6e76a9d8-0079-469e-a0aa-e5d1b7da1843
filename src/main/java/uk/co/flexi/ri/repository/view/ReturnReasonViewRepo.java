package uk.co.flexi.ri.repository.view;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.dto.TreeMapResponse;
import uk.co.flexi.ri.model.view.ReturnReasonView;

import java.time.LocalDate;
import java.util.List;

public interface ReturnReasonViewRepo extends JpaRepository<ReturnReasonView, Long> {

    @Query("""
                SELECT new uk.co.flexi.ri.dto.TreeMapResponse(
                 rv.returnReason,
                 rv.returnReason,
                 SUM(rv.returnCount),
                 SUM(rv.price)
                )
                FROM ReturnReasonView rv
                WHERE rv.createdAt BETWEEN :startDate AND :endDate
                GROUP BY rv.returnReason
            """)
    List<TreeMapResponse> findGroupedDataByReturnReason(@Param("startDate") LocalDate startDate,
                                                        @Param("endDate") LocalDate endDate);

    @Query("""
                SELECT new uk.co.flexi.ri.dto.TreeMapResponse(
                 MAX(rv.departmentName),
                 rv.productClass,
                 rv.productClass,
                 SUM(rv.returnCount),
                 SUM(rv.price)
                )
                FROM ReturnReasonView rv
                WHERE rv.createdAt BETWEEN :startDate AND :endDate
                AND rv.returnReason = :returnReason
                GROUP BY rv.productClass
            """)
    List<TreeMapResponse> findGroupedDataByProductClass(@Param("startDate") LocalDate startDate,
                                                        @Param("endDate") LocalDate endDate,
                                                        @Param("returnReason") String returnReason);

    @Query("""
                SELECT new uk.co.flexi.ri.dto.TreeMapResponse(
                 rv.productSubClass,
                 rv.productSubClass,
                 SUM(rv.returnCount),
                 SUM(rv.price)
                )
                FROM ReturnReasonView rv
                WHERE rv.createdAt BETWEEN :startDate AND :endDate
                AND rv.returnReason = :returnReason AND rv.productClass = :productClass
                GROUP BY rv.productSubClass
            """)
    List<TreeMapResponse> findGroupedDataByProductSubClass(@Param("startDate") LocalDate startDate,
                                                           @Param("endDate") LocalDate endDate,
                                                           @Param("returnReason") String returnReason,
                                                           @Param("productClass") String productClass);

    @Query("""
                SELECT new uk.co.flexi.ri.dto.TreeMapResponse(
                MAX(rv.productName),
                rv.productStyle,
                rv.productStyle,
                SUM(rv.returnCount),
                SUM(rv.price)
                )
                FROM ReturnReasonView rv
                WHERE rv.createdAt BETWEEN :startDate AND :endDate
                AND rv.returnReason = :returnReason AND rv.productClass = :productClass AND rv.productSubClass=:productSubClass
                GROUP BY rv.productStyle
            """)
    List<TreeMapResponse> findGroupedDataByProductStyle(@Param("startDate") LocalDate startDate,
                                                        @Param("endDate") LocalDate endDate,
                                                        @Param("returnReason") String returnReason,
                                                        @Param("productClass") String productClass,
                                                        @Param("productSubClass") String productSubClass);

    @Query("""
                SELECT new uk.co.flexi.ri.dto.TreeMapResponse(
                rv.sku,
                MAX(rv.productSize),
                rv.sku,
                SUM(rv.returnCount),
                SUM(rv.price)
                )
                FROM ReturnReasonView rv
                WHERE rv.createdAt BETWEEN :startDate AND :endDate
                AND rv.returnReason = :returnReason AND rv.productClass = :productClass AND
                rv.productSubClass=:productSubClass AND rv.productStyle=:productStyle
                GROUP BY rv.sku
            """)
    List<TreeMapResponse> findGroupedDataByProductSku(@Param("startDate") LocalDate startDate,
                                                      @Param("endDate") LocalDate endDate,
                                                      @Param("returnReason") String returnReason,
                                                      @Param("productClass") String productClass,
                                                      @Param("productSubClass") String productSubClass,
                                                      @Param("productStyle") String productStyle);

}
