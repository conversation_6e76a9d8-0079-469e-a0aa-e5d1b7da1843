package uk.co.flexi.ri.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.MerchantProductPrice;

import java.util.List;
import java.util.Set;

public interface MerchantProductPriceRepo extends JpaRepository<MerchantProductPrice, Long> {

    List<MerchantProductPrice> findByTenantAndSkuIn(@Param("tenant") Long tenant, @Param("skuValues") Set<String> skuValues);
}
