package uk.co.flexi.ri.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.ProductInstruction;

import java.util.List;
import java.util.Optional;

public interface ProductInstructionRepo extends JpaRepository<ProductInstruction, Long> {

    Optional<ProductInstruction> findByProductInstructionId(String productInstructionId);

    List<ProductInstruction> findByProductInstructionIdIn(List<String> ids);

    Optional<ProductInstruction> findByProductInstructionIdAndInstructions_LanguageCode(String productInstructionId, String languageCode);

    @Query(""" 
                SELECT pi FROM ProductInstruction pi
                WHERE CONCAT(pi.productClass, '|', pi.productSubclass) IN :keys
            """)
    List<ProductInstruction> findAllByClassSubClassIn(@Param("keys") List<String> keys);
}
