package uk.co.flexi.ri.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import uk.co.flexi.ri.model.AuthProvider;

import java.util.Optional;


public interface AuthProviderRepo extends JpaRepository<AuthProvider, Long> {

    Optional<AuthProvider> findByProvider(AuthProvider.Provider provider);

    @Query(value = "select * from auth_provider ap " +
            "where ap.registration_id = :registrationId", nativeQuery = true)
    Optional<AuthProvider> findByRegistrationId(String registrationId);
}
