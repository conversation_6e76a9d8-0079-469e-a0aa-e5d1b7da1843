package uk.co.flexi.ri.repository.view;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.view.InspectionCompletedView;

import java.time.LocalDate;
import java.util.List;

public interface InspectionCompletedViewRepo extends JpaRepository<InspectionCompletedView, Long> {

    @Query("""
                SELECT
                CASE
                   WHEN :granularity = 'daily' THEN DATE_FORMAT(cv.completedDate, '%Y-%m-%d')
                   WHEN :granularity = 'weekly' THEN DATE_FORMAT(STR_TO_DATE(CONCAT(YEARWEEK(cv.completedDate, 3), ' Sunday'), '%X%V %W'), '%Y-%m-%d')
                   WHEN :granularity = 'monthly' THEN DATE_FORMAT(cv.completedDate, '%Y-%m-01')
                   END AS period,
                   cv.completedBy,
                   SUM(cv.completedCount)
                FROM InspectionCompletedView cv
                WHERE cv.completedDate BETWEEN :startDate AND :endDate
                GROUP BY period, cv.completedBy
            """)
    List<Object[]> findCompletedStatus(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate, @Param("granularity") String granularity);

}
