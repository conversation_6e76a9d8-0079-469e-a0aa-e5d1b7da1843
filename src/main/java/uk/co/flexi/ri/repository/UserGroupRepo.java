package uk.co.flexi.ri.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.UserGroup;

import java.util.List;
import java.util.Optional;
import java.util.Set;


public interface UserGroupRepo extends JpaRepository<UserGroup, Long> {

    Optional<UserGroup> findByUserGroupId(String userGroupId);

    @Query("""
                SELECT r FROM UserGroup ug
                JOIN ug.reviewers r
                WHERE ug.id = :id
            """)
    Set<UserGroup> findReviewersByUserGroupId(Long id);

    @Query(value = "select * from user_group ug where ug.user_group_id = :userGroupId", nativeQuery = true)
    Optional<UserGroup> findUserGroupById(String userGroupId);

    @Query(value = "select * from user_group ug where ug.tenant = :tenant", nativeQuery = true)
    List<UserGroup> findAllUserGroup(Long tenant);

    @Query(value = "select * from user_group ug where ug.is_active = true AND ug.tenant = :tenant", nativeQuery = true)
    List<UserGroup> findAllActiveUserGroup(Long tenant);

    @Query("SELECT ug FROM UserGroup ug LEFT JOIN FETCH ug.reviewers")
    List<UserGroup> findAllWithReviewers();

    @Modifying
    @Query(value = "DELETE FROM user_group_reviewers WHERE user_group_id = :id", nativeQuery = true)
    void deleteAllReviewers(@Param("id") Long id);

    Optional<UserGroup> findByNameIgnoreCase(String name);
}
