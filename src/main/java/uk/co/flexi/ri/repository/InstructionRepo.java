package uk.co.flexi.ri.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.Instruction;

import java.util.Optional;

public interface InstructionRepo extends JpaRepository<Instruction, Long> {

    Optional<Instruction> findByProductInstruction_ProductClassAndProductInstruction_ProductSubclassAndLanguageCode(String productClass, String productSubclass, String languageCode);

    @Modifying
    @Query(value = "DELETE FROM instruction WHERE product_instruction_id = :id", nativeQuery = true)
    void deleteAllInstruction(@Param("id") Long id);

}
