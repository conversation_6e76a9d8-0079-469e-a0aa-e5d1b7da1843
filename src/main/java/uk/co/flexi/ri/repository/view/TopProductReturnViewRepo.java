package uk.co.flexi.ri.repository.view;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import uk.co.flexi.ri.dto.TreeMapResponse;
import uk.co.flexi.ri.model.view.TopProductReturnView;

import java.time.LocalDate;
import java.util.List;

public interface TopProductReturnViewRepo extends JpaRepository<TopProductReturnView, Long> {

    @Query("""
                SELECT new uk.co.flexi.ri.dto.TreeMapResponse(
                 MAX(prv.productName),
                 prv.productStyle,
                 prv.productStyle,
                 SUM(prv.returnCount),
                 SUM(prv.price)
                )
                FROM TopProductReturnView prv
                WHERE prv.createdAt BETWEEN :startDate AND :endDate
                GROUP BY prv.productStyle
            """)
    List<TreeMapResponse> findGroupedDataByProductStyle(LocalDate startDate, LocalDate endDate);

    @Query("""
                SELECT new uk.co.flexi.ri.dto.TreeMapResponse(
                 MAX(prv.productName),
                 prv.sku,
                 MAX(prv.productSize),
                 prv.sku,
                 SUM(prv.returnCount),
                 SUM(prv.price)
                )
                FROM TopProductReturnView prv
                WHERE prv.createdAt BETWEEN :startDate AND :endDate
                GROUP BY prv.sku
            """)
    List<TreeMapResponse> findGroupedDataByProductSku(LocalDate startDate, LocalDate endDate);


    @Query("""
                SELECT new uk.co.flexi.ri.dto.TreeMapResponse(
                 prv.returnReason,
                 prv.returnReason,
                 SUM(prv.returnCount),
                 SUM(prv.price)
                )
                FROM TopProductReturnView prv
                WHERE prv.createdAt BETWEEN :startDate AND :endDate
                AND (
                  (:skuFilter IS NOT NULL AND prv.sku = :skuFilter) OR
                  (:styleFilter IS NOT NULL AND prv.productStyle = :styleFilter)
                    )
                GROUP BY prv.returnReason
            """)
    List<TreeMapResponse> findGroupedDataByReturnReason(LocalDate startDate, LocalDate endDate, String styleFilter, String skuFilter);


    @Query("""
                SELECT new uk.co.flexi.ri.dto.TreeMapResponse(
                 prv.sellingChannel,
                 prv.sellingChannel,
                 SUM(prv.returnCount),
                 SUM(prv.price)
                )
                FROM TopProductReturnView prv
                WHERE prv.createdAt BETWEEN :startDate AND :endDate
                AND (
                  (:skuFilter IS NOT NULL AND prv.sku = :skuFilter) OR
                  (:styleFilter IS NOT NULL AND prv.productStyle = :styleFilter)
                    )
                GROUP BY prv.sellingChannel
            """)
    List<TreeMapResponse> findGroupedDataByReturnChannel(LocalDate startDate, LocalDate endDate, String styleFilter, String skuFilter);
}
