package uk.co.flexi.ri.repository;

import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.lang.NonNull;
import uk.co.flexi.ri.model.Comment;

import java.util.Optional;

public interface CommentRepo extends JpaRepository<Comment, Long> {

    Optional<Comment> findByCommentId(String commentId);

    @Modifying
    @Transactional
    @Query(value ="DELETE FROM comment WHERE id = :id", nativeQuery = true)
    void deleteByCommentId(@NonNull Long id);
}
