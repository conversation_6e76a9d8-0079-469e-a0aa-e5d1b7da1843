package uk.co.flexi.ri.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.AuditEvent;

import java.time.OffsetDateTime;
import java.util.List;

public interface AuditEventRepo extends JpaRepository<AuditEvent, Long>, AuditRepository {

    @Override
    default void saveEvent(AuditEvent event) {
        save(event);
    }

    @Override
    default void saveEvents(List<AuditEvent> events) {
        saveAll(events);
    }

    @Override
    default Long countByDateBetweenAndEventType(OffsetDateTime startDate, OffsetDateTime endDate, AuditEvent.EventType eventType) {
        return countDistinctEntityIdsByEventTypeAndCreatedAtBetween(eventType, startDate, endDate);
    }

    @Query("SELECT COUNT(DISTINCT ae.entityId) FROM AuditEvent ae " +
            "WHERE ae.eventType = :eventType " +
            "AND ae.createdAt BETWEEN :startDate AND :endDate")
    long countDistinctEntityIdsByEventTypeAndCreatedAtBetween(@Param("eventType") AuditEvent.EventType eventType, @Param("startDate") OffsetDateTime startDate, @Param("endDate") OffsetDateTime endDate);
}
