package uk.co.flexi.ri.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import uk.co.flexi.ri.model.Merchant;

public interface MerchantRepo extends JpaRepository<Merchant, Long> {

    @Query(value = "SELECT * FROM merchant WHERE tenant = :tenant", nativeQuery = true)
    Merchant findByTenantNative(@Param("tenant") Long tenant);

    @Query("SELECT hmacKey FROM Merchant WHERE tenant = :tenant")
    String findHmacKeyByTenant(@Param("tenant") Long tenant);

}
