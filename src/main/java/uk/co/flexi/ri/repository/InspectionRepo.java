package uk.co.flexi.ri.repository;

import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import uk.co.flexi.ri.model.Inspection;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface InspectionRepo extends JpaRepository<Inspection, Long>, JpaSpecificationExecutor<Inspection> {

    Optional<Inspection> findByInspectionId(@NonNull String inspectionId);

    List<Inspection> findByReferenceId(@NonNull String referenceId);

    Page<Inspection> findAll(Pageable pageable);

    @Query(value = "select reference_id from inspection i join user_group ug on ug.id = i.assignee_group " +
            "where ug.user_group_id = :userGroupId and i.status ='IN_PROGRESS' and i.tenant = :tenant", nativeQuery = true)
    List<String> findNotificationEligibleInspection(String userGroupId, Long tenant);

    @Query(value = "select reference_id from inspection i join user_group ug on ug.id = i.assignee_group " +
            "where ug.user_group_id = :userGroupId and i.assigned_date >= :lastExecTime and i.status ='IN_PROGRESS' and i.tenant =:tenant", nativeQuery = true)
    List<String> findNotificationEligibleInspection(String userGroupId, OffsetDateTime lastExecTime, Long tenant);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM media WHERE id = :mediaId", nativeQuery = true)
    void deleteMedia(@Param("mediaId") Long mediaId);

    @Modifying
    @Transactional
    @Query(value = "DELETE FROM media_aud WHERE id = :mediaId", nativeQuery = true)
    void deleteMediaAud(@Param("mediaId") Long mediaId);

    @Modifying
    @Transactional
    @Query(value ="DELETE FROM comment WHERE id = :commentId", nativeQuery = true)
    void deleteComment(@Param("commentId") Long commentId);

    @Modifying
    @Transactional
    @Query(value ="DELETE FROM comment_aud WHERE id = :commentId", nativeQuery = true)
    void deleteCommentAud(@Param("commentId") Long commentId);

    @Modifying
    @Transactional
    @Query(value ="DELETE FROM inspection_item WHERE id = :inspectionItemId", nativeQuery = true)
    void deleteInspectionItem(@Param("inspectionItemId") Long inspectionItemId);

    @Modifying
    @Transactional
    @Query(value ="DELETE FROM inspection_item_aud WHERE id = :inspectionItemId", nativeQuery = true)
    void deleteInspectionItemAud(@Param("inspectionItemId") Long inspectionItemId);

    @Modifying
    @Transactional
    @Query(value ="DELETE FROM inspection WHERE id = :inspectionId", nativeQuery = true)
    void deleteInspection(@Param("inspectionId") Long inspectionId);

    @Modifying
    @Transactional
    @Query(value ="DELETE FROM inspection_aud WHERE id = :inspectionId", nativeQuery = true)
    void deleteInspectionAud(@Param("inspectionId") Long inspectionId);

    @Modifying
    @Transactional
    @Query(value ="DELETE FROM product WHERE id IN(:productIds)", nativeQuery = true)
    void deleteProducts(@Param("productIds") Set<Long> productIds);

    @Modifying
    @Transactional
    @Query(value ="DELETE FROM orders WHERE id = :orderId", nativeQuery = true)
    void deleteOrder(@Param("orderId") Long orderId);

    @Modifying
    @Transactional
    @Query(value ="DELETE FROM event_queue WHERE unique_id = :uniqueId", nativeQuery = true)
    void deleteEventQueue(@Param("uniqueId") String uniqueId);

    @Modifying
    @Transactional
    @Query(value ="DELETE FROM event_queue_req WHERE unique_id = :uniqueId", nativeQuery = true)
    void deleteEventReq(@Param("uniqueId") String uniqueId);

    @Modifying
    @Transactional
    @Query(value ="DELETE re FROM event_queue_res re " +
            "JOIN event_queue_req rq ON re.request_id = rq.id " +
            "WHERE rq.unique_id = :uniqueId", nativeQuery = true)
    void deleteEventRes(@Param("uniqueId") String uniqueId);
}

