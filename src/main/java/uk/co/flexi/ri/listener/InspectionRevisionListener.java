package uk.co.flexi.ri.listener;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.envers.EntityTrackingRevisionListener;
import org.hibernate.envers.RevisionType;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.model.CustomRevisionEntity;
import uk.co.flexi.ri.model.Media;

@Component
@Slf4j
public class InspectionRevisionListener implements EntityTrackingRevisionListener {

    private static EntityManager entityManager;

    @PersistenceContext
    public void setEntityManager(EntityManager entityManager) {
        InspectionRevisionListener.entityManager = entityManager;
    }

    @Override
    public void entityChanged(Class entityClass, String entityName, Object entityId,
                              RevisionType revisionType, Object revisionEntity) {
        ((CustomRevisionEntity) revisionEntity).setUniqueId(getUniqueId(entityClass, entityId));
    }

    private String getUniqueId(Class<?> entityClass, Object entityId) {
        Long id = (Long) entityId;
        try {
            if (entityClass.equals(Comment.class)) {
                return entityManager.createQuery(
                                "SELECT i.referenceId FROM Comment c " +
                                        "JOIN c.inspectionItem ii " +
                                        "JOIN ii.inspection i " +
                                        "WHERE c.id = :id", String.class)
                        .setParameter("id", id)
                        .getResultStream()
                        .findFirst()
                        .orElse(null);
            } else if (entityClass.equals(Inspection.class)) {
                return entityManager.createQuery(
                                "SELECT i.referenceId FROM inspection i " +
                                        "WHERE i.id = :id", String.class)
                        .setParameter("id", id)
                        .getResultStream()
                        .findFirst()
                        .orElse(null);
            } else if (entityClass.equals(InspectionItem.class)) {
                return entityManager.createQuery(
                                "SELECT i.referenceId FROM InspectionItem ii " +
                                        "JOIN ii.inspection i " +
                                        "WHERE ii.id = :id", String.class)
                        .setParameter("id", id)
                        .getResultStream()
                        .findFirst()
                        .orElse(null);
            } else if (entityClass.equals(Media.class)) {
                return entityManager.createQuery(
                                "SELECT i.referenceId FROM Media m " +
                                        "JOIN m.inspectionItem ii " +
                                        "JOIN ii.inspection i " +
                                        "WHERE m.id = :id", String.class)
                        .setParameter("id", id)
                        .getResultStream()
                        .findFirst()
                        .orElse(null);
            }
        } catch (Exception e) {
            log.warn("Exception while getting unique id");
        }
        return null;
    }

    @Override
    public void newRevision(Object o) {
      //No need this implementation
    }
}
