package uk.co.flexi.ri.listener;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import uk.co.flexi.ri.dto.EventQueueDTO;
import uk.co.flexi.ri.dto.event.EventData;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.service.EventQueueService;
import uk.co.flexi.ri.service.MerchantProductPriceService;

import java.util.Map;

@Component
@AllArgsConstructor
@Async("eventTaskExecutor")
public class ApplicationEventListener {

    private final EventQueueService eventQueueService;

    private final MerchantProductPriceService merchantProductPriceService;

    private final ObjectMapper objectMapper;

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void publishToEventQueue(EventData<?> eventData) {
        Map<String, Object> newPayload = objectMapper.convertValue(eventData.getPayload(), new TypeReference<>() {
        });

        EventQueueDTO queueDTO = EventQueueDTO.builder()
                .eventType(eventData.getEventType())
                .uniqueId(eventData.getUniqueId())
                .referenceType(eventData.getReferenceType())
                .performedByUser(eventData.getPerformedByUser())
                .performedByGroup(eventData.getPerformedByGroup())
                .tenant(eventData.getTenant())
                .payload(newPayload)
                .build();

        eventQueueService.save(queueDTO);
    }

    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void saveToMerchantPrice(Inspection inspection) {
        merchantProductPriceService.saveToMerchantProductPrice(inspection);
    }
}
