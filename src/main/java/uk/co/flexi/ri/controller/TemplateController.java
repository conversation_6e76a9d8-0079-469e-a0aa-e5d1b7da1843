package uk.co.flexi.ri.controller;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.TemplateDTO;
import uk.co.flexi.ri.service.TemplateService;

import java.util.List;


@RestController
@RequestMapping(value = "/api/v1/templates")
@AllArgsConstructor
public class TemplateController {

    private final TemplateService templateService;

    @GetMapping
    public ResponseEntity<List<TemplateDTO>> findAll() {
        return ResponseEntity.ok(templateService.getTemplates());
    }

}
