package uk.co.flexi.ri.controller;

import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.MediaDTO;
import uk.co.flexi.ri.service.MediaService;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping(value = "/api/v1/media")
public class MediaController {

    private final MediaService mediaService;

    public MediaController(MediaService mediaService){
        this.mediaService = mediaService;
    }

    @PostMapping("/upload")
    public ResponseEntity<List<MediaDTO>> uploadFile(@RequestParam("file") List<MultipartFile> files,
                                        @RequestParam("inspectionItemId") String inspectionItemId,
                                        @RequestParam("commentId") String commentId) throws IOException {
        MediaDTO mediaDTO = new MediaDTO();
        mediaDTO.setCommentId(commentId);
        mediaDTO.setInspectionItemId(inspectionItemId);
        return ResponseEntity.ok(mediaService.uploadFile(files, mediaDTO));
    }

    @GetMapping("/download")
    public ResponseEntity<InputStreamResource> findById(@RequestParam("fileName") String fileName,
                                                        @RequestParam("mediaType") String mediaType) {
        return mediaService.downloadFile(fileName, mediaType);
    }

    @PostMapping("/temp-upload")
    public void uploadTempFile(@RequestHeader("X-token") String token,
                               @RequestParam("file") List<MultipartFile> files) {
        mediaService.uploadTempFile(files, token);
    }
}
