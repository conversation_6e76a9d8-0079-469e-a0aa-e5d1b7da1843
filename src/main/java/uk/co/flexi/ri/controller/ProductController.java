package uk.co.flexi.ri.controller;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.ProductInstructionDTO;
import uk.co.flexi.ri.dto.ProductInstructionUpdateDTO;
import uk.co.flexi.ri.service.ProductService;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping(value = "/api/v1/products")
@PreAuthorize("hasAuthority('RI_INSP_EDIT_INSTRUCTIONS')")
@AllArgsConstructor
public class ProductController {

    private final ProductService productService;

    @PostMapping("/instruction")
    public ResponseEntity<ProductInstructionDTO> createProductInstruction(@Valid @RequestBody ProductInstructionDTO instructionDTO) {
        return ResponseEntity.ok(productService.createProductInstruction(instructionDTO));
    }

    @GetMapping("/instruction")
    public ResponseEntity<List<ProductInstructionDTO>> findAllProductInstruction() {
        return ResponseEntity.ok(productService.findAllProductInstruction());
    }

    @GetMapping("/instruction/{id}")
    public ResponseEntity<ProductInstructionDTO> findByIdAndLanguage(@PathVariable(value = "id") String productInstructionId,
                                                                     @RequestParam(defaultValue = "en") String languageCode) {
        return ResponseEntity.ok(productService.findByIdAndLanguage(productInstructionId, languageCode));
    }

    @PutMapping("/instruction/{id}/update")
    public ResponseEntity<ProductInstructionDTO> updateProductInstruction(@PathVariable(value = "id") String productInstructionId,
                                                                          @Valid @RequestBody List<ProductInstructionUpdateDTO> instructionDTOs) {
        return ResponseEntity.ok(productService.updateProductInstruction(productInstructionId, instructionDTOs));
    }

    @DeleteMapping("/instruction/{id}")
    public void deleteById(@PathVariable(value = "id") String productInstructionId) {
        productService.deleteById(productInstructionId);
    }

    @DeleteMapping("/instruction/bulk-delete")
    public void deleteByIds(@RequestParam List<String> instructionIds) {
        productService.deleteByIds(instructionIds);
    }

    @PostMapping("/instruction/import")
    public ResponseEntity<List<ProductInstructionDTO>> importProductInstruction(@RequestParam String format,
                                                                                @RequestParam("file") MultipartFile file) throws IOException {
        return ResponseEntity.ok(productService.importProductInstruction(format, file));
    }

    @GetMapping("/instruction/export")
    public ResponseEntity<byte[]> exportProductInstruction(@RequestParam String extension,
                                                           @RequestParam(required = false) List<String> ids) {
        String fileExtension = extension.toLowerCase();
        byte[] fileBytes = productService.exportProductInstruction(fileExtension, ids);

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=product_instruction." + fileExtension)
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(fileBytes);
    }
}
