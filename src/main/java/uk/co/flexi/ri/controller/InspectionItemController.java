package uk.co.flexi.ri.controller;


import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import uk.co.flexi.ri.dto.InspectionItemDTO;
import uk.co.flexi.ri.dto.InspectionItemUpdateReqDTO;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.InstructionDTO;
import uk.co.flexi.ri.dto.ItemStatusUpdateReqDTO;
import uk.co.flexi.ri.dto.TempMediaDTO;
import uk.co.flexi.ri.service.InspectionItemService;

import java.io.IOException;
import java.util.List;


@RestController
@RequestMapping(value = "/api/v1/inspection-items")
public class InspectionItemController {

    private final InspectionItemService inspectionItemService;

    public InspectionItemController(InspectionItemService inspectionItemService){
        this.inspectionItemService = inspectionItemService;
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('RI_INSP_SLCT_RETN_RSN')")
    public ResponseEntity<InspectionItemDTO> update(@PathVariable(value = "id") String inspectionItemId,
                                                    @RequestBody InspectionItemUpdateReqDTO updateReqDTO) throws IOException {
        return ResponseEntity.ok(inspectionItemService.update(inspectionItemId, updateReqDTO));
    }

    @PutMapping("/{id}/update-status")
    @PreAuthorize("hasAuthority('RI_INSP_APRV') and hasAuthority('RI_INSP_RJCT')")
    public ResponseEntity<InspectionItemDTO> updateStatus(@PathVariable(value = "id") String inspectionItemId,
                                                          @RequestBody ItemStatusUpdateReqDTO updateReqDTO) {
        return ResponseEntity.ok(inspectionItemService.updateStatus(inspectionItemId, updateReqDTO));
    }

    @GetMapping("/return-reasons")
    public ResponseEntity<List<String>> getReturnReasons() {
        return ResponseEntity.ok(inspectionItemService.getReturnReasons());
    }

    @GetMapping("/{id}/instruction")
    @PreAuthorize("hasAuthority('RI_INSP_QI_INSTRUCTIONS')")
    public ResponseEntity<InstructionDTO> findByProductInstruction(@PathVariable(value = "id") String inspectionItemId,
                                                                   @RequestParam(defaultValue = "en") String languageCode) {
        return ResponseEntity.ok(inspectionItemService.findByProductInstruction(inspectionItemId, languageCode));
    }

    @GetMapping("/{id}/temp-image")
    public ResponseEntity<List<TempMediaDTO>> getTempImages(@PathVariable(value = "id") String inspectionItemId) {
        return ResponseEntity.ok(inspectionItemService.getTempImages(inspectionItemId));
    }

}
