package uk.co.flexi.ri.controller;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import uk.co.flexi.ri.dto.NotificationChannelDTO;
import uk.co.flexi.ri.model.NotificationChannel;
import uk.co.flexi.ri.service.NotificationChannelService;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api/v1/notification-channel")
@AllArgsConstructor
public class NotificationChannelController {

    private final NotificationChannelService notificationChannelService;

    @GetMapping("/{id}")
    public ResponseEntity<NotificationChannelDTO> getNotificationChannel(@PathVariable Long id) {
        return ResponseEntity.ok(notificationChannelService.getNotificationChannelById(id));
    }

    @GetMapping
    public ResponseEntity<List<NotificationChannelDTO>> getChannelConfig() {
        return ResponseEntity.ok(notificationChannelService.getAllNotificationChannels());
    }

    @GetMapping("/channel-list")
    public ResponseEntity<List<NotificationChannel.Channel>> getChannels() {
        return ResponseEntity.ok(notificationChannelService.getChannelList());
    }

    @GetMapping("/config-key")
    public ResponseEntity<Map<String, Object>> getChannelConfigKeys(@RequestParam NotificationChannel.Channel channel) {
        return ResponseEntity.ok(notificationChannelService.getChannelConfigKeys(channel));
    }

    @PostMapping("/create")
    public ResponseEntity<NotificationChannelDTO> createNotificationChannel(@RequestBody NotificationChannelDTO notificationChannelDTO) {
        return ResponseEntity.ok(notificationChannelService.createNotificationChannel(notificationChannelDTO));
    }

    @PutMapping("/{id}")
    public ResponseEntity<NotificationChannelDTO> updateNotificationChannel(@PathVariable Long id, @RequestBody NotificationChannelDTO notificationChannelDTO) {
        return ResponseEntity.ok(notificationChannelService.updateNotificationChannel(id, notificationChannelDTO));
    }

    @DeleteMapping("/{id}")
    public void deleteNotificationChannel(@PathVariable Long id) {
        notificationChannelService.deleteNotificationChannel(id);
    }
}
