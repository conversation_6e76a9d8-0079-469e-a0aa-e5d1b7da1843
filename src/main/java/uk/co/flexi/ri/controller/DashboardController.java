package uk.co.flexi.ri.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.InspectionCompletedDTO;
import uk.co.flexi.ri.dto.ItemConditionResponseDTO;
import uk.co.flexi.ri.dto.TreeMapResponseDTO;
import uk.co.flexi.ri.service.DashboardService;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/api/v1/dashboard")
@AllArgsConstructor
@PreAuthorize("hasAuthority('RI_INSP_ACCESS_DSHBRD_PAGE')")
public class DashboardController {

    private final DashboardService dashboardService;

    @GetMapping("/return-reasons")
    public ResponseEntity<TreeMapResponseDTO> getCommonReturnReasons(@RequestParam LocalDate startDate,
                                                                     @RequestParam LocalDate endDate,
                                                                     @RequestParam(defaultValue = "0") Integer level,
                                                                     @RequestParam(required = false) String filterValue
    ) {
        return ResponseEntity.ok(dashboardService.getCommonReturnReasons(startDate, endDate, filterValue,
                level));
    }

    @GetMapping("/item-condition")
    public ResponseEntity<List<ItemConditionResponseDTO>> getCommonItemCondition(@RequestParam LocalDate startDate,
                                                                                 @RequestParam LocalDate endDate) {
        return ResponseEntity.ok(dashboardService.getCommonItemCondition(startDate, endDate));
    }

    @GetMapping("/item-condition/grouped")
    public ResponseEntity<Map<String, Map<String, Map<String, Map<String, Object>>>>> getItemConditionGroupedByChannelAndCategory(@RequestParam LocalDate startDate,
                                                                                                                   @RequestParam LocalDate endDate,
                                                                                                                   @RequestParam String groupBy) throws JsonProcessingException {
        return ResponseEntity.ok(dashboardService.getItemConditionGroupedByChannelAndCategory(startDate, endDate, groupBy));
    }

    @GetMapping("/inspection-status")
    public ResponseEntity<Map<String, Map<String, Object>>> getInspectionStatus(@RequestParam LocalDate startDate,
                                                                 @RequestParam LocalDate endDate) {
        return ResponseEntity.ok(dashboardService.getInspectionStatusWithPercentage(startDate, endDate));
    }

    @GetMapping("/top-products")
    public ResponseEntity<TreeMapResponseDTO> getTopProductsReturns(@RequestParam LocalDate startDate,
                                                                    @RequestParam LocalDate endDate,
                                                                    @RequestParam String groupBy,
                                                                    @RequestParam(defaultValue = "0") Integer level,
                                                                    @RequestParam(required = false) String styleFilter,
                                                                    @RequestParam(required = false) String skuFilter
    ) {
        return ResponseEntity.ok(dashboardService.getTopProductsReturns(startDate, endDate, groupBy, styleFilter,
                skuFilter, level));
    }

    @GetMapping("/time-tracker")
    public ResponseEntity<Map<String, Object>> getInspectionTimeTracker(@RequestParam LocalDate startDate,
                                                                        @RequestParam LocalDate endDate) {
        return ResponseEntity.ok(dashboardService.getInspectionTimeTracker(startDate, endDate));
    }

    @GetMapping("/inspection-completed")
    public ResponseEntity<InspectionCompletedDTO> getInspectionCompletedStatus(@RequestParam LocalDate startDate,
                                                                               @RequestParam LocalDate endDate,
                                                                               @RequestParam String granularity) {
        return ResponseEntity.ok(dashboardService.getInspectionCompletedStatus(startDate, endDate, granularity));
    }
}
