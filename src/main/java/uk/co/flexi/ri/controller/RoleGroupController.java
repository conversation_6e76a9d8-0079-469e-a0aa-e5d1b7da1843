package uk.co.flexi.ri.controller;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.RoleDTO;
import uk.co.flexi.ri.dto.RoleGroupDTO;
import uk.co.flexi.ri.service.RoleGroupService;

import java.util.List;

@RestController
@RequestMapping(value = "/api/v1/role-groups")
@AllArgsConstructor
@PreAuthorize("hasAuthority('RI_INSP_CREATE_USER_ROLE_GRP')")
public class RoleGroupController {

    private final RoleGroupService roleGroupService;

    @GetMapping("/roles")
    public ResponseEntity<List<RoleDTO>> getRoles() {
        return ResponseEntity.ok(roleGroupService.getRoles());
    }

    @GetMapping
    public ResponseEntity<List<RoleGroupDTO>> getRoleGroups() {
        return ResponseEntity.ok(roleGroupService.getRoleGroups());
    }

    @GetMapping("/{id}")
    public ResponseEntity<RoleGroupDTO> findById(@PathVariable(value = "id") String roleGroupId) {
        return ResponseEntity.ok(roleGroupService.findRoleGroupDTOById(roleGroupId));
    }

    @PostMapping
    public ResponseEntity<RoleGroupDTO> createRoleGroup(@RequestBody RoleGroupDTO roleGroupDTO) {
        return ResponseEntity.ok(roleGroupService.createRoleGroup(roleGroupDTO));
    }

    @PutMapping("/{id}/update")
    public ResponseEntity<RoleGroupDTO> updateRoleGroup(@PathVariable(value = "id") String roleGroupId,
                                                        @RequestBody RoleGroupDTO roleGroupDTO) {
        return ResponseEntity.ok(roleGroupService.updateRoleGroup(roleGroupId, roleGroupDTO));
    }

    @DeleteMapping("/{id}/delete")
    public void deleteRoleGroups(@PathVariable(value = "id") String roleGroupId) {
        roleGroupService.deleteRoleGroups(roleGroupId);
    }
}
