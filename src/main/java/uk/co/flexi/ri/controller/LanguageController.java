package uk.co.flexi.ri.controller;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.SupportedLanguageDTO;
import uk.co.flexi.ri.service.LanguageService;

import java.util.List;

@RestController
@RequestMapping(value = "/api/v1/supported-languages")
@AllArgsConstructor
public class LanguageController {

    private final LanguageService languageService;

    @GetMapping
    public ResponseEntity<List<SupportedLanguageDTO>> findByConditionType() {
        return ResponseEntity.ok(languageService.findAllSupportedLanguages());
    }
}
