package uk.co.flexi.ri.controller;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import uk.co.flexi.ri.dto.NotificationConfigDTO;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.service.NotificationConfigService;

import java.util.List;

@RestController
@RequestMapping(value = "/api/v1/notification-config")
@AllArgsConstructor
public class NotificationConfigController {

    private final NotificationConfigService notificationConfigService;

    @GetMapping("/list")
    public ResponseEntity<List<NotificationConfigDTO>> getAllNotificationConfigs(){
        return ResponseEntity.ok(notificationConfigService.getAllNotificationConfigs());
    }

    @PostMapping("/config")
    public ResponseEntity<NotificationConfigDTO> create(@RequestBody NotificationConfigDTO notificationConfigDTO) throws IllegalAccessException {
        return ResponseEntity.ok(notificationConfigService.save(notificationConfigDTO));
    }

    @GetMapping("/{id}")
    public ResponseEntity<NotificationConfigDTO> findById(@PathVariable(value = "id") Long id) {
        return ResponseEntity.ok(notificationConfigService.findById(id));
    }

    @PutMapping("/{id}")
    public ResponseEntity<NotificationConfigDTO> updateNotificationConfig(@PathVariable(value = "id") Long id,@RequestBody NotificationConfigDTO updateReqDTO) {
        return ResponseEntity.ok(notificationConfigService.updateConfig(id, updateReqDTO));
    }

    @DeleteMapping("/{id}")
    public void deleteConfig(@PathVariable(value = "id") Long id) {
        notificationConfigService.deleteById(id);
    }

    @GetMapping("/types")
    public ResponseEntity<List<NotificationConfig.NotificationType>> getNotificationTypes() {
        return ResponseEntity.ok(notificationConfigService.getNotificationTypes());
    }

    @GetMapping("/frequencies")
    public ResponseEntity<List<NotificationConfigDTO.Frequency>> getFrequencies() {
        return ResponseEntity.ok(notificationConfigService.getFrequencies());
    }
}
