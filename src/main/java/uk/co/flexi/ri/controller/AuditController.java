package uk.co.flexi.ri.controller;

import lombok.AllArgsConstructor;
import org.hibernate.envers.RevisionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.HistoryResDTO;
import uk.co.flexi.ri.service.AuditService;

import java.time.OffsetDateTime;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping(value = "/api/v1/audit-logs")
@PreAuthorize("hasAuthority('RI_INSP_AUDIT')")
public class AuditController {

    private final AuditService auditService;

    @GetMapping("/categories")
    public ResponseEntity<List<String>> categories() {
        return ResponseEntity.ok(auditService.getCategories());
    }

    @GetMapping
    public ResponseEntity<Page<HistoryResDTO>> history(@RequestParam AuditService.AuditCategory category,
                                                             @RequestParam(required = false) String uniqueId,
                                                             @RequestParam(required = false) RevisionType revisionType,
                                                             @RequestParam(required = false) String user,
                                                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime startDate,
                                                             @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) OffsetDateTime endDate,
                                                             @PageableDefault(size = 10) Pageable pageable) {
        return ResponseEntity.ok(auditService.getAuditLogs(category, uniqueId, revisionType, user, startDate, endDate, pageable));
    }
}
