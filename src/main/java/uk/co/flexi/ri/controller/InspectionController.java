package uk.co.flexi.ri.controller;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import uk.co.flexi.ri.dto.AssignedPackagesDTO;
import uk.co.flexi.ri.dto.AssignedPackagesFilterDTO;
import uk.co.flexi.ri.dto.InspectionDTO;
import uk.co.flexi.ri.dto.InspectionItemConditionDTO;
import uk.co.flexi.ri.dto.InspectionQtyUpdateReqDTO;
import uk.co.flexi.ri.dto.InspectionStatusUpdateReqDTO;
import uk.co.flexi.ri.dto.UserGroupDTO;
import uk.co.flexi.ri.model.InspectionItemCondition;
import uk.co.flexi.ri.service.InspectionService;
import uk.co.flexi.ri.service.UserGroupService;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(value = "/api/v1/inspections")
@AllArgsConstructor
public class InspectionController {

    private final InspectionService inspectionService;

    private final UserGroupService userGroupService;

    @PostMapping
    @PreAuthorize("hasAuthority('RI_INSP_CREATE_INSP')")
    public ResponseEntity<InspectionDTO> create(@Valid @RequestBody InspectionDTO inspectionDTO) {
        return ResponseEntity.ok(inspectionService.save(inspectionDTO));
    }

    @GetMapping("/{id}")
    public ResponseEntity<InspectionDTO> findById(@PathVariable(value = "id") String inspectionId) {
        return ResponseEntity.ok(inspectionService.findById(inspectionId));
    }

    @GetMapping("/reference/{id}")
    public ResponseEntity<List<InspectionDTO>> findByReferenceIdId(@PathVariable(value = "id") String referenceId) {
        return ResponseEntity.ok(inspectionService.findByReferenceIdId(referenceId));
    }

    @PutMapping("/{id}/update-qty")
    @PreAuthorize("hasAuthority('RI_INSP_ODP_EDIT')")
    public ResponseEntity<InspectionDTO> updateInspectionQty(@PathVariable(value = "id") String inspectionId,
                                                             @RequestBody List<InspectionQtyUpdateReqDTO> reqDTOList) {


        return ResponseEntity.ok(inspectionService.updateInspectionQty(inspectionId, reqDTOList));
    }

    @PutMapping("/{id}/update-qty/check")
    @PreAuthorize("hasAuthority('RI_INSP_ODP_EDIT')")
    public ResponseEntity<Map<String, Object>> updateInspectionQtyCheck(@PathVariable(value = "id") String inspectionId,
                                                                        @RequestBody List<InspectionQtyUpdateReqDTO> reqDTOList) {


        return ResponseEntity.ok(inspectionService.updateInspectionQtyCheck(inspectionId, reqDTOList));
    }

    @PutMapping("/{id}/update-status")
    @PreAuthorize("hasAuthority('RI_INSP_SND_REVIEW')")
    public ResponseEntity<InspectionDTO> updateStatus(@PathVariable(value = "id") String inspectionId,
                                                      @Valid @RequestBody InspectionStatusUpdateReqDTO updateReqDTO) {
        return ResponseEntity.ok(inspectionService.updateStatus(inspectionId, updateReqDTO));
    }

    /**
     * @deprecated Testing purpose(Need to remove before production release
     */
    @DeleteMapping("/{referenceID}")
    @Deprecated(since = "")
    public ResponseEntity<String> deleteInspection(@PathVariable(value = "referenceID") String referenceId) {
        inspectionService.deleteInspection(referenceId);
        return ResponseEntity.ok("Deleted Inspections with reference ID: " + referenceId);
    }

    /**
     * @deprecated Testing purpose(Need to remove before production release
     */
    @DeleteMapping("/delete-all")
    @Deprecated(since = "")
    public ResponseEntity<String> deleteAllInspection(@RequestParam String merchantName) {
        inspectionService.deleteAllInspection(merchantName);
        return ResponseEntity.ok("Deleted All Inspections");
    }

    @GetMapping("/assigned-packages")
    @PreAuthorize("hasAuthority('RI_INSP_ACCESS_INSP_PAGE')")
    public ResponseEntity<Page<AssignedPackagesDTO>> getAssignedPackages(AssignedPackagesFilterDTO filterDTO,
                                                                         @PageableDefault(size = 10) Pageable pageable) {
        return ResponseEntity.ok(inspectionService.getAllAssignedPackages(filterDTO, pageable));
    }

    @GetMapping("/{inspectionId}/reviewers")
    public ResponseEntity<List<UserGroupDTO>> getAllReviewers(@PathVariable(value = "inspectionId") String inspectionId) {
        return ResponseEntity.ok(userGroupService.getUserGroupReviewers());
    }

    @GetMapping("/{inspectionId}/item-conditions")
    public ResponseEntity<List<InspectionItemConditionDTO>> getInspectionItemCondition(@PathVariable(value = "inspectionId") String inspectionId,
                                                                                       @RequestParam InspectionItemCondition.ConditionType conditionType) {
        return ResponseEntity.ok(inspectionService.getInspectionItemCondition(conditionType));
    }
}
