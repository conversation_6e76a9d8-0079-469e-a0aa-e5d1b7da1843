package uk.co.flexi.ri.controller;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.ChannelDTO;
import uk.co.flexi.ri.service.ChannelService;

import java.util.List;


@RestController
@RequestMapping(value = "/api/v1/channels")
@AllArgsConstructor
@PreAuthorize("hasAuthority('RI_INSP_SLA')")
public class ChannelController {

    private final ChannelService channelService;

    @PostMapping
    public ResponseEntity<ChannelDTO> create(@Valid @RequestBody ChannelDTO channelDTO) {
        return ResponseEntity.ok(channelService.save(channelDTO));
    }

    @PutMapping("/{id}/update")
    public ResponseEntity<ChannelDTO> update(@PathVariable(value = "id") String channelId,
                                             @Valid @RequestBody ChannelDTO channelDTO) {
        return ResponseEntity.ok(channelService.update(channelId, channelDTO));
    }

    @GetMapping
    public ResponseEntity<List<ChannelDTO>> findAll() {
        return ResponseEntity.ok(channelService.findAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<ChannelDTO> findById(@PathVariable(value = "id") String channelId) {
        return ResponseEntity.ok(channelService.findById(channelId));
    }

    @DeleteMapping("/{id}/delete")
    public void delete(@PathVariable(value = "id") String channelId) {
        channelService.delete(channelId);
    }
}
