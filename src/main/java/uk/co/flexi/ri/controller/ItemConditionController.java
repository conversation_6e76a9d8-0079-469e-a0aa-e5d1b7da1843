package uk.co.flexi.ri.controller;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.InspectionItemConditionDTO;
import uk.co.flexi.ri.model.InspectionItemCondition;
import uk.co.flexi.ri.service.ItemConditionService;

import java.util.List;


@RestController
@RequestMapping(value = "/api/v1/item-conditions")
@AllArgsConstructor
@PreAuthorize("hasAuthority('RI_INSP_ITEM_CONDN')")
public class ItemConditionController {

    private final ItemConditionService itemConditionService;

    @PostMapping
    public ResponseEntity<InspectionItemConditionDTO> create(@Valid @RequestBody InspectionItemConditionDTO itemConditionDTO) {
        return ResponseEntity.ok(itemConditionService.save(itemConditionDTO));
    }

    @PutMapping("/{id}/update")
    public ResponseEntity<InspectionItemConditionDTO> update(@PathVariable(value = "id") String itemConditionId,
                                                   @Valid @RequestBody InspectionItemConditionDTO itemConditionDTO) {
        return ResponseEntity.ok(itemConditionService.update(itemConditionId, itemConditionDTO));
    }

    @GetMapping
    public ResponseEntity<List<InspectionItemConditionDTO>> findByConditionType(@RequestParam InspectionItemCondition.ConditionType conditionType) {
        return ResponseEntity.ok(itemConditionService.findByConditionType(conditionType));
    }

    @GetMapping("/{id}")
    public ResponseEntity<InspectionItemConditionDTO> findById(@PathVariable(value = "id") String itemConditionId) {
        return ResponseEntity.ok(itemConditionService.findById(itemConditionId));
    }

    @DeleteMapping("/{id}/delete")
    public void delete(@PathVariable(value = "id") String itemConditionId) {
        itemConditionService.delete(itemConditionId);
    }
}
