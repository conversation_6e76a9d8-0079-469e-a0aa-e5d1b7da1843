package uk.co.flexi.ri.controller;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.UserGroupDTO;
import uk.co.flexi.ri.service.UserGroupService;

import java.util.List;


@RestController
@RequestMapping(value = "/api/v1/user-groups")
@PreAuthorize("hasAuthority('RI_INSP_USER_GRP')")
@AllArgsConstructor
public class UserGroupController {

    private final UserGroupService userGroupService;

    @PostMapping
    public ResponseEntity<UserGroupDTO> create(@Valid @RequestBody UserGroupDTO userGroupDTO) {
        return ResponseEntity.ok(userGroupService.save(userGroupDTO));
    }

    @PutMapping("/{id}/update")
    public ResponseEntity<UserGroupDTO> update(@PathVariable(value = "id") String userGroupId,
                                               @Valid @RequestBody UserGroupDTO userGroupDTO) {
        return ResponseEntity.ok(userGroupService.update(userGroupId, userGroupDTO));
    }

    @GetMapping("/{id}")
    public ResponseEntity<UserGroupDTO> findByUserGroupId(@PathVariable(value = "id") String userGroupId) {
        return ResponseEntity.ok(userGroupService.findUserGroupDTOById(userGroupId));
    }

    @PutMapping("/{id}/activate")
    public ResponseEntity<UserGroupDTO> activateUserGroup(@PathVariable(value = "id") String userGroupId) {
        return ResponseEntity.ok(userGroupService.activateUserGroup(userGroupId));
    }

    @PutMapping("/{id}/deactivate")
    public ResponseEntity<UserGroupDTO> deactivateUserGroup(@PathVariable(value = "id") String userGroupId) {
        return ResponseEntity.ok(userGroupService.deactivateUserGroup(userGroupId));
    }

    @GetMapping
    public ResponseEntity<List<UserGroupDTO>> findAll(@RequestParam(defaultValue = "false") boolean includeInactive) {
        return ResponseEntity.ok(userGroupService.findAll(includeInactive));
    }

    @GetMapping("/all")
    public ResponseEntity<List<UserGroupDTO>> findAllGroups() {
        return ResponseEntity.ok(userGroupService.findAllGroups());
    }

    @GetMapping("/list")
    public ResponseEntity<List<UserGroupDTO>> getAllReviewers() {
        return ResponseEntity.ok(userGroupService.getAllReviewers());
    }

    @GetMapping("/{id}/reviewers")
    public ResponseEntity<UserGroupDTO> getByUserGroupId(@PathVariable(value = "id") String userGroupId) {
        return ResponseEntity.ok(userGroupService.getByUserGroupId(userGroupId));
    }

    @PostMapping("/{id}/add-reviewers")
    public ResponseEntity<UserGroupDTO> addReviewers(@PathVariable(value = "id") String userGroupId,
                                                     @RequestBody List<String> reviewers) {
        return ResponseEntity.ok(userGroupService.addReviewers(userGroupId, reviewers));
    }

    @PutMapping("/{id}/update-reviewers")
    public ResponseEntity<UserGroupDTO> updateReviewers(@PathVariable(value = "id") String userGroupId,
                                                        @RequestBody List<String> reviewers) {
        return ResponseEntity.ok(userGroupService.updateReviewers(userGroupId, reviewers));
    }

    @DeleteMapping("/{id}/reviewers")
    public void deleteReviewers(@PathVariable(value = "id") String userGroupId) {
        userGroupService.deleteReviewers(userGroupId);
    }
}
