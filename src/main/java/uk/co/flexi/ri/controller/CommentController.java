package uk.co.flexi.ri.controller;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ModelAttribute;

import uk.co.flexi.ri.dto.CommentDTO;
import uk.co.flexi.ri.dto.CommentMediaDTO;
import uk.co.flexi.ri.dto.CommentMediaUpdateDTO;
import uk.co.flexi.ri.service.CommentService;

import java.io.IOException;

@RestController
@RequestMapping(value = "/api/v1/comments")
public class CommentController {

    private final CommentService commentService;

    public CommentController(CommentService commentService) {
        this.commentService = commentService;
    }

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAnyAuthority('RI_INSP_WRITE_CMNT','RI_INSP_WRITE_CMNT_EXT') and hasAuthority('RI_INSP_MEDIA_UPLD')")
    public ResponseEntity<CommentDTO> createComment(@ModelAttribute CommentMediaDTO commentMediaDTO) throws IOException {

        return ResponseEntity.ok(commentService.save(commentMediaDTO));
    }

    @PutMapping(value = "/{commentId}/update", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasAnyAuthority('RI_INSP_WRITE_CMNT','RI_INSP_WRITE_CMNT_EXT') and hasAuthority('RI_INSP_MEDIA_UPLD')")
    public ResponseEntity<CommentDTO> updateComment(@PathVariable(value = "commentId") String commentId,
                                                    @ModelAttribute CommentMediaUpdateDTO commentMediaUpdateDTO) throws IOException {
        return ResponseEntity.ok(commentService.update(commentId, commentMediaUpdateDTO));
    }

    @DeleteMapping
    @PreAuthorize("hasAnyAuthority('RI_INSP_WRITE_CMNT','RI_INSP_WRITE_CMNT_EXT') and hasAuthority('RI_INSP_MEDIA_UPLD')")
    public ResponseEntity<String> deleteComments(@RequestParam String commentID) {
        commentService.deleteComment(commentID);
        return ResponseEntity.ok("Comment Deleted Cheers!! \uD83D\uDC4D ");
    }

}
