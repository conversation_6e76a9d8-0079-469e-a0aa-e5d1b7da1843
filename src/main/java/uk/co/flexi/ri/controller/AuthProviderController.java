package uk.co.flexi.ri.controller;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.AuthProviderConfigDTO;
import uk.co.flexi.ri.model.AuthProvider;
import uk.co.flexi.ri.service.AuthProviderService;

import java.io.IOException;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping(value = "/api/v1/auth-providers")
@PreAuthorize("hasAuthority('RI_INSP_SSO_CONFIG')")
public class AuthProviderController {

    private final AuthProviderService authProviderService;

    @GetMapping("/sso/names")
    public ResponseEntity<List<String>> getAuthProviders() {
        return ResponseEntity.ok(authProviderService.getAuthProviders());
    }

    @PostMapping("/{provider}/sso-config")
    public ResponseEntity<AuthProviderConfigDTO> addSSOConfig(@PathVariable(value = "provider") AuthProvider.Provider provider,
                                                              @RequestBody JsonNode reqDTO) throws IOException {
        return ResponseEntity.ok(authProviderService.addSSOConfig(provider, reqDTO));
    }

    @GetMapping("/{provider}/sso-config")
    public ResponseEntity<AuthProviderConfigDTO> getSSOConfig(@PathVariable(value = "provider") AuthProvider.Provider provider) {
        return ResponseEntity.ok(authProviderService.getSSOConfig(provider));
    }
}
