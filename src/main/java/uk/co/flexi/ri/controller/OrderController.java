package uk.co.flexi.ri.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.OrderDetailDTO;
import uk.co.flexi.ri.service.OrderService;

@RestController
@RequestMapping(value = "/api/v1/orders")
public class OrderController {

    private final OrderService orderService;

    public OrderController(OrderService orderService){
        this.orderService = orderService;
    }

    @GetMapping("/{input}")
    @PreAuthorize("hasAuthority('RI_INSP_PCKG_SCN')")
    public ResponseEntity<OrderDetailDTO> getOrderDetails(@PathVariable(value = "input") String input,
                                                          @RequestParam(required = false) String inspectionId) {
        return ResponseEntity.ok(orderService.getOrderDetails(input.trim(), inspectionId));
    }

}
