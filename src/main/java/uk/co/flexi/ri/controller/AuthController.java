package uk.co.flexi.ri.controller;

import org.springframework.web.bind.annotation.PutMapping;
import uk.co.flexi.ri.dto.AuthReqDTO;
import uk.co.flexi.ri.dto.AuthResDTO;
import uk.co.flexi.ri.dto.ImageTokenReqDTO;
import uk.co.flexi.ri.dto.RefreshTokenReqDTO;
import uk.co.flexi.ri.dto.UpdateTokenReqDTO;
import uk.co.flexi.ri.exception.custom.RIAuthenticationException;
import uk.co.flexi.ri.service.AuthService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/v1/auth")
public class AuthController {

    private final AuthService authService;

    public AuthController(AuthService authService) {
        this.authService = authService;
    }

    @PostMapping("/login")
    public ResponseEntity<AuthResDTO> authenticate(@RequestBody AuthReqDTO authReqDTO) throws RIAuthenticationException {
        return ResponseEntity.ok(authService.authenticate(authReqDTO));
    }

    @PostMapping("/refresh-token")
    public ResponseEntity<AuthResDTO> getRefreshToken(@RequestBody RefreshTokenReqDTO refreshTokenReqDTO) throws RIAuthenticationException {
        return ResponseEntity.ok(authService.generateRefreshToken(refreshTokenReqDTO));
    }

    @PutMapping("/update-token")
    public ResponseEntity<AuthResDTO> getRefreshToken(@RequestBody UpdateTokenReqDTO updateTokenReqDTO) throws RIAuthenticationException {
        return ResponseEntity.ok(authService.regenerateToken(updateTokenReqDTO));
    }

    @PostMapping("/image-token")
    public ResponseEntity<String> generateImageToken(@RequestBody ImageTokenReqDTO imageTokenReqDTO) throws RIAuthenticationException {
        return ResponseEntity.ok(authService.generateImageToken(imageTokenReqDTO));
    }
}
