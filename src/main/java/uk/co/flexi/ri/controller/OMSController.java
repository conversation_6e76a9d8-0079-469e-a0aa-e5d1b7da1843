package uk.co.flexi.ri.controller;

import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.OMSConfigDTO;
import uk.co.flexi.ri.service.OMSConfigService;
import uk.co.flexi.ri.service.OMSProviderService;

import java.util.List;
import java.util.Map;

@RestController
@AllArgsConstructor
@PreAuthorize("hasAuthority('RI_INSP_OMS_CONFIG')")
@RequestMapping(value = "/api/v1/oms")
public class OMSController {

    private final OMSConfigService omsConfigService;

    private final OMSProviderService omsProviderService;

    private final ApplicationEventPublisher eventPublisher;

    @GetMapping("/list")
    public ResponseEntity<List<String>> getOMSList() {
        return ResponseEntity.ok(omsProviderService.getOMSList());
    }

    @PostMapping("/{name}/config")
    public ResponseEntity<OMSConfigDTO> addOMSConfig(@PathVariable(value = "name") String name,
                                                     @RequestBody OMSConfigDTO configReq) {
        OMSConfigDTO response = new OMSConfigDTO();
        response.setConfigs(omsConfigService.addOMSConfig(name, configReq.getConfigs()));
        response.setStrategies(omsProviderService.addSearchStrategies(name, configReq.getStrategies()));
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{name}/config")
    public ResponseEntity<Map<String, String>> getOMSConfig(@PathVariable(value = "name") String name) {
        return ResponseEntity.ok(omsConfigService.getOMSConfig(name));
    }

    @GetMapping("/{name}/search-strategies")
    public ResponseEntity<List<String>> getSearchStrategies(@PathVariable(value = "name") String name) {
        return ResponseEntity.ok(omsProviderService.getSearchStrategies(name));
    }

    @GetMapping("/{name}/all-search-strategies")
    public ResponseEntity<List<String>> getAllSearchStrategies(@PathVariable(value = "name") String name) {
        return ResponseEntity.ok(omsProviderService.getAllSearchStrategies(name));
    }

    @GetMapping("/{name}/test-connection")
    public ResponseEntity<Map<String, String>> testConnection(@PathVariable(value = "name") String name) {
        return ResponseEntity.ok(omsProviderService.testConnection(name));
    }
}
