package uk.co.flexi.ri.controller;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.MerchantDTO;
import uk.co.flexi.ri.service.MerchantService;


@RestController
@RequestMapping(value = "/api/v1/merchants")
@AllArgsConstructor
public class MerchantController {

    private final MerchantService merchantService;

    @GetMapping
    public ResponseEntity<MerchantDTO> getMerchantDetails() {
        return ResponseEntity.ok(merchantService.getMerchantDetails());
    }
}
