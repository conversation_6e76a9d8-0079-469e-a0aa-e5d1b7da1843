package uk.co.flexi.ri.controller;

import jakarta.validation.Valid;
import org.springframework.data.web.PageableDefault;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.MerchantDTO;
import uk.co.flexi.ri.dto.UserDTO;
import uk.co.flexi.ri.dto.UserDetailDTO;
import uk.co.flexi.ri.dto.UserLoginDTO;
import uk.co.flexi.ri.exception.custom.UserNotFoundException;
import uk.co.flexi.ri.service.UserService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;


@RestController
@RequestMapping(value = "/api/v1/users")
public class UserManagementController {

    private final UserService userService;

    public UserManagementController(UserService userService) {
        this.userService = userService;
    }

    @PostMapping
    @PreAuthorize("hasAuthority('RI_INSP_CREATE_USER')")
    public ResponseEntity<UserDTO> create(@RequestBody UserDTO userDTO) {
        return ResponseEntity.ok(userService.save(userDTO));
    }

    @PutMapping("/{id}/update")
    @PreAuthorize("hasAuthority('RI_INSP_CREATE_USER')")
    public ResponseEntity<UserDTO> update(@PathVariable(value = "id") String userId,
                                             @Valid @RequestBody UserDTO userDTO) {
        return ResponseEntity.ok(userService.update(userId, userDTO));
    }

    @GetMapping("/list")
    public ResponseEntity<Page<UserDTO>> findAll(@RequestParam(defaultValue = "false") boolean includeInactive,
                                                 @PageableDefault(size = 25) Pageable pageable) {
        return ResponseEntity.ok(userService.findAll(includeInactive, pageable));
    }

    @GetMapping("/user-name/search")
    public ResponseEntity<List<String>> searchUserName(@RequestParam(required = false) String userName) {
        return ResponseEntity.ok(userService.searchUserName(userName));
    }

    @GetMapping("/{id}")
    public ResponseEntity<UserDTO> findById(@PathVariable(value = "id") String userId) {
        return ResponseEntity.ok(userService.findByUserId(userId));
    }

    @GetMapping("/user-name/{userName}")
    public ResponseEntity<UserDTO> findByUserName(@PathVariable(value = "userName") String userName) {
        return ResponseEntity.ok(userService.findUserByUserName(userName));
    }

    @PutMapping("/{id}/activate")
    @PreAuthorize("hasAuthority('RI_INSP_CREATE_USER')")
    public ResponseEntity<UserDTO> activateUser(@PathVariable(value = "id") String userId) {
        return ResponseEntity.ok(userService.activateUser(userId));
    }

    @PutMapping("/{id}/deactivate")
    @PreAuthorize("hasAuthority('RI_INSP_CREATE_USER')")
    public ResponseEntity<UserDTO> deactivateUser(@PathVariable(value = "id") String userId) {
        return ResponseEntity.ok(userService.deactivateUser(userId));
    }

    @GetMapping
    public ResponseEntity<UserLoginDTO> findByEmail(@RequestParam(value = "userName") String userName) throws UserNotFoundException {
        return ResponseEntity.ok(userService.findByUserName(userName));
    }

    @GetMapping("/details")
    public ResponseEntity<UserDetailDTO> getLoggedInUserDetails() {
        return ResponseEntity.ok(userService.getLoggedInUserDetails());
    }

    @PutMapping("/upload-picture")
    @PreAuthorize("hasAuthority('RI_INSP_MY_PROFILE')")
    public ResponseEntity<UserDetailDTO> updateProfilePicture(@RequestParam("file") MultipartFile file) throws Exception {
        return ResponseEntity.ok(userService.updateProfilePicture(file));
    }

    @DeleteMapping("/remove-picture")
    @PreAuthorize("hasAuthority('RI_INSP_MY_PROFILE')")
    public ResponseEntity<UserDetailDTO> removeProfilePicture() {
        return ResponseEntity.ok(userService.removeProfilePicture());
    }

    @PutMapping("/time-zone")
    @PreAuthorize("hasAuthority('RI_INSP_MY_PROFILE')")
    public ResponseEntity<UserDetailDTO> updateLoggedInUserTimeZone(@RequestParam String timeZone) {
        return ResponseEntity.ok(userService.updateLoggedInUserTimeZone(timeZone));
    }

    @PutMapping("/language")
    @PreAuthorize("hasAuthority('RI_INSP_MY_PROFILE')")
    public ResponseEntity<UserDetailDTO> updateLoggedInUserLanguage(@RequestParam String lang) {
        return ResponseEntity.ok(userService.updateLoggedInUserLanguage(lang));
    }

    @GetMapping("/merchants")
    public ResponseEntity<List<MerchantDTO>> getAllMerchantByUserName(@RequestParam(value = "userName") String userName) {
        return ResponseEntity.ok(userService.getAllMerchantByUserName(userName));
    }
}
