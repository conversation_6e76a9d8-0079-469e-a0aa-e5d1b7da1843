package uk.co.flexi.ri.controller;

import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import uk.co.flexi.ri.dto.ReturnReasonDTO;
import uk.co.flexi.ri.service.ReturnReasonService;
import java.util.List;


@RestController
@RequestMapping(value = "/api/v1/return-reason")
@AllArgsConstructor
@PreAuthorize("hasAuthority('RI_INSP_RETN_RSN')")
public class ReturnReasonController {

    private final ReturnReasonService returnReasonService;

    @PostMapping
    public ResponseEntity<ReturnReasonDTO> create(@Valid @RequestBody ReturnReasonDTO returnReasonDTO) {
        return ResponseEntity.ok(returnReasonService.save(returnReasonDTO));
    }

    @PutMapping("/{id}/update")
    public ResponseEntity<ReturnReasonDTO> update(@PathVariable(value = "id") String returnReasonId,
                                                  @Valid @RequestBody ReturnReasonDTO returnReasonDTO) {
        return ResponseEntity.ok(returnReasonService.update(returnReasonId, returnReasonDTO));
    }

    @GetMapping
    public ResponseEntity<List<ReturnReasonDTO>> findAll() {
        return ResponseEntity.ok(returnReasonService.findAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<ReturnReasonDTO> findById(@PathVariable(value = "id") String returnReasonId) {
        return ResponseEntity.ok(returnReasonService.findById(returnReasonId));
    }

    @DeleteMapping("/{id}/delete")
    public void delete(@PathVariable(value = "id") String returnReasonId) {
        returnReasonService.delete(returnReasonId);
    }
}
