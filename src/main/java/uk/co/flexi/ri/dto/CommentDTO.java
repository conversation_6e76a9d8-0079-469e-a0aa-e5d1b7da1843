package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.model.Comment;

import java.time.OffsetDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommentDTO {

    private String commentId;

    private UserDetailDTO author;

    private String content;

    private List<MediaDTO> media;

    private String inspectionItemId;

    private Comment.CommentVisibility visibility;

    private Comment.CommentType commentType;

    private OffsetDateTime createdAt;

    private OffsetDateTime updatedAt;
}
