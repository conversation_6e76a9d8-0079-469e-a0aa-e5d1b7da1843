package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.model.NotificationConfig;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NotificationConfigDTO {

    public enum Frequency {
        IMMEDIATELY,
        EVERY_ONE_HOUR,
        EVERY_TWO_HOUR,
        CUSTOM
    }

    private long id;

    private NotificationConfig.NotificationType notificationType;

    private Long channelId;

    private Frequency frequency;

    private String customFrequency;

    private String userGroupId;

    private String destination;
}
