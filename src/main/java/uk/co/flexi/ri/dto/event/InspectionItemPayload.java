package uk.co.flexi.ri.dto.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InspectionItemPayload {

    private String inspectionItemId;

    private String orderLineId;

    private String sku;

    private String returnReason;

    private String expectedCondition;

    private String condition;

    private String status;

    private OffsetDateTime completedDate;

    private String completedByGroup;

    private Map<String, Object> inspection;

    private List<CommentPayload> comments;
}
