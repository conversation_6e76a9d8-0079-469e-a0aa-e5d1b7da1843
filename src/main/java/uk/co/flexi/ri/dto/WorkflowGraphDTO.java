package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * Graph-based data structure for modeling inspection workflow transitions.
 * This provides an efficient way to track and analyze workflow patterns.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowGraphDTO {

    /**
     * Map of node name to WorkflowNode
     */
    private Map<String, WorkflowNode> nodes = new HashMap<>();

    /**
     * Map of edge key (source->target) to WorkflowEdge
     */
    private Map<String, WorkflowEdge> edges = new HashMap<>();

    /**
     * Adds a node to the graph
     */
    public void addNode(String nodeName, String nodeType, Map<String, Object> metadata) {
        nodes.put(nodeName, new WorkflowNode(nodeName, nodeType, metadata));
    }

    /**
     * Adds an edge between two nodes with a weight
     */
    public void addEdge(String source, String target, int weight, Map<String, Object> metadata) {
        String edgeKey = source + "->" + target;
        WorkflowEdge existingEdge = edges.get(edgeKey);
        
        if (existingEdge != null) {
            existingEdge.setWeight(existingEdge.getWeight() + weight);
        } else {
            edges.put(edgeKey, new WorkflowEdge(source, target, weight, metadata));
        }

        // Ensure nodes exist
        if (!nodes.containsKey(source)) {
            addNode(source, "state", new HashMap<>());
        }
        if (!nodes.containsKey(target)) {
            addNode(target, "state", new HashMap<>());
        }
    }

    /**
     * Gets all outgoing edges from a node
     */
    public List<WorkflowEdge> getOutgoingEdges(String nodeName) {
        return edges.values().stream()
                .filter(edge -> edge.getSource().equals(nodeName))
                .toList();
    }

    /**
     * Gets all incoming edges to a node
     */
    public List<WorkflowEdge> getIncomingEdges(String nodeName) {
        return edges.values().stream()
                .filter(edge -> edge.getTarget().equals(nodeName))
                .toList();
    }

    /**
     * Converts the graph to Sankey chart format
     */
    public SankeyChartDTO toSankeyChart() {
        List<String> nodeNames = new ArrayList<>(nodes.keySet());
        List<SankeyChartDTO.SankeyLinkDTO> links = edges.values().stream()
                .map(edge -> new SankeyChartDTO.SankeyLinkDTO(
                        edge.getSource(),
                        edge.getTarget(),
                        edge.getWeight()
                ))
                .toList();

        return new SankeyChartDTO(nodeNames, links);
    }

    /**
     * Represents a node in the workflow graph
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WorkflowNode {
        private String name;
        private String type; // "state", "user_group", "decision_point"
        private Map<String, Object> metadata = new HashMap<>();
        private int totalTransitions = 0;

        public WorkflowNode(String name, String type, Map<String, Object> metadata) {
            this.name = name;
            this.type = type;
            this.metadata = metadata != null ? metadata : new HashMap<>();
        }
    }

    /**
     * Represents an edge (transition) in the workflow graph
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class WorkflowEdge {
        private String source;
        private String target;
        private int weight; // Number of transitions
        private Map<String, Object> metadata = new HashMap<>();
        private List<String> inspectionIds = new ArrayList<>();

        public WorkflowEdge(String source, String target, int weight, Map<String, Object> metadata) {
            this.source = source;
            this.target = target;
            this.weight = weight;
            this.metadata = metadata != null ? metadata : new HashMap<>();
        }

        public void addInspectionId(String inspectionId) {
            if (!inspectionIds.contains(inspectionId)) {
                inspectionIds.add(inspectionId);
            }
        }
    }

    /**
     * Gets the total number of transitions in the graph
     */
    public int getTotalTransitions() {
        return edges.values().stream()
                .mapToInt(WorkflowEdge::getWeight)
                .sum();
    }

    /**
     * Gets nodes sorted by total incoming transitions (most popular destinations)
     */
    public List<WorkflowNode> getNodesByPopularity() {
        Map<String, Integer> nodeWeights = new HashMap<>();
        
        for (WorkflowEdge edge : edges.values()) {
            nodeWeights.merge(edge.getTarget(), edge.getWeight(), Integer::sum);
        }

        return nodes.values().stream()
                .sorted((a, b) -> Integer.compare(
                        nodeWeights.getOrDefault(b.getName(), 0),
                        nodeWeights.getOrDefault(a.getName(), 0)
                ))
                .toList();
    }

    /**
     * Finds the most common workflow path
     */
    public List<String> getMostCommonPath() {
        // Simple implementation - find the path with highest cumulative weight
        List<String> path = new ArrayList<>();
        String currentNode = "Started"; // Assuming this is the starting node
        
        while (currentNode != null) {
            path.add(currentNode);
            
            // Find the edge with highest weight from current node
            WorkflowEdge maxEdge = getOutgoingEdges(currentNode).stream()
                    .max(Comparator.comparing(WorkflowEdge::getWeight))
                    .orElse(null);
            
            if (maxEdge != null && !path.contains(maxEdge.getTarget())) {
                currentNode = maxEdge.getTarget();
            } else {
                break;
            }
        }
        
        return path;
    }
}
