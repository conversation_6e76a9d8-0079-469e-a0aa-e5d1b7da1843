package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.model.AuditEvent;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AuditEventDTO {

    private AuditEvent.EventType eventType;

    private String entityId;

    private String uniqueId;

    private String userGroupId;

    private String userId;

    private Map<String, Object> details;
}
