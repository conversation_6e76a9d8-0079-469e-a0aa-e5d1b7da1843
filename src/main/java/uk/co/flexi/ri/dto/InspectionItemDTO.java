package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.model.InspectionItem;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InspectionItemDTO {

    private String inspectionItemId;

    private ProductDTO product;

    private BigDecimal expectedQuantity;

    private BigDecimal receivedQuantity;

    private InspectionItem.InspectionItemStatus itemStatus;

    private String itemCondition;

    private String returnReason;

    private String expectedItemCondition;

    private List<CommentDTO> comments;

    private Boolean enableReturnReasonEdit = Boolean.TRUE;
}
