package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.model.NotificationChannel;

import java.util.HashMap;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NotificationChannelDTO {

    private Long id;

    private NotificationChannel.Channel channel;

    private Map<String, Object> config = new HashMap<>();
}
