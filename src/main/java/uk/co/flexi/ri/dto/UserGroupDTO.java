package uk.co.flexi.ri.dto;

import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.model.Comment;

import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserGroupDTO {

    private Long id;

    private String userGroupId;

    @NotEmpty
    private String name;

    private Comment.CommentVisibility visibility;

    private String partnerName;

    private Set<ReviewerDTO> reviewers;

    private Boolean isActive;
}
