package uk.co.flexi.ri.dto.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommentPayload {

    private String commentId;

    private String comment;

    private String author;

    private Map<String, Object> inspectionItem;

    private List<MediaPayload> media;
}
