package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.model.Comment;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommentMediaDTO {

    private String content;

    private List<MultipartFile> mediaFiles;

    private List<String> mediaIds;

    private String inspectionItemId;

    private Comment.CommentVisibility visibility;

    private Comment.CommentType commentType;
}