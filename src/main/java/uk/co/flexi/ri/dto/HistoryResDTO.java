package uk.co.flexi.ri.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HistoryResDTO {

    @JsonIgnore
    private Long entityId;

    private String uniqueId;

    String revisionType;

    private String field;

    private String oldValue;

    private String newValue;

    private String updatedBy;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd' 'HH:mm:ss")
    private OffsetDateTime updatedAt;
}
