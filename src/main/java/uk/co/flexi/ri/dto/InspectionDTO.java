package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.validation.annotation.HasInspectionItemWithReceivedQuantityOne;

import java.time.OffsetDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@HasInspectionItemWithReceivedQuantityOne
public class InspectionDTO {

    private String inspectionId;

    private Inspection.InspectionStatus status = Inspection.InspectionStatus.IN_PROGRESS;

    private String userStatus;

    private OrderDTO order;

    private String referenceId;

    private String partnerName;

    private String customerFirstName;

    private String customerLastName;

    private String customerEmail;

    private String sellingChannel;

    private String assigneeGroup;

    private List<InspectionItemDTO> inspectionItems;

    private OffsetDateTime createdAt;
}
