package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserDetailDTO {

    private String userId;

    private String userName;

    private String firstName;

    private String lastName;

    private Boolean isActive;

    private String supplierName;

    private Set<RoleDTO> roles;

    private String timeZone;

    private String language;

    private AuthProviderDTO authProvider;

    private String userGroup;

    private MediaDTO profileImage;

    private String reportingCurrency;
}
