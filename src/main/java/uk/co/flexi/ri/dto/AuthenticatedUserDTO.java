package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import uk.co.flexi.ri.model.UserGroup;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AuthenticatedUserDTO {

    private Long id;

    private String userId;

    private Long merchantId;

    private Long tenant;

    private String userName;

    private String merchantName;

    private String reportingCurrency;

    private String hmacKey;

    private UserGroup userGroup;

    @Override
    public String toString() {
        return "AuthenticatedUserDTO{" +
                "id=" + id +
                ", merchantId=" + merchantId +
                ", userName='" + userName + '\'' +
                ", merchantName='" + merchantName + '\'' +
                '}';
    }
}
