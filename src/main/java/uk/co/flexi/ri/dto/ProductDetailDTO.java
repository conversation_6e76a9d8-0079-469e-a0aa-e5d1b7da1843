package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductDetailDTO {

    private String inspectionItemId;

    private String orderLineId;

    private String productName;

    private String productImage;

    private List<String> productImages;

    private String description;

    private String sku;

    private BigDecimal expectedQuantity;

    private BigDecimal receivedQuantity;

    private String colour;

    private String size;

    private String returnReason;

    private String productClass;

    private String productSubclass;

    private String season;

    private String brand;

    private String country;

    private String region;

    private String city;

    private String style;

    private String departmentName;

    private String expectedItemCondition;

    private String currencyCode;

    private BigDecimal unitPrice;
}
