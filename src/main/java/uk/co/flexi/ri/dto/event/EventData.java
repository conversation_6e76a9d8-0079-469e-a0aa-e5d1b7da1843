package uk.co.flexi.ri.dto.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.model.EventQueue;

import java.time.OffsetDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventData<T> {

    private String eventId;

    private EventQueue.EventType eventType;

    private EventQueue.ReferenceType referenceType;

    private OffsetDateTime eventTime;

    private String uniqueId;

    private String performedByUser;

    private String performedByGroup;

    private T payload;

    private Long tenant;
}
