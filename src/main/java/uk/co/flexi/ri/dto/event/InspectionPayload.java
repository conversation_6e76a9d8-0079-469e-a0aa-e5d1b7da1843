package uk.co.flexi.ri.dto.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InspectionPayload {

    private String inspectionId;

    private String referenceId;

    private String orderId;

    private String returnOrderId;

    private String returnTrackingId;

    private String inspectionStatus;

    private String createdUser;

    private String assignedToGroup;

    private String partnerName;

    private String sellingChannel;

    private OffsetDateTime assignedDate;

    private OffsetDateTime completedDate;

    private String completedByGroup;

    private List<InspectionItemPayload> items;
}
