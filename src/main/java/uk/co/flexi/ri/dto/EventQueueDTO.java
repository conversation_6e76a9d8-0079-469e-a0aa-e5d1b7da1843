package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.model.EventQueue;

import java.time.OffsetDateTime;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EventQueueDTO {

    private String eventId;

    private String uniqueId;

    private EventQueue.EventType eventType;

    private EventQueue.ReferenceType referenceType;

    private OffsetDateTime eventTime;

    private String performedByUser;

    private String performedByGroup;

    private Map<String, Object> payload;

    private Long tenant;
}
