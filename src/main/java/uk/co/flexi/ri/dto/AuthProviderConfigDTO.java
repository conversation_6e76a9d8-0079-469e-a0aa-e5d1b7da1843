package uk.co.flexi.ri.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthProviderConfigDTO {

    private String clientId;

    private String clientSecret;

    private String redirectUri;

    private String tenantId;

    private String domain;
}
