package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductInstructionDTO {

    public ProductInstructionDTO(String productInstructionId, String productClass, String productSubclass) {
        this.productInstructionId = productInstructionId;
        this.productClass = productClass;
        this.productSubclass = productSubclass;
    }


    private String productInstructionId;

    private String productClass;

    private String productSubclass;

    private List<InstructionDTO> instructions;
}
