package uk.co.flexi.ri.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderDetailDTO {

    private String inspectionId;

    private String orderId;

    private String returnOrderId;

    private String returnTrackingId;

    private String referenceId;

    private String partnerName;

    private String sellingChannel;

    private String customerFirstName;

    private String customerLastName;

    private String customerEmail;

    private List<ProductDetailDTO> productDetails;

    @JsonIgnore
    private Long orderPk;
}
