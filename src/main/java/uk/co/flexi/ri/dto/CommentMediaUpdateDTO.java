package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.model.Comment;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommentMediaUpdateDTO {

    private String content;

    private List<MultipartFile> addMediaFiles;

    private List<String> removeMediaIds;

    private Comment.CommentVisibility visibility;
}