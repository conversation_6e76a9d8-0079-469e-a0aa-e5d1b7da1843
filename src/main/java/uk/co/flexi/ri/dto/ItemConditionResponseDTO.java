package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.model.InspectionItem;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ItemConditionResponseDTO {

    private InspectionItem.InspectionItemStatus itemStatus;

    private String itemCondition;

    private Long count;

    private BigDecimal price;

    private BigDecimal percentage;
}
