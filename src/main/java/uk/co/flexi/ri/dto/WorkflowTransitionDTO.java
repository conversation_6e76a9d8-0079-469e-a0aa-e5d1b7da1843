package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

/**
 * DTO representing a workflow transition for inspection items.
 * Used to track the flow of inspections through different states and user groups.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowTransitionDTO {

    /**
     * Unique identifier for the inspection
     */
    private String inspectionId;

    /**
     * Reference ID for the inspection (order/return tracking number)
     */
    private String referenceId;

    /**
     * Source state/group name
     */
    private String fromState;

    /**
     * Target state/group name
     */
    private String toState;

    /**
     * Timestamp when the transition occurred
     */
    private OffsetDateTime transitionTime;

    /**
     * User who performed the transition
     */
    private String performedBy;

    /**
     * Type of transition (ASSIGNMENT, STATUS_CHANGE, COMPLETION)
     */
    private TransitionType transitionType;

    /**
     * Additional details about the transition
     */
    private String details;

    public enum TransitionType {
        CREATED,           // Inspection created
        ASSIGNMENT,        // Assigned to user group
        SENT_FOR_REVIEW,   // Sent for review
        APPROVED,          // Item approved
        REJECTED,          // Item rejected
        COMPLETED          // Inspection completed
    }
}
