package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for Sankey chart data representation.
 * Contains nodes and links for visualizing inspection workflow flows.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SankeyChartDTO {

    /**
     * List of all nodes (states) in the workflow
     */
    private List<String> nodes;

    /**
     * List of links showing transitions between nodes with their values
     */
    private List<SankeyLinkDTO> links;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SankeyLinkDTO {
        /**
         * Source node name
         */
        private String source;

        /**
         * Target node name
         */
        private String target;

        /**
         * Number of transitions from source to target
         */
        private Integer value;
    }
}
