package uk.co.flexi.ri.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class TreeMapResponse {

    public TreeMapResponse(String displayName, String filterValue, Long value, BigDecimal price) {
        this.displayName = displayName;
        this.filterValue = filterValue;
        this.value = value;
        this.price = price;
    }

    public TreeMapResponse(String displayName, String displayName2, String filterValue,
                           Long value, BigDecimal price) {
        this.displayName = displayName;
        this.displayName2 = displayName2;
        this.filterValue = filterValue;
        this.value = value;
        this.price = price;
    }

    private String displayName;

    private String displayName2;

    private String displayName3;

    private String filterValue;

    private Long value;

    private BigDecimal price;
}
