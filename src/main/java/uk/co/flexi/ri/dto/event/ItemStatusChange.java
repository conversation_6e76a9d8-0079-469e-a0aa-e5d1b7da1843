package uk.co.flexi.ri.dto.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import uk.co.flexi.ri.model.InspectionItem;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ItemStatusChange {

    private String updatedBy;

    private String updatedByGroup;

    private String referenceId;

    private String sku;

    private InspectionItem.InspectionItemStatus status;

    private String condition;

    private Long tenant;
}
