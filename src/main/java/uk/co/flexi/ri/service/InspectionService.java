package uk.co.flexi.ri.service;

import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.*;
import uk.co.flexi.ri.exception.custom.InspectionUpdateException;
import uk.co.flexi.ri.exception.custom.OrderNotFoundException;
import uk.co.flexi.ri.mapper.specification.InspectionSpecifications;
import uk.co.flexi.ri.model.*;
import uk.co.flexi.ri.model.view.InspectionView;
import uk.co.flexi.ri.repository.view.AssignedPackagesRepo;
import uk.co.flexi.ri.repository.InspectionItemRepo;
import uk.co.flexi.ri.repository.InspectionRepo;
import uk.co.flexi.ri.util.Util;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class InspectionService {

    private final AuthenticatedUserService authenticatedUserService;

    private final InspectionRepo inspectionRepo;

    private final UserGroupService userGroupService;

    private final ModelMapper modelMapper;

    private final InspectionItemRepo inspectionItemRepo;

    private final OrderService orderService;

    private final CommentService commentService;

    private final AssignedPackagesRepo assignedPackagesRepo;

    private final ItemConditionService itemConditionService;

    private final EventPublisher eventPublisher;


    public InspectionDTO save(InspectionDTO inspectionDTO) {
        Map<String, BigDecimal> qtyMap = inspectionDTO.getInspectionItems().stream()
                .collect(Collectors.toMap(
                        item -> item.getProduct().getSku(),
                        InspectionItemDTO::getReceivedQuantity,
                        (existing, replacement) -> existing
                ));
        OrderDetailDTO orderDetailDTO = orderService.getOrderDetails(inspectionDTO.getReferenceId(), null);
        AuthenticatedUserDTO authenticatedUser = authenticatedUserService.getAuthenticatedUserDetails();
        Inspection inspection = mapInspectionDetails(qtyMap, authenticatedUser, orderDetailDTO);
        setInspectionItems(inspection, authenticatedUser);
        Inspection savedInspection = inspectionRepo.save(inspection);
        eventPublisher.publish(savedInspection, EventQueue.EventType.INSP_CREATED);
        return modelMapper.map(savedInspection, InspectionDTO.class);
    }

    private Inspection mapInspectionDetails(Map<String, BigDecimal> qtyMap, AuthenticatedUserDTO authenticatedUser,
                                            OrderDetailDTO orderDetailDTO) {
        List<InspectionItem> inspectionItemList = Optional.ofNullable(orderDetailDTO.getProductDetails())
                .orElse(Collections.emptyList())
                .stream()
                .flatMap(item -> {
                    Integer recQty = qtyMap.get(item.getSku()).intValue();
                    Product product = mapProductDetails(item);
                    return IntStream.range(0, recQty).mapToObj(i -> {
                        InspectionItem inspectionItem = new InspectionItem();
                        inspectionItem.setProduct(product);
                        inspectionItem.setExpectedQuantity(item.getExpectedQuantity());
                        inspectionItem.setReceivedQuantity(BigDecimal.ONE);
                        inspectionItem.setReturnReason(item.getReturnReason());
                        inspectionItem.setCountry(item.getCountry());
                        inspectionItem.setRegion(item.getRegion());
                        inspectionItem.setCity(item.getCity());
                        inspectionItem.setExpectedItemCondition(item.getExpectedItemCondition());
                        inspectionItem.setOrderLineId(item.getOrderLineId());
                        return inspectionItem;
                    });
                }).toList();

        User user = new User();
        user.setId(authenticatedUser.getId());

        Merchant merchant = new Merchant();
        merchant.setId(authenticatedUser.getMerchantId());

        Inspection inspection = new Inspection();
        inspection.setStatus(Inspection.InspectionStatus.IN_PROGRESS);
        inspection.setReferenceId(orderDetailDTO.getReferenceId());
        inspection.setPartnerName(orderDetailDTO.getPartnerName());
        inspection.setSellingChannel(orderDetailDTO.getSellingChannel());
        inspection.setCustomerFirstName(orderDetailDTO.getCustomerFirstName());
        inspection.setCustomerLastName(orderDetailDTO.getCustomerLastName());
        inspection.setCustomerEmail(orderDetailDTO.getCustomerEmail());
        inspection.setCreatedByGroup(authenticatedUser.getUserGroup());
        inspection.setAssignee(user);
        inspection.setAssigneeGroup(authenticatedUser.getUserGroup());
        inspection.setAssignedDate(OffsetDateTime.now());
        inspection.setAssigneeGroupName(authenticatedUser.getUserGroup().getName());
        inspection.setOrder(mapOrderDetails(orderDetailDTO));
        inspection.setInspectionItems(inspectionItemList);
        return inspection;
    }

    private Order mapOrderDetails(OrderDetailDTO orderDetailDTO) {
        if (orderDetailDTO.getOrderPk() != null) {
            return orderService.findById(orderDetailDTO.getOrderPk());
        } else {
            Order order = new Order();
            order.setOrderId(orderDetailDTO.getOrderId());
            order.setReturnTrackingId(orderDetailDTO.getReturnTrackingId());
            order.setReturnOrderId(orderDetailDTO.getReturnOrderId());
            return order;
        }
    }

    private Product mapProductDetails(ProductDetailDTO productDetailDTO) {
        Product product = new Product();
        product.setSku(productDetailDTO.getSku());
        product.setProductName(productDetailDTO.getProductName());
        product.setProductImage(Util.convertToCommaSeparated(productDetailDTO.getProductImages()));
        product.setDescription(productDetailDTO.getDescription());
        product.setProductClass(productDetailDTO.getProductClass());
        product.setProductSubclass(productDetailDTO.getProductSubclass());
        product.setDepartmentName(productDetailDTO.getDepartmentName());
        product.setProductStyle(productDetailDTO.getStyle());
        product.setProductSize(productDetailDTO.getSize());
        product.setUnitPrice(productDetailDTO.getUnitPrice());
        product.setCurrencyCode(productDetailDTO.getCurrencyCode());
        product.setReturnReason(productDetailDTO.getReturnReason());
        product.setProperties(loadProperties(productDetailDTO));
        return product;
    }

    private Map<String, Object> loadProperties(ProductDetailDTO productDTO) {
        Map<String, Object> properties = new HashMap<>();
        putIfValid(properties, "size", productDTO.getSize());
        putIfValid(properties, "color", productDTO.getColour());
        putIfValid(properties, "season", productDTO.getSeason());
        putIfValid(properties, "productClass", productDTO.getProductClass());
        putIfValid(properties, "productSubclass", productDTO.getProductSubclass());
        putIfValid(properties, "brand", productDTO.getBrand());
        return properties;
    }

    private void putIfValid(Map<String, Object> map, String key, String value) {
        if (value != null && !value.trim().isEmpty() && !"null".equalsIgnoreCase(value)) {
            map.put(key, value);
        }
    }

    private void setInspectionItems(Inspection inspection, AuthenticatedUserDTO authenticatedUser) {
        Merchant merchant = new Merchant();
        merchant.setId(authenticatedUser.getMerchantId());

        inspection.getInspectionItems().forEach(item -> {
            item.setInspection(inspection);
        });
    }

    public InspectionDTO findById(String inspectionId) {
        AuthenticatedUserDTO authenticatedUser = authenticatedUserService.getAuthenticatedUserDetails();
        Inspection inspection = inspectionRepo.findByInspectionId(inspectionId)
                .orElseThrow(() -> new NoSuchElementException("Inspection with ID " + inspectionId + " not found."));

        String userCommentVisibility = authenticatedUser.getUserGroup().getVisibility().name();

        InspectionDTO inspectionDTO = mapToDTO(inspection, authenticatedUser);

        inspectionDTO.getInspectionItems().stream()
                .filter(item -> item.getProduct() != null)
                .forEach(item -> {
                    if (item.getProduct().getReturnReason() != null) {
                        item.setEnableReturnReasonEdit(Boolean.FALSE);
                    }
                    item.getProduct().setProperties(loadProperties(item.getProduct().getProperties()));
                    item.setComments(filterComments(item.getComments(), userCommentVisibility));
                });

        return inspectionDTO;
    }

    private InspectionDTO mapToDTO(Inspection inspection, AuthenticatedUserDTO authenticatedUser) {
        InspectionDTO inspectionDTO = modelMapper.map(inspection, InspectionDTO.class);
        inspectionDTO.setUserStatus(getUserStatus(authenticatedUser, inspection));
        if (inspection.getAssigneeGroup() != null) {
            inspectionDTO.setAssigneeGroup(inspection.getAssigneeGroup().getName());
        }
        return inspectionDTO;
    }

    private List<CommentDTO> filterComments(List<CommentDTO> comments, String userCommentVisibility) {
        return comments.stream()
                .filter(comment -> isVisibleToUser(comment, userCommentVisibility))
                .toList();
    }

    private String getUserStatus(AuthenticatedUserDTO authenticatedUser, Inspection inspection) {
        UserGroup userGroup = authenticatedUser.getUserGroup();
        UserGroup assigneeGroup = inspection.getAssigneeGroup();

        if (userGroup == null || assigneeGroup == null || Inspection.InspectionStatus.COMPLETED.equals(inspection.getStatus())) {
            return inspection.getStatus().name();
        }

        return userGroup.getUserGroupId().equals(assigneeGroup.getUserGroupId())
                ? Inspection.InspectionStatus.IN_PROGRESS.name()
                : "IN_REVIEW";
    }

    private boolean isVisibleToUser(CommentDTO comment, String userCommentVisibility) {
        String commentVisibility = comment.getVisibility().name();
        return userCommentVisibility.equals("INTERNAL") ||
                userCommentVisibility.equals(commentVisibility);
    }

    private Map<String, String> loadProperties(Map<String, String> properties) {
        if (properties == null || properties.isEmpty()) {
            return new LinkedHashMap<>();
        }

        Map<String, String> productProperties = new LinkedHashMap<>();
        addIfValid(productProperties, "size", properties.get("size"));
        addIfValid(productProperties, "color", properties.get("color"));
        addIfValid(productProperties, "season", properties.get("season"));
        addIfValid(productProperties, "product class", properties.get("productClass"));
        addIfValid(productProperties, "product subclass", properties.get("productSubclass"));
        addIfValid(productProperties, "brand", properties.get("brand"));
        return productProperties;
    }

    private void addIfValid(Map<String, String> map, String key, String value) {
        if (value != null && !value.trim().isEmpty() && !"null".equalsIgnoreCase(value)) {
            map.put(key, value.trim());
        }
    }

    public List<InspectionDTO> findByReferenceIdId(String referenceId) {
        AuthenticatedUserDTO authenticatedUser = authenticatedUserService.getAuthenticatedUserDetails();
        return orderService.findByReferenceId(referenceId)
                .map(Order::getInspections)
                .orElse(Collections.emptyList())
                .stream()
                .map(inspection -> {
                    InspectionDTO dto = modelMapper.map(inspection, InspectionDTO.class);
                    String userStatus = getUserStatus(authenticatedUser, inspection);
                    dto.setUserStatus(userStatus);
                    if (inspection.getAssigneeGroup() != null) {
                        dto.setAssigneeGroup(inspection.getAssigneeGroup().getName());
                    }
                    return dto;
                })
                .toList();
    }

    public void updateStatus(Long id, Inspection.InspectionStatus status) {
        AuthenticatedUserDTO authenticatedUser = authenticatedUserService.getAuthenticatedUserDetails();
        Inspection inspection = inspectionRepo.findById(id)
                .orElseThrow(() -> new NoSuchElementException("Inspection with ID " + id + " not found."));
        if (status.equals(Inspection.InspectionStatus.COMPLETED)) {
            inspection.setCompletedDate(OffsetDateTime.now());
            inspection.setCompletedBy(authenticatedUser.getUserName());
        }
        inspection.setStatus(status);
        inspection = inspectionRepo.save(inspection);
        modelMapper.map(inspection, InspectionDTO.class);
    }


    public InspectionDTO updateStatus(String inspectionId, InspectionStatusUpdateReqDTO updateReqDTO) {
        AuthenticatedUserDTO authenticatedUser = authenticatedUserService.getAuthenticatedUserDetails();
        Inspection inspection = inspectionRepo.findByInspectionId(inspectionId)
                .orElseThrow(() -> new NoSuchElementException("Inspection with ID " + inspectionId + " not found."));
        if (inspection.getStatus().equals(Inspection.InspectionStatus.COMPLETED)) {
            throw new IllegalStateException("Inspection already competed");
        }
        UserGroup assigneeGroup = userGroupService.findByUserGroupId(updateReqDTO.getUserGroupId());
        inspection.setAssigneeGroup(assigneeGroup);
        inspection.setAssigneeGroupName(assigneeGroup.getName());
        inspection.setLastAssignedGroup(authenticatedUser.getUserGroup());
        inspection.setAssignedDate(OffsetDateTime.now());
        inspection = inspectionRepo.save(inspection);
        if (updateReqDTO.getComment() != null && !updateReqDTO.getComment().isBlank()) {
            List<InspectionItem> itemList = inspection.getInspectionItems();
            itemList.forEach(item -> {
                CommentMediaDTO commentMediaDTO = new CommentMediaDTO();
                commentMediaDTO.setContent(updateReqDTO.getComment());
                commentMediaDTO.setInspectionItemId(item.getInspectionItemId());
                commentMediaDTO.setCommentType(Comment.CommentType.REVIEW);
                try {
                    commentService.save(commentMediaDTO);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });
        }
        eventPublisher.publish(inspection, EventQueue.EventType.INSP_SEND_FOR_REVIEW);
        return findById(inspectionId);
    }

    public void deleteAllInspection(String merchantName) {
        AuthenticatedUserDTO authenticatedUser = authenticatedUserService.getAuthenticatedUserDetails();
        if (authenticatedUser.getMerchantName().equalsIgnoreCase(merchantName)) {
            List<Inspection> inspections = inspectionRepo.findAll();
            inspections.forEach(i -> {
                deleteInspection(i.getReferenceId());
            });
        } else {
            throw new IllegalArgumentException("Invalid merchant");
        }
    }

    public void deleteInspection(String referenceId) {
        List<Inspection> inspectionsToDelete = inspectionRepo.findByReferenceId(referenceId);
        if (inspectionsToDelete.isEmpty()) {
            throw new OrderNotFoundException("No orders found with reference ID: " + referenceId);
        }
        AtomicReference<Long> orderId = new AtomicReference<>();
        inspectionsToDelete.forEach(inspection -> {
            List<InspectionItem> inspectionItems = inspection.getInspectionItems();
            Set<Long> productIds = new HashSet<>();
            inspectionItems.forEach(item -> {
                item.getComments().forEach(com -> {
                    com.getMedia().forEach(med -> {
                        inspectionRepo.deleteMedia(med.getId());
                        inspectionRepo.deleteMediaAud(med.getId());
                    });
                    inspectionRepo.deleteComment(com.getId());
                    inspectionRepo.deleteCommentAud(com.getId());
                });
                productIds.add(item.getProduct().getId());
                inspectionRepo.deleteInspectionItem(item.getId());
                inspectionRepo.deleteInspectionItemAud(item.getId());
            });
            inspectionRepo.deleteInspection(inspection.getId());
            inspectionRepo.deleteInspectionAud(inspection.getId());
            inspectionRepo.deleteProducts(productIds);
            inspectionRepo.deleteEventRes(inspection.getInspectionId());
            inspectionRepo.deleteEventReq(inspection.getInspectionId());
            inspectionRepo.deleteEventQueue(inspection.getInspectionId());
            orderId.set(inspection.getOrder().getId());
        });
        inspectionRepo.deleteOrder(orderId.get());
    }

    public InspectionDTO updateInspectionQty(String inspectionId, List<InspectionQtyUpdateReqDTO> reqDTOList) {
        Optional<Inspection> optionalInspection = inspectionRepo.findByInspectionId(inspectionId);
        Inspection inspection = optionalInspection
                .orElseThrow(() -> new NoSuchElementException("No inspections found " +
                        "for Inspection ID: " + inspectionId));

        Map<String, List<InspectionItem>> existingSkuQtyMap = buildExistingSkuQtyMap(inspection);
        for (InspectionQtyUpdateReqDTO req : reqDTOList) {
            req.setReferenceId(inspection.getReferenceId());
            processQuantityUpdate(req, existingSkuQtyMap, inspection);
        }
        InspectionDTO response = new InspectionDTO();
        response.setInspectionId(inspectionId);
        return response;
    }

    private Map<String, List<InspectionItem>> buildExistingSkuQtyMap(Inspection inspection) {
        Map<String, List<InspectionItem>> skuQtyMap = new HashMap<>();
        inspection.getInspectionItems().forEach(item -> {
            if (item.getProduct() != null) {
                String sku = item.getProduct().getSku();
                if (!skuQtyMap.containsKey(sku)) {
                    skuQtyMap.put(sku, new ArrayList<>());
                }
                skuQtyMap.get(sku).add(item);
            }
        });
        return skuQtyMap;
    }

    private void processQuantityUpdate(InspectionQtyUpdateReqDTO req, Map<String, List<InspectionItem>> skuQtyMap, Inspection inspection) {
        if (skuQtyMap.containsKey(req.getSku())) {

            Integer existingQty = skuQtyMap.get(req.getSku()).size();
            List<InspectionItem> items = skuQtyMap.get(req.getSku());
            int difference = req.getReceivedQuantity() - existingQty;
            if (difference == 0) {
                return;
            }
            if (difference > 0) {
                addInspectionItems(items, difference);
            } else {
                removeInspectionItems(items, Math.abs(difference), inspection);
            }
        } else {
            addNewInspectionItems(req, inspection);
        }
    }

    private void addInspectionItems(List<InspectionItem> items, int difference) {
        InspectionItem templateItem = items.getFirst();
        List<InspectionItem> newItems = new ArrayList<>();
        for (int i = 0; i < difference; i++) {
            InspectionItem newItem = new InspectionItem();
            BeanUtils.copyProperties(templateItem, newItem);
            newItem.setItemStatus(null);
            newItem.setItemCondition(null);
            newItem.setId(null);
            newItem.setInspectionItemId(null);
            newItem.setComments(null);
            newItem.setMedia(null);
            newItems.add(newItem);
        }
        inspectionItemRepo.saveAll(newItems);
    }

    private void addNewInspectionItems(InspectionQtyUpdateReqDTO req, Inspection inspection) {
        OrderDetailDTO orderDetailDTO = orderService.getOrderDetails(req.getReferenceId(), inspection.getInspectionId());
        Map<String, ProductDetailDTO> detailDTOMap = buildProductDetailsMap(orderDetailDTO);
        ProductDetailDTO productDetailDTO = detailDTOMap.get(req.getSku());
        if (productDetailDTO != null) {
            List<InspectionItem> newItems = new ArrayList<>();
            Product product = mapProductDetails(productDetailDTO);
            for (int i = 0; i < req.getReceivedQuantity(); i++) {
                InspectionItem inspectionItem = new InspectionItem();
                inspectionItem.setInspection(inspection);
                inspectionItem.setOrderLineId(productDetailDTO.getOrderLineId());
                inspectionItem.setProduct(product);
                inspectionItem.setExpectedQuantity(productDetailDTO.getExpectedQuantity());
                inspectionItem.setReceivedQuantity(BigDecimal.ONE);
                inspectionItem.setReturnReason(productDetailDTO.getReturnReason());
                inspectionItem.setCountry(productDetailDTO.getCountry());
                inspectionItem.setRegion(productDetailDTO.getRegion());
                inspectionItem.setCity(productDetailDTO.getCity());
                inspectionItem.setExpectedItemCondition(productDetailDTO.getExpectedItemCondition());
                newItems.add(inspectionItem);
            }
            inspectionItemRepo.saveAll(newItems);
        }
    }

    private void removeInspectionItems(List<InspectionItem> items, int numToDelete, Inspection inspection) {
        boolean deleteAll = false;
        if (items == null || items.isEmpty()) {
            return;
        } else if (items.size() == numToDelete) {
            deleteAll = true;
        }
        Long productId = items.getFirst().getProduct().getId();
        items.stream()
                .limit(numToDelete)
                .forEach(item -> {
                    item.getComments().forEach(comment -> {
                        commentService.deleteComment(comment.getCommentId());
                    });
                    item.setProduct(null);
                    inspection.getInspectionItems().remove(item);
                    inspectionItemRepo.deleteById(item.getId());
                    inspectionItemRepo.flush();
                });

        if (deleteAll) {
            inspectionRepo.deleteProducts(Set.of(productId));
        }
    }

    private Map<String, ProductDetailDTO> buildProductDetailsMap(OrderDetailDTO orderDetailDTO) {
        return orderDetailDTO.getProductDetails().stream()
                .collect(Collectors.toMap(
                        ProductDetailDTO::getSku,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }

    public Map<String, Object> updateInspectionQtyCheck(String inspectionId, List<InspectionQtyUpdateReqDTO> reqDTOList) {
        Optional<Inspection> optionalInspection = inspectionRepo.findByInspectionId(inspectionId);
        Inspection inspection = optionalInspection
                .orElseThrow(() -> new NoSuchElementException("No inspections found " +
                        "for Inspection ID: " + inspectionId));

        Map<String, List<InspectionItem>> existingSkuQtyMap = buildExistingSkuQtyMap(inspection);
        for (InspectionQtyUpdateReqDTO req : reqDTOList) {
            req.setReferenceId(inspection.getReferenceId());
            processQuantityUpdateCheck(req, existingSkuQtyMap);
        }
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "Inspection update quantity check success");
        return response;
    }

    private void processQuantityUpdateCheck(InspectionQtyUpdateReqDTO req, Map<String, List<InspectionItem>> skuQtyMap) {
        if (skuQtyMap.containsKey(req.getSku())) {
            Integer existingQty = skuQtyMap.get(req.getSku()).size();
            List<InspectionItem> items = skuQtyMap.get(req.getSku());
            int difference = req.getReceivedQuantity() - existingQty;
            if (difference < 0) {
                if (items == null || items.isEmpty()) {
                    return;
                }
                items.stream()
                        .limit(Math.abs(difference))
                        .forEach(item -> {
                            if (!item.getComments().isEmpty()) {
                                throw new InspectionUpdateException("This will remove the saved comments and media associated with the inspection items.");
                            }
                        });
            }
        }
    }

    public Page<AssignedPackagesDTO> getAllAssignedPackages(AssignedPackagesFilterDTO filterDTO, Pageable pageable) {
        if (!List.of("eq", "neq").contains(filterDTO.getOper())) {
            throw new IllegalArgumentException("Invalid value for Operator. Supported values are 'eq' or 'neq'.");
        }
        AuthenticatedUserDTO authenticatedUser = authenticatedUserService.getAuthenticatedUserDetails();
        UserGroup assigneeGroup = authenticatedUser.getUserGroup();
        String partnerName = assigneeGroup.getPartnerName() != null ? assigneeGroup.getPartnerName() : filterDTO.getPartnerName();
        String partnerOper = assigneeGroup.getPartnerName() != null ? null : filterDTO.getOper();
        String assigneeGroupName = assigneeGroup.getName();

        Specification<InspectionView> specification = Specification.where(
                InspectionSpecifications.withAssigneeGroup(assigneeGroup.getId())
                        .or(InspectionSpecifications.withCreatedGroup(assigneeGroup.getId()))
                        .and(InspectionSpecifications.withPartnerName(partnerName, partnerOper))
                        .and(InspectionSpecifications.withReferenceId(filterDTO.getReferenceId(), filterDTO.getOper()))
                        .and(InspectionSpecifications.withOrderId(filterDTO.getOrderId(), filterDTO.getOper()))
                        .and(InspectionSpecifications.withCreatedAt(filterDTO.getCreatedAt(), filterDTO.getOper()))
                        .and(InspectionSpecifications.withStatus(filterDTO.getStatus(), filterDTO.getOper()))
                        .and(InspectionSpecifications.withSellingChannel(filterDTO.getSellingChannel(), filterDTO.getOper()))
                        .and(InspectionSpecifications.withAssigneeGroup(filterDTO.getAssigneeGroup(), filterDTO.getOper()))
                        .and(InspectionSpecifications.withRemainingDays(filterDTO.getRemainingDays(), filterDTO.getOper()))
        );

        Page<InspectionView> resultPage = assignedPackagesRepo.findAll((root, query, criteriaBuilder) -> {
            query.orderBy(
                    criteriaBuilder.asc(criteriaBuilder.selectCase()
                            .when(criteriaBuilder.equal(root.get("assigneeGroup"), assigneeGroupName), 0)
                            .otherwise(1)),
                    criteriaBuilder.asc(root.get("remainingDays"))
            );
            return specification.toPredicate(root, query, criteriaBuilder);
        }, pageable);

        return resultPage.map(inspection -> new AssignedPackagesDTO(
                inspection.getInspectionId(),
                inspection.getReferenceId(),
                inspection.getOrderId(),
                inspection.getCreatedAt(),
                inspection.getStatus().name(),
                inspection.getSellingChannel(),
                inspection.getPartnerName(),
                inspection.getAssigneeGroup(),
                inspection.getRemainingDays()
        ));
    }

    public List<InspectionItemConditionDTO> getInspectionItemCondition(InspectionItemCondition.ConditionType conditionType) {
        return itemConditionService.findByConditionType(conditionType)
                .stream().map(r -> modelMapper.map(r, InspectionItemConditionDTO.class)).toList();
    }
}

