package uk.co.flexi.ri.service;

import lombok.AllArgsConstructor;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.model.OMSConfig;
import uk.co.flexi.ri.model.OMSProvider;
import uk.co.flexi.ri.repository.OMSConfigRepo;
import uk.co.flexi.ri.repository.OMSProviderRepo;
import uk.co.flexi.sdk.oms.mao.client.config.MaoConfig;
import uk.co.flexi.sdk.oms.mao.client.config.MockConfig;
import uk.co.flexi.sdk.oms.model.OMSClientConfig;

import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@AllArgsConstructor
public class OMSConfigService {

    private final OMSConfigRepo omsConfigRepo;

    private final OMSProviderRepo omsProviderRepo;

    private final CacheManager cacheManager;

    public <T extends OMSClientConfig> T getConfig(OMSProvider omsProvider, Class<T> configClass) {
        List<OMSConfig> configs = omsConfigRepo.findByOMSProvider(omsProvider);
        Map<String, String> configMap = configs.stream()
                .collect(Collectors.toMap(OMSConfig::getConfigKey, OMSConfig::getConfigValue));
        if (MaoConfig.class.equals(configClass)) {
            return configClass.cast(new MaoConfig(
                    configMap.getOrDefault("auth_url", ""),
                    configMap.getOrDefault("base_path", ""),
                    configMap.getOrDefault("username", ""),
                    configMap.getOrDefault("password", ""),
                    configMap.getOrDefault("basic_auth_user", ""),
                    configMap.getOrDefault("basic_auth_password", ""),
                    configMap.getOrDefault("search_organizations", ""),
                    configMap.getOrDefault("product_organizations", "")
            ));
        } else if (MockConfig.class.equals(configClass)) {
            return configClass.cast(new MockConfig(
                    configMap.getOrDefault("base_path", ""),
                    configMap.getOrDefault("user_name", ""),
                    configMap.getOrDefault("password", "")
            ));
        }
        throw new UnsupportedOperationException("Unsupported OMS Provider or Configuration Type: " + omsProvider);
    }


    public Map<String, String> addOMSConfig(String name, Map<String, String> configs) {
        OMSProvider omsProvider = omsProviderRepo.findByProviderName(name).orElseThrow(() -> new NoSuchElementException(
                "OMS Provider not found"));
        List<OMSConfig> existingConfigs = omsConfigRepo.findByOMSProvider(omsProvider);
        Map<String, OMSConfig> existingConfigMap = existingConfigs.stream()
                .collect(Collectors.toMap(OMSConfig::getConfigKey, config -> config));
        List<OMSConfig> toSave = configs.entrySet().stream()
                .map(entry -> {
                    OMSConfig omsConfig = existingConfigMap.getOrDefault(entry.getKey(), new OMSConfig());
                    omsConfig.setConfigKey(entry.getKey());
                    omsConfig.setConfigValue(entry.getValue());
                    omsConfig.setOMSProvider(omsProvider);
                    return omsConfig;
                }).toList();
        Set<String> inputKeys = configs.keySet();
        List<OMSConfig> toDelete = existingConfigs.stream()
                .filter(config -> !inputKeys.contains(config.getConfigKey()))
                .toList();
        List<OMSConfig> savedConfigs = omsConfigRepo.saveAll(toSave);
        omsConfigRepo.deleteAll(toDelete);
        String cacheName = omsProvider.getProviderName().toUpperCase()+"_"+omsProvider.getTenant();
        Objects.requireNonNull(cacheManager.getCache("omsClients")).evictIfPresent(cacheName);
        return savedConfigs.stream()
                .collect(Collectors.toMap(OMSConfig::getConfigKey, OMSConfig::getConfigValue));
    }

    public Map<String, String> getOMSConfig(String name) {
        OMSProvider omsProvider = omsProviderRepo.findByProviderName(name).orElseThrow(() -> new NoSuchElementException(
                "OMS Provider not found"));
        List<OMSConfig> configs = omsConfigRepo.findByOMSProvider(omsProvider);
        return configs.stream()
                .collect(Collectors.toMap(OMSConfig::getConfigKey, OMSConfig::getConfigValue));
    }
}
