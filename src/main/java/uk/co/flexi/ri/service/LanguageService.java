package uk.co.flexi.ri.service;

import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.SupportedLanguageDTO;
import uk.co.flexi.ri.repository.SupportedLanguageRepo;

import java.util.List;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@AllArgsConstructor
public class LanguageService {

    private final SupportedLanguageRepo supportedLanguageRepo;

    private final ModelMapper modelMapper;

    public List<SupportedLanguageDTO> findAllSupportedLanguages() {
        return supportedLanguageRepo.findAll().stream().map(l -> modelMapper.map(l, SupportedLanguageDTO.class)).toList();
    }
}
