package uk.co.flexi.ri.service;

import lombok.AllArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.model.UserGroup;
import uk.co.flexi.ri.repository.UserRepo;
import uk.co.flexi.ri.security.model.CustomAuthDetails;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@AllArgsConstructor
public class AuthenticatedUserService {

    private final UserRepo userRepo;

    public AuthenticatedUserDTO getAuthenticatedUserDetails() {
        AuthenticatedUserDTO cachedUser = (AuthenticatedUserDTO) RequestContextHolder
                .currentRequestAttributes()
                .getAttribute("authenticatedUser", RequestAttributes.SCOPE_REQUEST);
        if (cachedUser != null) {
            return cachedUser;
        }

        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserDetails userDetails)) {
            return null;
        }

        Object authDetails = authentication.getDetails();
        if (!(authDetails instanceof CustomAuthDetails customDetails)) {
            return null;
        }

        String userGroupId = customDetails.getUserGroup();
        Long tenantId = Long.valueOf(customDetails.getTenant());

        return userRepo.findByUserNameAndIsActive(userDetails.getUsername(), tenantId, Boolean.TRUE)
                .map(user -> {
                    UserGroup matchingUserGroup = user.getUserGroups().stream()
                            .filter(group -> group.getUserGroupId().equals(userGroupId))
                            .findFirst()
                            .orElse(null);

                    AuthenticatedUserDTO dto = new AuthenticatedUserDTO(
                            user.getId(),
                            user.getUserId(),
                            user.getMerchant().getId(),
                            user.getMerchant().getTenant(),
                            user.getUserName(),
                            user.getMerchant().getMerchantName(),
                            user.getMerchant().getReportingCurrency(),
                            user.getMerchant().getHmacKey(),
                            matchingUserGroup
                    );

                    RequestContextHolder.currentRequestAttributes()
                            .setAttribute("authenticatedUser", dto, RequestAttributes.SCOPE_REQUEST);
                    return dto;
                })
                .orElse(null);
    }
}
