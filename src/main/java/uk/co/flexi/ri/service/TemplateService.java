package uk.co.flexi.ri.service;

import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.TemplateDTO;
import uk.co.flexi.ri.model.Template;
import uk.co.flexi.ri.model.UserGroup;
import uk.co.flexi.ri.repository.TemplateRepo;

import java.util.List;


@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@AllArgsConstructor
public class TemplateService {

    private final TemplateRepo templateRepo;

    private final ModelMapper modelMapper;

    private final AuthenticatedUserService authenticatedUserService;

    private final UserGroupService userGroupService;

    public List<TemplateDTO> getTemplates() {
        AuthenticatedUserDTO authenticatedUser = authenticatedUserService.getAuthenticatedUserDetails();
        UserGroup userGroup = authenticatedUser.getUserGroup();
        List<Template> templates = templateRepo.findByUserGroup(userGroup);
        if (templates.isEmpty()) {
            UserGroup adminGroup = userGroupService.findAdminGroup();
            templates = templateRepo.findByUserGroup(adminGroup);
        }
        return templates.stream()
                .map(template -> modelMapper.map(template, TemplateDTO.class))
                .toList();
    }
}
