package uk.co.flexi.ri.service;

import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.MerchantDTO;
import uk.co.flexi.ri.model.Merchant;
import uk.co.flexi.ri.repository.MerchantRepo;

import java.util.NoSuchElementException;

@Service
@AllArgsConstructor
@Transactional(propagation = Propagation.REQUIRED,rollbackFor=Exception.class)
public class MerchantService {

    private final AuthenticatedUserService authenticatedUserService;

    private final MerchantRepo merchantRepo;

    private final ModelMapper modelMapper;

    public Merchant findById(Long id) {
        return merchantRepo.findById(id).orElseThrow(() -> new NoSuchElementException("Merchant not found with " +
                "id: " + id));
    }

    public Merchant findByTenant(Long tenant) {
        return merchantRepo.findByTenantNative(tenant);
    }

    public MerchantDTO getMerchantDetails() {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        Merchant merchant = findById(authenticatedUserDTO.getMerchantId());
        return modelMapper.map(merchant, MerchantDTO.class);
    }
}
