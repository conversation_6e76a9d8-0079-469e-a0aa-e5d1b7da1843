package uk.co.flexi.ri.service;

import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.UserGroupDTO;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.model.UserGroup;
import uk.co.flexi.ri.repository.UserGroupRepo;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class UserGroupService {

    private static final String INVALID_USER_GROUP = "Invalid User Group";

    private final ModelMapper modelMapper;

    private final UserGroupRepo userGroupRepo;

    private final AuthenticatedUserService authenticatedUserService;

    public UserGroup findById(Long id) {
        Optional<UserGroup> optionalUserGroup = userGroupRepo.findById(id);
        return optionalUserGroup.orElseThrow(() ->
                new NoSuchElementException(INVALID_USER_GROUP));
    }

    public UserGroup findByUserGroupId(String userGroupId) {
        Optional<UserGroup> optionalUserGroup = userGroupRepo.findByUserGroupId(userGroupId);
        return optionalUserGroup.orElseThrow(() ->
                new NoSuchElementException(INVALID_USER_GROUP));
    }

    public UserGroupDTO getByUserGroupId(String userGroupId) {
        return modelMapper.map(findByUserGroupId(userGroupId), UserGroupDTO.class);
    }

    public UserGroupDTO save(UserGroupDTO userGroupDTO) {
        UserGroup userGroup = modelMapper.map(userGroupDTO, UserGroup.class);
        if (userGroupDTO.getVisibility().equals(Comment.CommentVisibility.EXTERNAL)) {
            userGroup.setPartnerName(userGroupDTO.getPartnerName());
        }
        userGroup = userGroupRepo.save(userGroup);
        return modelMapper.map(userGroup, UserGroupDTO.class);
    }

    public UserGroupDTO update(String userGroupId, UserGroupDTO userGroupDTO) {
        UserGroup userGroup = findByUserGroupId(userGroupId);
        if (userGroupDTO.getVisibility().equals(Comment.CommentVisibility.EXTERNAL)) {
            userGroup.setPartnerName(userGroupDTO.getPartnerName());
        } else {
            userGroup.setPartnerName(null);
        }
        userGroup.setVisibility(userGroupDTO.getVisibility());
        userGroup.setName(userGroupDTO.getName());
        userGroup = userGroupRepo.save(userGroup);
        return modelMapper.map(userGroup, UserGroupDTO.class);
    }

    public List<UserGroupDTO> findAllUserGroups() {
        List<UserGroup> userGroups = userGroupRepo.findAll();
        return userGroups.stream()
                .map(group -> modelMapper.map(group, UserGroupDTO.class))
                .toList();
    }

    public UserGroupDTO findUserGroupDTOById(String userGroupId) {
        Optional<UserGroup> optionalUserGroup = userGroupRepo.findByUserGroupId(userGroupId);
        UserGroup userGroup = optionalUserGroup.orElseThrow(() ->
                new NoSuchElementException(INVALID_USER_GROUP));
        return modelMapper.map(userGroup, UserGroupDTO.class);
    }

    public UserGroupDTO activateUserGroup(String userGroupId) {
        Optional<UserGroup> optionalUserGroup = userGroupRepo.findUserGroupById(userGroupId);
        UserGroup userGroup = optionalUserGroup.orElseThrow(() ->
                new NoSuchElementException(INVALID_USER_GROUP));
        userGroup.setIsActive(Boolean.TRUE);
        userGroup = userGroupRepo.save(userGroup);
        return modelMapper.map(userGroup, UserGroupDTO.class);
    }

    public UserGroupDTO deactivateUserGroup(String userGroupId) {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        if (authenticatedUserDTO.getUserGroup().getUserGroupId().equals(userGroupId)) {
            throw new IllegalArgumentException("Deactivation not allowed.");
        }
        UserGroup userGroup = findByUserGroupId(userGroupId);
        userGroup.setIsActive(Boolean.FALSE);
        userGroup = userGroupRepo.save(userGroup);
        return modelMapper.map(userGroup, UserGroupDTO.class);
    }

    public List<UserGroupDTO> findAll(boolean includeInactive) {
        Long tenant = authenticatedUserService.getAuthenticatedUserDetails().getTenant();

        List<UserGroup> userGroups = includeInactive
                ? userGroupRepo.findAllUserGroup(tenant)
                : userGroupRepo.findAllActiveUserGroup(tenant);

        return userGroups.stream()
                .filter(group -> group.getName() != null && !group.getName().equalsIgnoreCase("admin"))
                .map(group -> modelMapper.map(group, UserGroupDTO.class))
                .toList();
    }

    public List<UserGroupDTO> getUserGroupReviewers() {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        UserGroup loggedUserGroup = authenticatedUserDTO.getUserGroup();
        Set<UserGroup> reviewers = userGroupRepo.findReviewersByUserGroupId(loggedUserGroup.getId());
        return reviewers.stream()
                .map(group -> modelMapper.map(group, UserGroupDTO.class))
                .toList();
    }

    public UserGroupDTO updateReviewers(String userGroupId, List<String> reviewers) {
        UserGroup userGroup = findByUserGroupId(userGroupId);
        if (reviewers.contains(userGroupId)) {
            throw new IllegalArgumentException("Cannot select the same user group as the reviewer.");
        }
        deleteReviewers(userGroupId);
        Set<UserGroup> updatedReviewers = reviewers.stream()
                .map(this::findByUserGroupId)
                .collect(Collectors.toSet());

        userGroup.setReviewers(updatedReviewers);
        userGroup = userGroupRepo.save(userGroup);

        return modelMapper.map(userGroup, UserGroupDTO.class);
    }

    public void deleteReviewers(String userGroupId) {
        UserGroup userGroup = findByUserGroupId(userGroupId);
        userGroupRepo.deleteAllReviewers(userGroup.getId());
    }

    public List<UserGroupDTO> getAllReviewers() {
        List<UserGroup> userGroups = userGroupRepo.findAllWithReviewers();
        return userGroups.stream().filter(g -> !g.getName().equalsIgnoreCase("admin") &&
                        !g.getReviewers().isEmpty())
                .map(group -> modelMapper.map(group, UserGroupDTO.class))
                .toList();
    }

    public UserGroupDTO addReviewers(String userGroupId, List<String> reviewers) {
        UserGroup userGroup = findByUserGroupId(userGroupId);
        if (reviewers.contains(userGroupId)) {
            throw new IllegalArgumentException("Cannot select the same user group as the reviewer.");
        }
        if (!userGroup.getReviewers().isEmpty()) {
            throw new IllegalArgumentException("Reviewers are already added for this user group.");
        }
        Set<UserGroup> updatedReviewers = reviewers.stream()
                .map(this::findByUserGroupId)
                .collect(Collectors.toSet());

        userGroup.setReviewers(updatedReviewers);
        userGroup = userGroupRepo.save(userGroup);

        return modelMapper.map(userGroup, UserGroupDTO.class);
    }

    public List<UserGroupDTO> findAllGroups() {
        List<UserGroup> userGroups = userGroupRepo.findAllWithReviewers();
        return userGroups.stream()
                .map(group -> modelMapper.map(group, UserGroupDTO.class))
                .toList();
    }

    public UserGroup findAdminGroup(){
        Optional<UserGroup> optionalUserGroup = userGroupRepo.findByNameIgnoreCase("admin");
        return optionalUserGroup.orElseThrow(() ->
                new NoSuchElementException(INVALID_USER_GROUP));
    }
}
