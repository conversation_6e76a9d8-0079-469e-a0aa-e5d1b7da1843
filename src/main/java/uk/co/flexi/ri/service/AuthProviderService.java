package uk.co.flexi.ri.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.AuthProviderConfigDTO;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.model.AuthProvider;
import uk.co.flexi.ri.model.Merchant;
import uk.co.flexi.ri.repository.AuthProviderRepo;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class AuthProviderService {

    private static final String AZURE_BASE_URL = "https://login.microsoftonline.com/";

    @SuppressWarnings("squid:S1075")
    private static final String REDIRECT_PATH = "/oauth2/authorization/";

    private final AuthProviderRepo authProviderRepo;

    private final ModelMapper modelMapper;

    private final ObjectMapper objectMapper;

    private final AuthenticatedUserService authenticatedUserService;

    public List<String> getAuthProviders() {
        return Arrays.stream(AuthProvider.Provider.values())
                .filter(provider -> provider != AuthProvider.Provider.DB)
                .map(Enum::name)
                .toList();
    }

    public AuthProvider findByProvider(AuthProvider.Provider provider) {
        return authProviderRepo.findByProvider(provider).orElseThrow();
    }

    public AuthProviderConfigDTO addSSOConfig(AuthProvider.Provider provider, JsonNode reqDTO) throws IOException {
        AuthProvider authProvider = authProviderRepo.findByProvider(provider)
                .orElseGet(() -> {
                    AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
                    Merchant merchant = new Merchant();
                    merchant.setId(authenticatedUserDTO.getMerchantId());
                    return new AuthProvider(provider, merchant);
                });
        ObjectReader readerForUpdating = objectMapper.readerForUpdating(authProvider);
        AuthProvider updatedAuthProvider = readerForUpdating.readValue(reqDTO);
        setProviderValues(provider, updatedAuthProvider);
        updatedAuthProvider = authProviderRepo.save(updatedAuthProvider);
        return modelMapper.map(updatedAuthProvider, AuthProviderConfigDTO.class);
    }

    public AuthProviderConfigDTO getSSOConfig(AuthProvider.Provider provider) {
        switch (provider) {
            case GOOGLE -> {
                return AuthProviderConfigDTO.builder()
                        .clientId("")
                        .clientSecret("")
                        .redirectUri("")
                        .build();
            }

            case AZURE -> {
                return AuthProviderConfigDTO.builder()
                        .clientId("")
                        .clientSecret("")
                        .redirectUri("")
                        .tenantId("")
                        .build();
            }

            case OKTA, MANHATTAN -> {
                return AuthProviderConfigDTO.builder()
                        .clientId("")
                        .clientSecret("")
                        .redirectUri("")
                        .domain("")
                        .build();
            }

            default -> throw new IllegalArgumentException("Unsupported provider: " + provider);
        }
    }

    public void setProviderValues(AuthProvider.Provider provider, AuthProvider saveAuthProvider) {
        switch (provider) {
            case GOOGLE -> {
                if (saveAuthProvider.getRedirectUri() != null) {
                    String registrationId = extractRegistrationId(saveAuthProvider.getRedirectUri());
                    saveAuthProvider.setRegistrationId(registrationId);
                    saveAuthProvider.setAuthUrl(REDIRECT_PATH + registrationId);
                }
                saveAuthProvider.setAuthorizationUri("https://accounts.google.com/o/oauth2/auth");
                saveAuthProvider.setTokenUri("https://oauth2.googleapis.com/token");
                saveAuthProvider.setUserInfoUri("https://www.googleapis.com/oauth2/v3/userinfo");
                saveAuthProvider.setNameAttr("sub");
                saveAuthProvider.setScopes("profile,email");
            }

            case AZURE -> {
                String tenantId = saveAuthProvider.getTenantId();
                if (saveAuthProvider.getRedirectUri() != null) {
                    String registrationId = extractRegistrationId(saveAuthProvider.getRedirectUri());
                    saveAuthProvider.setRegistrationId(registrationId);
                    saveAuthProvider.setAuthUrl(REDIRECT_PATH + registrationId);
                }
                saveAuthProvider.setAuthorizationUri(AZURE_BASE_URL + tenantId + "/oauth2/v2.0/authorize");
                saveAuthProvider.setTokenUri(AZURE_BASE_URL + tenantId + "/oauth2/v2.0/token");
                saveAuthProvider.setUserInfoUri("https://graph.microsoft.com/oidc/userinfo");
                saveAuthProvider.setJwkSetUri(AZURE_BASE_URL + tenantId + "/discovery/v2.0/keys");
                saveAuthProvider.setNameAttr("name");
                saveAuthProvider.setScopes("openid,profile,email,https://graph.microsoft.com/.default");
            }

            case OKTA -> {
                if (saveAuthProvider.getRedirectUri() != null) {
                    String registrationId = extractRegistrationId(saveAuthProvider.getRedirectUri());
                    saveAuthProvider.setRegistrationId(registrationId);
                    saveAuthProvider.setAuthUrl(REDIRECT_PATH + registrationId);
                }
                String oktaDomain = saveAuthProvider.getDomain();
                saveAuthProvider.setAuthorizationUri(oktaDomain + "/oauth2/default/v1/authorize");
                saveAuthProvider.setTokenUri(oktaDomain + "/oauth2/default/v1/token");
                saveAuthProvider.setUserInfoUri(oktaDomain + "/oauth2/default/v1/userinfo");
                saveAuthProvider.setJwkSetUri(oktaDomain + "/oauth2/default/v1/keys");
                saveAuthProvider.setNameAttr("sub");
                saveAuthProvider.setScopes("openid,profile,email");
            }

            case MANHATTAN -> {
                if (saveAuthProvider.getRedirectUri() != null) {
                    String registrationId = extractRegistrationId(saveAuthProvider.getRedirectUri());
                    saveAuthProvider.setRegistrationId(registrationId);
                    saveAuthProvider.setAuthUrl(REDIRECT_PATH + registrationId);
                }
                String maoDomain = saveAuthProvider.getDomain();
                saveAuthProvider.setAuthorizationUri(maoDomain + "/oauth/authorize");
                saveAuthProvider.setTokenUri(maoDomain + "/oauth/token");
                saveAuthProvider.setUserInfoUri(maoDomain + "/auth/realms/maactive/protocol/openid-connect/userinfo");
                saveAuthProvider.setJwkSetUri(maoDomain + "/auth/realms/maactive/protocol/openid-connect/certs");
                saveAuthProvider.setNameAttr("sub");
                saveAuthProvider.setScopes("openid,profile,email");
            }

            default -> throw new IllegalArgumentException("Unsupported provider: " + provider);
        }
    }

    public static String extractRegistrationId(String url) {
        String marker = "/code/";
        int index = url.indexOf(marker);
        if (index != -1) {
            return url.substring(index + marker.length());
        }
        return null;
    }
}
