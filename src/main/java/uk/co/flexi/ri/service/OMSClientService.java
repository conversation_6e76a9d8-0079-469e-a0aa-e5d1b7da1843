package uk.co.flexi.ri.service;

import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import uk.co.flexi.ri.model.OMSProvider;
import uk.co.flexi.sdk.oms.mao.client.MaoOmsClient;
import uk.co.flexi.sdk.oms.mao.client.MockOMSClient;
import uk.co.flexi.sdk.oms.mao.client.OMSClient;
import uk.co.flexi.sdk.oms.mao.client.OMSClientFactory;
import uk.co.flexi.sdk.oms.mao.client.config.MaoConfig;
import uk.co.flexi.sdk.oms.mao.client.config.MockConfig;

@Service
@RequiredArgsConstructor
public class OMSClientService {

    public enum OMSType {
        MAO,
        MOCK
    }

    private final OMSConfigService omsConfigService;


    @Cacheable(value = "omsClients", key = "#type.name() + '_' + #omsProvider.tenant")
    public OMSClient<?> getClient(OMSClientService.OMSType type, OMSProvider omsProvider) {
        return switch (type) {
            case MAO -> createMaoClient(omsProvider);
            case MOCK -> createMockClient(omsProvider);
        };
    }

    private MaoOmsClient createMaoClient(OMSProvider omsProvider) {
        OMSClientFactory.OMSProvider provider = OMSClientFactory.OMSProvider.MAO;
        MaoConfig maoConfig = omsConfigService.getConfig(omsProvider, MaoConfig.class);
        return OMSClientFactory.getClient(provider, maoConfig);
    }

    private MockOMSClient createMockClient(OMSProvider omsProvider) {
        OMSClientFactory.OMSProvider provider = OMSClientFactory.OMSProvider.MOCK;
        MockConfig mockConfig = omsConfigService.getConfig(omsProvider, MockConfig.class);
        return OMSClientFactory.getClient(provider, mockConfig);
    }
}