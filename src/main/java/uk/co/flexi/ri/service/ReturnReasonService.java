package uk.co.flexi.ri.service;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.ReturnReasonDTO;
import uk.co.flexi.ri.model.ReturnReason;
import uk.co.flexi.ri.repository.ReturnReasonRepo;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;


@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class ReturnReasonService {

    private static final String INVALID_RETURN_REASON = "Invalid Return Reason";

    private final ModelMapper modelMapper;

    private final ReturnReasonRepo returnReasonRepo;

    public ReturnReasonDTO findById(String returnReasonId) {
        Optional<ReturnReason> optionalReturnReason = returnReasonRepo.findByReturnReasonId(returnReasonId);
        ReturnReason returnReason = optionalReturnReason.orElseThrow(() ->
                new NoSuchElementException(INVALID_RETURN_REASON));
        return modelMapper.map(returnReason, ReturnReasonDTO.class);
    }

    public ReturnReasonDTO save(ReturnReasonDTO returnReasonDTO) {
        ReturnReason returnReason = modelMapper.map(returnReasonDTO, ReturnReason.class);
        returnReason = returnReasonRepo.save(returnReason);
        return modelMapper.map(returnReason, ReturnReasonDTO.class);
    }

    public List<ReturnReasonDTO> findAll() {
        List<ReturnReason> returnReasons = returnReasonRepo.findAll();
        return returnReasons.stream().map(channel -> modelMapper.map(channel,
                        ReturnReasonDTO.class))
                .toList();
    }

    public ReturnReasonDTO update(String returnReasonId, @Valid ReturnReasonDTO returnReasonDTO) {
        Optional<ReturnReason> optionalReturnReason = returnReasonRepo.findByReturnReasonId(returnReasonId);
        ReturnReason returnReason = optionalReturnReason.orElseThrow(() ->
                new NoSuchElementException(INVALID_RETURN_REASON));
        returnReason.setReason(returnReasonDTO.getReason());
        returnReason = returnReasonRepo.save(returnReason);
        return modelMapper.map(returnReason, ReturnReasonDTO.class);
    }

    public void delete(String returnReasonId) {
        Optional<ReturnReason> optionalReturnReason = returnReasonRepo.findByReturnReasonId(returnReasonId);
        ReturnReason returnReason = optionalReturnReason.orElseThrow(() ->
                new NoSuchElementException(INVALID_RETURN_REASON));
        returnReasonRepo.delete(returnReason);
    }
}
