package uk.co.flexi.ri.service;

import jakarta.persistence.EntityManager;
import org.hibernate.Hibernate;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.AuditEventDTO;
import uk.co.flexi.ri.dto.HistoryResDTO;
import uk.co.flexi.ri.model.AuditEvent;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.model.CustomRevisionEntity;
import uk.co.flexi.ri.model.Media;
import uk.co.flexi.ri.repository.AuditEventRepo;
import uk.co.flexi.ri.repository.AuditRepository;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.ToLongFunction;
import java.util.stream.Collectors;

@SuppressWarnings({"java:S3011","java:S3776"})
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class AuditService {

    public enum AuditCategory {
        INSPECTION,INSPECTION_ITEM,COMMENT,MEDIA
    }

    private final AuditRepository auditRepository;

    private final TenantIdentifierResolver tenantResolver;

    private final ModelMapper modelMapper;

    private final EntityManager entityManager;

    @Value("#{'${audit.entity.inspection.fields}'.split(',')}")
    private List<String> inspectionFields;

    @Value("#{'${audit.entity.inspection.item.fields}'.split(',')}")
    private List<String> inspectionItemFields;

    @Value("#{'${audit.entity.comment.fields}'.split(',')}")
    private List<String> commentItemFields;

    @Value("#{'${audit.entity.media.fields}'.split(',')}")
    private List<String> mediaFields;

    public AuditService(@Value("${audit.repository.type}") String repositoryType,
                        AuditEventRepo auditEventRepo,
                        TenantIdentifierResolver tenantResolver,
                        EntityManager entityManager,
                        ModelMapper modelMapper) {
        this.auditRepository = switch (repositoryType.toLowerCase()) {
            case "db" -> auditEventRepo;
            default -> throw new IllegalArgumentException("Invalid repository type: " + repositoryType);
        };
        this.tenantResolver = tenantResolver;
        this.modelMapper = modelMapper;
        this.entityManager = entityManager;
    }

    public void saveAuditEvent(AuditEventDTO dto) {
        auditRepository.saveEvent(modelMapper.map(dto, AuditEvent.class));
    }

    public void saveAuditEvents(List<AuditEventDTO> dtos) {
        List<AuditEvent> auditEvents = dtos.stream().map(dto -> modelMapper.map(dto, AuditEvent.class)).toList();
        auditRepository.saveEvents(auditEvents);
    }

    public Long getCountByDate(LocalDate startDate, LocalDate endDate, AuditEvent.EventType... eventTypes) {
        OffsetDateTime startDateTime = startDate.atStartOfDay(ZoneOffset.UTC).toOffsetDateTime();
        OffsetDateTime endDateTime = endDate.atTime(LocalTime.MAX).atOffset(ZoneOffset.UTC);

        return Arrays.stream(eventTypes)
                .mapToLong(type -> auditRepository.countByDateBetweenAndEventType(startDateTime, endDateTime, type))
                .sum();
    }

    public Long getCreatedCountByDate(LocalDate startDate, LocalDate endDate) {
        return getCountByDate(startDate, endDate,
                AuditEvent.EventType.INSPECTION_ITEM_CREATE)
                - getCountByDate(startDate, endDate,
                AuditEvent.EventType.INSPECTION_ITEM_DELETE);
    }

    public Long getCompletedCountByDate(LocalDate startDate, LocalDate endDate) {
        return getCountByDate(startDate, endDate,
                AuditEvent.EventType.INSPECTION_ITEM_APPROVED,
                AuditEvent.EventType.INSPECTION_ITEM_REJECTED);
    }

    public Long getSendForReviewCountByDate(LocalDate startDate, LocalDate endDate) {
        return getCountByDate(startDate, endDate,
                AuditEvent.EventType.INSPECTION_ITEM_SEND_FOR_REVIEW);
    }

    public Page<HistoryResDTO> getAuditLogs(AuditCategory category, String uniqueId, RevisionType revisionType,
                                                  String user, OffsetDateTime startDate, OffsetDateTime endDate, Pageable pageable) {
        List<HistoryResDTO> fullHistory = switch (category) {
            case INSPECTION -> getAuditEntries(Inspection.class, Inspection::getId,
                    revisionType, inspectionFields, user, startDate, endDate);
            case INSPECTION_ITEM -> getAuditEntries(InspectionItem.class, InspectionItem::getId,
                    revisionType, inspectionItemFields, user, startDate, endDate);
            case COMMENT -> getAuditEntries(Comment.class, Comment::getId,
                    revisionType, commentItemFields, user, startDate, endDate);
            case MEDIA -> getAuditEntries(Media.class, Media::getId,
                    revisionType, mediaFields, user, startDate, endDate);
        };

        List<HistoryResDTO> filteredHistory = (uniqueId != null)
                ? fullHistory.stream()
                .filter(h -> uniqueId.equals(h.getUniqueId()))
                .toList()
                : fullHistory;

        int start = (int) pageable.getOffset();
        int end = Math.min(start + pageable.getPageSize(), filteredHistory.size());
        List<HistoryResDTO> pagedHistory = (start >= filteredHistory.size()) ? Collections.emptyList() : filteredHistory.subList(start, end);
        return new PageImpl<>(pagedHistory, pageable, filteredHistory.size());
    }

    public <T> List<HistoryResDTO> getAuditEntries(Class<T> entityClass, ToLongFunction<T> idExtractor,
                                              RevisionType revisionType, List<String> inputFields, String user, OffsetDateTime startDate, OffsetDateTime endDate) {
        AuditReader reader = AuditReaderFactory.get(entityManager);
        AuditQuery query = reader.createQuery()
                .forRevisionsOfEntity(entityClass, false, true)
                .addOrder(AuditEntity.revisionNumber().asc());

        if (user != null) query.add(AuditEntity.property("updatedBy").eq(user));

        if (startDate != null)
            query.add(AuditEntity.revisionProperty("timestamp").ge(startDate.toInstant().toEpochMilli()));

        if (endDate != null)
            query.add(AuditEntity.revisionProperty("timestamp").le(endDate.toInstant().toEpochMilli()));

        query.add(AuditEntity.property("tenant").eq(tenantResolver.resolveCurrentTenantIdentifier()));

        @SuppressWarnings("unchecked")
        List<Object[]> results = query.getResultList();

        Map<Long, List<Object[]>> grouped = results.stream()
                .collect(Collectors.groupingBy(o -> idExtractor.applyAsLong((T) o[0])));

        return extractHistory(grouped, entityClass, inputFields, revisionType);
    }

    private <T> List<HistoryResDTO> extractHistory(Map<Long, List<Object[]>> grouped, Class<T> clazz,
                                                   List<String> inputFields, RevisionType revisionType) {
        List<HistoryResDTO> history = new ArrayList<>();
        for (Map.Entry<Long, List<Object[]>> entry : grouped.entrySet()) {
            Long entityId = entry.getKey();
            List<Object[]> revisions = entry.getValue();
            T previous = null;
            String referenceId = null;

            for (Object[] row : revisions) {
                T current = (T) row[0];
                CustomRevisionEntity revisionEntity = (CustomRevisionEntity) row[1];
                Hibernate.initialize(revisionEntity);

                if (revisionEntity.getUniqueId() != null) {
                    referenceId = revisionEntity.getUniqueId();
                }

                RevisionType revType = (RevisionType) row[2];
                String updatedBy = (String) getFieldValue(current, "updatedBy");
                OffsetDateTime updatedAt = (OffsetDateTime) getFieldValue(current, "updatedAt");
                    for (Field field : getAllFields(clazz, inputFields)) {
                        field.setAccessible(true);
                        try {
                            Object oldValue = previous != null ? field.get(previous) : null;
                            Object newValue = field.get(current);
                            if(revisionType !=null && !revType.equals(revisionType)) continue;
                            if (revType.equals(RevisionType.DEL)) {
                                history.add(new HistoryResDTO(entityId, referenceId, revType.name(),
                                        field.getName(), newValue != null ? newValue.toString() : null,
                                        null, updatedBy, updatedAt));
                            } else if (!Objects.equals(oldValue, newValue)) {
                                history.add(new HistoryResDTO(entityId, referenceId, revType.name(),
                                        field.getName(),
                                        oldValue != null ? oldValue.toString() : null,
                                        newValue != null ? newValue.toString() : null,
                                        updatedBy, updatedAt));
                            }
                        } catch (IllegalAccessException ignored) {
                            // Ignore fields that cannot be accessed
                        }
                    }
                previous = current;
            }
        }
        return history;
    }

    private Object getFieldValue(Object entity, String fieldName) {
        Class<?> clazz = entity.getClass();
        while (clazz != null) {
            try {
                Field field = clazz.getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(entity);
            } catch (NoSuchFieldException ignored) {
                clazz = clazz.getSuperclass();
            } catch (IllegalAccessException ignored) {
                break;
            }
        }
        return null;
    }

    private List<Field> getAllFields(Class<?> clazz, List<String> inputFields) {
        List<Field> filteredFields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                if (inputFields.contains(field.getName())) {
                    filteredFields.add(field);
                }
            }
            clazz = clazz.getSuperclass();
        }
        return filteredFields;
    }

    public List<String> getCategories() {
        return Arrays.stream(AuditCategory.values())
                .map(Enum::name)
                .toList();
    }
}
