package uk.co.flexi.ri.service;

import org.hibernate.cfg.MultiTenancySettings;
import org.hibernate.context.spi.CurrentTenantIdentifierResolver;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class TenantIdentifierResolver implements CurrentTenantIdentifierResolver<Long>, HibernatePropertiesCustomizer {

    private static final ThreadLocal<Long> CURRENT_TENANT = new ThreadLocal<>();

    public void setCurrentTenant(Long tenant) {
        CURRENT_TENANT.set(tenant);
    }

    public void clear() {
        CURRENT_TENANT.remove();
    }

    @Override
    public Long resolveCurrentTenantIdentifier() {
        return CURRENT_TENANT.get() != null ? CURRENT_TENANT.get() : -1L;
    }

    @Override
    public boolean validateExistingCurrentSessions() {
        return false;
    }

    @Override
    public void customize(Map<String, Object> hibernateProperties) {
        hibernateProperties.put(MultiTenancySettings.MULTI_TENANT_IDENTIFIER_RESOLVER, this);
    }
}
