package uk.co.flexi.ri.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.exception.custom.OMSException;
import uk.co.flexi.ri.model.OMSProvider;
import uk.co.flexi.ri.repository.OMSProviderRepo;

import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;


@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class OMSProviderService {

    private static final String OMS_PROVIDER_NOT_FOUND = "OMS Provider not found";

    private final OMSProviderRepo omsProviderRepo;

    private final OrderLookUpService orderLookUpService;

    private final SearchStrategyRegistry strategyRegistry;

    public record OMSProviderInfo(OMSProvider omsProvider, List<String> searchStrategy) {}

    public OMSProviderService(OMSProviderRepo omsProviderRepo,
                              OrderLookUpService orderLookUpService,
                              SearchStrategyRegistry strategyRegistry) {
        this.omsProviderRepo = omsProviderRepo;
        this.orderLookUpService = orderLookUpService;
        this.strategyRegistry = strategyRegistry;
    }

    public List<String> getOMSList() {
        return omsProviderRepo.findAll().stream().map(OMSProvider::getProviderName).toList();
    }

    public OMSProviderInfo getOMSProviderInfo() {
        return omsProviderRepo.findAll().stream()
                .findFirst()
                .map(provider -> new OMSProviderInfo(provider, provider.getSearchStrategy()))
                .orElseThrow(() -> new NoSuchElementException(OMS_PROVIDER_NOT_FOUND));
    }

    public OMSProviderInfo getOMSProviderInfo(String name) {
        return omsProviderRepo.findByProviderName(name)
                .map(provider -> new OMSProviderInfo(provider, provider.getSearchStrategy()))
                .orElseThrow(() -> new NoSuchElementException(OMS_PROVIDER_NOT_FOUND));
    }

    public List<String> getSearchStrategies(String name) {
        OMSProvider omsProvider = omsProviderRepo.findByProviderName(name).orElseThrow(() -> new NoSuchElementException(
                OMS_PROVIDER_NOT_FOUND));
        return omsProvider.getSearchStrategy();
    }

    public List<String> getAllSearchStrategies(String name) {
        OMSClientService.OMSType omsType;
        try {
            omsType = OMSClientService.OMSType.valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new NoSuchElementException(
                    "Invalid provider: " + name);
        }
        return strategyRegistry.getStrategiesFor(omsType).stream().map(Enum::name)
                .toList();
    }

    public List<String> addSearchStrategies(String name, List<String> strategies) {
        OMSProvider omsProvider = omsProviderRepo.findByProviderName(name).orElseThrow(() -> new NoSuchElementException(
                OMS_PROVIDER_NOT_FOUND));
        omsProvider.setSearchStrategy(strategies);
        omsProviderRepo.save(omsProvider);
        return strategies;
    }

    public Map<String, String> testConnection(String name) {
        OMSProviderService.OMSProviderInfo omsProviderInfo = getOMSProviderInfo(name);
        OMSProvider omsProvider = omsProviderInfo.omsProvider();
        List<String> searchStrategy = omsProviderInfo.searchStrategy();
        try {
            orderLookUpService.orderLookUp(new OrderLookUpService.OMSSearchData("test",
                    searchStrategy), omsProvider);
            return Map.of("status", "success", "message", "Connection successful");
        }catch(Exception e){
            throw new OMSException("OMS Connection failed");
        }
    }
}
