package uk.co.flexi.ri.service;

import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.EventQueueDTO;
import uk.co.flexi.ri.model.EventQueue;
import uk.co.flexi.ri.integration.repository.EventConfigRepo;
import uk.co.flexi.ri.repository.EventQueueRepo;

import java.util.List;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@AllArgsConstructor
public class EventQueueService {

    private final ModelMapper modelMapper;

    private final EventQueueRepo eventQueueRepo;

    private final EventConfigRepo eventConfigRepo;

    public void save(EventQueueDTO eventQueueDTO) {
        List<Long> optionalIds = eventConfigRepo.findIdByTenantId(eventQueueDTO.getTenant(),
                eventQueueDTO.getEventType().name());
        optionalIds.forEach(optionalId -> {
            EventQueue eventQueue = modelMapper.map(eventQueueDTO, EventQueue.class);
            eventQueue.setConfigId(optionalId);
            eventQueueRepo.save(eventQueue);
        });
    }
}
