package uk.co.flexi.ri.service;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class FileFormatServiceFactory {

    private final Map<String, FileFormatService> formatMap;

    public FileFormatServiceFactory(List<FileFormatService> services) {
        this.formatMap = services.stream()
                .flatMap(service -> service.getFileExtensions().stream().map(format -> Map.entry(format, service)))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    public FileFormatService getService(String fileExtension) {
        return Optional.ofNullable(formatMap.get(fileExtension))
                .orElseThrow(() -> new IllegalArgumentException("Unsupported file extension: " + fileExtension));
    }
}
