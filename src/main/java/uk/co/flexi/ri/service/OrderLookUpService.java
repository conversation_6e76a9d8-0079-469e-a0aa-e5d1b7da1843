package uk.co.flexi.ri.service;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.model.OMSProvider;
import uk.co.flexi.ri.model.SearchStrategy;
import uk.co.flexi.sdk.oms.mao.client.OMSClient;
import uk.co.flexi.sdk.oms.model.OrderData;

import java.util.List;
import java.util.Set;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class OrderLookUpService {

    private final SearchStrategyRegistry strategyRegistry;

    private final OMSClientService omsClientService;

    public OrderData orderLookUp(OMSSearchData searchData, OMSProvider omsProvider){
        OMSClientService.OMSType omsType = OMSClientService.OMSType.valueOf(omsProvider.getProviderName().toUpperCase());
        OMSClient<?> client = omsClientService.getClient(omsType, omsProvider);
        Set<SearchStrategy> validStrategies = strategyRegistry.getStrategiesFor(omsType);

        for (String strategyName : searchData.searchStrategy) {
            try {
                SearchStrategy strategy = SearchStrategy.valueOf(strategyName.toUpperCase());
                if (validStrategies.contains(strategy)) {
                    OrderData orderData = strategy.apply(client, searchData.input);
                    if (orderData != null) return orderData;
                }
            } catch (IllegalArgumentException e) {
                throw new UnsupportedOperationException("Unsupported search strategy: " + strategyName);
            }
        }
        return null;
    }

    public record OMSSearchData(String input, List<String> searchStrategy) {}
}
