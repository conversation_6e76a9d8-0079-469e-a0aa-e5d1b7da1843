package uk.co.flexi.ri.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.InspectionCompletedDTO;
import uk.co.flexi.ri.dto.ItemConditionResponseDTO;
import uk.co.flexi.ri.dto.TimeTrackerDTO;
import uk.co.flexi.ri.dto.TreeMapResponse;
import uk.co.flexi.ri.dto.TreeMapResponseDTO;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.repository.view.ItemConditionViewRepo;
import uk.co.flexi.ri.repository.view.InspectionCompletedViewRepo;
import uk.co.flexi.ri.repository.view.ReturnReasonViewRepo;
import uk.co.flexi.ri.repository.view.TimeTrackerViewRepo;
import uk.co.flexi.ri.repository.view.TopProductReturnViewRepo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class DashboardService {

    private static final String COUNT = "count";

    private static final String TOTAL = "total";

    private static final String PERCENTAGE = "percentage";

    private final ReturnReasonViewRepo returnReasonViewRepo;

    private final ItemConditionViewRepo itemConditionViewRepo;

    private final InspectionCompletedViewRepo inspectionCompletedViewRepo;

    private final AuditService auditService;

    private final TopProductReturnViewRepo topProductReturnViewRepo;

    private final TimeTrackerViewRepo timeTrackerViewRepo;

    public TreeMapResponseDTO getCommonReturnReasons(LocalDate startDate, LocalDate endDate, String filterValue, Integer level) {
        List<TreeMapResponse> responses;
        String[] filters = filterValue != null ? filterValue.split(",") : new String[0];
        responses = switch (level) {
            case 0 -> returnReasonViewRepo.findGroupedDataByReturnReason(startDate, endDate);
            case 1 -> filters.length > 0 ?
                    returnReasonViewRepo.findGroupedDataByProductClass(startDate, endDate, filters[0]) : List.of();
            case 2 -> filters.length > 1 ?
                    returnReasonViewRepo.findGroupedDataByProductSubClass(startDate, endDate, filters[0], filters[1]) : List.of();
            case 3 -> filters.length > 2 ?
                    returnReasonViewRepo.findGroupedDataByProductStyle(startDate, endDate, filters[0], filters[1], filters[2]) : List.of();
            case 4 -> filters.length > 3 ?
                    returnReasonViewRepo.findGroupedDataByProductSku(startDate, endDate, filters[0], filters[1], filters[2], filters[3]) : List.of();
            default -> throw new IllegalArgumentException("Invalid groupBy or filterValue");
        };
        long totalValue = responses.stream().mapToLong(TreeMapResponse::getValue).sum();
        return new TreeMapResponseDTO(totalValue, responses);
    }

    public List<ItemConditionResponseDTO> getCommonItemCondition(LocalDate startDate, LocalDate endDate) {
        return itemConditionViewRepo.findGroupedData(startDate, endDate);
    }

    public Map<String, Map<String, Map<String, Map<String, Object>>>> getItemConditionGroupedByChannelAndCategory(LocalDate startDate, LocalDate endDate, String groupBy) throws JsonProcessingException {
        List<Object[]> results;
        if ("productClass".equalsIgnoreCase(groupBy)) {
            results = itemConditionViewRepo.findGroupedDataByProductClass(startDate, endDate);
        } else {
            results = itemConditionViewRepo.findGroupedDataByChannel(startDate, endDate);
        }

        Map<String, Map<String, Map<String, Map<String, Object>>>> response = new LinkedHashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();

        for (Object[] row : results) {
            String groupData = (String) row[0];
            InspectionItem.InspectionItemStatus itemStatus = (InspectionItem.InspectionItemStatus) row[1];
            String conditionsJson = (String) row[2];
            Long total = (Long) row[3];
            BigDecimal totalPrice = (BigDecimal) row[4];
            Map<String, Map<String, Object>> itemConditions = objectMapper.readValue(conditionsJson, new TypeReference<>() {
            });
            Map<String, Object> totalEntry = new HashMap<>();
            totalEntry.put(COUNT, total);
            totalEntry.put("price", totalPrice);
            itemConditions.put(TOTAL, totalEntry);

            response
                    .computeIfAbsent(groupData, k -> new LinkedHashMap<>())
                    .put(itemStatus.name(), itemConditions);
        }

        return response;
    }

    public Map<String, Map<String, Object>> getInspectionStatusWithPercentage(LocalDate startDate, LocalDate endDate) {
        long createdCount = auditService.getCreatedCountByDate(startDate, endDate);
        long completedCount = auditService.getCompletedCountByDate(startDate, endDate);
        long sendForReviewCount = auditService.getSendForReviewCountByDate(startDate, endDate);
        long totalCount = createdCount + completedCount + sendForReviewCount;
        if (totalCount == 0) {
            return Map.of(
                    "created", Map.of(COUNT, createdCount, PERCENTAGE, "0.00"),
                    "completed", Map.of(COUNT, completedCount, PERCENTAGE, "0.00"),
                    "sendForReview", Map.of(COUNT, sendForReviewCount, PERCENTAGE, "0.00")
            );
        }
        return Map.of(
                "created", Map.of(COUNT, createdCount, PERCENTAGE,
                        formatPercentage((createdCount * 100.0) / totalCount)),
                "completed", Map.of(COUNT, completedCount, PERCENTAGE,
                        formatPercentage((completedCount * 100.0) / totalCount)),
                "sendForReview", Map.of(COUNT, sendForReviewCount, PERCENTAGE,
                        formatPercentage((sendForReviewCount * 100.0) / totalCount))
        );
    }

    private String formatPercentage(double value) {
        return BigDecimal.valueOf(value)
                .setScale(2, RoundingMode.HALF_UP)
                .toString();
    }

    public TreeMapResponseDTO getTopProductsReturns(LocalDate startDate, LocalDate endDate, String groupBy, String styleFilter,String skuFilter, Integer level) {
        List<TreeMapResponse> responses;
        responses = switch (level) {
            case 0 -> switch (groupBy) {
                case "style" -> topProductReturnViewRepo.findGroupedDataByProductStyle(startDate, endDate);
                case "sku" -> topProductReturnViewRepo.findGroupedDataByProductSku(startDate, endDate);
                default -> throw new IllegalArgumentException("Invalid groupBy value: " + groupBy);
            };
            case 1 -> switch (groupBy) {
                case "reason" -> topProductReturnViewRepo.findGroupedDataByReturnReason(startDate, endDate,
                        styleFilter, skuFilter);
                case "channel" -> topProductReturnViewRepo.findGroupedDataByReturnChannel(startDate, endDate, styleFilter, skuFilter);
                default -> throw new IllegalArgumentException("Invalid groupBy value: " + groupBy);
            };
            default -> throw new IllegalArgumentException("Invalid level");
        };
        long totalValue = responses.stream().mapToLong(TreeMapResponse::getValue).sum();
        return new TreeMapResponseDTO(totalValue, responses);
    }

    public Map<String, Object> getInspectionTimeTracker(LocalDate startDate, LocalDate endDate) {
        List<Object[]> result = timeTrackerViewRepo.getInspectionCompletionTime(startDate, endDate);
        int total = result.stream().mapToInt(r -> ((Number) r[1]).intValue()).sum();
        List<TimeTrackerDTO> data = result.stream().map(r -> {
            String label = (String) r[0];
            long value = ((Number) r[1]).longValue();
            double percentage = total > 0 ? (value * 100.0) / total : 0.0;
            return new TimeTrackerDTO(label, value, percentage);
        }).toList();
        return Map.of(
                TOTAL, total,
                "data", data
        );
    }

    public InspectionCompletedDTO getInspectionCompletedStatus(LocalDate startDate, LocalDate endDate, String granularity) {
        List<Object[]> results = inspectionCompletedViewRepo.findCompletedStatus(startDate, endDate, granularity);
        Map<String, Map<String, Object>> dataMap = new HashMap<>();
        Map<String, Object> highestMap = new HashMap<>();
        List<String> highestDates = new ArrayList<>();
        int highestTotal = 0;
        for (Object[] row : results) {
            String period = row[0].toString();
            String user = row[1].toString();
            int count = ((Number) row[2]).intValue();
            dataMap.putIfAbsent(period, new HashMap<>());
            Map<String, Object> userStats = dataMap.get(period);
            userStats.put(user, count);
            userStats.put(TOTAL, userStats.getOrDefault(TOTAL, 0));
            int currentTotal = (int) userStats.get(TOTAL) + count;
            userStats.put(TOTAL, currentTotal);
            int userCount = userStats.containsKey("average") ? userStats.size() - 2 : userStats.size() - 1;
            double average = userCount > 0 ? (double) currentTotal / userCount : 0;
            userStats.put("average", formatPercentage(average));
            if (currentTotal > highestTotal) {
                highestTotal = currentTotal;
                highestDates.clear();
                highestDates.add(period);
            } else if (currentTotal == highestTotal) {
                highestDates.add(period);
            }
        }
        highestMap.put("date", highestDates);
        highestMap.put(TOTAL, highestTotal);
        return new InspectionCompletedDTO(dataMap, highestMap);
    }
}
