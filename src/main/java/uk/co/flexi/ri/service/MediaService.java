package uk.co.flexi.ri.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.client.MediaClient;
import uk.co.flexi.ri.client.factory.MediaClientFactory;
import uk.co.flexi.ri.dto.MediaDTO;
import uk.co.flexi.ri.exception.custom.MediaUploadException;
import uk.co.flexi.ri.exception.custom.OrderNotFoundException;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.model.Media;
import uk.co.flexi.ri.model.TempMedia;
import uk.co.flexi.ri.repository.CommentRepo;
import uk.co.flexi.ri.repository.MediaRepo;
import uk.co.flexi.ri.repository.InspectionItemRepo;
import uk.co.flexi.ri.repository.TempMediaRepo;
import uk.co.flexi.ri.security.service.JWTService;
import uk.co.flexi.ri.util.Constants;
import uk.co.flexi.ri.util.MediaUtil;
import uk.co.flexi.ri.util.Util;

import java.io.IOException;
import java.io.InputStream;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.NoSuchElementException;


@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
@Slf4j
public class MediaService {

    private static final String MEDIA_CLIENT = "minio";

    private static final float MEDIA_IMAGE_QUALITY = 0.7f;

    private final MediaRepo mediaRepo;

    private final MediaClientFactory mediaClientFactory;

    private final ModelMapper modelMapper;

    private final CommentRepo commentRepo;

    private final InspectionItemRepo inspectionItemRepo;

    private final AuthenticatedUserService authenticatedUserService;

    private final JWTService jwtService;

    private final TempMediaRepo tempMediaRepo;

    private final MerchantService merchantService;

    public Media findByMediaId(String mediaId) {
        return mediaRepo.findByMediaId(mediaId);
    }

    public Media save(Media media) {
        return mediaRepo.save(media);
    }

    private void handleInterruptedException(Exception e) {
        if (e instanceof InterruptedException || (e.getCause() instanceof InterruptedException)) {
            Thread.currentThread().interrupt();
        }
    }

    private MediaClient getMediaClient() {
        try {
            return mediaClientFactory.getClient(MEDIA_CLIENT + Constants.MEDIA_CLIENT);
        } catch (Exception e) {
            handleInterruptedException(e);
            throw new IllegalStateException("Failed to initialize media client", e);
        }
    }

    public MultipartFile compressFileIfNeeded(MultipartFile file) throws IOException {
        if (MediaUtil.isImage(file.getContentType())) {
            return MediaUtil.compressImage(file, MEDIA_IMAGE_QUALITY);
        } else if (MediaUtil.isVideo(file.getContentType())) {
            return MediaUtil.compressVideo(file);
        }
        return file;
    }

    private TempMedia uploadAndBuildTempMedia(MultipartFile file, MediaClient mediaClient, String merchantName, String inspectionItemId, String userId, Long tenant) {
        MultipartFile fileToUpload = file;
        try {
            fileToUpload = compressFileIfNeeded(file);
        } catch (Exception e) {
            handleInterruptedException(e);
            log.warn("Compression failed for temp file: {}. Uploading original.", file.getOriginalFilename(), e);
        }
        try {
            String response = mediaClient.putFile(fileToUpload, MediaClient.FileGroup.TEMP_IMAGE, getBucketName(merchantName));
            TempMedia tempMedia = new TempMedia();
            tempMedia.setInspectionItemId(inspectionItemId);
            tempMedia.setUserId(userId);
            tempMedia.setMediaType(fileToUpload.getContentType());
            tempMedia.setFileName(fileToUpload.getOriginalFilename());
            tempMedia.setUrl(response);
            tempMedia.setTenant(tenant);
            tempMedia.setCreatedAt(OffsetDateTime.now());
            return tempMedia;
        } catch (Exception e) {
            handleInterruptedException(e);
            log.error("Failed to upload file: {}", file.getOriginalFilename(), e);
            return null;
        }
    }

    public void uploadTempFile(List<MultipartFile> files, String token) {
        if (files == null || files.isEmpty()) {
            throw new IllegalArgumentException("File list is empty or null");
        }
        if (token == null || token.isBlank()) {
            throw new IllegalArgumentException("Token is null or blank");
        }
        String inspectionItemId;
        String userId;
        Long tenant;
        String merchantName;
        try {
            inspectionItemId = jwtService.extractSubject(token);
            userId = jwtService.extractUserId(token);
            tenant = Long.valueOf(jwtService.extractTenant(token));
            merchantName = merchantService.findByTenant(tenant).getMerchantName();
        } catch (Exception e) {
            handleInterruptedException(e);
            throw new IllegalStateException("Failed to extract authentication details from token", e);
        }
        MediaClient mediaClient = getMediaClient();
        List<TempMedia> mediaList = new ArrayList<>();
        for (MultipartFile file : files) {
            if (file == null || file.isEmpty()) {
                continue;
            }
            TempMedia tempMedia = uploadAndBuildTempMedia(file, mediaClient, merchantName, inspectionItemId, userId, tenant);
            if (tempMedia != null) {
                mediaList.add(tempMedia);
            }
        }
        if (!mediaList.isEmpty()) {
            try {
                tempMediaRepo.saveAll(mediaList);
            } catch (Exception e) {
                handleInterruptedException(e);
                throw new MediaUploadException("Failed to save media records", e);
            }
        }
    }

    public List<MediaDTO> uploadFile(List<MultipartFile> files, MediaDTO mediaDTO) throws IOException {
        String merchantName = authenticatedUserService.getAuthenticatedUserDetails().getMerchantName();
        MediaClient mediaClient = mediaClientFactory.getClient(MEDIA_CLIENT + Constants.MEDIA_CLIENT);
        List<Media> mediaList = new ArrayList<>();
        for (MultipartFile file : files) {
            MultipartFile fileToUpload = file;
            try {
                fileToUpload = compressFileIfNeeded(file);
            } catch (Exception e) {
                handleInterruptedException(e);
                log.warn("Compression failed for file: {}. Uploading original.", file.getOriginalFilename(), e);
            }
            String response = mediaClient.putFile(fileToUpload, MediaClient.FileGroup.COMMENT, getBucketName(merchantName));
            mediaList.add(buildMediaEntity(fileToUpload, mediaDTO, response));
        }
        if (!mediaList.isEmpty())
            mediaList = mediaRepo.saveAll(mediaList);
        return mediaList.stream()
                .map(media -> {
                    MediaDTO savedmediaDTO = modelMapper.map(media, MediaDTO.class);
                    savedmediaDTO.setCommentId(media.getComment().getCommentId());
                    savedmediaDTO.setInspectionItemId(media.getInspectionItem().getInspectionItemId());
                    return savedmediaDTO;
                })
                .toList();
    }

    public MultipartFile getCompressedOrOriginal(MultipartFile file, String logContext) {
        try {
            return compressFileIfNeeded(file);
        } catch (Exception e) {
            handleInterruptedException(e);
            log.warn("Compression failed for {}. Uploading original.", logContext, e);
            return file;
        }
    }

    public Media uploadSSOProfileImage(String url, String merchantName) {
        try {
            MediaClient mediaClient = mediaClientFactory.getClient(MEDIA_CLIENT + Constants.MEDIA_CLIENT);
            MultipartFile file = Util.convertUrlToMultipartFile(url, "profileimage.png", "image/png");
            MultipartFile fileToUpload = getCompressedOrOriginal(file, "SSO profile image");
            String response = mediaClient.putFile(fileToUpload, MediaClient.FileGroup.PROFILE_PIC, getBucketName(merchantName));
            Media media = buildMediaEntity(fileToUpload, null, response);
            return mediaRepo.save(media);
        } catch (Exception ex) {
            handleInterruptedException(ex);
            throw new MediaUploadException("Failed to upload SSO profile image", ex);
        }
    }

    public Media uploadProfileImage(MultipartFile file) {
        try {
            String merchantName = authenticatedUserService.getAuthenticatedUserDetails().getMerchantName();
            MediaClient mediaClient = mediaClientFactory.getClient(MEDIA_CLIENT + Constants.MEDIA_CLIENT);
            MultipartFile fileToUpload = getCompressedOrOriginal(file, "profile image");
            String response = mediaClient.putFile(fileToUpload, MediaClient.FileGroup.PROFILE_PIC, getBucketName(merchantName));
            Media media = buildMediaEntity(fileToUpload, null, response);
            return mediaRepo.save(media);
        } catch (Exception ex) {
            handleInterruptedException(ex);
            throw new MediaUploadException("Failed to upload profile image", ex);
        }
    }

    public ResponseEntity<InputStreamResource> downloadFile(String fileName, String mediaType) {
        String merchantName = authenticatedUserService.getAuthenticatedUserDetails().getMerchantName();
        try {
            MediaClient mediaClient = mediaClientFactory.getClient(MEDIA_CLIENT + Constants.MEDIA_CLIENT);
            InputStream fileStream = mediaClient.getFile(fileName, getBucketName(merchantName));
            return ResponseEntity.ok()
                    .contentType(MediaType.valueOf(mediaType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileName + "\"")
                    .body(new InputStreamResource(fileStream));
        } catch (Exception e) {
            handleInterruptedException(e);
            throw new MediaUploadException("Failed to fetch file", e);
        }
    }

    private Media buildMediaEntity(MultipartFile file, MediaDTO mediaDTO, String response) throws IOException {
        Media media = new Media();
        media.setMediaType(file.getContentType());
        media.setFileName(file.getOriginalFilename());
        media.setUrl(response);
        if (response == null) {
            media.setData(file.getBytes());
        }
        if (mediaDTO != null) {
            Comment comment = commentRepo.findByCommentId(mediaDTO.getCommentId())
                    .orElseThrow(() -> new OrderNotFoundException("Comment with " + mediaDTO.getCommentId() + " not found."));
            media.setComment(comment);

            InspectionItem inspectionItem = inspectionItemRepo.findByInspectionItemId(mediaDTO.getInspectionItemId())
                    .orElseThrow(() -> new NoSuchElementException("InspectionItem not found with id: " + mediaDTO.getInspectionItemId()));
            media.setInspectionItem(inspectionItem);
        }
        return media;
    }

    public void deleteFile(String mediaId) {
        String merchantName = authenticatedUserService.getAuthenticatedUserDetails().getMerchantName();
        Media media = mediaRepo.findByMediaId(mediaId);
        long count = mediaRepo.countByInspectionItemAndUrl(media.getInspectionItem(), media.getUrl());
        String filename = media.getUrl();
        MediaClient mediaClient = mediaClientFactory.getClient(MEDIA_CLIENT + Constants.MEDIA_CLIENT);
        try {
            if (count <= 1) {
                mediaClient.deleteFile(filename, getBucketName(merchantName));
            }
            mediaRepo.deleteById(media.getId());
        } catch (Exception e) {
            handleInterruptedException(e);
            throw new MediaUploadException("Failed to delete file", e);
        }
    }

    public String getBucketName(String merchantName) {
        String bucketName = Util.extractValidCharacters(merchantName) + "-media";
        MediaClient mediaClient = mediaClientFactory.getClient(MEDIA_CLIENT + Constants.MEDIA_CLIENT);
        mediaClient.createBucketNotExist(bucketName);
        return bucketName;
    }

    @Scheduled(cron = "0 0 2 * * *") // Runs every day at 2:00 AM
    public void deleteOldTempMedia() {
        OffsetDateTime cutoff = OffsetDateTime.now().minusDays(7);
        log.info("Running TempMedia cleanup job. Deleting records older than {}", cutoff);
        tempMediaRepo.deleteByCreatedAtBefore(cutoff);
    }
}
