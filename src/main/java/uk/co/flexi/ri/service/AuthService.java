package uk.co.flexi.ri.service;

import uk.co.flexi.ri.dto.AuthReqDTO;
import uk.co.flexi.ri.dto.AuthResDTO;
import uk.co.flexi.ri.dto.ImageTokenReqDTO;
import uk.co.flexi.ri.dto.RefreshTokenReqDTO;
import uk.co.flexi.ri.dto.UpdateTokenReqDTO;
import uk.co.flexi.ri.exception.custom.RIAuthenticationException;
import uk.co.flexi.ri.model.User;
import uk.co.flexi.ri.model.UserGroup;
import uk.co.flexi.ri.repository.UserRepo;
import uk.co.flexi.ri.security.service.JWTService;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import uk.co.flexi.ri.util.Constants;

import java.util.HashMap;
import java.util.Map;

@Service
public class AuthService {

    private final JWTService jwtService;

    private final AuthenticationManager authenticationManager;

    private final UserRepo userRepo;

    public AuthService(JWTService jwtService,
                       AuthenticationManager authenticationManager,
                       UserRepo userRepo) {
        this.jwtService = jwtService;
        this.authenticationManager = authenticationManager;
        this.userRepo = userRepo;
    }

    public AuthResDTO authenticate(AuthReqDTO authReqDTO) throws RIAuthenticationException {
        try {
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(authReqDTO.getUserName(), authReqDTO.getPassword()));

            if (!authentication.isAuthenticated()) {
                throw new RIAuthenticationException("Authentication failed. Please check your credentials.");
            }

            User user = userRepo.findByUserNameAndIsActive(authReqDTO.getUserName(), Boolean.TRUE).stream()
                    .findFirst()
                    .orElseThrow(() -> new RIAuthenticationException("Active user not found."));

            UserGroup userGroup = user.getUserGroups().stream()
                    .findFirst()
                    .orElseThrow(() -> new RIAuthenticationException("User group not found for user."));

            String token = jwtService.generateToken(user, userGroup.getUserGroupId());
            String refreshToken = jwtService.generateRefreshToken(user, userGroup.getUserGroupId());

            return new AuthResDTO(token, refreshToken);

        } catch (BadCredentialsException ex) {
            throw new RIAuthenticationException("Authentication failed. Please check your credentials.");
        }
    }

    public AuthResDTO generateRefreshToken(RefreshTokenReqDTO refreshTokenReqDTO) throws RIAuthenticationException {
        String refreshToken = refreshTokenReqDTO.getRefreshToken();

        if (!Boolean.TRUE.equals(jwtService.validateToken(refreshToken))) {
            throw new RIAuthenticationException("Invalid refresh token.");
        }

        String userName = jwtService.extractSubject(refreshToken);
        Long tenant = Long.valueOf(jwtService.extractTenant(refreshToken));

        User user = userRepo.findByUserNameAndIsActive(userName, tenant, Boolean.TRUE)
                .orElseThrow(() -> new RIAuthenticationException("Active user not found for refresh token."));

        UserGroup userGroup = user.getUserGroups().stream()
                .findFirst()
                .orElseThrow(() -> new RIAuthenticationException("User group not found for user."));

        String newAccessToken = jwtService.generateToken(user, userGroup.getUserGroupId());
        String newRefreshToken = jwtService.generateRefreshToken(user, userGroup.getUserGroupId());

        return new AuthResDTO(newAccessToken, newRefreshToken);
    }

    public AuthResDTO regenerateToken(UpdateTokenReqDTO dto) throws RIAuthenticationException {
        User user = userRepo.findByUserNameAndIsActive(dto.getUserName(), dto.getTenant(), Boolean.TRUE)
                .orElseThrow(() -> new RIAuthenticationException("Active user not found for merchant"));

        UserGroup userGroup = user.getUserGroups().stream()
                .findFirst()
                .orElseThrow(() -> new RIAuthenticationException("User group not found for user"));

        String userGroupId = userGroup.getUserGroupId();
        String newAccessToken = jwtService.generateToken(user, userGroupId);
        String newRefreshToken = jwtService.generateRefreshToken(user, userGroupId);

        return new AuthResDTO(newAccessToken, newRefreshToken);
    }

    public String generateImageToken(ImageTokenReqDTO reqDTO) throws RIAuthenticationException {
        User user = userRepo.findByUserId(reqDTO.getUserId())
                .orElseThrow(() -> new RIAuthenticationException("User not found..."));
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", reqDTO.getUserId());
        claims.put(Constants.TENANT, user.getTenant());
        return jwtService.generateCustomToken(reqDTO.getInspectionItemId(), claims);
    }
}
