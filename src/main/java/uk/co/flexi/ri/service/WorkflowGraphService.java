package uk.co.flexi.ri.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.SankeyChartDTO;
import uk.co.flexi.ri.dto.WorkflowGraphDTO;
import uk.co.flexi.ri.model.AuditEvent;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.repository.AuditEventRepo;
import uk.co.flexi.ri.repository.InspectionRepo;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;

/**
 * Service for building and analyzing inspection workflow graphs.
 * Uses graph data structures to efficiently model workflow transitions.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkflowGraphService {

    private final InspectionRepo inspectionRepo;
    private final AuditEventRepo auditEventRepo;
    private final AuthenticatedUserService authenticatedUserService;

    /**
     * Builds a workflow graph from inspection data for the specified period.
     */
    public WorkflowGraphDTO buildWorkflowGraph(LocalDate startDate, LocalDate endDate) {
        log.info("Building workflow graph for period: {} to {}", startDate, endDate);

        AuthenticatedUserDTO user = authenticatedUserService.getAuthenticatedUserDetails();
        Long tenantId = user.getTenant();

        WorkflowGraphDTO graph = new WorkflowGraphDTO();

        // Initialize standard workflow nodes
        initializeStandardNodes(graph);

        // Add transitions from inspection data
        addInspectionTransitions(graph, startDate, endDate, tenantId);

        // Add transitions from audit events
        addAuditEventTransitions(graph, startDate, endDate, tenantId);

        log.info("Built workflow graph with {} nodes and {} edges", 
                graph.getNodes().size(), graph.getEdges().size());

        return graph;
    }

    /**
     * Generates Sankey chart data from workflow graph.
     */
    public SankeyChartDTO generateSankeyChart(LocalDate startDate, LocalDate endDate) {
        WorkflowGraphDTO graph = buildWorkflowGraph(startDate, endDate);
        return graph.toSankeyChart();
    }

    /**
     * Generates sample Sankey chart data for demonstration.
     */
    public SankeyChartDTO generateSampleSankeyChart() {
        WorkflowGraphDTO graph = new WorkflowGraphDTO();

        // Add sample nodes
        Map<String, Object> metadata = new HashMap<>();
        graph.addNode("Started", "initial_state", metadata);
        graph.addNode("Sent for Review", "review_state", metadata);
        graph.addNode("Completed w/o Review", "direct_completion", metadata);
        graph.addNode("Supervisor", "user_group", metadata);
        graph.addNode("Client Services", "user_group", metadata);
        graph.addNode("Marketplace Users", "user_group", metadata);
        graph.addNode("Approved", "final_state", metadata);
        graph.addNode("Rejected", "final_state", metadata);

        // Add sample edges with weights (matching your example data)
        graph.addEdge("Started", "Sent for Review", 16, metadata);
        graph.addEdge("Started", "Completed w/o Review", 9, metadata);
        graph.addEdge("Sent for Review", "Supervisor", 8, metadata);
        graph.addEdge("Sent for Review", "Client Services", 5, metadata);
        graph.addEdge("Sent for Review", "Marketplace Users", 3, metadata);
        graph.addEdge("Supervisor", "Client Services", 1, metadata);
        graph.addEdge("Supervisor", "Marketplace Users", 1, metadata);
        graph.addEdge("Client Services", "Marketplace Users", 1, metadata);
        graph.addEdge("Supervisor", "Approved", 5, metadata);
        graph.addEdge("Supervisor", "Rejected", 4, metadata);
        graph.addEdge("Client Services", "Approved", 3, metadata);
        graph.addEdge("Client Services", "Rejected", 2, metadata);
        graph.addEdge("Marketplace Users", "Approved", 3, metadata);
        graph.addEdge("Marketplace Users", "Rejected", 2, metadata);
        graph.addEdge("Completed w/o Review", "Approved", 5, metadata);
        graph.addEdge("Completed w/o Review", "Rejected", 4, metadata);

        return graph.toSankeyChart();
    }

    /**
     * Initializes standard workflow nodes.
     */
    private void initializeStandardNodes(WorkflowGraphDTO graph) {
        Map<String, Object> metadata = new HashMap<>();

        // Initial states
        graph.addNode("Started", "initial_state", metadata);

        // Review states
        graph.addNode("Sent for Review", "review_state", metadata);
        graph.addNode("Completed w/o Review", "direct_completion", metadata);

        // User group states (these would be dynamically loaded from database)
        graph.addNode("Supervisor", "user_group", metadata);
        graph.addNode("Client Services", "user_group", metadata);
        graph.addNode("Marketplace Users", "user_group", metadata);
        graph.addNode("Admin", "user_group", metadata);

        // Final states
        graph.addNode("Approved", "final_state", metadata);
        graph.addNode("Rejected", "final_state", metadata);
        graph.addNode("Completed", "final_state", metadata);
    }

    /**
     * Adds transitions from inspection data.
     */
    private void addInspectionTransitions(WorkflowGraphDTO graph, LocalDate startDate, LocalDate endDate, Long tenantId) {
        OffsetDateTime startDateTime = startDate.atStartOfDay(ZoneOffset.UTC).toOffsetDateTime();
        OffsetDateTime endDateTime = endDate.plusDays(1).atStartOfDay(ZoneOffset.UTC).toOffsetDateTime();

        // Query inspections created in the period
        List<Inspection> inspections = inspectionRepo.findAll().stream()
                .filter(inspection -> inspection.getTenant().equals(tenantId))
                .filter(inspection -> inspection.getCreatedAt().isAfter(startDateTime) && 
                                    inspection.getCreatedAt().isBefore(endDateTime))
                .toList();

        for (Inspection inspection : inspections) {
            processInspectionWorkflow(graph, inspection);
        }
    }

    /**
     * Processes workflow transitions for a single inspection.
     */
    private void processInspectionWorkflow(WorkflowGraphDTO graph, Inspection inspection) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("inspectionId", inspection.getInspectionId());
        metadata.put("referenceId", inspection.getReferenceId());

        // Started -> Assignment or Direct Completion
        String initialTarget = determineInitialTarget(inspection);
        graph.addEdge("Started", initialTarget, 1, metadata);

        // Process assignment transitions
        if (inspection.getAssigneeGroup() != null) {
            String assigneeGroupName = inspection.getAssigneeGroup().getName();
            
            // Add user group node if not exists
            graph.addNode(assigneeGroupName, "user_group", metadata);
            
            // If sent for review, add transition
            if ("Sent for Review".equals(initialTarget)) {
                graph.addEdge("Sent for Review", assigneeGroupName, 1, metadata);
            }
        }

        // Process completion transitions
        processCompletionTransitions(graph, inspection, metadata);
    }

    /**
     * Determines the initial target state for an inspection.
     */
    private String determineInitialTarget(Inspection inspection) {
        // Logic to determine if inspection was sent for review or completed directly
        if (inspection.getAssigneeGroup() != null && 
            !inspection.getAssigneeGroup().equals(inspection.getCreatedByGroup())) {
            return "Sent for Review";
        }
        return "Completed w/o Review";
    }

    /**
     * Processes completion transitions for inspection items.
     */
    private void processCompletionTransitions(WorkflowGraphDTO graph, Inspection inspection, Map<String, Object> metadata) {
        if (inspection.getInspectionItems() != null) {
            for (InspectionItem item : inspection.getInspectionItems()) {
                if (item.getItemStatus() != null) {
                    String sourceState = inspection.getAssigneeGroup() != null ? 
                            inspection.getAssigneeGroup().getName() : "Completed w/o Review";
                    
                    String targetState = switch (item.getItemStatus()) {
                        case APPROVED -> "Approved";
                        case REJECTED -> "Rejected";
                        case REVIEW -> "Sent for Review";
                    };
                    
                    graph.addEdge(sourceState, targetState, 1, metadata);
                }
            }
        }
    }

    /**
     * Adds transitions from audit events.
     */
    private void addAuditEventTransitions(WorkflowGraphDTO graph, LocalDate startDate, LocalDate endDate, Long tenantId) {
        OffsetDateTime startDateTime = startDate.atStartOfDay(ZoneOffset.UTC).toOffsetDateTime();
        OffsetDateTime endDateTime = endDate.plusDays(1).atStartOfDay(ZoneOffset.UTC).toOffsetDateTime();

        List<AuditEvent> auditEvents = auditEventRepo.findByCreatedAtBetweenAndTenant(startDateTime, endDateTime, tenantId);

        for (AuditEvent event : auditEvents) {
            processAuditEventTransition(graph, event);
        }
    }

    /**
     * Processes a single audit event transition.
     */
    private void processAuditEventTransition(WorkflowGraphDTO graph, AuditEvent event) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("eventType", event.getEventType().name());
        metadata.put("entityId", event.getEntityId());
        metadata.put("userId", event.getUserId());

        String sourceState = extractSourceState(event);
        String targetState = extractTargetState(event);

        if (sourceState != null && targetState != null) {
            graph.addEdge(sourceState, targetState, 1, metadata);
        }
    }

    /**
     * Extracts source state from audit event.
     */
    private String extractSourceState(AuditEvent event) {
        // Extract source state based on event type and details
        return switch (event.getEventType()) {
            case INSPECTION_ITEM_SEND_FOR_REVIEW -> "Started";
            case INSPECTION_ITEM_APPROVED, INSPECTION_ITEM_REJECTED -> 
                    event.getDetails().getOrDefault("assigneeGroup", "Unknown").toString();
            default -> null;
        };
    }

    /**
     * Extracts target state from audit event.
     */
    private String extractTargetState(AuditEvent event) {
        return switch (event.getEventType()) {
            case INSPECTION_ITEM_SEND_FOR_REVIEW -> "Sent for Review";
            case INSPECTION_ITEM_APPROVED -> "Approved";
            case INSPECTION_ITEM_REJECTED -> "Rejected";
            default -> null;
        };
    }
}
