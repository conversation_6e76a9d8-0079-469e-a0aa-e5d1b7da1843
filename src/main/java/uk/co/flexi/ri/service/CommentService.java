package uk.co.flexi.ri.service;

import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.CommentDTO;
import uk.co.flexi.ri.dto.CommentMediaUpdateDTO;
import uk.co.flexi.ri.dto.CommentMediaDTO;
import uk.co.flexi.ri.dto.MediaDTO;
import uk.co.flexi.ri.exception.custom.RIAuthenticationException;
import uk.co.flexi.ri.exception.custom.OrderNotFoundException;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.model.EventQueue;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.model.Media;
import uk.co.flexi.ri.model.User;
import uk.co.flexi.ri.repository.CommentRepo;
import uk.co.flexi.ri.repository.InspectionItemRepo;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.NoSuchElementException;

@Service
@Transactional(propagation = Propagation.REQUIRED,rollbackFor=Exception.class)
@AllArgsConstructor
public class CommentService {

    private final CommentRepo commentRepo;

    private final UserService userService;

    private final ModelMapper modelMapper;

    private final InspectionItemRepo inspectionItemRepo;

    private final MediaService mediaService;

    private final AuthenticatedUserService authenticatedUserService;

    private final EventPublisher eventPublisher;

    public CommentDTO save(CommentMediaDTO commentMediaDTO) throws IOException {
        AuthenticatedUserDTO authenticatedUser = authenticatedUserService.getAuthenticatedUserDetails();

        if (commentMediaDTO.getVisibility() == null) {
            commentMediaDTO.setVisibility(authenticatedUser.getUserGroup().getVisibility());
        }

        CommentDTO commentDTO = mapToCommentDTO(commentMediaDTO);
        Comment savedComment = commentRepo.save(mapToComment(commentDTO, authenticatedUser));

        List<MediaDTO> mediaDTOs = uploadMediaFiles(commentMediaDTO.getMediaFiles(), savedComment);
        List<MediaDTO> reffMediaDTOs = addMediaReference(commentMediaDTO.getMediaIds(), savedComment);

        List<MediaDTO> combinedMedia = new ArrayList<>();
        combinedMedia.addAll(mediaDTOs);
        combinedMedia.addAll(reffMediaDTOs);

        CommentDTO savedCommentDTO = modelMapper.map(savedComment, CommentDTO.class);
        savedCommentDTO.setMedia(combinedMedia); // set merged list
        savedCommentDTO.setInspectionItemId(commentMediaDTO.getInspectionItemId());

        eventPublisher.publish(savedComment, savedCommentDTO, EventQueue.EventType.INSP_ITEM_COMMENT_ADDED);
        return savedCommentDTO;
    }

    private CommentDTO mapToCommentDTO(CommentMediaDTO commentMediaDTO) {
        CommentDTO commentDTO = new CommentDTO();
        commentDTO.setContent(commentMediaDTO.getContent());
        commentDTO.setCommentType(commentMediaDTO.getCommentType());
        commentDTO.setVisibility(commentMediaDTO.getVisibility());
        commentDTO.setInspectionItemId(commentMediaDTO.getInspectionItemId());
        return commentDTO;
    }

    private Comment mapToComment(CommentDTO commentDTO, AuthenticatedUserDTO authenticatedUser) {
        Comment comment = modelMapper.map(commentDTO, Comment.class);
        User user = userService.findById(authenticatedUser.getId());
        InspectionItem inspectionItem =
                inspectionItemRepo.findByInspectionItemId(commentDTO.getInspectionItemId())
                .orElseThrow(() -> new NoSuchElementException("InspectionItem not found with id: " + commentDTO.getInspectionItemId()));
        comment.setInspectionItem(inspectionItem);
        comment.setAuthor(user);
        return comment;
    }

    public CommentDTO update(String commentId, CommentMediaUpdateDTO commentMediaUpdateDTO) throws IOException {
        AuthenticatedUserDTO authenticatedUser = authenticatedUserService.getAuthenticatedUserDetails();
        Comment comment = commentRepo.findByCommentId(commentId)
                .orElseThrow(() -> new OrderNotFoundException("Comment with " + commentId + " not found."));

        if (!authenticatedUser.getId().equals(comment.getAuthor().getId())) {
            throw new RIAuthenticationException("You are not authorized to Update this comment.");
        }

        if (commentMediaUpdateDTO.getContent() != null) {
            comment.setContent(commentMediaUpdateDTO.getContent());
        }
        if (commentMediaUpdateDTO.getVisibility() != null) {
            comment.setVisibility(commentMediaUpdateDTO.getVisibility());
        }else{
            comment.setVisibility(authenticatedUser.getUserGroup().getVisibility());
        }

        if (commentMediaUpdateDTO.getAddMediaFiles() != null && !commentMediaUpdateDTO.getAddMediaFiles().isEmpty()) {
            uploadMediaFiles(commentMediaUpdateDTO.getAddMediaFiles(), comment);
        }
        if (commentMediaUpdateDTO.getRemoveMediaIds() != null && !commentMediaUpdateDTO.getRemoveMediaIds().isEmpty()) {
            deleteMediaFiles(commentMediaUpdateDTO.getRemoveMediaIds(), comment);
        }
        comment = commentRepo.save(comment);
        CommentDTO savedCommentDTO = modelMapper.map(comment, CommentDTO.class);
        savedCommentDTO.setMedia(savedCommentDTO.getMedia());
        savedCommentDTO.setInspectionItemId(comment.getInspectionItem().getInspectionItemId());
        eventPublisher.publish(comment, savedCommentDTO , EventQueue.EventType.INSP_ITEM_COMMENT_UPDATED);
        return savedCommentDTO;
    }

    private List<MediaDTO> uploadMediaFiles(List<MultipartFile> mediaFiles, Comment savedComment) throws IOException {
        if (mediaFiles == null) {
            return Collections.emptyList();
        }

        MediaDTO mediaDTO = new MediaDTO();
        mediaDTO.setCommentId(savedComment.getCommentId());
        mediaDTO.setInspectionItemId(savedComment.getInspectionItem().getInspectionItemId());

        return mediaService.uploadFile(mediaFiles, mediaDTO);
    }

    private List<MediaDTO> addMediaReference(List<String> mediaIds, Comment comment) {
        if (mediaIds == null) {
            return Collections.emptyList();
        }

        return mediaIds.stream()
                .map(mediaId -> {
                    Media media = mediaService.findByMediaId(mediaId);
                    Media newMedia = new Media();
                    BeanUtils.copyProperties(media, newMedia);
                    newMedia.setId(null);
                    newMedia.setMediaId(null);
                    newMedia.setComment(comment);
                    Media savedMedia = mediaService.save(newMedia);
                    return modelMapper.map(savedMedia, MediaDTO.class);
                })
                .toList();
    }

    private void deleteMediaFiles(List<String> mediaIds, Comment comment) {
        comment.getMedia().removeIf(media -> mediaIds.contains(media.getMediaId()));
        mediaIds.stream().forEach(mediaService::deleteFile);
    }

    public void deleteComment(String commentID) {
        AuthenticatedUserDTO authenticatedUser = authenticatedUserService.getAuthenticatedUserDetails();
        Comment comment = commentRepo.findByCommentId(commentID)
                .orElseThrow(() -> new OrderNotFoundException("Comment with " + commentID + " not found."));
        CommentDTO commentDTO = modelMapper.map(comment, CommentDTO.class);
        if (!authenticatedUser.getId().equals(comment.getAuthor().getId())) {
            throw new RIAuthenticationException("You are not authorized to delete this comment.");
        }
        List<String> mediaIds = comment.getMedia().stream().map(Media::getMediaId).toList();
        deleteMediaFiles(mediaIds, comment);
        eventPublisher.publish(comment, commentDTO, EventQueue.EventType.INSP_ITEM_COMMENT_DELETED);
        commentRepo.deleteById(comment.getId());
    }
}
