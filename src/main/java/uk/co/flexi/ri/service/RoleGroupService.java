package uk.co.flexi.ri.service;

import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.RoleDTO;
import uk.co.flexi.ri.dto.RoleGroupDTO;
import uk.co.flexi.ri.model.Role;
import uk.co.flexi.ri.model.RoleGroup;
import uk.co.flexi.ri.repository.RoleGroupRepo;
import uk.co.flexi.ri.repository.RoleRepo;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.REQUIRED,rollbackFor=Exception.class)
public class RoleGroupService {

    private static final String INVALID_ROLE_GROUP = "Invalid Role Group";

    private final RoleGroupRepo roleGroupRepo;

    private final RoleRepo roleRepo;

    private final ModelMapper modelMapper;

    public List<RoleGroupDTO> getRoleGroups() {
        return roleGroupRepo.findAll().stream().map(roleGroup -> {
            RoleGroupDTO roleGroupDTO = modelMapper.map(roleGroup, RoleGroupDTO.class);
            List<Role> roles = roleRepo.findByRoleGroup(roleGroup);
            roleGroupDTO.setRoles(roles.stream()
                    .map(role -> modelMapper.map(role, RoleDTO.class))
                    .collect(Collectors.toSet()));
            return roleGroupDTO;
        }).toList();
    }

    public RoleGroupDTO createRoleGroup(RoleGroupDTO roleGroupDTO) {
        RoleGroup roleGroup = modelMapper.map(roleGroupDTO, RoleGroup.class);
        Set<Role> roles = roleGroupDTO.getRoles().stream()
                .map(r -> roleRepo.findById(r.getId())
                        .orElseThrow(() -> new IllegalArgumentException("Role not found: " + r.getId())))
                .collect(Collectors.toSet());
        roleGroup.setRoles(roles);
        RoleGroup savedRoleGroup = roleGroupRepo.save(roleGroup);
        return modelMapper.map(savedRoleGroup, RoleGroupDTO.class);
    }

    public RoleGroupDTO findRoleGroupDTOById(String roleGroupId) {
        RoleGroup roleGroup = roleGroupRepo.findByRoleGroupId(roleGroupId).orElseThrow(() ->
                new NoSuchElementException(INVALID_ROLE_GROUP));
        RoleGroupDTO roleGroupDTO = modelMapper.map(roleGroup, RoleGroupDTO.class);
        List<Role> roles = roleRepo.findByRoleGroup(roleGroup);
        roleGroupDTO.setRoles(roles.stream()
                .map(role -> modelMapper.map(role, RoleDTO.class))
                .collect(Collectors.toSet()));
        return roleGroupDTO;
    }

    public RoleGroup findByRoleGroupId(String roleGroupId) {
        return roleGroupRepo.findByRoleGroupId(roleGroupId).orElseThrow(() ->
                new NoSuchElementException(INVALID_ROLE_GROUP));
    }

    public void deleteRoleGroups(String roleGroupId) {
        RoleGroup roleGroup = roleGroupRepo.findByRoleGroupId(roleGroupId).orElseThrow(() ->
                new NoSuchElementException(INVALID_ROLE_GROUP));
        roleGroupRepo.delete(roleGroup);
    }

    public List<RoleDTO> getRoles() {
        return roleRepo.findAll().stream().map(r -> modelMapper.map(r, RoleDTO.class)).toList();
    }

    public RoleGroupDTO updateRoleGroup(String roleGroupId, RoleGroupDTO roleGroupDTO) {
        RoleGroup roleGroup = roleGroupRepo.findByRoleGroupId(roleGroupId).orElseThrow(() ->
                new NoSuchElementException(INVALID_ROLE_GROUP));
        Set<Role> inputRoles = roleGroupDTO.getRoles().stream()
                .map(r -> roleRepo.findById(r.getId())
                        .orElseThrow(() -> new IllegalArgumentException("Role not found: " + r.getId())))
                .collect(Collectors.toSet());
        roleGroup.setRoles(inputRoles);
        roleGroup.setName(roleGroupDTO.getName());
        RoleGroup savedRoleGroup = roleGroupRepo.save(roleGroup);
        return modelMapper.map(savedRoleGroup, RoleGroupDTO.class);
    }
}
