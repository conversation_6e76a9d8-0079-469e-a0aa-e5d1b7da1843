package uk.co.flexi.ri.service;

import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.CommentDTO;
import uk.co.flexi.ri.dto.event.CommentPayload;
import uk.co.flexi.ri.dto.event.EventData;
import uk.co.flexi.ri.dto.event.InspectionItemPayload;
import uk.co.flexi.ri.dto.event.InspectionPayload;
import uk.co.flexi.ri.dto.event.MediaPayload;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.model.EventQueue;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.model.Media;
import uk.co.flexi.ri.model.User;
import uk.co.flexi.ri.model.UserGroup;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class DBEventPublisher implements EventPublisher {

    private static final String ORDER_ID = "orderId";

    private static final String RETURN_ORDER_ID = "returnOrderId";

    private static final String RETURN_TRACKING_ID = "returnTrackingId";

    private final ApplicationEventPublisher applicationEventPublisher;

    private final AuthenticatedUserService authenticatedUserService;

    @Override
    public void publish(Inspection inspection, EventQueue.EventType eventType) {
        publishToEventQueue(inspection, eventType);

        saveToMerchantPrice(inspection, eventType);
    }

    @Override
    public void publish(InspectionItem item, EventQueue.EventType eventType) {
        AuthenticatedUserDTO user = authenticatedUserService.getAuthenticatedUserDetails();
        Inspection inspection = item.getInspection();
        EventData<InspectionItemPayload> eventPayload = new EventData<>();
        eventPayload.setEventId(UUID.randomUUID().toString());
        eventPayload.setUniqueId(inspection.getInspectionId());
        eventPayload.setEventType(eventType);
        eventPayload.setReferenceType(EventQueue.ReferenceType.INSPECTION_ITEM);
        eventPayload.setEventTime(OffsetDateTime.now());
        eventPayload.setPerformedByUser(user.getUserName());
        eventPayload.setPerformedByGroup(user.getUserGroup().getName());
        eventPayload.setTenant(item.getTenant());
        InspectionItemPayload inspectionItemPayload = createInspectionItemPayload(item);
        eventPayload.setPayload(inspectionItemPayload);
        applicationEventPublisher.publishEvent(eventPayload);
    }

    @Override
    public void publish(Comment comment, CommentDTO commentDTO, EventQueue.EventType eventType) {
        AuthenticatedUserDTO user = authenticatedUserService.getAuthenticatedUserDetails();
        Inspection inspection = comment.getInspectionItem().getInspection();
        EventData<CommentPayload> eventPayload = new EventData<>();
        eventPayload.setEventId(UUID.randomUUID().toString());
        eventPayload.setUniqueId(inspection.getInspectionId());
        eventPayload.setEventType(eventType);
        eventPayload.setReferenceType(EventQueue.ReferenceType.COMMENT);
        eventPayload.setEventTime(OffsetDateTime.now());
        eventPayload.setPerformedByUser(user.getUserName());
        eventPayload.setPerformedByGroup(user.getUserGroup().getName());
        eventPayload.setTenant(comment.getTenant());
        CommentPayload commentPayload = createCommentPayload(comment, commentDTO);
        eventPayload.setPayload(commentPayload);
        applicationEventPublisher.publishEvent(eventPayload);
    }

    private void publishToEventQueue(Inspection inspection, EventQueue.EventType eventType) {
        AuthenticatedUserDTO user = authenticatedUserService.getAuthenticatedUserDetails();
        EventData<InspectionPayload> eventPayload = new EventData<>();
        eventPayload.setEventId(UUID.randomUUID().toString());
        eventPayload.setUniqueId(inspection.getInspectionId());
        eventPayload.setEventType(eventType);
        eventPayload.setReferenceType(EventQueue.ReferenceType.INSPECTION);
        eventPayload.setEventTime(OffsetDateTime.now());
        eventPayload.setPerformedByUser(user.getUserName());
        eventPayload.setPerformedByGroup(user.getUserGroup().getName());
        eventPayload.setTenant(inspection.getTenant());
        InspectionPayload inspectionPayload = createInspectionPayload(inspection);
        eventPayload.setPayload(inspectionPayload);
        applicationEventPublisher.publishEvent(eventPayload);
    }

    private void saveToMerchantPrice(Inspection inspection, EventQueue.EventType eventType){
        if(eventType.equals(EventQueue.EventType.INSP_CREATED)){
            applicationEventPublisher.publishEvent(inspection);
        }
    }


    private InspectionPayload createInspectionPayload(Inspection inspection) {
        InspectionPayload payload = new InspectionPayload();
        payload.setInspectionId(inspection.getInspectionId());
        payload.setReferenceId(inspection.getReferenceId());
        payload.setOrderId(getOrderValue(inspection, ORDER_ID));
        payload.setReturnOrderId(getOrderValue(inspection, RETURN_ORDER_ID));
        payload.setReturnTrackingId(getOrderValue(inspection, RETURN_TRACKING_ID));
        payload.setInspectionStatus(getEnumName(inspection.getStatus()));
        payload.setCreatedUser(getUserName(inspection.getAssignee()));
        payload.setAssignedToGroup(getGroupName(inspection.getAssigneeGroup()));
        payload.setPartnerName(inspection.getPartnerName());
        payload.setSellingChannel(inspection.getSellingChannel());
        payload.setAssignedDate(inspection.getAssignedDate());
        payload.setCompletedDate(inspection.getCompletedDate());
        payload.setCompletedByGroup(inspection.getCompletedBy());
        payload.setItems(buildItemPayloads(inspection));
        return payload;
    }

    private InspectionItemPayload createInspectionItemPayload(InspectionItem item) {
        InspectionItemPayload itemPayload = new InspectionItemPayload();

        itemPayload.setInspectionItemId(item.getInspectionItemId());
        itemPayload.setOrderLineId(item.getOrderLineId());
        itemPayload.setSku(getSku(item));
        itemPayload.setReturnReason(item.getReturnReason());
        itemPayload.setExpectedCondition(item.getExpectedItemCondition());
        itemPayload.setCondition(item.getItemCondition());
        itemPayload.setStatus(getEnumName(item.getItemStatus()));
        itemPayload.setCompletedDate(item.getCompletedDate());
        itemPayload.setCompletedByGroup(item.getCompletedBy());
        itemPayload.setInspection(getInspectionDetails(item.getInspection()));
        itemPayload.setComments(mapComments(item.getComments()));

        return itemPayload;
    }

    private CommentPayload createCommentPayload(Comment comment, CommentDTO commentDTO) {
        CommentPayload commentPayload = new CommentPayload();
        commentPayload.setCommentId(comment.getCommentId());
        commentPayload.setComment(comment.getContent());
        commentPayload.setAuthor(comment.getAuthor() != null ? comment.getAuthor().getUserName() : null);
        commentPayload.setInspectionItem(getInspectionItemDetails(comment.getInspectionItem()));
        if (commentDTO.getMedia() != null && !commentDTO.getMedia().isEmpty()) {
            List<MediaPayload> mediaPayloads = new ArrayList<>();
            commentDTO.getMedia().forEach(media -> {
                if (media != null) {
                    MediaPayload mediaPayload = new MediaPayload();
                    mediaPayload.setMediaId(media.getMediaId());
                    mediaPayload.setMediaType(media.getMediaType());
                    mediaPayload.setFileName(media.getFileName());
                    mediaPayloads.add(mediaPayload);
                }
            });
            commentPayload.setMedia(mediaPayloads);
        }
        return commentPayload;
    }

    private Map<String, Object> getInspectionDetails(Inspection inspection) {
        return Map.of(
                "inspectionId", inspection.getInspectionId() != null ? inspection.getInspectionId() : "",
                "referenceId", inspection.getReferenceId() != null ? inspection.getReferenceId() : "",
                ORDER_ID, inspection.getOrder() != null && inspection.getOrder().getOrderId() != null ?
                        inspection.getOrder().getOrderId() : "",
                RETURN_ORDER_ID, inspection.getOrder() != null && inspection.getOrder().getReturnOrderId() != null ?
                        inspection.getOrder().getReturnOrderId() : "",
                RETURN_TRACKING_ID, inspection.getOrder() != null && inspection.getOrder().getReturnTrackingId() != null ? inspection.getOrder().getReturnTrackingId() : ""
        );
    }

    private Map<String, Object> getInspectionItemDetails(InspectionItem inspectionItem) {
        return Map.of(
                "inspectionItemId", inspectionItem.getInspectionItemId() != null ? inspectionItem.getInspectionItemId() : "",
                "sku", inspectionItem.getProduct() != null ? inspectionItem.getProduct().getSku() : "",
                "inspection", inspectionItem.getInspection() != null ? getInspectionDetails(inspectionItem.getInspection()) : ""
        );
    }


    //Helper methods to extract values

    private String getOrderValue(Inspection inspection, String field) {
        if (inspection.getOrder() == null) return null;
        return switch (field) {
            case ORDER_ID -> inspection.getOrder().getOrderId();
            case RETURN_ORDER_ID -> inspection.getOrder().getReturnOrderId();
            case RETURN_TRACKING_ID -> inspection.getOrder().getReturnTrackingId();
            default -> null;
        };
    }

    private String getEnumName(Enum<?> enumVal) {
        return enumVal != null ? enumVal.name() : null;
    }

    private String getUserName(User user) {
        return user != null ? user.getUserName() : null;
    }

    private String getGroupName(UserGroup group) {
        return group != null ? group.getName() : null;
    }

    private List<InspectionItemPayload> buildItemPayloads(Inspection inspection) {
        if (inspection.getInspectionItems() == null) return Collections.emptyList();

        return inspection.getInspectionItems().stream()
                .filter(Objects::nonNull)
                .map(this::mapToItemPayload)
                .toList();
    }

    private InspectionItemPayload mapToItemPayload(InspectionItem item) {
        InspectionItemPayload itemPayload = new InspectionItemPayload();
        itemPayload.setInspectionItemId(item.getId() != null ? item.getId().toString() : null);
        itemPayload.setOrderLineId(item.getOrderLineId());
        itemPayload.setSku(item.getProduct() != null ? item.getProduct().getSku() : null);
        itemPayload.setReturnReason(item.getReturnReason());
        itemPayload.setExpectedCondition(item.getExpectedItemCondition());
        itemPayload.setCondition(item.getItemCondition());
        itemPayload.setStatus(getEnumName(item.getItemStatus()));
        itemPayload.setCompletedDate(item.getCompletedDate());
        itemPayload.setCompletedByGroup(item.getCompletedBy());
        itemPayload.setComments(mapComments(item.getComments()));
        return itemPayload;
    }

    private List<CommentPayload> mapComments(List<Comment> comments) {
        if (comments == null || comments.isEmpty()) return Collections.emptyList();

        return comments.stream()
                .filter(Objects::nonNull)
                .map(this::mapToCommentPayload)
                .toList();
    }

    private CommentPayload mapToCommentPayload(Comment comment) {
        CommentPayload commentPayload = new CommentPayload();
        commentPayload.setCommentId(comment.getId() != null ? comment.getId().toString() : null);
        commentPayload.setComment(comment.getContent());
        commentPayload.setAuthor(getUserName(comment.getAuthor()));
        commentPayload.setMedia(mapMedia(comment.getMedia()));
        return commentPayload;
    }

    private List<MediaPayload> mapMedia(List<Media> mediaList) {
        if (mediaList == null || mediaList.isEmpty()) return Collections.emptyList();

        return mediaList.stream()
                .filter(Objects::nonNull)
                .map(media -> {
                    MediaPayload payload = new MediaPayload();
                    payload.setMediaId(media.getId() != null ? media.getId().toString() : null);
                    payload.setMediaType(media.getMediaType());
                    payload.setFileName(media.getFileName());
                    return payload;
                })
                .toList();
    }

    private String getSku(InspectionItem item) {
        return item.getProduct() != null ? item.getProduct().getSku() : null;
    }

}
