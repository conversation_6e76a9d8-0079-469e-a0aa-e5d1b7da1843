package uk.co.flexi.ri.service;

import uk.co.flexi.ri.dto.CommentDTO;
import uk.co.flexi.ri.model.Comment;
import uk.co.flexi.ri.model.EventQueue;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItem;

public interface EventPublisher {

    void publish(Inspection inspection, EventQueue.EventType eventType);

    void publish(InspectionItem item, EventQueue.EventType eventType);

    void publish(Comment comment, CommentDTO commentDTO, EventQueue.EventType eventType);
}
