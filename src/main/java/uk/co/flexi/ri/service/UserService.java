package uk.co.flexi.ri.service;

import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.MerchantDTO;
import uk.co.flexi.ri.dto.RoleDTO;
import uk.co.flexi.ri.dto.UserDTO;
import uk.co.flexi.ri.dto.UserDetailDTO;
import uk.co.flexi.ri.dto.UserLoginDTO;
import uk.co.flexi.ri.exception.custom.UserNotFoundException;
import uk.co.flexi.ri.model.AuthProvider;
import uk.co.flexi.ri.model.Media;
import uk.co.flexi.ri.model.Merchant;
import uk.co.flexi.ri.model.User;
import uk.co.flexi.ri.model.UserGroup;
import uk.co.flexi.ri.repository.UserRepo;
import org.modelmapper.ModelMapper;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class UserService {

    private static final String USER_WITH_ID = "User with userId ";

    private static final String NOT_FOUND = " not found";

    private static final String USER_NOT_FOUND = "User not found";

    private final ModelMapper modelMapper;

    private final AuthenticatedUserService authenticatedUserService;

    private final UserRepo userRepo;

    private final PasswordEncoder passwordEncoder;

    private final MediaService mediaService;

    private final UserGroupService userGroupService;

    private final RoleGroupService roleGroupService;

    private final AuthProviderService authProviderService;

    public UserDTO save(UserDTO userDTO) {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        Merchant merchant = new Merchant();
        merchant.setId(authenticatedUserDTO.getMerchantId());

        User user = modelMapper.map(userDTO, User.class);
        if (user.getPassword() != null) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }

        Set<UserGroup> userGroups = new HashSet<>();
        userGroups.add(userGroupService.findByUserGroupId(userDTO.getUserGroup()));
        user.setUserGroups(userGroups);
        user.setRoleGroup(roleGroupService.findByRoleGroupId(userDTO.getRoleGroup()));
        user.setAuthProvider(authProviderService.findByProvider(userDTO.getAuthProvider().getProvider()));
        user.setMerchant(merchant);

        User savedUser = userRepo.save(user);
        UserDTO dto = modelMapper.map(savedUser, UserDTO.class);

        savedUser.getUserGroups().stream()
                .findFirst()
                .ifPresent(userGroup -> dto.setUserGroup(userGroup.getName()));

        dto.setRoleGroup(savedUser.getRoleGroup().getName());

        return dto;
    }

    public UserDTO update(String userId, UserDTO userDTO) {
        User user = userRepo.findByUserId(userId)
                .orElseThrow(() -> new UserNotFoundException(USER_WITH_ID + userId + "' not found"));

        boolean isAdmin = user.getUserGroups().stream()
                .anyMatch(group -> "admin".equalsIgnoreCase(group.getName()));

        if (isAdmin) {
            if (!user.getRoleGroup().getRoleGroupId().equals(userDTO.getRoleGroup())) {
                throw new IllegalArgumentException("Modification of role group for admin users is not allowed.");
            }
        } else {
            user.setRoleGroup(roleGroupService.findByRoleGroupId(userDTO.getRoleGroup()));
        }

        Set<UserGroup> userGroups = new HashSet<>();
        userGroups.add(userGroupService.findByUserGroupId(userDTO.getUserGroup()));
        user.setUserGroups(userGroups);

        user.setFirstName(userDTO.getFirstName());
        user.setLastName(userDTO.getLastName());
        user.setAuthProvider(authProviderService.findByProvider(userDTO.getAuthProvider().getProvider()));

        User savedUser = userRepo.save(user);
        UserDTO dto = modelMapper.map(savedUser, UserDTO.class);

        savedUser.getUserGroups().stream()
                .findFirst()
                .ifPresent(userGroup -> dto.setUserGroup(userGroup.getName()));

        dto.setRoleGroup(savedUser.getRoleGroup().getName());

        return dto;
    }

    public Page<UserDTO> findAll(boolean includeInactive, Pageable pageable) {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();

        Page<User> usersPage = includeInactive
                ? userRepo.findAllUsers(authenticatedUserDTO.getTenant(), pageable)
                : userRepo.findAll(pageable);

        return usersPage.map(user -> {
            UserDTO dto = modelMapper.map(user, UserDTO.class);
            dto.setUserGroup(
                    user.getUserGroups() != null && !user.getUserGroups().isEmpty()
                            ? user.getUserGroups().iterator().next().getName()
                            : null
            );
            if (user.getRoleGroup() != null) {
                dto.setRoleGroup(user.getRoleGroup().getName());
            }
            return dto;
        });
    }

    public UserDTO findByUserId(String userId) {
        return userRepo.findByUserId(userId)
                .map(user -> {
                    UserDTO dto = modelMapper.map(user, UserDTO.class);
                    dto.setUserGroup(user.getUserGroups().stream().findFirst().get().getName());
                    dto.setRoleGroup(user.getRoleGroup().getName());
                    return dto;
                })
                .orElseThrow(() -> new UserNotFoundException(USER_WITH_ID + userId + NOT_FOUND));
    }

    public UserDTO activateUser(String userId) {
        User user = userRepo.findUserById(userId)
                .orElseThrow(() -> new UserNotFoundException(USER_WITH_ID + userId + NOT_FOUND));
        user.setIsActive(Boolean.TRUE);
        user = userRepo.save(user);
        return modelMapper.map(user, UserDTO.class);
    }

    public UserDTO deactivateUser(String userId) {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        if (authenticatedUserDTO.getUserId().equals(userId)) {
            throw new IllegalArgumentException("Deactivation not allowed.");
        }
        User user = userRepo.findByUserId(userId)
                .orElseThrow(() -> new UserNotFoundException(USER_WITH_ID + userId + NOT_FOUND));
        user.setIsActive(Boolean.FALSE);
        user = userRepo.save(user);
        return modelMapper.map(user, UserDTO.class);
    }


    public User findById(Long id) throws UserNotFoundException {
        Optional<User> optionalUser = userRepo.findById(id);
        return optionalUser.orElseThrow(() ->
                new UserNotFoundException(USER_WITH_ID + id + NOT_FOUND));
    }

    public UserLoginDTO findByUserName(String userName) throws UserNotFoundException {
        User user = userRepo.findByUserNameAndIsActive(userName, Boolean.TRUE).stream()
                .findFirst()
                .orElseThrow(() -> new UserNotFoundException("User with user name " + userName + NOT_FOUND));

        AuthProvider authProvider = user.getAuthProvider();

        return new UserLoginDTO(
                authProvider.getProvider().name(),
                user.getUserName(),
                authProvider.getAuthUrl()
        );
    }

    public UserDTO findUserByUserName(String userName) throws UserNotFoundException {
        return userRepo.findByUserNameAndIsActive(userName, Boolean.TRUE).stream()
                .findFirst()
                .map(user -> modelMapper.map(user, UserDTO.class))
                .orElseThrow(() -> new UserNotFoundException("User with user name " + userName + NOT_FOUND));
    }

    public UserDetailDTO getLoggedInUserDetails() {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        Optional<User> optionalUser = userRepo.findById(authenticatedUserDTO.getId());
        User user = optionalUser.orElseThrow(() ->
                new UserNotFoundException(USER_WITH_ID + authenticatedUserDTO.getId() + NOT_FOUND));
        UserDetailDTO userDetailDTO = modelMapper.map(user, UserDetailDTO.class);
        userDetailDTO.setReportingCurrency(authenticatedUserDTO.getReportingCurrency());
        userDetailDTO.setUserGroup(authenticatedUserDTO.getUserGroup().getName());
        userDetailDTO.setRoles(user.getRoleGroup().getRoles().stream().map(r -> modelMapper.map(r, RoleDTO.class)).collect(Collectors.toSet()));
        return userDetailDTO;
    }

    public UserDetailDTO updateLoggedInUserTimeZone(String timeZone) {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        Optional<User> optionalUser = userRepo.findById(authenticatedUserDTO.getId());
        User user = optionalUser.orElseThrow(() ->
                new UserNotFoundException(USER_NOT_FOUND));
        user.setTimeZone(timeZone);
        user = userRepo.save(user);
        UserDetailDTO userDetailDTO = modelMapper.map(user, UserDetailDTO.class);
        userDetailDTO.setUserGroup(authenticatedUserDTO.getUserGroup().getName());
        return userDetailDTO;
    }

    public UserDetailDTO updateProfilePicture(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("File must not be empty");
        }
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        Optional<User> optionalUser = userRepo.findById(authenticatedUserDTO.getId());
        User user = optionalUser.orElseThrow(() ->
                new UserNotFoundException(USER_NOT_FOUND));
        Media media = mediaService.uploadProfileImage(file);
        user.setProfileImage(media);
        user = userRepo.save(user);
        UserDetailDTO userDetailDTO = modelMapper.map(user, UserDetailDTO.class);
        userDetailDTO.setUserGroup(authenticatedUserDTO.getUserGroup().getName());
        return userDetailDTO;
    }

    public UserDetailDTO removeProfilePicture() {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        Optional<User> optionalUser = userRepo.findById(authenticatedUserDTO.getId());
        User user = optionalUser.orElseThrow(() ->
                new UserNotFoundException(USER_NOT_FOUND));
        user.setProfileImage(null);
        user = userRepo.save(user);
        UserDetailDTO userDetailDTO = modelMapper.map(user, UserDetailDTO.class);
        userDetailDTO.setUserGroup(authenticatedUserDTO.getUserGroup().getName());
        return userDetailDTO;
    }

    public UserDetailDTO updateLoggedInUserLanguage(String lang) {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        Optional<User> optionalUser = userRepo.findById(authenticatedUserDTO.getId());
        User user = optionalUser.orElseThrow(() ->
                new UserNotFoundException(USER_NOT_FOUND));
        user.setLanguage(lang);
        user = userRepo.save(user);
        UserDetailDTO userDetailDTO = modelMapper.map(user, UserDetailDTO.class);
        userDetailDTO.setUserGroup(authenticatedUserDTO.getUserGroup().getName());
        return userDetailDTO;
    }

    public List<MerchantDTO> getAllMerchantByUserName(String userName) {
        List<User> users = userRepo.findByUserNameAndIsActive(userName, Boolean.TRUE);
        return users.stream()
                .map(user -> modelMapper.map(user.getMerchant(), MerchantDTO.class))
                .toList();
    }

    public List<String> searchUserName(String userName) {
        return userRepo.findUserNamesBySearch(userName);
    }
}
