package uk.co.flexi.ri.service;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.model.Merchant;
import uk.co.flexi.ri.model.MerchantProductPrice;
import uk.co.flexi.ri.model.Product;
import uk.co.flexi.ri.repository.MerchantProductPriceRepo;
import uk.co.flexi.ri.repository.MerchantRepo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@AllArgsConstructor
public class MerchantProductPriceService {

    private final CurrencyConversionService currencyConversionService;

    private final MerchantProductPriceRepo merchantProductPriceRepo;

    private final MerchantRepo merchantRepo;

    public void saveToMerchantProductPrice(Inspection inspection) {
        Map<String, List<Product>> skuProductMap = groupProductsBySku(inspection);
        Set<String> skuSet = skuProductMap.keySet();

        List<MerchantProductPrice> existingPrices = merchantProductPriceRepo.findByTenantAndSkuIn(
                inspection.getTenant(), skuSet);

        Map<String, MerchantProductPrice> skuPriceMap = existingPrices.stream()
                .collect(Collectors.toMap(MerchantProductPrice::getSku, p -> p));

        Merchant merchant = merchantRepo.findByTenantNative(inspection.getTenant());
        String reportingCurrency = merchant.getReportingCurrency();

        for (Map.Entry<String, List<Product>> entry : skuProductMap.entrySet()) {
            String sku = entry.getKey();
            List<Product> products = entry.getValue();
            BigDecimal newUnits = BigDecimal.valueOf(products.size());
            BigDecimal newTotalPrice = calculateTotalPriceInReportingCurrency(products, reportingCurrency);
            if (newTotalPrice.equals(BigDecimal.ZERO)) {
                continue;
            }
            MerchantProductPrice existingPrice = skuPriceMap.get(sku);
            if (existingPrice != null) {
                updatePrice(existingPrice, newUnits, newTotalPrice, reportingCurrency);
            } else {
                createNewPrice(inspection.getTenant(), products.get(0), newUnits, newTotalPrice, reportingCurrency);
            }
        }
    }

    private Map<String, List<Product>> groupProductsBySku(Inspection inspection) {
        Map<String, List<Product>> skuProductMap = new HashMap<>();
        for (InspectionItem item : inspection.getInspectionItems()) {
            Product product = item.getProduct();
            if (product != null) {
                skuProductMap
                        .computeIfAbsent(product.getSku(), k -> new ArrayList<>())
                        .add(product);
            }
        }
        return skuProductMap;
    }

    private BigDecimal calculateTotalPriceInReportingCurrency(List<Product> products, String reportingCurrency) {
        return products.stream()
                .filter(p -> p.getUnitPrice() != null && p.getCurrencyCode() != null)
                .map(p -> {
                    BigDecimal price = p.getUnitPrice();
                    String productCurrency = p.getCurrencyCode();
                    if (!productCurrency.equalsIgnoreCase(reportingCurrency)) {
                        return currencyConversionService.convert(price, productCurrency, reportingCurrency);
                    }
                    return price;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private void updatePrice(MerchantProductPrice existingPrice, BigDecimal newUnits, BigDecimal newTotalPrice, String reportingCurrency) {
        BigDecimal oldUnits = existingPrice.getTotalUnit();
        BigDecimal oldTotalPrice = existingPrice.getUnitPrice().multiply(oldUnits);

        BigDecimal combinedUnits = oldUnits.add(newUnits);
        BigDecimal combinedTotalPrice = oldTotalPrice.add(newTotalPrice);

        BigDecimal averagePrice = combinedTotalPrice.divide(combinedUnits, 6, RoundingMode.HALF_UP);

        existingPrice.setTotalUnit(combinedUnits);
        existingPrice.setUnitPrice(averagePrice);
        existingPrice.setCurrencyCode(reportingCurrency);

        merchantProductPriceRepo.save(existingPrice);
    }

    private void createNewPrice(Long tenant, Product product, BigDecimal totalUnits, BigDecimal totalPrice, String reportingCurrency) {
        BigDecimal unitPrice = totalPrice.divide(totalUnits, 6, RoundingMode.HALF_UP);

        MerchantProductPrice newPrice = new MerchantProductPrice();
        newPrice.setSku(product.getSku());
        newPrice.setTotalUnit(totalUnits);
        newPrice.setUnitPrice(unitPrice);
        newPrice.setCurrencyCode(reportingCurrency);
        newPrice.setTenant(tenant);

        merchantProductPriceRepo.save(newPrice);
    }
}
