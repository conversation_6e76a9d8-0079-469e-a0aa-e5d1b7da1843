package uk.co.flexi.ri.service;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.ChannelDTO;
import uk.co.flexi.ri.model.Channel;
import uk.co.flexi.ri.repository.ChannelRepo;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;


@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class ChannelService {

    private final ModelMapper modelMapper;

    private final ChannelRepo channelRepo;

    private static final String INVALID_CHANNEL = "Invalid Channel";

    public ChannelDTO findById(String channelId) {
        Optional<Channel> optionalSupplier = channelRepo.findByChannelId(channelId);
        Channel channel = optionalSupplier.orElseThrow(() ->
                new NoSuchElementException(INVALID_CHANNEL));
        return modelMapper.map(channel, ChannelDTO.class);
    }

    public ChannelDTO save(ChannelDTO channelDTO) {
        Channel channel= modelMapper.map(channelDTO, Channel.class);
        channel = channelRepo.save(channel);
        return modelMapper.map(channel, ChannelDTO.class);
    }

    public List<ChannelDTO> findAll() {
        List<Channel> channels = channelRepo.findAll();
        return channels.stream().map(channel -> modelMapper.map(channel,
                        ChannelDTO.class))
                .toList();
    }

    public ChannelDTO update(String channelId, @Valid ChannelDTO channelDTO) {
        Optional<Channel> optionalSupplier = channelRepo.findByChannelId(channelId);
        Channel channel = optionalSupplier.orElseThrow(() ->
                new NoSuchElementException(INVALID_CHANNEL));
        channel.setName(channelDTO.getName());
        channel.setServiceLevelAgreement(channelDTO.getServiceLevelAgreement());
        channel = channelRepo.save(channel);
        return modelMapper.map(channel, ChannelDTO.class);
    }

    public void delete(String channelId) {
        Optional<Channel> optionalSupplier = channelRepo.findByChannelId(channelId);
        Channel channel = optionalSupplier.orElseThrow(() ->
                new NoSuchElementException(INVALID_CHANNEL));
        channelRepo.delete(channel);
    }
}
