package uk.co.flexi.ri.service;


import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Component
@AllArgsConstructor
public class CurrencyConversionService {

    private final RestClient restClient;

    public BigDecimal convert(BigDecimal price, String fromCurrency, String toCurrency) {
        if (price == null) {
            throw new IllegalArgumentException("Price cannot be null");
        }

        if (fromCurrency.equalsIgnoreCase(toCurrency)) {
            return price.setScale(2, RoundingMode.HALF_UP);
        }
        String uri = UriComponentsBuilder.fromHttpUrl("https://api.frankfurter.app/latest")
                .queryParam("amount", price)
                .queryParam("from", fromCurrency)
                .queryParam("to", toCurrency)
                .toUriString();

        JsonNode response = restClient.get()
                .uri(uri)
                .retrieve()
                .body(JsonNode.class);

        if (response == null || response.path("rates").isMissingNode()) {
            throw new IllegalStateException("Invalid response from currency conversion API");
        }

        JsonNode rateNode = response.path("rates").path(toCurrency);
        if (rateNode.isMissingNode() || !rateNode.isNumber()) {
            throw new IllegalStateException("Conversion rate not found for currency: " + toCurrency);
        }

        return rateNode.decimalValue();
    }
}
