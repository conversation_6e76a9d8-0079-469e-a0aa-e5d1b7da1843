package uk.co.flexi.ri.service;

import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.NotificationChannelDTO;
import uk.co.flexi.ri.model.NotificationChannel;
import uk.co.flexi.ri.repository.NotificationChannelRepo;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class NotificationChannelService {

    private final NotificationChannelRepo notificationChannelRepo;

    private final ModelMapper modelMapper;

    public NotificationChannelDTO getNotificationChannelById(Long id) {
        NotificationChannel notificationChannel = notificationChannelRepo.findById(id)
                .orElseThrow(() -> new NoSuchElementException("NotificationChannel not found with id: " + id));

        return modelMapper.map(notificationChannel, NotificationChannelDTO.class);
    }

    public List<NotificationChannelDTO> getAllNotificationChannels() {
        List<NotificationChannel> notificationChannels = notificationChannelRepo.findAll();
        return notificationChannels.stream()
                .map(entity ->modelMapper.map(entity, NotificationChannelDTO.class))
                .toList();
    }

    public NotificationChannelDTO createNotificationChannel(NotificationChannelDTO notificationChannelDTO) {
        NotificationChannel notificationChannel = modelMapper.map(notificationChannelDTO, NotificationChannel.class);
        notificationChannelRepo.save(notificationChannel);
        return modelMapper.map(notificationChannel, NotificationChannelDTO.class);
    }

    public NotificationChannelDTO updateNotificationChannel(Long id, NotificationChannelDTO notificationChannelDTO) {
        NotificationChannel existingChannel = notificationChannelRepo.findById(id)
                .orElseThrow(() -> new NoSuchElementException("NotificationChannel not found with id: " + id));

        modelMapper.map(notificationChannelDTO, existingChannel);
        notificationChannelRepo.save(existingChannel);
        return modelMapper.map(existingChannel, NotificationChannelDTO.class);
    }

    public void deleteNotificationChannel(Long id) {
        NotificationChannel notificationChannel = notificationChannelRepo.findById(id)
                .orElseThrow(() -> new NoSuchElementException("NotificationChannel not found with id: " + id));
        notificationChannelRepo.delete(notificationChannel);
    }

    public Map<String, Object> getChannelConfigKeys(NotificationChannel.Channel channel) {
        return buildNotificationConfig(channel);
    }

    public List<NotificationChannel.Channel> getChannelList() {
        return Arrays.stream(NotificationChannel.Channel.values()).toList();
    }

    private Map<String, Object> buildNotificationConfig(NotificationChannel.Channel channel) {
        return switch (channel) {
            case EMAIL -> Map.of(
                    "smtpHost", "",
                    "smtpPort", "",
                    "smtpUsername", "",
                    "smtpPassword", "",
                    "emailFrom", ""
            );
            case SLACK -> Map.of(
                    "slackWebhookUrl", ""
            );
            case TEAMS -> Map.of(
                    "teamsWebhookUrl", ""
            );
        };
    }
}
