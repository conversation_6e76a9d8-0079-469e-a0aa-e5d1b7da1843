package uk.co.flexi.ri.service;

import jakarta.persistence.EntityNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.OrderDetailDTO;
import uk.co.flexi.ri.exception.custom.OrderNotFoundException;
import uk.co.flexi.ri.mapper.OrderDetailMapper;
import uk.co.flexi.ri.mapper.factory.OrderDetailMapperFactory;
import uk.co.flexi.ri.model.OMSProvider;
import uk.co.flexi.ri.model.Order;
import uk.co.flexi.ri.repository.OrderRepo;
import uk.co.flexi.sdk.oms.model.OrderData;

import java.util.List;
import java.util.Optional;


@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class OrderService {

    private static final String MAPPER = "OrderDetailMapper";

    private final OrderRepo orderRepo;

    private final OMSProviderService omsProviderService;

    private final OrderDetailMapperFactory orderDetailMapperFactory;

    private final OrderLookUpService orderLookUpService;

    public OrderService(OrderRepo orderRepo,
                        OMSProviderService omsProviderService,
                        OrderDetailMapperFactory orderDetailMapperFactory,
                        OrderLookUpService orderLookUpService) {
        this.orderRepo = orderRepo;
        this.omsProviderService = omsProviderService;
        this.orderDetailMapperFactory = orderDetailMapperFactory;
        this.orderLookUpService = orderLookUpService;
    }

    public Optional<Order> findByReferenceId(String referenceId) {
        return orderRepo.findByReferenceId(referenceId);
    }

    public OrderDetailDTO getOrderDetails(String input, String inspectionId) {
        OMSProviderService.OMSProviderInfo omsProviderInfo = omsProviderService.getOMSProviderInfo();
        OMSProvider omsProvider = omsProviderInfo.omsProvider();
        String omsProviderName = omsProvider.getProviderName();
        List<String> searchStrategy = omsProviderInfo.searchStrategy();
        OrderDetailMapper mapper = orderDetailMapperFactory.getMapper(omsProviderName.toLowerCase() + MAPPER);
        OrderData orderData = orderLookUpService.orderLookUp(new OrderLookUpService.OMSSearchData(input, searchStrategy), omsProvider);
        if (orderData == null) {
            throw new OrderNotFoundException("Order not found for reference id: " + input);
        }
        OrderDetailDTO orderDetailDTO = mapper.map(orderData, input);
        Optional<Order> optionalOrder = findByReferenceId(input);
        if (optionalOrder.isPresent()) {
            orderDetailDTO = mapper.mapExistingOrderDetailDTO(optionalOrder.get(), orderDetailDTO, inspectionId);
        }
        return orderDetailDTO;
    }

    public Order findById(Long id) {
        return orderRepo.findById(id).
                orElseThrow(() -> new EntityNotFoundException("Order not found"));
    }

}
