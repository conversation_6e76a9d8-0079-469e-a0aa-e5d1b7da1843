package uk.co.flexi.ri.service;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.InspectionItemConditionDTO;
import uk.co.flexi.ri.model.InspectionItemCondition;
import uk.co.flexi.ri.repository.InspectionItemConditionRepo;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;


@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class ItemConditionService {

    private static final String INVALID_ITEM_CONDITION = "Invalid Item Condition";

    private final ModelMapper modelMapper;

    private final InspectionItemConditionRepo itemConditionRepo;

    public InspectionItemConditionDTO findById(String itemConditionId) {
        Optional<InspectionItemCondition> optionalCondition = itemConditionRepo.findByItemConditionId(itemConditionId);
        InspectionItemCondition itemCondition = optionalCondition.orElseThrow(() ->
                new NoSuchElementException(INVALID_ITEM_CONDITION));
        return modelMapper.map(itemCondition, InspectionItemConditionDTO.class);
    }

    public InspectionItemConditionDTO save(InspectionItemConditionDTO itemConditionDTO) {
        InspectionItemCondition itemCondition= modelMapper.map(itemConditionDTO, InspectionItemCondition.class);
        itemCondition = itemConditionRepo.save(itemCondition);
        return modelMapper.map(itemCondition, InspectionItemConditionDTO.class);
    }

    public List<InspectionItemConditionDTO> findByConditionType(InspectionItemCondition.ConditionType conditionType) {
        List<InspectionItemCondition> itemConditions = itemConditionRepo.findByConditionType(conditionType);
        return itemConditions.stream().map(itemCondition -> modelMapper.map(itemCondition,
                        InspectionItemConditionDTO.class))
                .toList();
    }

    public InspectionItemConditionDTO update(String itemConditionId, @Valid InspectionItemConditionDTO itemConditionDTO) {
        Optional<InspectionItemCondition> optionalCondition = itemConditionRepo.findByItemConditionId(itemConditionId);
        InspectionItemCondition itemCondition = optionalCondition.orElseThrow(() ->
                new NoSuchElementException(INVALID_ITEM_CONDITION));
        itemCondition.setName(itemConditionDTO.getName());
        itemCondition = itemConditionRepo.save(itemCondition);
        return modelMapper.map(itemCondition, InspectionItemConditionDTO.class);
    }

    public void delete(String itemConditionId) {
        Optional<InspectionItemCondition> optionalCondition = itemConditionRepo.findByItemConditionId(itemConditionId);
        InspectionItemCondition itemCondition = optionalCondition.orElseThrow(() ->
                new NoSuchElementException(INVALID_ITEM_CONDITION));
        itemConditionRepo.delete(itemCondition);
    }
}
