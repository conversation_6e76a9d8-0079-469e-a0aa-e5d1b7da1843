package uk.co.flexi.ri.service;

import org.springframework.stereotype.Component;
import uk.co.flexi.ri.model.SearchStrategy;

import java.util.Collections;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.Map;
import java.util.Set;

@Component
public class SearchStrategyRegistry {

    private final Map<OMSClientService.OMSType, Set<SearchStrategy>> strategyMap;

    public SearchStrategyRegistry() {
        Map<OMSClientService.OMSType, Set<SearchStrategy>> map = new EnumMap<>(OMSClientService.OMSType.class);
        map.put(OMSClientService.OMSType.MAO, EnumSet.of(
                SearchStrategy.RETURN_BY_TRACKING_NUMBER,
                SearchStrategy.ORDER_BY_ID,
                SearchStrategy.ORDER_BY_RETURN_TRACKING,
                SearchStrategy.ORDER_BY_PRODUCT_SERIAL_NUMBER
        ));
        map.put(OMSClientService.OMSType.MOCK, EnumSet.of(
                SearchStrategy.RETURN_BY_TRACKING_NUMBER,
                SearchStrategy.ORDER_BY_ID,
                SearchStrategy.ORDER_BY_RETURN_TRACKING,
                SearchStrategy.ORDER_BY_PRODUCT_SERIAL_NUMBER
        ));

        this.strategyMap = Collections.unmodifiableMap(map);
    }

    public Set<SearchStrategy> getStrategiesFor(OMSClientService.OMSType omsType) {
        return strategyMap.getOrDefault(omsType, Set.of());
    }
}
