package uk.co.flexi.ri.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;

@Service
@RequiredArgsConstructor
public class ExcelFileFormatService implements FileFormatService {

    private final ObjectMapper objectMapper;

    @Override
    public List<String> getFileExtensions() {
        return Arrays.asList("xlsx", "xls") ;
    }

    @Override
    public byte[] exportData(List<LinkedHashMap<String, Object>> data) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Data");

            if (data.isEmpty()) return new byte[0];

            // Write header
            Row headerRow = sheet.createRow(0);
            List<String> headers = new ArrayList<>(data.get(0).keySet());
            for (int i = 0; i < headers.size(); i++) {
                headerRow.createCell(i).setCellValue(headers.get(i));
            }

            // Write rows
            for (int i = 0; i < data.size(); i++) {
                Row row = sheet.createRow(i + 1);
                LinkedHashMap<String, Object> rowData = data.get(i);
                int col = 0;
                for (String key : headers) {
                    Object value = rowData.get(key);
                    row.createCell(col++).setCellValue(value != null ? value.toString() : "");
                }
            }

            ByteArrayOutputStream out = new ByteArrayOutputStream();
            workbook.write(out);
            return out.toByteArray();
        } catch (IOException e) {
            throw new IllegalStateException("Failed to export Excel", e);
        }
    }

    @Override
    public List<LinkedHashMap<String, Object>> importData(InputStream inputStream) {
        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            List<String> headers = new ArrayList<>();
            List<LinkedHashMap<String, Object>> result = new ArrayList<>();

            Iterator<Row> rows = sheet.iterator();
            if (rows.hasNext()) {
                Row headerRow = rows.next();
                headerRow.forEach(cell -> headers.add(cell.getStringCellValue()));
            }

            while (rows.hasNext()) {
                Row row = rows.next();
                LinkedHashMap<String, Object> rowData = new LinkedHashMap<>();
                for (int i = 0; i < headers.size(); i++) {
                    Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    rowData.put(headers.get(i), getCellValue(cell));
                }
                result.add(rowData);
            }

            return result;
        } catch (IOException e) {
            throw new IllegalStateException("Failed to import Excel", e);
        }
    }

    private Object getCellValue(Cell cell) {
        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue();
            case NUMERIC -> cell.getNumericCellValue();
            case BOOLEAN -> cell.getBooleanCellValue();
            case FORMULA -> cell.getCellFormula();
            case BLANK -> "";
            default -> cell.toString();
        };
    }
}
