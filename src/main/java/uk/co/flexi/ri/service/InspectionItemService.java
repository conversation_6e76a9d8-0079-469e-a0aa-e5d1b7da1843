package uk.co.flexi.ri.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import uk.co.flexi.ri.dto.AuthenticatedUserDTO;
import uk.co.flexi.ri.dto.InspectionItemDTO;
import uk.co.flexi.ri.dto.InspectionItemUpdateReqDTO;
import uk.co.flexi.ri.dto.InstructionDTO;
import uk.co.flexi.ri.dto.ItemStatusUpdateReqDTO;
import uk.co.flexi.ri.dto.ReturnReasonDTO;
import uk.co.flexi.ri.dto.TempMediaDTO;
import uk.co.flexi.ri.model.EventQueue;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.model.Merchant;
import uk.co.flexi.ri.model.Product;
import uk.co.flexi.ri.repository.InspectionItemRepo;
import uk.co.flexi.ri.repository.TempMediaRepo;

import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.NoSuchElementException;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class InspectionItemService {

    private static final String INSTRUCTION_NOT_FOUND = " not fount.";

    private final InspectionItemRepo inspectionItemRepo;

    private final InspectionService inspectionService;

    private final ModelMapper modelMapper;

    private final ObjectMapper objectMapper;

    private final AuthenticatedUserService authenticatedUserService;

    private final ItemConditionService itemConditionService;

    private final ProductService productService;

    private final ReturnReasonService returnReasonService;

    private final EventPublisher eventPublisher;

    private final TempMediaRepo tempMediaRepo;

    public List<InspectionItem> findByInspection(Inspection inspection) {
        return inspectionItemRepo.findByInspection(inspection);
    }

    public InspectionItemDTO update(String inspectionItemId, InspectionItemUpdateReqDTO updateReqDTO) throws IOException {
        InspectionItem inspectionItem = inspectionItemRepo.findByInspectionItemId(inspectionItemId).orElseThrow(()
                -> new NoSuchElementException("Inspection item with " + inspectionItemId + INSTRUCTION_NOT_FOUND));
        ObjectReader readerForUpdating = objectMapper.readerForUpdating(inspectionItem);
        InspectionItem updatedInspectionItem = readerForUpdating.readValue(updateReqDTO.getData());
        updatedInspectionItem = inspectionItemRepo.save(updatedInspectionItem);
        eventPublisher.publish(updatedInspectionItem, EventQueue.EventType.INSP_ITEM_RETURN_REASON_UPDATED);
        return modelMapper.map(updatedInspectionItem, InspectionItemDTO.class);
    }

    public InspectionItemDTO updateStatus(String inspectionItemId, ItemStatusUpdateReqDTO updateReqDTO) {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        Merchant merchant = new Merchant();
        merchant.setId(authenticatedUserDTO.getMerchantId());
        InspectionItem inspectionItem = inspectionItemRepo.findByInspectionItemId(inspectionItemId)
                .orElseThrow(() -> new NoSuchElementException("Inspection item with " + inspectionItemId + INSTRUCTION_NOT_FOUND));
        inspectionItem.setCompletedDate(OffsetDateTime.now());
        inspectionItem.setCompletedBy(authenticatedUserDTO.getUserName());
        inspectionItem.setItemStatus(updateReqDTO.getStatus());
        inspectionItem.setItemCondition(itemConditionService.findById(updateReqDTO.getItemConditionId()).getName());
        inspectionItem = inspectionItemRepo.save(inspectionItem);
        EventQueue.EventType eventType = inspectionItem.getItemStatus() == InspectionItem.InspectionItemStatus.APPROVED
                ? EventQueue.EventType.INSP_ITEM_APPROVED
                : EventQueue.EventType.INSP_ITEM_REJECTED;
        eventPublisher.publish(inspectionItem, eventType);
        Inspection inspection = inspectionItem.getInspection();
        boolean allItemsFiltered = areAllItemsFiltered(inspection);
        Inspection.InspectionStatus newStatus = allItemsFiltered
                ? Inspection.InspectionStatus.COMPLETED
                : Inspection.InspectionStatus.IN_PROGRESS;
        inspectionService.updateStatus(inspection.getId(), newStatus);
        if (newStatus.equals(Inspection.InspectionStatus.COMPLETED)) {
            eventPublisher.publish(inspection, EventQueue.EventType.INSP_COMPLETED);
        }
        return modelMapper.map(inspectionItem, InspectionItemDTO.class);
    }

    private boolean areAllItemsFiltered(Inspection inspection) {
        List<InspectionItem> inspectionItems = findByInspection(inspection);
        long filteredCount = inspectionItems.stream()
                .filter(item -> item.getItemStatus()!=null && filterStatus().contains(item.getItemStatus()))
                .count();
        return filteredCount == inspectionItems.size();
    }

    private List<InspectionItem.InspectionItemStatus> filterStatus() {
        return List.of(
                InspectionItem.InspectionItemStatus.APPROVED,
                InspectionItem.InspectionItemStatus.REJECTED
        );
    }

    public List<String> getReturnReasons() {
        return returnReasonService.findAll().stream().map(ReturnReasonDTO::getReason).toList();
    }

    public InstructionDTO findByProductInstruction(String inspectionItemId, String languageCode) {
        InspectionItem inspectionItem = inspectionItemRepo.findByInspectionItemId(inspectionItemId)
                .orElseThrow(() -> new NoSuchElementException("Inspection item with ID " + inspectionItemId + INSTRUCTION_NOT_FOUND));

        Product product = inspectionItem.getProduct();

        if (product.getProductClass() != null && product.getProductSubclass() != null) {
            return productService.findByProductDetails(
                    product.getProductClass(), product.getProductSubclass(), languageCode);
        } else {
            throw new NoSuchElementException("Instruction not found for this inspection item");
        }
    }

    public List<TempMediaDTO> getTempImages(String inspectionItemId) {
        AuthenticatedUserDTO authenticatedUserDTO = authenticatedUserService.getAuthenticatedUserDetails();
        return tempMediaRepo.findByInspectionItemIdAndTenant(inspectionItemId, authenticatedUserDTO.getTenant()).stream()
                .map(m -> modelMapper.map(m, TempMediaDTO.class)).toList();
    }
}

