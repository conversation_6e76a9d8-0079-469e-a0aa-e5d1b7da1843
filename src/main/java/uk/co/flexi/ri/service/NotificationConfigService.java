package uk.co.flexi.ri.service;

import org.modelmapper.ModelMapper;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import uk.co.flexi.ri.dto.NotificationConfigDTO;
import uk.co.flexi.ri.model.NotificationChannel;
import uk.co.flexi.ri.model.NotificationConfig;
import uk.co.flexi.ri.repository.NotificationChannelRepo;
import uk.co.flexi.ri.repository.NotificationConfigRepo;

import java.util.Arrays;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

import static uk.co.flexi.ri.util.Util.validateCronExpression;

@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class NotificationConfigService {

    private final NotificationConfigRepo notificationConfigRepo;

    private final NotificationChannelRepo notificationChannelRepo;

    private final ModelMapper modelMapper;

    public List<NotificationConfig.NotificationType> getNotificationTypes() {
        return Arrays.stream(NotificationConfig.NotificationType.values()).toList();
    }

    public List<NotificationConfigDTO.Frequency> getFrequencies() {
        return Arrays.stream(NotificationConfigDTO.Frequency.values()).toList();
    }

    public NotificationConfigDTO save(NotificationConfigDTO notificationConfigDTO) throws IllegalAccessException {
        NotificationConfig notificationConfig = modelMapper.map(notificationConfigDTO, NotificationConfig.class);
        NotificationChannel channel = notificationChannelRepo.findById(notificationConfigDTO.getChannelId())
                .orElseThrow(() -> new NoSuchElementException("Channel not found: " + notificationConfigDTO.getChannelId()));
        notificationConfig.setChannel(channel);
        if (notificationConfigDTO.getNotificationType().equals(NotificationConfig.NotificationType.INTEGRATION_FAILURE)) {
            notificationConfig.setFrequency("IMMEDIATELY");
        }
        if (notificationConfigDTO.getNotificationType().equals(NotificationConfig.NotificationType.ASSIGN_TO_ME)) {
            notificationConfig.setFrequency(getFrequency(notificationConfigDTO));
            if (notificationConfigDTO.getUserGroupId() != null) {
                notificationConfig.setUserGroupId(notificationConfigDTO.getUserGroupId());
            } else {
                throw new IllegalAccessException("User group id should not be null for notification type ASSIGN_TO_ME");
            }
        }
        notificationConfig = notificationConfigRepo.save(notificationConfig);
        return modelMapper.map(notificationConfig, NotificationConfigDTO.class);
    }

    public NotificationConfigDTO findById(Long id) {
        Optional<NotificationConfig> notificationConfig = notificationConfigRepo.findById(id);
        return modelMapper.map((notificationConfig.orElseThrow(() -> new NoSuchElementException("NotificationConfig not found with id: " + id))), NotificationConfigDTO.class);
    }

    public NotificationConfigDTO updateConfig(Long id, NotificationConfigDTO updateReqDTO) {
        NotificationConfig existingConfig = notificationConfigRepo.findById(id)
                .orElseThrow(() -> new NoSuchElementException("NotificationConfig not found with id: " + id));

        modelMapper.map(updateReqDTO, existingConfig);
        if (updateReqDTO.getChannelId() != null) {
            NotificationChannel channel = notificationChannelRepo
                    .findById(updateReqDTO.getChannelId())
                    .orElseThrow(() -> new NoSuchElementException("Channel not found: " + updateReqDTO.getChannelId()));
            existingConfig.setChannel(channel);
        }

        if (updateReqDTO.getFrequency() != null) {
            if (updateReqDTO.getNotificationType().equals(NotificationConfig.NotificationType.INTEGRATION_FAILURE)) {
                existingConfig.setFrequency("IMMEDIATELY");
            } else {
                existingConfig.setFrequency(getFrequency(updateReqDTO));
            }
        }

        NotificationConfig updatedConfig = notificationConfigRepo.save(existingConfig);

        return modelMapper.map(updatedConfig, NotificationConfigDTO.class);
    }

    public void deleteById(Long id) {
        NotificationConfig notificationConfig = notificationConfigRepo.findById(id).orElseThrow(() -> new NoSuchElementException("NotificationConfig not found with id: " + id));
        notificationConfigRepo.delete(notificationConfig);
    }

    private String getFrequency(NotificationConfigDTO notificationConfigDTO) {
        return switch (notificationConfigDTO.getFrequency()) {
            case IMMEDIATELY -> "IMMEDIATELY";
            case CUSTOM -> validateCronExpression(notificationConfigDTO.getCustomFrequency());
            case EVERY_ONE_HOUR -> "0 0 0/1 ? * * *";
            case EVERY_TWO_HOUR -> "0 0 0/2 ? * * *";
        };
    }

}
