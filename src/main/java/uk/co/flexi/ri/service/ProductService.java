package uk.co.flexi.ri.service;

import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.InstructionDTO;
import uk.co.flexi.ri.dto.ProductInstructionDTO;
import uk.co.flexi.ri.dto.ProductInstructionUpdateDTO;
import uk.co.flexi.ri.dto.SupportedLanguageDTO;
import uk.co.flexi.ri.model.Instruction;
import uk.co.flexi.ri.model.ProductInstruction;
import uk.co.flexi.ri.repository.InstructionRepo;
import uk.co.flexi.ri.repository.ProductInstructionRepo;

import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
@RequiredArgsConstructor
public class ProductService {

    private static final String PRODUCT_INS_NOT_FOUND = "Product Instruction not found";

    private static final String PRODUCT_CLASS = "Product Class";

    private static final String PRODUCT_SUB_CLASS = "Product Sub Class";

    private static final String INSTRUCTION = "Instruction[%s]";

    private final ProductInstructionRepo productInstructionRepo;

    private final InstructionRepo instructionRepo;

    private final FileFormatServiceFactory fileFormatServiceFactory;

    private final LanguageService languageService;

    private final ModelMapper modelMapper;

    private final EntityManager entityManager;

    public ProductInstructionDTO createProductInstruction(ProductInstructionDTO productInstructionDTO) {
        ProductInstruction productInstruction = modelMapper.map(productInstructionDTO, ProductInstruction.class);
        ProductInstruction finalProductInstruction = productInstruction;
        productInstruction.getInstructions().forEach(instruction -> instruction.setProductInstruction(finalProductInstruction));
        productInstruction = productInstructionRepo.save(productInstruction);
        return modelMapper.map(productInstruction, ProductInstructionDTO.class);
    }

    public List<ProductInstructionDTO> findAllProductInstruction() {
        return productInstructionRepo.findAll().stream().map(r -> modelMapper.map(r, ProductInstructionDTO.class)).toList();
    }

    public List<ProductInstructionDTO> findAllProductInstruction(List<String> ids) {
        return productInstructionRepo.findByProductInstructionIdIn(ids).stream().map(r -> modelMapper.map(r,
                ProductInstructionDTO.class)).toList();
    }

    public ProductInstruction findByProductInstructionId(String productInstructionId) {
        return productInstructionRepo.findByProductInstructionId(productInstructionId).orElseThrow(() -> new NoSuchElementException(PRODUCT_INS_NOT_FOUND));
    }

    public ProductInstructionDTO updateProductInstruction(String productInstructionId, List<ProductInstructionUpdateDTO> instructionDTOs) {
        ProductInstruction productInstruction = findByProductInstructionId(productInstructionId);
        instructionRepo.deleteAllInstruction(productInstruction.getId());
        List<Instruction> instructions = new ArrayList<>();
        instructionDTOs.forEach(ins -> {
            Instruction newInstruction = new Instruction();
            newInstruction.setProductInstruction(productInstruction);
            newInstruction.setLanguageCode(ins.getLanguageCode());
            newInstruction.setContent(ins.getContent());
            instructions.add(newInstruction);
        });
        instructionRepo.saveAll(instructions);
        return modelMapper.map(findByProductInstructionId(productInstructionId), ProductInstructionDTO.class);
    }

    public ProductInstructionDTO findByIdAndLanguage(String productInstructionId, String languageCode) {
        ProductInstruction productInstruction =
                productInstructionRepo.findByProductInstructionIdAndInstructions_LanguageCode(productInstructionId,
                        languageCode).orElseThrow(() -> new NoSuchElementException(PRODUCT_INS_NOT_FOUND));
        return modelMapper.map(productInstruction, ProductInstructionDTO.class);
    }

    public InstructionDTO findByProductDetails(String productClass, String productSubclass, String languageCode) {
        return instructionRepo
                .findByProductInstruction_ProductClassAndProductInstruction_ProductSubclassAndLanguageCode(
                        productClass, productSubclass, languageCode)
                .or(() -> instructionRepo.findByProductInstruction_ProductClassAndProductInstruction_ProductSubclassAndLanguageCode(
                        productClass, productSubclass, "en"))
                .map(instruction -> modelMapper.map(instruction, InstructionDTO.class))
                .orElseThrow(() -> new NoSuchElementException("Instruction not found"));
    }

    public void deleteById(String productInstructionId) {
        ProductInstruction productInstruction =
                productInstructionRepo.findByProductInstructionId(productInstructionId).orElseThrow(() -> new NoSuchElementException(PRODUCT_INS_NOT_FOUND));
        productInstructionRepo.delete(productInstruction);
    }

    public void batchUpsertProductInstructions(List<ProductInstructionDTO> newInputs) {
        if (newInputs.isEmpty()) return;

        Map<String, ProductInstruction> existingMap = fetchExistingProductInstructions(newInputs);
        int batchSize = 50;
        int count = 0;

        for (ProductInstructionDTO dto : newInputs) {
            ProductInstruction newInput = modelMapper.map(dto, ProductInstruction.class);
            String key = getKey(newInput);

            ProductInstruction existing = existingMap.get(key);
            if (existing != null) {
                mergeInstructions(existing, newInput);
                entityManager.merge(existing);
            } else {
                assignBackReference(newInput);
                entityManager.persist(newInput);
            }

            if (++count % batchSize == 0) {
                flushAndClear();
            }
        }
        flushAndClear();
    }

    public List<ProductInstructionDTO> importProductInstruction(String fileExtension, MultipartFile file) throws IOException {
        List<String> lang = languageService.findAllSupportedLanguages().stream()
                .map(SupportedLanguageDTO::getCode).toList();

        List<LinkedHashMap<String, Object>> result =
                fileFormatServiceFactory.getService(fileExtension).importData(file.getInputStream());

        Map<String, ProductInstructionDTO> dtoMap = result.stream()
                .map(i -> convertToInstructionDTO(i, lang))
                .collect(Collectors.toMap(
                        dto -> dto.getProductClass() + "|" + dto.getProductSubclass(),
                        Function.identity(),
                        (existing, replacement) -> replacement
                ));

        batchUpsertProductInstructions(new ArrayList<>(dtoMap.values()));

        return findAllProductInstruction();
    }

    public ProductInstructionDTO convertToInstructionDTO(LinkedHashMap<String, Object> input, List<String> lang) {
        String productClass = String.valueOf(input.get(PRODUCT_CLASS));
        String productSubclass = String.valueOf(input.get(PRODUCT_SUB_CLASS));

        if (productClass == null || productClass.equals("null") || productClass.trim().isEmpty()) {
            throw new IllegalArgumentException("Product Class must not be null or empty");
        }

        if (productSubclass == null || productSubclass.equals("null") || productSubclass.trim().isEmpty()) {
            throw new IllegalArgumentException("Product Sub Class must not be null or empty");
        }

        ProductInstructionDTO instructionDTO = new ProductInstructionDTO();
        instructionDTO.setProductClass(productClass);
        instructionDTO.setProductSubclass(productSubclass);

        List<InstructionDTO> instructions = new ArrayList<>();
        lang.forEach(l -> {
            String key = String.format(INSTRUCTION, l);
            String content = (String) input.getOrDefault(key, null);
            if (content != null) {
                InstructionDTO dto = new InstructionDTO();
                dto.setLanguageCode(l);
                dto.setContent(content);
                instructions.add(dto);
            }
        });

        instructionDTO.setInstructions(instructions);
        return instructionDTO;
    }

    public byte[] exportProductInstruction(String fileExtension, List<String> ids) {
        List<String> lang = languageService.findAllSupportedLanguages().stream()
                .map(SupportedLanguageDTO::getCode)
                .toList();

        List<LinkedHashMap<String, Object>> result;

        List<ProductInstructionDTO> instructionList = (ids != null && !ids.isEmpty()) ? findAllProductInstruction(ids) : findAllProductInstruction();

        if (instructionList.isEmpty()) {
            LinkedHashMap<String, Object> headerMap = new LinkedHashMap<>();
            headerMap.put(PRODUCT_CLASS, "");
            headerMap.put(PRODUCT_SUB_CLASS, "");
            lang.forEach(l -> headerMap.put(String.format(INSTRUCTION, l), ""));
            result = List.of(headerMap);
        } else {
            result = instructionList.stream()
                    .map(r -> convertToMap(r, lang))
                    .toList();
        }
        return fileFormatServiceFactory.getService(fileExtension).exportData(result);
    }

    public LinkedHashMap<String, Object> convertToMap(ProductInstructionDTO input, List<String> lang) {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        map.put(PRODUCT_CLASS, input.getProductClass());
        map.put(PRODUCT_SUB_CLASS, input.getProductSubclass());

        Map<String, String> instructionMap = input.getInstructions().stream()
                .collect(Collectors.toMap(InstructionDTO::getLanguageCode, InstructionDTO::getContent));

        lang.forEach(l -> {
            String key = String.format(INSTRUCTION, l);
            String value = instructionMap.getOrDefault(l, "");
            map.put(key, value);
        });
        return map;
    }

    public void deleteByIds(List<String> instructionIds) {
        List<ProductInstruction> instructions = productInstructionRepo.findByProductInstructionIdIn(instructionIds);
        productInstructionRepo.deleteAll(instructions);
    }

    //Helper methods
    public Map<String, ProductInstruction> fetchExistingProductInstructions(List<ProductInstructionDTO> inputs) {
        List<String> keys = inputs.stream()
                .map(i -> i.getProductClass() + "|" + i.getProductSubclass())
                .distinct()
                .toList();

        List<ProductInstruction> existingList = productInstructionRepo.findAllByClassSubClassIn(keys);
        return existingList.stream()
                .collect(Collectors.toMap(
                        e -> e.getProductClass() + "|" + e.getProductSubclass(),
                        Function.identity()
                ));
    }

    public void mergeInstructions(ProductInstruction existing, ProductInstruction incoming) {
        Map<String, Instruction> existingInstructionsMap = existing.getInstructions().stream()
                .collect(Collectors.toMap(Instruction::getLanguageCode, Function.identity()));

        for (Instruction instruction : incoming.getInstructions()) {
            Instruction existingInstruction = existingInstructionsMap.get(instruction.getLanguageCode());
            if (existingInstruction != null) {
                instruction.setId(existingInstruction.getId());
                instruction.setTenant(existingInstruction.getTenant());
            }
            instruction.setProductInstruction(existing);
        }
        existing.setInstructions(incoming.getInstructions());
    }

    public void assignBackReference(ProductInstruction newInput) {
        for (Instruction instruction : newInput.getInstructions()) {
            instruction.setProductInstruction(newInput);
        }
    }

    public void flushAndClear() {
        entityManager.flush();
        entityManager.clear();
    }

    public String getKey(ProductInstruction input) {
        return input.getProductClass() + "|" + input.getProductSubclass();
    }
}
