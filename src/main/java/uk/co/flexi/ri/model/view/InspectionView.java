package uk.co.flexi.ri.model.view;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.TenantId;
import uk.co.flexi.ri.model.Inspection;

import java.time.OffsetDateTime;

@Getter
@Setter
@Entity
@Immutable
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "inspection_view")
public class InspectionView {

    @Id
    @Column(name = "inspection_id")
    private String inspectionId;

    @Column(name = "reference_id")
    private String referenceId;

    @Column(name = "order_id")
    private String orderId;

    @Column(name = "created_by_group_id")
    private Long createdByGroupId;

    @Column(name = "assignee_group_id")
    private Long assigneeGroupId;

    @Column(name = "assignee_group")
    private String assigneeGroup;

    @Column(name = "created_at")
    private OffsetDateTime createdAt;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private Inspection.InspectionStatus status;

    @Column(name = "selling_channel")
    private String sellingChannel;

    @Column(name = "partner_name")
    private String partnerName;

    @Column(name = "remaining_days")
    private Integer remainingDays;

    @TenantId
    private Long tenant;
}
