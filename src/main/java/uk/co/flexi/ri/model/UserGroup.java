package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.TenantId;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SQLRestriction("is_active = true")
@Table(name = "user_group", uniqueConstraints = @UniqueConstraint(columnNames = {"name", "tenant"}),
        indexes = {
                @Index(name = "idx_user_group_id", columnList = "user_group_id")
        })
public class UserGroup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "user_group_id", columnDefinition = "varchar(36)", nullable = false, unique = true)
    private String userGroupId;

    @Column(name = "name", columnDefinition = "varchar(30)", nullable = false)
    private String name;

    @ManyToMany(mappedBy = "userGroups")
    private Set<User> users = new HashSet<>();

    @Enumerated(EnumType.STRING)
    @Column(name = "comment_visibility", nullable = false)
    private Comment.CommentVisibility visibility = Comment.CommentVisibility.INTERNAL;

    @Column(name = "partner_name", columnDefinition = "varchar(50)")
    private String partnerName;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "user_group_reviewers",
            joinColumns = @JoinColumn(name = "user_group_id"),
            inverseJoinColumns = @JoinColumn(name = "reviewer_group_id")
    )
    private Set<UserGroup> reviewers = new HashSet<>();

    @TenantId
    private Long tenant;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = Boolean.TRUE;

    @PrePersist
    public void prePersist() {
        if (this.userGroupId == null) {
            this.userGroupId = UUID.randomUUID().toString();
        }
    }

    @Override
    public String toString() {
        return "UserGroup{" +
                "id=" + id +
                ", userGroupId='" + userGroupId + '\'' +
                ", name='" + name + '\'' +
                ", users=" + users +
                ", visibility=" + visibility +
                ", partnerName='" + partnerName + '\'' +
                ", tenant=" + tenant +
                '}';
    }
}
