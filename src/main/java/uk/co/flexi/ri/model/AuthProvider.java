package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

@Entity
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "auth_provider")
public class AuthProvider {

    public AuthProvider(Provider provider, Merchant merchant) {
        this.provider = provider;
        this.merchant = merchant;
    }

    public enum Provider {
        DB,
        GOOGLE,
        AZURE,
        OKTA,
        MANHATTAN
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "provider", nullable = false)
    private Provider provider;

    @Column(name = "auth_url", nullable = false)
    private String authUrl;

    @Column(name = "registration_id", unique = true)
    private String registrationId;

    @Column(name = "client_id")
    private String clientId;

    @Column(name = "client_secret")
    private String clientSecret;

    @Column(name = "authorization_uri")
    private String authorizationUri;

    @Column(name = "token_uri")
    private String tokenUri;

    @Column(name = "redirect_uri")
    private String redirectUri;

    @Column(name = "user_info_uri")
    private String userInfoUri;

    @Column(name = "name_attr")
    private String nameAttr;

    @Column(name = "scopes")
    private String scopes;

    @Column(name = "client_authentication_method")
    private String clientAuthenticationMethod;

    @Column(name = "authorization_grant_type")
    private String authorizationGrantType;

    @Column(name = "jwk_set_uri")
    private String jwkSetUri;

    @Column(name = "tenant_id")
    private String tenantId;

    @Column(name = "domain")
    private String domain;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "merchant_id", nullable = false)
    private Merchant merchant;

    @TenantId
    private Long tenant;
}
