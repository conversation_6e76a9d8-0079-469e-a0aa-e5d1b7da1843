package uk.co.flexi.ri.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;
import uk.co.flexi.ri.util.covertor.JsonNodeConverter;
import java.util.UUID;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "template")
public class Template extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "template_id", columnDefinition = "varchar(36)", nullable = false, unique = true)
    private String templateId;

    @Column(name = "template_name", nullable = false)
    private String templateName;

    @Column(name = "template_data", columnDefinition = "json")
    @Convert(converter = JsonNodeConverter.class)
    private JsonNode templateData;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_group")
    @JsonIgnore
    private UserGroup userGroup;

    @TenantId
    private Long tenant;

    @PrePersist
    public void prePersist() {
        if (this.templateId == null) {
            this.templateId = UUID.randomUUID().toString();
        }
    }
}
