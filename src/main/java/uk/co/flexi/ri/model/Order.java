package uk.co.flexi.ri.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

import java.util.List;


@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "orders",
        indexes = {
                @Index(name = "order_id_idx01", columnList = "order_id"),
                @Index(name = "return_tracking_id_idx", columnList = "return_tracking_id"),
                @Index(name = "return_order_id_idx", columnList = "return_order_id")
        })
public class Order extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("id DESC")
    private List<Inspection> inspections;

    @Column(name = "order_id", columnDefinition = "varchar(100)")
    private String orderId;

    @Column(name = "return_order_id", columnDefinition = "varchar(100)")
    private String returnOrderId;

    @Column(name = "return_tracking_id", columnDefinition = "varchar(100)")
    private String returnTrackingId;

    @TenantId
    private Long tenant;
}