package uk.co.flexi.ri.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;
import org.hibernate.envers.Audited;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "comment",
        indexes = @Index(name = "idx_comment_comment_id", columnList = "comment_id"))
public class Comment extends DateAudit {

    public enum CommentVisibility {
        INTERNAL,
        EXTERNAL
    }

    public enum CommentType {
        GENERAL,
        APPROVE,
        REJECT,
        REVIEW
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "comment_id",columnDefinition = "varchar(36)", nullable = false, unique = true)
    @Audited
    private String commentId;

    @Lob
    @Column(name = "content", nullable = false, length = 65536)
    @Audited
    private String content;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User author;

    @OneToMany(mappedBy = "comment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("id DESC")
    private List<Media> media;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "inspection_item_id", nullable = false)
    private InspectionItem inspectionItem;

    @Enumerated(EnumType.STRING)
    @Column(name = "visibility", nullable = false)
    private CommentVisibility visibility = CommentVisibility.INTERNAL;

    @Enumerated(EnumType.STRING)
    @Column(name = "comment_type")
    private CommentType commentType = CommentType.GENERAL;

    @TenantId
    @Audited
    private Long tenant;

    @Transient
    @Audited(modifiedColumnName = "reference_id")
    private String referenceId;

    @PrePersist
    public void prePersist() {
        if (this.commentId == null) {
            this.commentId = UUID.randomUUID().toString();
        }
    }

    public String getReferenceId() {
        return Optional.ofNullable(inspectionItem)
                .map(InspectionItem::getInspection)
                .map(Inspection::getReferenceId)
                .orElse(null);
    }
}
