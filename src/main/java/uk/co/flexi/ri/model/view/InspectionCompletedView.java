package uk.co.flexi.ri.model.view;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.TenantId;

import java.time.LocalDate;

@Getter
@Setter
@Entity
@Immutable
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "inspection_completed_view")
public class InspectionCompletedView {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "completed_by")
    private String completedBy;

    @Column(name = "completed_date")
    private LocalDate completedDate;

    @Column(name = "completed_count")
    private Long completedCount;

    @TenantId
    private Long tenant;
}
