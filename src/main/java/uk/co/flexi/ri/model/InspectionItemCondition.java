package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

import java.util.UUID;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "inspection_item_condition", uniqueConstraints = @UniqueConstraint(columnNames = {"name", "tenant"}))
public class InspectionItemCondition {

    public enum ConditionType {
        APPROVED,
        REJECTED
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "item_condition_id", columnDefinition = "varchar(36)", nullable = false, unique = true)
    private String itemConditionId;

    @Enumerated(EnumType.STRING)
    @Column(name = "condition_type", nullable = false)
    private ConditionType conditionType;

    @Column(name = "name", columnDefinition = "varchar(100)", nullable = false)
    private String name;

    @TenantId
    private Long tenant;

    @PrePersist
    public void prePersist() {
        if (this.itemConditionId == null) {
            this.itemConditionId = UUID.randomUUID().toString();
        }
    }
}
