package uk.co.flexi.ri.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.List;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "merchant")
public class Merchant {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "merchant_name", unique = true, columnDefinition = "varchar(50)", nullable = false)
    private String merchantName;

    @Column(name = "merchant_email", columnDefinition = "varchar(30)")
    private String merchantEmail;

    @OneToMany(mappedBy = "merchant", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<AuthProvider> authProviders;

    @Column(name = "reporting_currency", columnDefinition = "varchar(3)")
    private String reportingCurrency;

    @Column(name = "hmac_key")
    private String hmacKey;

    @Column(name = "tenant", unique = true, nullable = false, updatable = false)
    private Long tenant;

    @PrePersist
    public void generateTenantId() {
        if (merchantName != null && tenant == null) {
            tenant = calculateTenantId(merchantName);
        }
    }

    private long calculateTenantId(String name) {
        long tenantId = 0;
        int weightFactor = 10000;
        for (char ch : name.toCharArray()) {
            tenantId += ((long) ch) * weightFactor;
            weightFactor /= 10;
        }
        return tenantId;
    }

    public String generateHmacKey(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] encodedHash = digest.digest(input.getBytes());
            return Base64.getEncoder().encodeToString(encodedHash);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Unable to generate HMAC key", e);
        }
    }

//    public static void main(String[] args) {
//        Merchant merchant = new Merchant();
//        merchant.setMerchantName("FlexiRetail");
//
//        long tenantId = merchant.calculateTenantId(merchant.getMerchantName());
//        String hmac = merchant.generateHmacKey(merchant.getMerchantName());
//
//        System.out.println("Generated Tenant ID: " + tenantId);
//        System.out.println("Generated HMAC Key: " + hmac);
//    }
}
