package uk.co.flexi.ri.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.TenantId;
import uk.co.flexi.ri.util.covertor.MapToJsonConverter;

import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "audit_events")
public class AuditEvent {

    public enum EventType {
        INSPECTION_ITEM_CREATE,
        INSPECTION_ITEM_DELETE,
        INSPECTION_ITEM_SEND_FOR_REVIEW,
        INSPECTION_ITEM_APPROVED,
        INSPECTION_ITEM_REJECTED
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_type")
    private EventType eventType;

    @Column(name = "entity_id", columnDefinition = "varchar(36)")
    private String entityId;

    @Column(name = "unique_id", columnDefinition = "varchar(100)")
    private String uniqueId;

    @Column(name = "user_group_id", columnDefinition = "varchar(36)")
    private String userGroupId;

    @Column(name = "user_id", columnDefinition = "varchar(36)")
    private String userId;

    @Column(name = "details", columnDefinition = "json")
    @Convert(converter = MapToJsonConverter.class)
    private Map<String, Object> details = new HashMap<>();

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd' 'HH:mm:ss")
    @Column(name = "created_at", updatable = false)
    @CreationTimestamp
    private OffsetDateTime createdAt;

    @TenantId
    private Long tenant;
}
