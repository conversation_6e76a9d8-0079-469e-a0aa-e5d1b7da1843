package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;
import org.hibernate.envers.Audited;

import java.util.UUID;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "media",
        indexes = @Index(name = "idx_media_media_id", columnList = "media_id"))
public class Media extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "media_id",columnDefinition = "varchar(36)", nullable = false, unique = true)
    @Audited
    private String mediaId;

    @Column(name = "media_type", nullable = false)
    private String mediaType;

    @Column(name = "file_name", nullable = false)
    private String fileName;

    @Column(name = "url")
    @Audited
    private String url;

    @Lob
    @Column(name = "data", length=20000000)
    private byte[] data;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inspection_item_id")
    private InspectionItem inspectionItem;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "comment_id")
    private Comment comment;

    @TenantId
    @Audited
    private Long tenant;

    @PrePersist
    public void prePersist() {
        if (this.mediaId == null) {
            this.mediaId = UUID.randomUUID().toString();
        }
    }
}

