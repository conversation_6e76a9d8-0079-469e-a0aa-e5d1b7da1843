package uk.co.flexi.ri.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "instruction", uniqueConstraints = @UniqueConstraint(columnNames = {"language_code", "product_instruction_id"}))
public class Instruction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "product_instruction_id", referencedColumnName = "id", nullable = false)
    @JsonIgnore
    private ProductInstruction productInstruction;

    @Column(name = "language_code",columnDefinition = "varchar(10)", nullable = false)
    private String languageCode;

    @Lob
    @Column(name = "content", length=20000000, nullable = false)
    private String content;

    @TenantId
    private Long tenant;
}
