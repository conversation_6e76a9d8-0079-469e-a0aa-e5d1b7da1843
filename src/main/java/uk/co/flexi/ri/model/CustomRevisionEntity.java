package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.DefaultRevisionEntity;
import org.hibernate.envers.RevisionEntity;
import uk.co.flexi.ri.listener.InspectionRevisionListener;

@Setter
@Getter
@Entity
@Table(name = "revinfo")
@RevisionEntity(InspectionRevisionListener.class)
public class CustomRevisionEntity extends DefaultRevisionEntity {

    @Column(name = "unique_id")
    private String uniqueId;
}
