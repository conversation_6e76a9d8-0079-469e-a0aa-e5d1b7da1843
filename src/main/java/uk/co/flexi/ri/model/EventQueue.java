package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import uk.co.flexi.ri.util.covertor.MapToJsonConverter;

import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;


@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "event_queue")
public class EventQueue {

    public enum EventType {
        INSP_CREATED,
        INSP_ITEM_APPROVED,
        INSP_ITEM_REJECTED,
        INSP_SEND_FOR_REVIEW,
        INSP_ITEM_COMMENT_ADDED,
        INSP_ITEM_COMMENT_UPDATED,
        INSP_ITEM_COMMENT_DELETED,
        INSP_ITEM_RETURN_REASON_UPDATED,
        INSP_COMPLETED
    }

    public enum ReferenceType {
        INSPECTION_ITEM, INSPECTION, COMMENT
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "event_id", columnDefinition = "varchar(36)", nullable = false, unique = true)
    private String eventId;

    @Column(name = "unique_id", nullable = false)
    private String uniqueId;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_type", nullable = false)
    private EventType eventType;

    @Column(name = "config_id", nullable = false)
    private Long configId;

    @Enumerated(EnumType.STRING)
    @Column(name = "reference_type", nullable = false)
    private ReferenceType referenceType;

    @Column(name = "event_time", nullable = false)
    private OffsetDateTime eventTime = OffsetDateTime.now();

    @Column(name = "performed_by_user", nullable = false)
    private String performedByUser;

    @Column(name = "performed_by_group", nullable = false)
    private String performedByGroup;

    @Column(name = "payload", columnDefinition = "json")
    @Convert(converter = MapToJsonConverter.class)
    private Map<String, Object> payload = new HashMap<>();

    @Column(name = "tenant", nullable = false)
    private Long tenant;

    @PrePersist
    public void prePersist() {
        if (this.eventId == null) {
            this.eventId = UUID.randomUUID().toString();
        }
    }
}
