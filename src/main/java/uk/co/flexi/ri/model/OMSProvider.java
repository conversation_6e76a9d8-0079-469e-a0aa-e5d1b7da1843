package uk.co.flexi.ri.model;

import jakarta.persistence.CollectionTable;
import jakarta.persistence.Column;
import jakarta.persistence.ElementCollection;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

import java.util.List;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "oms_provider",
        uniqueConstraints = @UniqueConstraint(name = "unique_provider", columnNames = {"provider_name", "tenant"}))
public class OMSProvider extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "provider_name", nullable = false)
    private String providerName;

    @ElementCollection
    @CollectionTable(name = "search_strategies", joinColumns = @JoinColumn(name = "search_strategy_id"))
    @Column(name = "search_strategy")
    private List<String> searchStrategy;

    @TenantId
    private Long tenant;
}
