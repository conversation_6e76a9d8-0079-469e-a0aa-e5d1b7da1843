package uk.co.flexi.ri.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;
import org.hibernate.envers.Audited;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "inspection",
        indexes = {
                @Index(name = "idx_inspection_inspection_id", columnList = "inspection_id")
        })
public class Inspection extends DateAudit {

    public enum InspectionStatus {
        IN_PROGRESS,
        COMPLETED
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "inspection_id", columnDefinition = "varchar(36)", nullable = false, unique = true)
    @Audited
    private String inspectionId;

    @Column(name = "reference_id", columnDefinition = "varchar(50)", nullable = false)
    @Audited
    private String referenceId;

    @OneToMany(mappedBy = "inspection", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @OrderBy("id DESC")
    private List<InspectionItem> inspectionItems;

    @ManyToOne(optional = false, cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", referencedColumnName = "id", nullable = false)
    private Order order;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Audited
    private InspectionStatus status;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "assignee", nullable = false)
    @JsonIgnore
    private User assignee;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assignee_group")
    @JsonIgnore
    private UserGroup assigneeGroup;

    @Column(name = "assignee_group_name")
    @Audited
    private String assigneeGroupName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "last_assigned_group")
    @JsonIgnore
    private UserGroup lastAssignedGroup;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by_group")
    @JsonIgnore
    private UserGroup createdByGroup;

    @Column(name = "partner_name", columnDefinition = "varchar(50)")
    private String partnerName;

    @Column(name = "selling_channel", columnDefinition = "varchar(50)")
    private String sellingChannel;

    @Column(name = "customer_first_name")
    private String customerFirstName;

    @Column(name = "customer_last_name")
    private String customerLastName;

    @Column(name = "customer_email")
    private String customerEmail;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd' 'HH:mm:ss")
    @Column(name = "assigned_date")
    private OffsetDateTime assignedDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd' 'HH:mm:ss")
    @Column(name = "completed_date")
    private OffsetDateTime completedDate;

    @Column(name = "completed_by")
    private String completedBy;

    @TenantId
    @Audited
    private Long tenant;

    @PrePersist
    public void prePersist() {
        if (this.inspectionId == null) {
            this.inspectionId = UUID.randomUUID().toString();
        }
    }
}