package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "supported_language")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SupportedLanguage {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "code", columnDefinition = "varchar(10)", nullable = false)
    private String code;

    @Column(name = "name", columnDefinition = "varchar(10)", nullable = false)
    private String name;
}
