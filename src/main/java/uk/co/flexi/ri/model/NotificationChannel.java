package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;
import uk.co.flexi.ri.util.covertor.MapToJsonConverter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "notification_channel")
public class NotificationChannel {

    public enum Channel {
        EMAIL,
        SLACK,
        TEAMS
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(name = "channel", nullable = false)
    private Channel channel;

    @Column(name = "config", columnDefinition = "json")
    @Convert(converter = MapToJsonConverter.class)
    private Map<String, Object> config = new HashMap<>();

    @TenantId
    private Long tenant;
}
