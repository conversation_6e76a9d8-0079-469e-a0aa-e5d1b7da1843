package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

import java.util.UUID;


@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "channel", uniqueConstraints = @UniqueConstraint(columnNames = {"name", "tenant"}))
public class Channel {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "channel_id",columnDefinition = "varchar(36)", nullable = false, unique = true)
    private String channelId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "sla")
    private Integer serviceLevelAgreement;

    @TenantId
    private Long tenant;

    @PrePersist
    public void prePersist() {
        if (this.channelId == null) {
            this.channelId = UUID.randomUUID().toString();
        }
    }
}
