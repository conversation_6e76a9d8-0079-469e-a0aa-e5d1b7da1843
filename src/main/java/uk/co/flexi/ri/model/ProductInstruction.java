package uk.co.flexi.ri.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

import java.util.List;
import java.util.UUID;


@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "product_instruction", uniqueConstraints = @UniqueConstraint(columnNames = {"product_class",
        "product_sub_class", "tenant"}))
public class ProductInstruction extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "product_instruction_id", columnDefinition = "varchar(36)", nullable = false, unique = true)
    private String productInstructionId;

    @Column(name = "product_class", nullable = false)
    private String productClass;

    @Column(name = "product_sub_class")
    private String productSubclass;

    @OneToMany(mappedBy = "productInstruction", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("id DESC")
    private List<Instruction> instructions;

    @TenantId
    private Long tenant;

    @PrePersist
    public void prePersist() {
        if (this.productInstructionId == null) {
            this.productInstructionId = UUID.randomUUID().toString();
        }
    }
}
