package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.annotations.TenantId;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@SQLRestriction("is_active = true")
@Table(name = "user", uniqueConstraints = @UniqueConstraint(columnNames = {"user_name", "tenant"}),
        indexes = {@Index(name = "user_idx01", columnList = "user_name")})
public class User extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "user_id", columnDefinition = "varchar(36)", nullable = false, unique = true)
    private String userId;

    @Column(name = "user_name", columnDefinition = "varchar(50)", nullable = false)
    private String userName;

    @Column(name = "password")
    private String password;

    @Column(name = "first_name", columnDefinition = "varchar(30)")
    private String firstName;

    @Column(name = "last_name", columnDefinition = "varchar(30)")
    private String lastName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_group_id", nullable = false)
    private RoleGroup roleGroup;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(name = "user_group_rel", joinColumns = @JoinColumn(name = "user_id"), inverseJoinColumns = @JoinColumn(name = "group_id"))
    private Set<UserGroup> userGroups = new HashSet<>();

    @ManyToOne
    @JoinColumn(name = "auth_provider_id", referencedColumnName = "id")
    private AuthProvider authProvider;

    @Column(name = "time_zone", columnDefinition = "varchar(50)")
    private String timeZone;

    @Column(name = "language", columnDefinition = "varchar(50)", nullable = false)
    private String language = "en";

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "merchant_id", nullable = false)
    private Merchant merchant;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "profile_image_id")
    private Media profileImage;

    @TenantId
    private Long tenant;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = Boolean.TRUE;

    @PrePersist
    public void prePersist() {
        if (this.userId == null) {
            this.userId = UUID.randomUUID().toString();
        }
    }
}
