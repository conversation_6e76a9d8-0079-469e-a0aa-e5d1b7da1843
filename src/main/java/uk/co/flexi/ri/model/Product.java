package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Digits;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;
import uk.co.flexi.ri.util.covertor.MapToJsonConverter;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "product")
public class Product extends DateAudit {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "sku",columnDefinition = "varchar(50)", nullable = false)
    private String sku;

    @Column(name = "product_name", nullable = false)
    private String productName;

    @Column(name = "product_class")
    private String productClass;

    @Column(name = "product_sub_class")
    private String productSubclass;

    @Column(name = "department_name")
    private String departmentName;

    @Column(name = "product_size")
    private String productSize;

    @Column(name = "product_style")
    private String productStyle;

    @Column(name = "unit_price")
    @Digits(integer = 13, fraction = 6)
    private BigDecimal unitPrice;

    @Column(name = "currency_code", columnDefinition = "varchar(3)")
    private String currencyCode;

    @Lob
    @Column(name = "product_image", nullable = false, length = 65536)
    private String productImage;

    @Lob
    @Column(name = "description", nullable = false, length = 65536)
    private String description;

    @Column(name = "return_reason")
    private String returnReason;

    @Column(columnDefinition = "json")
    @Convert(converter = MapToJsonConverter.class)
    private Map<String, Object> properties = new HashMap<>();

    @TenantId
    private Long tenant;
}
