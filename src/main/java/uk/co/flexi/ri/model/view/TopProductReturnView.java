package uk.co.flexi.ri.model.view;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.TenantId;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Immutable
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "top_product_return_view")
public class TopProductReturnView {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "created_at")
    private LocalDate createdAt;

    @Column(name = "product_style")
    private String productStyle;

    @Column(name = "product_name")
    private String productName;

    @Column(name = "sku")
    private String sku;

    @Column(name = "product_size")
    private String productSize;

    @Column(name = "return_reason")
    private String returnReason;

    @Column(name = "selling_channel")
    private String sellingChannel;

    @Column(name = "return_count")
    private Long returnCount;

    @Column(name = "price")
    private BigDecimal price;

    @TenantId
    private Long tenant;
}
