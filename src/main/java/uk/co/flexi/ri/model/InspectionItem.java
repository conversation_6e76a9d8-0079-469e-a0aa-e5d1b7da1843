package uk.co.flexi.ri.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Digits;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "inspection_item",
        indexes = @Index(name = "idx_inspection_item_inspection_item_id", columnList = "inspection_item_id"))
public class InspectionItem extends DateAudit {

    public enum InspectionItemStatus {
        APPROVED,
        REJECTED,
        REVIEW
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "inspection_item_id",columnDefinition = "varchar(36)", nullable = false, unique = true)
    @Audited
    private String inspectionItemId;

    @Column(name = "order_line_id", nullable = false)
    private String orderLineId;

    @Enumerated(EnumType.STRING)
    @Column(name = "item_status")
    @Audited
    private InspectionItemStatus itemStatus;

    @Column(name = "item_condition")
    private String itemCondition;

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JoinColumn(name = "inspection_id", referencedColumnName = "id", nullable = false)
    @JsonIgnore
    private Inspection inspection;

    @ManyToOne(optional = false, cascade = CascadeType.ALL , fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", referencedColumnName = "id", nullable = false)
    private Product product;

    @OneToMany(mappedBy = "inspectionItem", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @OrderBy("id DESC")
    private List<Comment> comments;

    @OneToMany(mappedBy = "inspectionItem", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @OrderBy("id DESC")
    private List<Media> media;

    @Column(name = "expected_quantity", columnDefinition = "Decimal(14,4)", nullable = false)
    @Digits(integer = 10, fraction = 4)
    private BigDecimal expectedQuantity;

    @Column(name = "received_quantity", columnDefinition = "Decimal(14,4)", nullable = false)
    @Digits(integer = 10, fraction = 4)
    private BigDecimal receivedQuantity;

    @Column(name = "return_reason")
    @Audited
    private String returnReason;

    @Column(name = "expected_item_condition")
    private String expectedItemCondition;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd' 'HH:mm:ss")
    @Column(name = "completed_date")
    private OffsetDateTime completedDate;

    @Column(name = "completed_by")
    private String completedBy;

    @Column(name = "country")
    private String country;

    @Column(name = "region")
    private String region;

    @Column(name = "city")
    private String city;

    @TenantId
    @Audited
    private Long tenant;

    @PrePersist
    public void prePersist() {
        if (this.inspectionItemId == null) {
            this.inspectionItemId = UUID.randomUUID().toString();
        }
    }
}
