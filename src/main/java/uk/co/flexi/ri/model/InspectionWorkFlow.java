package uk.co.flexi.ri.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

import java.time.OffsetDateTime;


@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "inspection_work_flow")
public class InspectionWorkFlow {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "inspection_id", columnDefinition = "varchar(36)", nullable = false)
    private String inspectionId;

    @Column(name = "source_group", nullable = false)
    private String sourceGroup;

    @Column(name = "target_group", nullable = false)
    private String targetGroup;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "final_status", nullable = false)
    private String finalStatus;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd' 'HH:mm:ss")
    @Column(name = "created_date")
    private OffsetDateTime createdDate;

    @TenantId
    private Long tenant;
}
