package uk.co.flexi.ri.model;

import uk.co.flexi.sdk.oms.mao.client.OMSClient;
import uk.co.flexi.sdk.oms.model.OrderData;

public enum SearchStrategy {

    RETURN_BY_TRACKING_NUMBER {
        @Override
        public OrderData apply(OMSClient<?> client, String input) {
            return client.getReturnOrderByTrackingNumber(input);
        }
    },
    ORDER_BY_ID {
        @Override
        public OrderData apply(OMSClient<?> client, String input) {
            return client.getOrderById(input);
        }
    },
    ORDER_BY_RETURN_TRACKING {
        @Override
        public OrderData apply(OMSClient<?> client, String input) {
            return client.getOrderByReturnTrackingNumber(input);
        }
    },
    ORDER_BY_PRODUCT_SERIAL_NUMBER {
        @Override
        public OrderData apply(OMSClient<?> client, String input) {
            return client.getOrderByProductSerialNumber(input);
        }
    };

    public abstract OrderData apply(OMSClient<?> client, String input);
}
