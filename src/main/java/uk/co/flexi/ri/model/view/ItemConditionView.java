package uk.co.flexi.ri.model.view;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.TenantId;
import uk.co.flexi.ri.model.InspectionItem;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Immutable
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "item_condition_view")
public class ItemConditionView {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "created_at")
    private LocalDate createdAt;

    @Column(name = "selling_channel")
    private String sellingChannel;

    @Column(name = "item_condition")
    private String itemCondition;

    @Enumerated(EnumType.STRING)
    @Column(name = "item_status")
    private InspectionItem.InspectionItemStatus itemStatus;

    @Column(name = "product_class")
    private String productClass;

    @Column(name = "department_name")
    private String departmentName;

    @Column(name = "return_count")
    private Long returnCount;

    @Column(name = "price")
    private BigDecimal price;

    @TenantId
    private Long tenant;
}
