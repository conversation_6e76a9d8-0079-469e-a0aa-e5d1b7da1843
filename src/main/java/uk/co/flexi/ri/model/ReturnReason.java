package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.TenantId;

import java.util.UUID;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "return_reason", uniqueConstraints = @UniqueConstraint(columnNames = {"reason", "tenant"}))
public class ReturnReason {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "return_reason_id", columnDefinition = "varchar(36)", nullable = false, unique = true)
    private String returnReasonId;

    @Column(name = "reason", nullable = false)
    private String reason;

    @TenantId
    private Long tenant;

    @PrePersist
    public void prePersist() {
        if (this.returnReasonId == null) {
            this.returnReasonId = UUID.randomUUID().toString();
        }
    }
}
