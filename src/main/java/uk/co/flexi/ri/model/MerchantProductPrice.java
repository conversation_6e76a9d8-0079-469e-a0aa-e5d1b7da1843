package uk.co.flexi.ri.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.Digits;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "merchant_product_price",
        indexes = {
                @Index(name = "idx_merchant_product_price_sku", columnList = "sku"),
                @Index(name = "idx_merchant_product_price_tenant", columnList = "tenant")
        },
        uniqueConstraints = @UniqueConstraint(name = "uk_sku_tenant", columnNames = {"sku", "tenant"}))
public class MerchantProductPrice {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false, updatable = false)
    private Long id;

    @Column(name = "sku", nullable = false)
    private String sku;

    @Column(name = "currency_code", columnDefinition = "varchar(3)", nullable = false)
    private String currencyCode;

    @Column(name = "total_unit", columnDefinition = "Decimal(14,4)", nullable = false)
    @Digits(integer = 10, fraction = 4)
    private BigDecimal totalUnit;

    @Column(name = "unit_price", nullable = false)
    @Digits(integer = 13, fraction = 6)
    private BigDecimal unitPrice;

    @Column(name = "tenant", nullable = false)
    private Long tenant;
}
