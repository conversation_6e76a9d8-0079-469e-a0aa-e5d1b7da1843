package uk.co.flexi.ri.model.view;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.TenantId;

import java.time.LocalDate;

@Getter
@Setter
@Entity
@Immutable
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "inspection_time_tracker_view")
public class TimeTrackerView {

    @Id
    @Column(name = "id")
    private Long id;

    @Column(name = "completed_date")
    private LocalDate completedAt;

    @Column(name = "return_count")
    private Long returnCount;

    @Column(name = "days")
    private Integer days;

    @TenantId
    private Long tenant;
}
