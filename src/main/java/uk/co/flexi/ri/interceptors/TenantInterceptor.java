package uk.co.flexi.ri.interceptors;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.servlet.HandlerInterceptor;
import uk.co.flexi.ri.service.TenantIdentifierResolver;
import uk.co.flexi.ri.util.Constants;

public class TenantInterceptor implements HandlerInterceptor {

    private final TenantIdentifierResolver tenantResolver;

    public TenantInterceptor(TenantIdentifierResolver tenantResolver) {
        this.tenantResolver = tenantResolver;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
        String headerTenant = (String) request.getAttribute(Constants.TENANT);
        if (headerTenant != null) {
            Long tenantId = Long.valueOf(headerTenant);
            tenantResolver.setCurrentTenant(tenantId);
        }
        return true;
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) {
        tenantResolver.clear();
    }
}
