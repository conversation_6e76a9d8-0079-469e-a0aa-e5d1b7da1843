package uk.co.flexi.ri.mapper.factory;

import org.springframework.stereotype.Component;
import uk.co.flexi.ri.mapper.OrderDetailMapper;

import java.util.Map;

@Component
public class OrderDetailMapperFactory {

    private final Map<String, OrderDetailMapper> mappers;

    public OrderDetailMapperFactory(Map<String, OrderDetailMapper> mappers) {
        this.mappers = mappers;
    }

    public OrderDetailMapper getMapper(String provider) {
        return mappers.getOrDefault(provider, mappers.get("defaultMapper"));
    }
}
