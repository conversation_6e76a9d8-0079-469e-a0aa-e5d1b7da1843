package uk.co.flexi.ri.mapper;

import uk.co.flexi.ri.dto.OrderDetailDTO;
import uk.co.flexi.ri.model.Inspection;
import uk.co.flexi.ri.model.InspectionItem;
import uk.co.flexi.ri.model.Order;
import uk.co.flexi.sdk.oms.model.OrderData;

import java.math.BigDecimal;
import java.util.Map;
import java.util.stream.Collectors;

public interface OrderDetailMapper {

    OrderDetailDTO map(OrderData orderData, String referenceId);

    default OrderDetailDTO mapExistingOrderDetailDTO(Order order, OrderDetailDTO orderDetailDTO, String inspectionId) {
        Inspection inspection;
        if (inspectionId != null) {
            inspection = order.getInspections().stream()
                    .filter(i -> i.getInspectionId().equals(inspectionId))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("No inspections found for the order"));
        } else {
            inspection = order.getInspections().stream()
                    .findFirst()
                    .orElseThrow(() -> new IllegalStateException("No inspections found for the order"));
        }
        orderDetailDTO.setInspectionId(inspection.getInspectionId());
        orderDetailDTO.setOrderPk(order.getId());

        Map<String, BigDecimal> skuQtyMap = buildExistingSkuQtyMap(inspection);

        orderDetailDTO.getProductDetails().forEach(productDetail -> {
            BigDecimal inspectedQty = skuQtyMap.get(productDetail.getSku());
            if (inspectedQty != null && inspectionId != null) {
                productDetail.setReceivedQuantity(inspectedQty);
            }
        });
        return orderDetailDTO;
    }

    private Map<String, BigDecimal> buildExistingSkuQtyMap(Inspection inspection) {
        return inspection.getInspectionItems().stream()
                .filter(item -> item.getProduct() != null)
                .collect(Collectors.groupingBy(
                        item -> item.getProduct().getSku(),
                        Collectors.reducing(
                                BigDecimal.ZERO,
                                InspectionItem::getReceivedQuantity,
                                BigDecimal::add
                        )
                ));
    }
}
