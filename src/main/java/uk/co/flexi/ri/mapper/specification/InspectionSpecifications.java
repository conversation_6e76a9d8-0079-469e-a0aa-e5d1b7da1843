package uk.co.flexi.ri.mapper.specification;

import org.springframework.data.jpa.domain.Specification;
import uk.co.flexi.ri.model.view.InspectionView;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

public class InspectionSpecifications {

    private static final String CREATED_AT = "createdAt";

    private InspectionSpecifications(){

    }

    public static Specification<InspectionView> withAssigneeGroup(Long assigneeGroup) {
        return (root, query, builder) ->
                builder.equal(root.get("assigneeGroupId"), assigneeGroup);
    }

    public static Specification<InspectionView> withCreatedGroup(Long createdGroup) {
        return (root, query, builder) ->
                builder.equal(root.get("createdByGroupId"), createdGroup);
    }

    public static Specification<InspectionView> withPartnerName(String partnerName, String oper) {
        return (root, query, builder) -> {
            if (partnerName == null) {
                return null;
            }
            if ("neq".equalsIgnoreCase(oper)) {
                return builder.notEqual(root.get("partnerName"), partnerName);
            }
            return builder.equal(root.get("partnerName"), partnerName);
        };
    }

    public static Specification<InspectionView> withReferenceId(String referenceId, String oper) {
        return (root, query, builder) -> {
            if (referenceId == null) {
                return null;
            }
            if ("neq".equalsIgnoreCase(oper)) {
                return builder.notEqual(root.get("referenceId"), referenceId);
            }
            return builder.equal(root.get("referenceId"), referenceId);
        };
    }

    public static Specification<InspectionView> withOrderId(String orderId, String oper) {
        return (root, query, builder) -> {
            if (orderId == null) {
                return null;
            }
            if ("neq".equalsIgnoreCase(oper)) {
                return builder.notEqual(root.get("orderId"), orderId);
            }
            return builder.equal(root.get("orderId"), orderId);
        };
    }

    public static Specification<InspectionView> withCreatedAt(String createdAt, String oper) {
        return (root, query, builder) -> {
            if (createdAt == null || createdAt.isBlank()) {
                return null;
            }
            LocalDate date = LocalDate.parse(createdAt);
            OffsetDateTime startOfDay = date.atStartOfDay().atOffset(ZoneOffset.UTC);
            OffsetDateTime endOfDay = date.plusDays(1).atStartOfDay().atOffset(ZoneOffset.UTC).minusNanos(1);
            if ("neq".equalsIgnoreCase(oper)) {
                return builder.or(
                        builder.lessThan(root.get(CREATED_AT), startOfDay),
                        builder.greaterThan(root.get(CREATED_AT), endOfDay)
                );
            }
            return builder.between(root.get(CREATED_AT), startOfDay, endOfDay);
        };
    }

    public static Specification<InspectionView> withStatus(String status, String oper) {
        return (root, query, builder) -> {
            if (status == null) {
                return null;
            }
            if ("neq".equalsIgnoreCase(oper)) {
                return builder.notEqual(root.get("status"), status);
            }
            return builder.equal(root.get("status"), status);
        };
    }

    public static Specification<InspectionView> withSellingChannel(String sellingChannel, String oper) {
        return (root, query, builder) -> {
            if (sellingChannel == null) {
                return null;
            }
            if ("neq".equalsIgnoreCase(oper)) {
                return builder.notEqual(root.get("sellingChannel"), sellingChannel);
            }
            return builder.equal(root.get("sellingChannel"), sellingChannel);
        };
    }

    public static Specification<InspectionView> withAssigneeGroup(String assigneeGroup, String oper) {
        return (root, query, builder) -> {
            if (assigneeGroup == null) {
                return null;
            }
            if ("neq".equalsIgnoreCase(oper)) {
                return builder.notEqual(root.get("assigneeGroup"), assigneeGroup);
            }
            return builder.equal(root.get("assigneeGroup"), assigneeGroup);
        };
    }

    public static Specification<InspectionView> withRemainingDays(Integer remainingDays, String oper) {
        return (root, query, builder) -> {
            if (remainingDays == null) {
                return null;
            }
            if ("neq".equalsIgnoreCase(oper)) {
                return builder.notEqual(root.get("remainingDays"), remainingDays);
            }
            return builder.equal(root.get("remainingDays"), remainingDays);
        };
    }
}
