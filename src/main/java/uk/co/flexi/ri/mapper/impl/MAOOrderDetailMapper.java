package uk.co.flexi.ri.mapper.impl;

import org.springframework.stereotype.Component;
import uk.co.flexi.ri.dto.OrderDetailDTO;
import uk.co.flexi.ri.dto.ProductDetailDTO;
import uk.co.flexi.ri.mapper.OrderDetailMapper;
import uk.co.flexi.sdk.oms.model.OrderData;
import uk.co.flexi.sdk.oms.model.OrderLineData;

import java.math.BigDecimal;
import java.util.List;

@Component("maoMOrderDetailMapper")
public class MAOOrderDetailMapper implements OrderDetailMapper {

    @Override
    public OrderDetailDTO map(OrderData orderData, String referenceId) {
        OrderDetailDTO orderDetailDTO = new OrderDetailDTO();
        orderDetailDTO.setReferenceId(referenceId);
        orderDetailDTO.setPartnerName(returnIfValid(orderData.getPartnerName()));
        orderDetailDTO.setSellingChannel(returnIfValid(orderData.getSellingChannel()));
        if (orderData.getParentOrderId() == null) {
            orderDetailDTO.setOrderId(orderData.getOrderId());
        } else {
            orderDetailDTO.setOrderId(orderData.getParentOrderId());
            orderDetailDTO.setReturnOrderId(orderData.getOrderId());
        }
        orderDetailDTO.setReturnTrackingId(orderData.getTrackingNumber());
        orderDetailDTO.setCustomerFirstName(orderData.getCustomerFirstName());
        orderDetailDTO.setCustomerLastName(orderData.getCustomerLastName());
        orderDetailDTO.setCustomerEmail(orderData.getCustomerEmail());

        List<ProductDetailDTO> productDetails = orderData.getOrderLines()
                .stream()
                .map(this::mapOrderLineDataToProductDetailDTO)
                .toList();
        orderDetailDTO.setProductDetails(productDetails);
        return orderDetailDTO;
    }

    private ProductDetailDTO mapOrderLineDataToProductDetailDTO(OrderLineData orderLineData) {
        ProductDetailDTO productDetailDTO = new ProductDetailDTO();

        productDetailDTO.setProductName(orderLineData.getItem().getName());
        productDetailDTO.setSku(orderLineData.getItem().getSku());
        productDetailDTO.setDescription(orderLineData.getItem().getDescription());
        productDetailDTO.setExpectedQuantity(BigDecimal.valueOf(orderLineData.getItem().getQuantity()));
        productDetailDTO.setReceivedQuantity(BigDecimal.ZERO);
        productDetailDTO.setProductImage(orderLineData.getItem().getImages().getFirst());
        productDetailDTO.setProductImages(orderLineData.getItem().getImages());
        productDetailDTO.setColour(convertToCommaSeparated(orderLineData.getItem().getColor()));
        productDetailDTO.setSize(orderLineData.getItem().getSize());
        productDetailDTO.setReturnReason(returnIfValid(orderLineData.getReturnReason()));
        productDetailDTO.setProductClass(orderLineData.getItem().getProductClass());
        productDetailDTO.setProductSubclass(orderLineData.getItem().getProductSubclass());
        productDetailDTO.setSeason(orderLineData.getItem().getSeason());
        productDetailDTO.setBrand(orderLineData.getItem().getBrand());
        productDetailDTO.setDepartmentName(orderLineData.getItem().getDepartmentName());
        productDetailDTO.setStyle(orderLineData.getItem().getStyle());
        productDetailDTO.setCountry(orderLineData.getCountry());
        productDetailDTO.setRegion(orderLineData.getRegion());
        productDetailDTO.setCity(orderLineData.getCity());
        productDetailDTO.setExpectedItemCondition(orderLineData.getItemCondition());
        productDetailDTO.setCurrencyCode(orderLineData.getItem().getCurrencyCode());
        productDetailDTO.setUnitPrice(orderLineData.getItem().getUnitPrice());
        productDetailDTO.setOrderLineId(orderLineData.getOrderLineId());
        return productDetailDTO;
    }

    public static String convertToCommaSeparated(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        return input.replace("/", ",");
    }

    private String returnIfValid(String value) {
        if (value != null && !value.trim().isEmpty() && !"null".equalsIgnoreCase(value)) {
            return value;
        }
        return null;
    }
}
