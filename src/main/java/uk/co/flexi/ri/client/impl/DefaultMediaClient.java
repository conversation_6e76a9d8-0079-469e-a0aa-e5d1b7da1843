package uk.co.flexi.ri.client.impl;

import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.client.MediaClient;

import java.io.InputStream;

@Component("defaultMediaClient")
public class DefaultMediaClient implements MediaClient {

    @Override
    public String putFile(MultipartFile file, FileGroup fileGroup, String bucketName) {
        return null;
    }

    @Override
    public InputStream getFile(String fileName, String bucketName) {
        return null;
    }

    @Override
    public void deleteFile(String filename, String bucketName) {
        // Default implementation
    }

    @Override
    public void createBucketNotExist(String bucketName) {
        // Default implementation
    }

}
