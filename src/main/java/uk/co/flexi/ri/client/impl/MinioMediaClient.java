package uk.co.flexi.ri.client.impl;

import io.minio.BucketExistsArgs;
import io.minio.GetObjectArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.client.MediaClient;
import uk.co.flexi.ri.exception.custom.MinioException;

import java.io.InputStream;
import java.util.UUID;


@Component("minioMediaClient")
@Slf4j
public class MinioMediaClient implements MediaClient {

    @Value("${minio.image.upload}")
    String minioImageUpload;

    @Value("${minio.profile.image.upload}")
    String minioProfileImageUpload;

    @Value("${minio.temp.image.upload}")
    String minioTempImageUpload;

    @Value("${minio.video.upload}")
    String minioVideoUpload;

    private final MinioClient minioClient;

    public MinioMediaClient(MinioClient minioClient) {
        this.minioClient = minioClient;
    }

    @Override
    public String putFile(MultipartFile file, FileGroup fileGroup, String bucketName) {
        String fileName = getFileName(file, fileGroup);
        try (InputStream inputStream = file.getInputStream()) {
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(fileName)
                            .stream(inputStream, file.getSize(), -1)
                            .contentType(file.getContentType())
                            .build()
            );

            return fileName;
        } catch (Exception e) {
            throw new MinioException("File upload failed");
        }
    }

    @Override
    public InputStream getFile(String fileName, String bucketName) {
        try {
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(fileName)
                            .build()
            );
        } catch (Exception e) {
            throw new MinioException("File download failed");
        }
    }

    @Override
    public void deleteFile(String filename, String bucketName) {
        try {
             minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(filename)
                            .build()
            );
        } catch (Exception e) {
            throw new MinioException("File deletion from storage failed");
        }
    }

    public void createBucketNotExist(String bucketName) {
        try {
            boolean exists = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!exists) {
                log.info("Bucket '{}' does not exist. Creating...", bucketName);
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                log.info("Bucket '{}' created successfully.", bucketName);
            } else {
                log.info("Bucket '{}' already exists.", bucketName);
            }
        } catch (Exception e) {
            log.error("Error while checking or creating bucket '{}': {}", bucketName, e.getMessage());
            throw new MinioException("Failed to ensure bucket exists");
        }
    }

    private String getFileName(MultipartFile file, FileGroup fileGroup) {
        String fileType = identifyFileType(file);
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isBlank()) {
            throw new IllegalArgumentException("File must have a valid name");
        }
        String uniqueSuffix = "_" + UUID.randomUUID();
        String uniqueFileName = originalFilename.replace(".", uniqueSuffix + ".");
        if ("image".equalsIgnoreCase(fileType)) {
            if (fileGroup.equals(FileGroup.PROFILE_PIC)) {
                return minioProfileImageUpload + uniqueFileName;
            } else if (fileGroup.equals(FileGroup.TEMP_IMAGE)) {
                return minioTempImageUpload + uniqueFileName;
            } else {
                return minioImageUpload + uniqueFileName;
            }
        } else if ("video".equalsIgnoreCase(fileType)) {
            return minioVideoUpload + uniqueFileName;
        } else {
            throw new IllegalArgumentException("Unsupported file type");
        }
    }
}
