package uk.co.flexi.ri.client;

import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

public interface MediaClient {

    enum FileGroup {
        COMMENT,
        PROFILE_PIC,
        TEMP_IMAGE
    }

    String putFile(MultipartFile file, FileGroup fileGroup, String bucketName);

    InputStream getFile(String fileName, String bucketName);

    void deleteFile(String filename, String bucketName);

    void createBucketNotExist(String bucketName);

    default String identifyFileType(MultipartFile file) {
        String contentType = file.getContentType();
        if (contentType != null) {
            if (contentType.startsWith("image")) {
                return "image";
            } else if (contentType.startsWith("video")) {
                return "video";
            }
        }
        throw new IllegalArgumentException("Unknown file type: " + contentType);
    }
}
