package uk.co.flexi.ri.client.factory;

import org.springframework.stereotype.Component;
import uk.co.flexi.ri.client.MediaClient;

import java.util.Map;

@Component
public class MediaClientFactory {

    private final Map<String, MediaClient> clients;

    public MediaClientFactory(Map<String, MediaClient> clients) {
        this.clients = clients;
    }

    public MediaClient getClient(String provider) {
        return clients.getOrDefault(provider, clients.get("defaultMediaClient"));
    }
}
