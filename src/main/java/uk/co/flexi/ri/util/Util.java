package uk.co.flexi.ri.util;
import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;
import org.apache.commons.io.IOUtils;
import org.springframework.web.multipart.MultipartFile;
import uk.co.flexi.ri.dto.CustomMultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.net.URL;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

public class Util {

    private static final CronParser parser =
            new CronParser(CronDefinitionBuilder.instanceDefinitionFor(CronType.QUARTZ));

    private Util(){

    }

    public static String extractValidCharacters(String input) {
        StringBuilder result = new StringBuilder();
        for (char ch : input.toCharArray()) {
            if (Character.isLetter(ch)) {
                result.append(Character.toLowerCase(ch));
            } else {
                break;
            }
        }
        return result.toString();
    }

    public static String convertToCommaSeparated(List<String> input) {
        if (input == null || input.isEmpty()) {
            return null;
        }
        return String.join(",", input);
    }

    public static MultipartFile convertUrlToMultipartFile(String fileUrl, String fileName, String contentType) throws IOException {
        URL url = URI.create(fileUrl).toURL();
        try (InputStream inputStream = url.openStream()) {
            byte[] fileBytes = IOUtils.toByteArray(inputStream);
            return new CustomMultipartFile(fileBytes, fileName, contentType);
        }
    }

    public static String[] splitName(String fullName) {
        if (fullName == null || fullName.trim().isEmpty()) {
            return new String[]{"", ""};
        }
        String[] parts = fullName.trim().split(" ", 2);
        String firstName = parts[0];
        String lastName = (parts.length > 1) ? parts[1] : "";

        return new String[]{firstName, lastName};
    }

    public static boolean shouldTriggerNow(String cronExpr) {
        Cron cron = parser.parse(cronExpr);
        ExecutionTime executionTime = ExecutionTime.forCron(cron);

        ZonedDateTime now = ZonedDateTime.now().truncatedTo(ChronoUnit.MINUTES); // round down to minute
        Optional<ZonedDateTime> nextExecution = executionTime.nextExecution(now.minusMinutes(1)); // go back a minute

        return nextExecution.isPresent() && nextExecution.get().truncatedTo(ChronoUnit.MINUTES).equals(now);
    }

    public static String validateCronExpression(String cronExpr) {
        try {
            Cron cron = parser.parse(cronExpr);
            cron.validate();
            return cronExpr;
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid cron expression: " + cronExpr, e);
        }
    }
}
