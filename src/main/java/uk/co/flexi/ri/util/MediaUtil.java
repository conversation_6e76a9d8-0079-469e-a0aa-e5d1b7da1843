package uk.co.flexi.ri.util;

import net.coobird.thumbnailator.Thumbnails;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.awt.image.BufferedImage;
import java.io.*;
import javax.imageio.ImageIO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Locale;

import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;

public final class MediaUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(MediaUtil.class);
    private static final String DEFAULT_VIDEO_FORMAT = "mp4";
    private static final int DEFAULT_CRF = 28;

    private MediaUtil() {
        // Utility class, no instantiation
    }

    public static MultipartFile compressImage(MultipartFile originalImage, float quality) throws IOException {
        String contentType = originalImage.getContentType();
        String originalFilename = originalImage.getOriginalFilename();
        if (contentType == null || originalFilename == null) {
            throw new IOException("Image contentType or filename is null");
        }
        try (
                InputStream is = originalImage.getInputStream();
                ByteArrayOutputStream os = new ByteArrayOutputStream()
        ) {
            BufferedImage image = ImageIO.read(is);
            if (image == null) {
                throw new IOException("File is not a valid image.");
            }

            String format = getImageFormat(originalFilename, contentType);

            Thumbnails.of(image)
                    .scale(1.0)
                    .outputQuality(quality)
                    .outputFormat(format)
                    .toOutputStream(os);

            return new MockMultipartFile(
                    originalFilename,
                    originalFilename,
                    contentType,
                    os.toByteArray()
            );
        } catch (IOException e) {
            LOGGER.error("Error compressing image: {}", originalFilename, e);
            throw new IOException("Error compressing image '" + originalFilename + "': " + e.getMessage(), e);
        }
    }

    public static MultipartFile compressVideo(MultipartFile originalVideo) throws IOException {
        String contentType = originalVideo.getContentType();
        String originalFilename = originalVideo.getOriginalFilename();
        if (contentType == null || originalFilename == null) {
            throw new IOException("Video contentType or filename is null");
        }
        return compressVideoWithJavaCV(originalVideo, DEFAULT_VIDEO_FORMAT, DEFAULT_CRF);
    }

    public static MultipartFile compressVideoWithJavaCV(MultipartFile originalVideo, String outputFormat, int crf) throws IOException {
        String originalFilename = originalVideo.getOriginalFilename();
        if (originalFilename == null) throw new IOException("Original filename is null");

        // Create temp files
        File inputFile = File.createTempFile("input-", originalFilename);
        File outputFile = File.createTempFile("output-", "." + outputFormat);

        // Save MultipartFile to temp file
        try (InputStream is = originalVideo.getInputStream();
             OutputStream os = new FileOutputStream(inputFile)) {
            is.transferTo(os);
        }

        try (FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputFile)) {
            grabber.start();

            try (FFmpegFrameRecorder recorder = new FFmpegFrameRecorder(outputFile, grabber.getImageWidth(), grabber.getImageHeight(), grabber.getAudioChannels())) {
                recorder.setFormat(outputFormat);
                recorder.setVideoCodec(org.bytedeco.ffmpeg.global.avcodec.AV_CODEC_ID_H264);
                recorder.setVideoOption("crf", String.valueOf(crf)); // Lower = better quality, higher = more compression
                recorder.setFrameRate(grabber.getFrameRate());
                recorder.setAudioChannels(grabber.getAudioChannels());
                recorder.start();

                Frame frame;
                while ((frame = grabber.grab()) != null) {
                    recorder.record(frame);
                }
                recorder.stop();
            }
            grabber.stop();
        } catch (Exception e) {
            LOGGER.error("Error compressing video with JavaCV: {}", originalFilename, e);
            throw new IOException("Error compressing video with JavaCV: " + originalFilename, e);
        }

        // Read output file to MultipartFile
        byte[] videoBytes = java.nio.file.Files.readAllBytes(outputFile.toPath());
        MultipartFile compressed = new MockMultipartFile(
                originalFilename,
                originalFilename,
                originalVideo.getContentType(),
                videoBytes
        );

        // Clean up
        inputFile.delete();
        outputFile.delete();

        return compressed;
    }

    public static boolean isImage(String contentType) {
        return contentType != null && contentType.toLowerCase(Locale.ROOT).startsWith("image");
    }

    public static boolean isVideo(String contentType) {
        return contentType != null && contentType.toLowerCase(Locale.ROOT).startsWith("video");
    }

    static String getImageFormat(String filename, String contentType) {
        if (filename != null && filename.contains(".")) {
            return filename.substring(filename.lastIndexOf('.') + 1).toLowerCase(Locale.ROOT);
        } else if (contentType != null && contentType.startsWith("image/")) {
            return contentType.substring("image/".length());
        } else {
            return "jpg";
        }
    }
}