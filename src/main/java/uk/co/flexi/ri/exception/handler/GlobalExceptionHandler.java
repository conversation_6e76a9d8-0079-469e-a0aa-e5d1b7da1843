package uk.co.flexi.ri.exception.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.security.authorization.AuthorizationDeniedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import uk.co.flexi.ri.exception.custom.InspectionUpdateException;
import uk.co.flexi.ri.exception.custom.IntegrationException;
import uk.co.flexi.ri.exception.custom.MediaUploadException;
import uk.co.flexi.ri.exception.custom.MinioException;
import uk.co.flexi.ri.exception.custom.OMSException;
import uk.co.flexi.ri.exception.custom.RIAuthenticationException;
import uk.co.flexi.ri.exception.custom.OrderNotFoundException;
import uk.co.flexi.ri.exception.custom.ScriptExecutionException;
import uk.co.flexi.ri.exception.custom.UserNotFoundException;
import uk.co.flexi.ri.exception.dto.ErrorResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.util.HashMap;
import java.util.Map;
import java.util.NoSuchElementException;

@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(UserNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleUserNotFoundException(UserNotFoundException ex) {
        return buildErrorResponse(HttpStatus.NOT_FOUND, "User Not Found", ex);
    }

    @ExceptionHandler(OrderNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleOrderNotFoundException(OrderNotFoundException ex) {
        return buildErrorResponse(HttpStatus.NOT_FOUND, "Order Not Found", ex);
    }

    @ExceptionHandler(OMSException.class)
    public ResponseEntity<ErrorResponse> handleOMSException(OMSException ex) {
        return buildErrorResponse(HttpStatus.BAD_REQUEST, "Connection failed", ex);
    }

    @ExceptionHandler(ScriptExecutionException.class)
    public ResponseEntity<ErrorResponse> handleScriptExecutionException(ScriptExecutionException ex) {
        return buildErrorResponse(HttpStatus.BAD_REQUEST, "Invalid Script", ex);
    }

    @ExceptionHandler(JsonProcessingException.class)
    public ResponseEntity<ErrorResponse> handleJsonProcessingException(JsonProcessingException ex) {
        return buildErrorResponse(HttpStatus.BAD_REQUEST, "Invalid Json", ex);
    }

    @ExceptionHandler(IntegrationException.class)
    public ResponseEntity<ErrorResponse> handleIntegrationException(IntegrationException ex) {
        return buildErrorResponse(HttpStatus.BAD_REQUEST, "Connection failed", ex);
    }

    @ExceptionHandler(MediaUploadException.class)
    public ResponseEntity<ErrorResponse> handleMediaUploadException(MediaUploadException ex) {
        return buildErrorResponse(HttpStatus.BAD_REQUEST, "Media Upload Error", ex);
    }

    @ExceptionHandler(InspectionUpdateException.class)
    public ResponseEntity<ErrorResponse> handleInspectionUpdateException(InspectionUpdateException ex) {
        return buildErrorResponse(HttpStatus.CONFLICT, "Inspection Update Warning", ex);
    }

    @ExceptionHandler(NoSuchElementException.class)
    public ResponseEntity<ErrorResponse> handleNoSuchElementException(NoSuchElementException ex) {
        return buildErrorResponse(HttpStatus.NOT_FOUND, "Not Found", ex);
    }

    @ExceptionHandler(AuthorizationDeniedException.class)
    public ResponseEntity<ErrorResponse> handleAuthorizationDeniedException(AuthorizationDeniedException ex) {
        return buildErrorResponse(HttpStatus.FORBIDDEN, "Access Denied", ex);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponse> handleIllegalArgumentException(IllegalArgumentException ex) {
        return buildErrorResponse(HttpStatus.BAD_REQUEST, "Invalid Argument", ex);
    }

    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ErrorResponse> handleDataIntegrityViolationException(DataIntegrityViolationException ex) {
        String message = "Duplicate entry or constraint violation.";
        return buildErrorResponse(HttpStatus.BAD_REQUEST, "Data Integrity Violation", message);
    }

    @ExceptionHandler(RIAuthenticationException.class)
    public ResponseEntity<ErrorResponse> handleAuthenticationException(RIAuthenticationException ex) {
        return buildErrorResponse(HttpStatus.UNAUTHORIZED, "Unauthorized", ex);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationExceptions(MethodArgumentNotValidException ex) {
        return buildValidationErrorResponse(HttpStatus.BAD_REQUEST, "Validation Error", ex);
    }

    @ExceptionHandler(MinioException.class)
    public ResponseEntity<ErrorResponse> handleMinioException(MinioException ex) {
        return buildErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR , "File upload error", ex);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGlobalException(Exception ex) {
        log.error("*** Generic exception occurred", ex);
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                "Internal Server Error",
                "An unexpected error occurred"
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private ResponseEntity<ErrorResponse> buildErrorResponse(HttpStatus status, String errorMessage, Exception ex) {
        log.error("*** {}: {}", errorMessage, ex.getMessage());
        ErrorResponse errorResponse = new ErrorResponse(
                status.value(),
                errorMessage,
                ex.getMessage()
        );
        return new ResponseEntity<>(errorResponse, status);
    }

    private ResponseEntity<ErrorResponse> buildErrorResponse(HttpStatus status, String errorMessage, String message) {
        log.error("*** {}: {}", errorMessage);
        ErrorResponse errorResponse = new ErrorResponse(
                status.value(),
                errorMessage,
                message
        );
        return new ResponseEntity<>(errorResponse, status);
    }

    private ResponseEntity<ErrorResponse> buildValidationErrorResponse(HttpStatus status, String errorMessage, MethodArgumentNotValidException ex) {
        Map<String, String> validationErrors = new HashMap<>();

        // Handle FieldErrors
        ex.getBindingResult().getFieldErrors().forEach(error ->
                validationErrors.put(error.getField(), error.getDefaultMessage())
        );

        // Handle ObjectErrors
        ex.getBindingResult().getGlobalErrors().forEach(error ->
                validationErrors.put(error.getObjectName(), error.getDefaultMessage())
        );

        // Build a detailed message
        String detailedMessage = validationErrors.entrySet()
                .stream()
                .map(entry -> entry.getKey() + ": " + entry.getValue())
                .reduce((msg1, msg2) -> msg1 + ", " + msg2)
                .orElse("Validation errors occurred");

        log.error("*** Validation failed: {}", detailedMessage);

        ErrorResponse errorResponse = new ErrorResponse(
                status.value(),
                errorMessage,
                detailedMessage
        );
        return new ResponseEntity<>(errorResponse, status);
    }
}
