package uk.co.flexi.ri.validation.validatior;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import uk.co.flexi.ri.dto.InspectionDTO;
import uk.co.flexi.ri.dto.InspectionItemDTO;
import uk.co.flexi.ri.validation.annotation.HasInspectionItemWithReceivedQuantityOne;

import java.math.BigDecimal;

public class HasInspectionItemWithReceivedQuantityOneValidator implements ConstraintValidator<HasInspectionItemWithReceivedQuantityOne, InspectionDTO> {

    @Override
    public boolean isValid(InspectionDTO inspectionDTO, ConstraintValidatorContext context) {

        if (inspectionDTO.getInspectionItems() == null || inspectionDTO.getInspectionItems().isEmpty()) {
            return true;
        }

        for (InspectionItemDTO item : inspectionDTO.getInspectionItems()) {
            if (item.getReceivedQuantity() != null && item.getReceivedQuantity().compareTo(BigDecimal.ZERO) > 0) {
                return true;
            }
        }
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(context.getDefaultConstraintMessageTemplate())
                .addConstraintViolation();
        return false;
    }
}
