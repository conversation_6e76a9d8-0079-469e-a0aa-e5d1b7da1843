package uk.co.flexi.ri.validation.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import uk.co.flexi.ri.validation.validatior.HasInspectionItemWithReceivedQuantityOneValidator;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Constraint(validatedBy = HasInspectionItemWithReceivedQuantityOneValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface HasInspectionItemWithReceivedQuantityOne {

    String message() default "At least one inspection item must have receivedQuantity equal to 1";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
