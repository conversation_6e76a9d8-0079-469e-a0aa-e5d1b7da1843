spring.application.name=return-insights

server.port=8080

#Database config
spring.datasource.url=jdbc:mysql://${QA_DATA_SOURCE_URL}
spring.datasource.username=${DATA_SOURCE_USERNAME}
spring.datasource.password=${DATA_SOURCE_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Hibernate config
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.show-sql=false

# Flyway migration
spring.flyway.enabled=false

#Google OAuth
spring.security.oauth2.client.registration.google.client-id=${CLIENT_ID}
spring.security.oauth2.client.registration.google.client-secret=${CLIENT-SECRET}
spring.security.oauth2.client.registration.google.scope=profile,email
spring.security.oauth2.client.registration.google.redirect-uri=https://qa-ri.flexicommerce.co.uk/login/oauth2/code/google

#JWT Config
jwt.secret=${JWT_SECRET}
jwt.accessExpirationMs=600000
jwt.refreshExpirationMs=3600000

#Docker-compose
spring.docker.compose.enabled=false

#MinioConfig
minio.access.key=6uj0IKLBnXnVRN0x4ZOa
minio.secret.key=Wggj3dkfMmpbkvcdY44Qxabw8d7asBfkwpuMl0hP
minio.secret.api=http://89.116.228.165:9000

#Spring Batch Configuration
spring.batch.jdbc.initialize-schema=always
spring.batch.job.enabled=false



