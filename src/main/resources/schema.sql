-- This file is automatically run by hibernate if the schema is initialized via model layer. These files are skipped during flyway migration
--Inspection view(Inspections)
DROP TABLE IF EXISTS inspection_view;
CREATE OR REPLACE VIEW inspection_view AS
SELECT
    i.inspection_id,
    COALESCE(o.return_order_id, o.order_id) AS order_id,
    i.tenant,
    i.created_by_group as`created_by_group_id`,
    i.assignee_group as`assignee_group_id`,
    aug.name as `assignee_group`,
    i.reference_id,
    i.created_at,
    i.status,
    i.selling_channel,
    i.partner_name,
    GREATEST(DATEDIFF(DATE_ADD(i.created_at, INTERVAL COALESCE(c1.sla, c2.sla) DAY), CURRENT_DATE), 0) AS remaining_days
FROM
    inspection i
LEFT JOIN channel c1
    ON LOWER(i.selling_channel) = LOWER(c1.name)
    AND i.tenant = c1.tenant
LEFT JOIN channel c2
    ON LOWER(c2.name) = 'default'
    AND i.tenant = c2.tenant
JOIN
	orders o ON o.id = i.order_id
LEFT JOIN
	user_group aug ON aug.id = i.assignee_group;

--Event Queue Processing(Batch Job)
DROP TABLE IF EXISTS event_queue_view;
CREATE OR REPLACE VIEW event_queue_view AS
SELECT
    eq.id,
    eq.event_id,
    eq.event_time,
    eq.event_type,
    eq.payload,
    eq.performed_by_group,
    eq.performed_by_user,
    eq.unique_id,
    eq.reference_type,
    eq.tenant,
    ec.id as config_id,
    ec.destination,
    ec.mapping_script,
    ec.config,
    m.hmac_key,
    COUNT(eqr.id) AS retry_count,
    MAX(eqr.request_time) AS last_attempt_time,
    TIMESTAMPDIFF(MINUTE, MAX(eqr.request_time), NOW()) AS minutes_since_last_retry,
    DATE_ADD(MAX(eqr.request_time), INTERVAL POW(2, COUNT(eqr.id)) MINUTE) AS next_retry_time
FROM event_queue eq
JOIN event_config ec ON ec.id = eq.config_id
JOIN merchant m on m.tenant = ec.tenant
LEFT JOIN event_queue_req eqr ON eqr.event_queue_id = eq.id
LEFT JOIN event_queue_res eqs ON eqs.request_id = eqr.id
WHERE eq.event_time >= NOW() - INTERVAL 1 HOUR
GROUP BY eq.id, ec.retry_count, ec.destination, ec.config
HAVING COUNT(eqr.id) <= ec.retry_count
   AND SUM(CASE WHEN eqs.status = 'SENT' THEN 1 ELSE 0 END) = 0
   AND (
       COUNT(eqr.id) = 0
       OR NOW() >= DATE_ADD(MAX(eqr.request_time), INTERVAL POW(2, COUNT(eqr.id)) MINUTE)
   )
ORDER BY eq.event_time;


--Return Reasons View(Dashboard)
DROP TABLE IF EXISTS return_reason_view;
CREATE OR REPLACE VIEW return_reason_view AS
SELECT
    ROW_NUMBER() OVER () AS id,
    DATE(ii.created_at) as created_at,
    p.sku,
	MAX(COALESCE(p.product_size,'Unknown')) as product_size,
    ii.tenant,
    CASE
        WHEN LOWER(ii.return_reason) IN ('change mind', 'change of mind') THEN 'CHANGE OF MIND'
        ELSE COALESCE(ii.return_reason, 'OTHER')
    END AS common_return_reason,
    COALESCE(p.product_style,'Unknown') as product_style,
    MAX(COALESCE(p.product_name,'Unknown')) as product_name,
    COALESCE(p.product_class,'Unknown') as product_class,
    MAX(COALESCE(p.department_name,'Unknown')) as department_name,
    COALESCE(p.product_sub_class,'Unknown') as product_sub_class,
    COUNT(ii.id) AS return_count,
    SUM(COALESCE(mp.unit_price,0)) AS price
FROM inspection_item ii
JOIN product p ON ii.product_id = p.id
LEFT JOIN merchant_product_price mp on mp.sku = p.sku and mp.tenant = ii.tenant
GROUP BY ii.tenant, DATE(ii.created_at), common_return_reason, p.sku, p.product_style,p.product_class,p.product_sub_class
ORDER BY return_count DESC;

--Item Condition View(Dashboard)
DROP TABLE IF EXISTS item_condition_view;
CREATE OR REPLACE VIEW item_condition_view AS
SELECT
    ROW_NUMBER() OVER () AS id,
    DATE(ii.created_at) as created_at,
    COALESCE(i.selling_channel,'Unknown') as selling_channel,
    ii.tenant,
    ii.item_condition,
    ii.item_status,
    p.product_class,
    MAX(p.department_name) as department_name,
    COUNT(ii.id) AS return_count,
    SUM(COALESCE(mp.unit_price,0)) AS price
FROM inspection i
JOIN inspection_item ii on ii.inspection_id = i.id
JOIN product p ON ii.product_id = p.id
LEFT JOIN merchant_product_price mp on mp.sku = p.sku and mp.tenant = ii.tenant
WHERE ii.item_condition IS NOT NULL
GROUP BY ii.tenant, DATE(ii.created_at),ii.item_condition,COALESCE(i.selling_channel,'Unknown'),ii.item_status,p.product_class
ORDER BY return_count DESC;


--Return top product view(Dashboard)
DROP TABLE IF EXISTS top_product_return_view;
CREATE OR REPLACE VIEW top_product_return_view AS
SELECT
    ROW_NUMBER() OVER () AS id,
    DATE(ii.created_at) as created_at,
    ii.tenant,
    COALESCE(p.product_style,'Unknown') as product_style,
    MAX(COALESCE(p.product_name,'Unknown')) as product_name,
    p.sku,
    MAX(COALESCE(p.product_size,'Unknown')) as product_size,
    COALESCE(ii.return_reason,'OTHER') as return_reason,
    COALESCE(i.selling_channel,'Unknown') as selling_channel,
    COUNT(ii.id) AS return_count,
    SUM(COALESCE(mp.unit_price,0)) AS price
FROM inspection_item ii
JOIN inspection i on i.id = ii.inspection_id
JOIN product p ON ii.product_id = p.id
LEFT JOIN merchant_product_price mp on mp.sku = p.sku and mp.tenant = ii.tenant
GROUP BY ii.tenant, DATE(ii.created_at), ii.return_reason, p.sku, p.product_style,i.selling_channel
ORDER BY return_count DESC;


--Inspection Time Tracker view(Dashboard)
DROP TABLE IF EXISTS inspection_time_tracker_view;
CREATE OR REPLACE VIEW inspection_time_tracker_view AS
SELECT
    ROW_NUMBER() OVER () AS id,
    i.tenant,
    DATE(i.completed_date) AS completed_date,
    COUNT(DISTINCT i.id) AS return_count,
    DATEDIFF(i.completed_date, i.created_at) AS days
FROM
    inspection i
WHERE
    i.completed_date IS NOT NULL
GROUP BY
    i.tenant, DATE(i.completed_date), days
ORDER BY return_count DESC;

--Inspection Completed View(Dashboard)
DROP TABLE IF EXISTS inspection_completed_view;
CREATE OR REPLACE VIEW inspection_completed_view AS
SELECT
    ROW_NUMBER() OVER () AS id,
    i.completed_by,
    DATE(i.completed_date) as completed_date,
    i.tenant,
    COUNT(i.id) AS completed_count
FROM inspection i
GROUP BY i.tenant, DATE(i.completed_date),i.completed_by
ORDER BY completed_count DESC;
