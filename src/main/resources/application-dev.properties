server.port=8080

#Log DB query
logging.level.org.hibernate.SQL=debug
logging.level.org.hibernate.type.description.sql=trace
logging.level.org.springframework.data.elasticsearch.core=DEBUG

#Database config
spring.jpa.hibernate.ddl-auto=update
spring.datasource.url=*******************************************
spring.datasource.username=root
spring.datasource.password=password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=always

# Flyway migration
spring.flyway.enabled=false

#JWT Config
jwt.secret=mqXGtMAJDRxRdGNKELVF9jn65kF30sJprPoie5IbhUQ=
jwt.accessExpirationMs=86400000
jwt.refreshExpirationMs=172800000

#MinioConfig
minio.access.key=blZbcK0d1aSXQZ7dwvde
minio.secret.key=mBBh0ygIeWe8CDUUzZ98qHpTK2xhK7A5Bomcim6o
minio.secret.api=http://localhost:9000

#Spring Batch Configuration
spring.batch.jdbc.initialize-schema=always
spring.batch.job.enabled=false

