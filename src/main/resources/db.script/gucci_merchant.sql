-- =====================================================
-- New Merchant Setup Script Template
-- =====================================================
-- This script provides a template for setting up a new merchant
-- Replace the placeholder values with actual merchant data

-- Step 1: Insert New Merchant
-- Replace values: merchant_email, merchant_name, tenant_id, reporting_currency, hmac_key
INSERT INTO merchant
(id, merchant_email, merchant_name, tenant, reporting_currency, hmac_key)
VALUES(1002, '<EMAIL>', 'Gucci', 837995, 'EUR', '6uj0IKLBnXnrt44545VRNrtrtrteaaaadfrgfg');

-- Step 2: Insert Default Channel
-- Each merchant needs at least one channel for order processing
INSERT INTO channel
(id, channel_id, name, sla, tenant)
VALUES(1002, UUID(), 'Default', 5, 837995);

-- Step 3: Insert Authentication Provider
-- Configure how users will authenticate (DB, GOOGLE, AZURE, OKTA, MANHATTAN)
INSERT INTO auth_provider
(id, auth_url, provider, tenant, merchant_id)
VALUES(1002, '/api/v1/auth/login', 'DB', 837995, 1002);

-- Step 4: Insert Admin User Group
-- Create the primary admin group for the merchant
INSERT INTO user_group
(id, name, tenant, comment_visibility, user_group_id,partner_name, is_active)
VALUES (1002 ,'Admin', 837995, 'INTERNAL', UUID(), NULL, 1);

-- Step 5: Insert Default Template Configuration
-- Template for inspection data display and filtering
INSERT INTO template (id, template_data, template_id, template_name, tenant, user_group)
VALUES (1002 , '[{"field":"status","label":"Status icon","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"referenceId","label":"Reference ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"orderId","label":"Order ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"createdAt","label":"Start date","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"sellingChannel","label":"Channel","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"partnerName","label":"Partner","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"assigneeGroup","label":"Assigned to","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"remainingDays","label":"Remaining days","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}}]', 'RI_INSPECTION_GUCCI', UUID(),837995 , 1002);

-- Step 6: Insert Role Group
-- Create admin role group with all permissions
INSERT INTO role_group (id, role_group_id, name, tenant)
VALUES (1002, UUID(), 'ADMIN_ROLES', 837995);

-- Step 7: Insert Admin User
-- Create the primary admin user for the merchant
-- Password: 'password' (hashed with BCrypt)
INSERT INTO user (id, is_active, password, user_name, auth_provider_id, tenant, first_name, last_name, user_id, merchant_id, language,role_group_id)
VALUES (1002, 1, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q','<EMAIL>', 1002, 837995, 'siva', 'sajja', UUID(), 1002, 'en',1002);

-- Step 8: Link User to User Group
-- Associate the admin user with the admin group
INSERT INTO user_group_rel (group_id, user_id)
VALUES (1002, 1002);

-- Step 9: Insert OMS Provider
-- Configure Order Management System integration
INSERT INTO oms_provider (id, provider_name, tenant)
VALUES (1002, 'MOCK', 837995);

-- Step 10: Insert OMS Configuration
-- Configure OMS connection parameters
INSERT INTO oms_config (config_key, config_value, oms_provider_id)
VALUES
    ('base_path', 'http://89.116.228.165:8090/api/oms/demo', 1002),
    ('user_name', 'demo', 1002),
    ('password', 'DPas0rd', 1002);


-- Step 11: Insert Search Strategies
-- Configure available search methods for orders/returns
INSERT INTO search_strategies (search_strategy_id, search_strategy)
VALUES (1002, 'RETURN_BY_TRACKING_NUMBER'),
    (1002, 'ORDER_BY_ID'),
    (1002, 'ORDER_BY_RETURN_TRACKING'),
    (1002, 'ORDER_BY_PRODUCT_SERIAL_NUMBER');


-- Step 12: Assign Admin Roles
-- Grant all available permissions to the admin role group
INSERT INTO role_role_groups (role_group_id, role_id)
VALUES
    (1002, 1021),
    (1002, 1014),
    (1002, 1016),
    (1002, 1017),
    (1002, 1013),
    (1002, 1019),
    (1002, 1015),
    (1002, 1018),
    (1002, 1022),
    (1002, 1000),
    (1002, 1012),
    (1002, 1023),
    (1002, 1024),
    (1002, 1025),
    (1002, 1026),
    (1002, 1027),
    (1002, 1028);

-- =====================================================
-- IMPORTANT NOTES:
-- =====================================================
-- 1. Update all ID values (2000) to unique values not used by existing merchants
-- 2. Replace tenant ID (123456) with calculated value or unique identifier
-- 3. Update merchant_email, merchant_name, and other merchant-specific values
-- 4. Generate secure HMAC key for webhook security
-- 5. Update OMS configuration based on actual integration requirements
-- 6. Consider adding additional user groups and users as needed
-- 7. Customize template configuration based on merchant requirements
-- =====================================================

