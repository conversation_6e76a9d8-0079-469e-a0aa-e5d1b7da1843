
INSERT INTO merchant (id, merchant_name, merchant_email, sla, tenant)
SELECT id, merchant_name, merchant_email, sla, tenant
FROM (
    SELECT 1002 AS id, '<PERSON><PERSON><PERSON>ck' AS merchant_name, '<EMAIL>' AS merchant_email, 3 AS sla, 819405 As tenant
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM merchant WHERE id = new_data.id
);

INSERT INTO return_reasons (return_reason_id, return_reason)
SELECT return_reason_id, return_reason
FROM (
    SELECT 1002 AS return_reason_id, 'CHANGE OF MIND' AS return_reason
    UNION ALL
    SELECT 1002 AS return_reason_id, 'ITEM IS DAMAGED OR DEFECTIVE' AS return_reason
    UNION ALL
    SELECT 1002 AS return_reason_id, 'DUPLICATE ORDER' AS return_reason
    UNION ALL
    SELECT 1002 AS return_reason_id, 'ITEM DOES NOT FIT' AS return_reason
    UNION ALL
    SELECT 1002 AS return_reason_id, 'DELIVERY ISSUE' AS return_reason
    UNION ALL
    SELECT 1002 AS return_reason_id, 'ITEM IS NOT AS SHOWN' AS return_reason
    UNION ALL
    SELECT 1002 AS return_reason_id, 'ITEM IS NOT AS EXPECTED' AS return_reason
    UNION ALL
    SELECT 1002 AS return_reason_id, 'WRONG ITEM RECEIVED' AS return_reason
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM return_reasons WHERE return_reason_id = new_data.return_reason_id AND return_reason = new_data.return_reason
);


INSERT INTO auth_provider (id, merchant_id, tenant, provider, auth_url)
SELECT id, merchant_id, tenant, provider, auth_url
FROM (
    SELECT 1004 AS id, 1002 AS merchant_id, 819405 AS tenant, 'DB' AS provider, '/api/v1/auth/login' AS auth_url
    UNION ALL
    SELECT 1005 AS id, 1002 AS merchant_id, 819405 AS tenant, 'GOOGLE' AS provider, '/oauth2/authorization/google' AS auth_url
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM auth_provider WHERE id = new_data.id
);


INSERT INTO user_group (id, name, tenant, comment_visibility, user_group_id,partner_name)
SELECT id, name, tenant, comment_visibility, user_group_id,partner_name
FROM (
    SELECT 1009 AS id, 'Warehouse Operator' AS name, 819405 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name
    UNION ALL
    SELECT 1010 AS id, 'Supervisor' AS name, 819405 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name
    UNION ALL
    SELECT 1011 AS id, 'Client Service' AS name, 819405 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name
    UNION ALL
    SELECT 1012 AS id, 'Farfetch' AS name, 819405 AS tenant, 'EXTERNAL' AS comment_visibility, UUID() AS user_group_id, 'Farfetch' As partner_name
    UNION ALL
    SELECT 1013 AS id, 'Harrods' AS name, 819405 AS tenant, 'EXTERNAL' AS comment_visibility, UUID() AS user_group_id, 'Harrods' As partner_name
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_group WHERE id = new_data.id
);


INSERT INTO supplier (id, name, tenant)
SELECT id, name, tenant
FROM (
    SELECT 1006 AS id, 'Marketplace' AS name, 819405 AS tenant
    UNION ALL
    SELECT 1007 AS id, 'Website' AS name, 819405 AS tenant
    UNION ALL
    SELECT 1008 AS id, 'Mobile App' AS name, 819405 AS tenant
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM supplier WHERE id = new_data.id
);


INSERT INTO user (id, is_active, password, user_name, auth_provider_id, tenant, first_name, last_name, profile_image, user_id, merchant_id)
SELECT id, is_active, password, user_name, auth_provider_id, tenant, first_name, last_name, profile_image, user_id, merchant_id
FROM (
    SELECT 1021 AS id, 1 AS is_active, '$2a$10$i7d/ZFgOjt8eOd5oScu6P.RTw0SEJrSypLhn6uhGlMM/1jTF6N4HG' AS password, '<EMAIL>' AS user_name, 1000 AS auth_provider_id, 819405 AS tenant, 'Warehouse' AS first_name, 'Operator' AS last_name, 'https://lh3.googleusercontent.com/a/ACg8ocLoWL9i1bHQic4912S64w3aDe4bswq6uEqPibXrPnjKU0O6jQ=s96-c' AS profile_image, UUID() AS user_id, 1002 As merchant_id
    UNION ALL
    SELECT 1022 AS id, 1 AS is_active, '$2a$10$i7d/ZFgOjt8eOd5oScu6P.RTw0SEJrSypLhn6uhGlMM/1jTF6N4HG' AS password, '<EMAIL>' AS user_name, 1000 AS auth_provider_id, 819405 AS tenant, 'Supervisor' AS first_name, '' AS last_name, 'https://lh3.googleusercontent.com/a/ACg8ocLoWL9i1bHQic4912S64w3aDe4bswq6uEqPibXrPnjKU0O6jQ=s96-c' AS profile_image, UUID() AS user_id, 1002 As merchant_id
    UNION ALL
    SELECT 1023 AS id, 1 AS is_active, '$2a$10$i7d/ZFgOjt8eOd5oScu6P.RTw0SEJrSypLhn6uhGlMM/1jTF6N4HG' AS password, '<EMAIL>' AS user_name, 1000 AS auth_provider_id, 819405 AS tenant, 'Client' AS first_name, 'Service' AS last_name, 'https://lh3.googleusercontent.com/a/ACg8ocLoWL9i1bHQic4912S64w3aDe4bswq6uEqPibXrPnjKU0O6jQ=s96-c' AS profile_image, UUID() AS user_id, 1002 As merchant_id
    UNION ALL
    SELECT 1024 AS id, 1 AS is_active, '$2a$10$i7d/ZFgOjt8eOd5oScu6P.RTw0SEJrSypLhn6uhGlMM/1jTF6N4HG' AS password, '<EMAIL>' AS user_name, 1002 AS auth_provider_id, 819405 AS tenant, 'Farfetch' AS first_name, 'User' AS last_name, 'https://lh3.googleusercontent.com/a/ACg8ocLoWL9i1bHQic4912S64w3aDe4bswq6uEqPibXrPnjKU0O6jQ=s96-c' AS profile_image, UUID() AS user_id, 1002 As merchant_id
    UNION ALL
    SELECT 1025 AS id, 1 AS is_active, '$2a$10$i7d/ZFgOjt8eOd5oScu6P.RTw0SEJrSypLhn6uhGlMM/1jTF6N4HG' AS password, '<EMAIL>' AS user_name, 1002 AS auth_provider_id, 819405 AS tenant, 'Harrods' AS first_name, 'User' AS last_name, 'https://lh3.googleusercontent.com/a/ACg8ocLoWL9i1bHQic4912S64w3aDe4bswq6uEqPibXrPnjKU0O6jQ=s96-c' AS profile_image, UUID() AS user_id, 1002 As merchant_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user WHERE id = new_data.id
);


INSERT INTO user_group_rel (group_id, user_id)
SELECT group_id, user_id
FROM (
    SELECT 1009 AS group_id, 1021 AS user_id
    UNION ALL
    SELECT 1010 AS group_id, 1022 AS user_id
    UNION ALL
    SELECT 1011 AS group_id, 1023 AS user_id
    UNION ALL
    SELECT 1012 AS group_id, 1024 AS user_id
    UNION ALL
    SELECT 1013 AS group_id, 1025 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_group_rel WHERE group_id = new_data.group_id AND user_id = new_data.user_id);



INSERT INTO oms_provider (id, provider_name, tenant)
SELECT id, provider_name, tenant
FROM (
    SELECT 1002 AS id, 'MOCK' AS provider_name, 819405 AS tenant
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM oms_provider WHERE id = new_data.id
);


INSERT INTO oms_config (id, config_key, config_value, oms_provider_id)
SELECT id, config_key, config_value, oms_provider_id
FROM (
    SELECT 1008 AS id, 'base_path' AS config_key, 'http://89.116.228.165:8090/api/oms/demo' AS config_value, 1002 AS oms_provider_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM oms_config WHERE id = new_data.id
);


INSERT INTO search_strategies (search_strategy_id, search_strategy)
SELECT search_strategy_id, search_strategy
FROM (
    SELECT 1002 AS search_strategy_id, 'RETURN_BY_TRACKING_NUMBER' AS search_strategy
    UNION ALL
    SELECT 1002 AS search_strategy_id, 'ORDER_BY_ID' AS search_strategy
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM search_strategies WHERE search_strategy_id = new_data.search_strategy_id and search_strategy = new_data.search_strategy
);


INSERT INTO template (id, template_data, template_id, template_name, tenant, user_group)
SELECT id, template_data, template_id, template_name, tenant, user_group
FROM (
    SELECT 1008 AS id, '[{"field":"status","label":"Status icon","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"referenceId","label":"Reference ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"orderId","label":"Order ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"createdAt","label":"Start date","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"sellingChannel","label":"Channel","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"partnerName","label":"Partner","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"assigneeGroup","label":"Assigned to","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"remainingDays","label":"Remaining days","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}}]' AS template_data, 'RI_INSPECTION' AS template_name,
        819405 AS tenant, 1009 AS user_group, UUID() AS template_id) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM template WHERE id = new_data.id
);

INSERT INTO template (id, template_data, template_id, template_name, tenant, user_group)
SELECT id, template_data, template_id, template_name, tenant, user_group
FROM (
    SELECT 1009 AS id, '[{"field":"status","label":"Status icon","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"referenceId","label":"Reference ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"orderId","label":"Order ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"createdAt","label":"Start date","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"sellingChannel","label":"Channel","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"partnerName","label":"Partner","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"assigneeGroup","label":"Assigned to","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"remainingDays","label":"Remaining days","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}}]' AS template_data, 'RI_INSPECTION' AS template_name,
        819405 AS tenant, 1010 AS user_group, UUID() AS template_id) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM template WHERE id = new_data.id
);

INSERT INTO template (id, template_data, template_id, template_name, tenant, user_group)
SELECT id, template_data, template_id, template_name, tenant, user_group
FROM (
    SELECT 1010 AS id, '[{"field":"status","label":"Status icon","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"referenceId","label":"Reference ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"orderId","label":"Order ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"createdAt","label":"Start date","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"sellingChannel","label":"Channel","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"partnerName","label":"Partner","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"assigneeGroup","label":"Assigned to","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"remainingDays","label":"Remaining days","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}}]' AS template_data, 'RI_INSPECTION' AS template_name,
        819405 AS tenant, 1011 AS user_group, UUID() AS template_id) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM template WHERE id = new_data.id
);

INSERT INTO template (id, template_data, template_id, template_name, tenant, user_group)
SELECT id, template_data, template_id, template_name, tenant, user_group
FROM (
    SELECT 1011 AS id, '[{"field":"status","label":"Status icon","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"referenceId","label":"Reference ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"orderId","label":"Order ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"createdAt","label":"Start date","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"sellingChannel","label":"Channel","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"partnerName","label":"Partner","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"assigneeGroup","label":"Assigned to","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"remainingDays","label":"Remaining days","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}}]' AS template_data, 'RI_INSPECTION' AS template_name,
        819405 AS tenant, 1012 AS user_group, UUID() AS template_id) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM template WHERE id = new_data.id
);

INSERT INTO template (id, template_data, template_id, template_name, tenant, user_group)
SELECT id, template_data, template_id, template_name, tenant, user_group
FROM (
    SELECT 1012 AS id, '[{"field":"status","label":"Status icon","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"referenceId","label":"Reference ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"orderId","label":"Order ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"createdAt","label":"Start date","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"sellingChannel","label":"Channel","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"partnerName","label":"Partner","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"assigneeGroup","label":"Assigned to","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"remainingDays","label":"Remaining days","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}}]' AS template_data, 'RI_INSPECTION' AS template_name,
        819405 AS tenant, 1013 AS user_group, UUID() AS template_id) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM template WHERE id = new_data.id
);

-- User Roles
--RI_INSP_PCKG_SCN
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1000 AS role_id, 1021 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);


--RI_INSP_PCKG_SCN_READ
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1001 AS role_id, 1022 AS user_id
    UNION ALL
    SELECT 1001 AS role_id, 1023 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);



--RI_INSP_ODP_EDIT
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1002 AS role_id, 1021 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);


--RI_INSP_SLCT_RETN_RSN
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1011 AS role_id, 1021 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);



--RI_INSP_SND_REVIEW
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1004 AS role_id, 1022 AS user_id
    UNION ALL
    SELECT 1004 AS role_id, 1023 AS user_id
    UNION ALL
    SELECT 1004 AS role_id, 1024 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);



--RI_INSP_APRV
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1005 AS role_id, 1021 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);




--RI_INSP_RJCT
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1010 AS role_id, 1021 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);



--RI_INSP_WRITE_CMNT_EXT
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1007 AS role_id, 1022 AS user_id
    UNION ALL
    SELECT 1007 AS role_id, 1023 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);


--RI_INSP_WRITE_CMNT
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1006 AS role_id, 1021 AS user_id
    UNION ALL
    SELECT 1006 AS role_id, 1024 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);


--RI_INSP_MEDIA_UPLD
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1008 AS role_id, 1022 AS user_id
    UNION ALL
    SELECT 1008 AS role_id, 1023 AS user_id
    UNION ALL
    SELECT 1008 AS role_id, 1024 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);



--RI_INSP_MEDIA_UPLD_EXT
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1003 AS role_id, 1022 AS user_id
    UNION ALL
    SELECT 1003 AS role_id, 1023 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);



--RI_INSP_CREATE_INSP
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1009 AS role_id, 1021 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);

--RI_INSP_LAND_SCAN
INSERT INTO user_roles (role_id, user_id)
SELECT role_id, user_id
FROM (
    SELECT 1012 AS role_id, 1021 AS user_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_roles
    WHERE role_id = new_data.role_id AND user_id = new_data.user_id
);

