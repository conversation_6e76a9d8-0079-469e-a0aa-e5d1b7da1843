{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "InspectionEventDTO", "type": "object", "properties": {"eventId": {"type": "string"}, "eventType": {"type": "string"}, "referenceType": {"type": "string"}, "eventTime": {"type": "string"}, "performedByUser": {"type": "string"}, "performedByGroup": {"type": "string"}, "payload": {"type": "object", "properties": {"inspection": {"type": "object", "properties": {"inspectionId": {"type": "string"}, "referenceId": {"type": "string"}, "orderId": {"type": "string"}, "returnOrderId": {"type": "string"}, "returnTrackingId": {"type": "string"}, "inspectionStatus": {"type": "string"}, "createdUser": {"type": "string"}, "assignedToGroup": {"type": "string"}, "partnerName": {"type": "string"}, "sellingChannel": {"type": "string"}, "assignedDate": {"type": "string"}, "completedDate": {"type": "string"}, "completedByGroup": {"type": "string"}, "items": {"type": "array", "items": {"type": "object", "properties": {"inspectionItemId": {"type": "string"}, "orderLineId": {"type": "string"}, "sku": {"type": "string"}, "returnReason": {"type": "string"}, "expectedCondition": {"type": "string"}, "condition": {"type": "string"}, "status": {"type": "string"}, "completedDate": {"type": "string"}, "completedByGroup": {"type": "string"}, "comments": {"type": "array", "items": {"type": "object", "properties": {"commentId": {"type": "string"}, "comment": {"type": "string"}, "author": {"type": "string"}, "media": {"type": "array", "items": {"type": "object", "properties": {"mediaId": {"type": "string"}, "mediaType": {"type": "string"}, "fileName": {"type": "string"}}, "required": ["mediaId", "mediaType", "fileName"]}}}, "required": ["commentId", "comment", "author", "media"]}}}, "required": ["inspectionItemId", "orderLineId", "sku", "returnReason", "expectedCondition", "condition", "status", "completedDate", "completedByGroup", "comments"]}}}, "required": ["inspectionId", "referenceId", "orderId", "returnOrderId", "returnTrackingId", "inspectionStatus", "created<PERSON>ser", "assignedToGroup", "partner<PERSON>ame", "sellingChannel", "assignedDate", "completedDate", "completedByGroup", "items"]}, "inspectionItem": {"type": "object", "properties": {"inspectionItemId": {"type": "string"}, "orderLineId": {"type": "string"}, "sku": {"type": "string"}, "returnReason": {"type": "string"}, "expectedCondition": {"type": "string"}, "condition": {"type": "string"}, "status": {"type": "string"}, "completedDate": {"type": "string"}, "completedByGroup": {"type": "string"}, "comments": {"type": "array", "items": {"type": "object", "properties": {"commentId": {"type": "string"}, "comment": {"type": "string"}, "author": {"type": "string"}, "media": {"type": "array", "items": {"type": "object", "properties": {"mediaId": {"type": "string"}, "mediaType": {"type": "string"}, "fileName": {"type": "string"}}, "required": ["mediaId", "mediaType", "fileName"]}}}, "required": ["commentId", "comment", "author", "media"]}}, "inspection": {"type": "object", "properties": {"inspectionId": {"type": "string"}, "referenceId": {"type": "string"}, "orderId": {"type": "string"}, "returnOrderId": {"type": "string"}, "returnTrackingId": {"type": "string"}}, "required": ["inspectionId", "referenceId", "orderId", "returnOrderId", "returnTrackingId"]}}, "required": ["inspectionItemId", "orderLineId", "sku", "returnReason", "expectedCondition", "condition", "status", "completedDate", "completedByGroup", "comments", "inspection"]}, "comment": {"type": "object", "properties": {"commentId": {"type": "string"}, "comment": {"type": "string"}, "author": {"type": "string"}, "media": {"type": "array", "items": {"type": "object", "properties": {"mediaId": {"type": "string"}, "mediaType": {"type": "string"}, "fileName": {"type": "string"}}, "required": ["mediaId", "mediaType", "fileName"]}}, "inspectionItem": {"type": "object", "properties": {"inspectionItemId": {"type": "string"}, "sku": {"type": "string"}, "inspection": {"type": "object", "properties": {"inspectionId": {"type": "string"}, "referenceId": {"type": "string"}, "orderId": {"type": "string"}, "returnOrderId": {"type": "string"}, "returnTrackingId": {"type": "string"}}, "required": ["inspectionId", "referenceId", "orderId", "returnOrderId", "returnTrackingId"]}}, "required": ["inspectionItemId", "sku", "inspection"]}}, "required": ["commentId", "comment", "author", "media", "inspectionItem"]}}, "required": ["inspection", "inspectionItem", "comment"]}}, "required": ["eventId", "eventType", "referenceType", "eventTime", "performedByUser", "performedByGroup", "payload"], "additionalProperties": false}