spring.profiles.active=${ACTIVE_PROFILE:dev}
spring.application.name=return-insights

spring.docker.compose.profiles.active=dev

spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

#MinioConfig
minio.image.upload=/comment/image/
minio.video.upload=/comment/video/
minio.profile.image.upload=/profile/image/
minio.temp.image.upload=/temp/image/

#Audit
spring.jpa.properties.org.hibernate.envers.store_data_at_delete: true
audit.repository.type=db
audit.entity.inspection.fields=status,assigneeGroupName
audit.entity.inspection.item.fields=returnReason,itemStatus
audit.entity.comment.fields=content
audit.entity.media.fields=url

#Camel Config
camel.springboot.auto-startup=true
