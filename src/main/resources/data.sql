-- Insert Languages
INSERT INTO supported_language (id, code, name)
SELECT id, code, name
FROM (
    SELECT 1000 AS id, 'en' AS code, 'English' AS name
    UNION ALL
    SELECT 1001 AS id, 'es' AS code, 'Spanish' AS name
    UNION ALL
    SELECT 1002 AS id, 'it' AS code, 'Italian' AS name
    UNION ALL
    SELECT 1003 AS id, 'ar' AS code, 'Arabic' AS name
    UNION ALL
    SELECT 1004 AS id, 'fr' AS code, 'French' AS name
    UNION ALL
    SELECT 1005 AS id, 'de' AS code, 'German' AS name
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM supported_language WHERE id = new_data.id
);

-- Insert Merchant
INSERT INTO merchant (id, merchant_name, merchant_email, reporting_currency, tenant)
SELECT id, merchant_name, merchant_email, reporting_currency ,tenant
FROM (
    SELECT 1000 AS id, 'Gucci' AS merchant_name, '<EMAIL>' AS merchant_email,'EUR' As reporting_currency ,837995 As tenant
    UNION ALL
    SELECT 1001 AS id, 'Penti' AS merchant_name, '<EMAIL>' AS merchant_email,'EUR' As reporting_currency, 913265 As tenant
    UNION ALL
    SELECT 1002 AS id, '<PERSON><PERSON><PERSON>' AS merchant_name, '<EMAIL>' AS merchant_email,'EUR' As reporting_currency, 819405 As tenant
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM merchant WHERE id = new_data.id
);

INSERT INTO channel (id, channel_id, name, sla, tenant)
SELECT id, UUID(), name, sla, tenant
FROM (
    SELECT 1000 AS id, 'Default' AS name, 3 AS sla, 837995 AS tenant
    UNION ALL
    SELECT 1001 AS id, 'Default' AS name, 5 AS sla, 913265 AS tenant
    UNION ALL
    SELECT 1002 AS id, 'Default' AS name, 3 AS sla, 819405 AS tenant
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM channel WHERE id = new_data.id
);

-- Insert Return Reasons
INSERT INTO return_reason (id, return_reason_id, reason, tenant)
SELECT id, return_reason_id, reason, tenant
FROM (
    SELECT 1000 AS id, UUID() AS return_reason_id, 'CHANGE OF MIND' AS reason, 837995 AS tenant
    UNION ALL
    SELECT 1001 AS id, UUID() AS return_reason_id, 'ITEM IS DAMAGED OR DEFECTIVE' AS reason, 837995 AS tenant
    UNION ALL
    SELECT 1002 AS id, UUID() AS return_reason_id, 'DUPLICATE ORDER' AS reason, 837995 AS tenant
    UNION ALL
    SELECT 1003 AS id, UUID() AS return_reason_id, 'ITEM DOES NOT FIT' AS reason, 837995 AS tenant
    UNION ALL
    SELECT 1004 AS id, UUID() AS return_reason_id, 'DELIVERY ISSUE' AS reason, 837995 AS tenant
    UNION ALL
    SELECT 1005 AS id, UUID() AS return_reason_id, 'ITEM IS NOT AS SHOWN' AS reason, 837995 AS tenant
    UNION ALL
    SELECT 1006 AS id, UUID() AS return_reason_id, 'ITEM IS NOT AS EXPECTED' AS reason, 837995 AS tenant
    UNION ALL
    SELECT 1007 AS id, UUID() AS return_reason_id, 'WRONG ITEM RECEIVED' AS reason, 837995 AS tenant
    UNION ALL
    SELECT 1008 AS id, UUID() AS return_reason_id, 'CHANGE OF MIND' AS reason, 913265 AS tenant
    UNION ALL
    SELECT 1009 AS id, UUID() AS return_reason_id, 'ITEM IS DAMAGED OR DEFECTIVE' AS reason, 913265 AS tenant
    UNION ALL
    SELECT 1010 AS id, UUID() AS return_reason_id, 'DUPLICATE ORDER' AS reason, 913265 AS tenant
    UNION ALL
    SELECT 1011 AS id, UUID() AS return_reason_id, 'ITEM DOES NOT FIT' AS reason, 913265 AS tenant
    UNION ALL
    SELECT 1012 AS id, UUID() AS return_reason_id, 'DELIVERY ISSUE' AS reason, 913265 AS tenant
    UNION ALL
    SELECT 1013 AS id, UUID() AS return_reason_id, 'ITEM IS NOT AS SHOWN' AS reason, 913265 AS tenant
    UNION ALL
    SELECT 1014 AS id, UUID() AS return_reason_id, 'ITEM IS NOT AS EXPECTED' AS reason, 913265 AS tenant
    UNION ALL
    SELECT 1015 AS id, UUID() AS return_reason_id, 'WRONG ITEM RECEIVED' AS reason, 913265 AS tenant
    UNION ALL
    SELECT 1016 AS id, UUID() AS return_reason_id, 'CHANGE OF MIND' AS reason, 819405 AS tenant
    UNION ALL
    SELECT 1017 AS id, UUID() AS return_reason_id, 'ITEM IS DAMAGED OR DEFECTIVE' AS reason, 819405 AS tenant
    UNION ALL
    SELECT 1018 AS id, UUID() AS return_reason_id, 'DUPLICATE ORDER' AS reason, 819405 AS tenant
    UNION ALL
    SELECT 1019 AS id, UUID() AS return_reason_id, 'ITEM DOES NOT FIT' AS reason, 819405 AS tenant
    UNION ALL
    SELECT 1020 AS id, UUID() AS return_reason_id, 'DELIVERY ISSUE' AS reason, 819405 AS tenant
    UNION ALL
    SELECT 1021 AS id, UUID() AS return_reason_id, 'ITEM IS NOT AS SHOWN' AS reason, 819405 AS tenant
    UNION ALL
    SELECT 1022 AS id, UUID() AS return_reason_id, 'ITEM IS NOT AS EXPECTED' AS reason, 819405 AS tenant
    UNION ALL
    SELECT 1023 AS id, UUID() AS return_reason_id, 'WRONG ITEM RECEIVED' AS reason, 819405 AS tenant
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM return_reason WHERE reason = new_data.reason AND tenant = new_data.tenant
);


-- Insert Auth Provider
INSERT INTO auth_provider (id, auth_url, provider, tenant, merchant_id)
SELECT *
FROM (
    SELECT 1000 AS id,'/api/v1/auth/login' AS auth_url,'DB' AS provider,837995 AS tenant,1000 AS merchant_id
    UNION ALL
    SELECT
         1001 AS id, 'test', 'GOOGLE', 837995, 1000
    UNION ALL
    SELECT
         1002 AS id, '/api/v1/auth/login', 'DB', 913265, 1000
    UNION ALL
    SELECT
         1003 AS id, '/oauth2/authorization/google-penti', 'GOOGLE', 913265, 1000
    UNION ALL
    SELECT
         1004 AS id, '/api/v1/auth/login', 'DB', 819405, 1002
    UNION ALL
    SELECT
         1005 AS id,  '/oauth2/authorization/google-demo', 'GOOGLE', 819405, 1002
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM auth_provider WHERE id = new_data.id
);


-- Insert Roles
INSERT INTO `role` (id, name)
SELECT id, name
FROM (
    SELECT 1000 AS id, 'RI_INSP_PCKG_SCN' AS name
    UNION ALL
    SELECT 1002 AS id, 'RI_INSP_ODP_EDIT' AS name
    UNION ALL
    SELECT 1003 AS id, 'RI_INSP_MEDIA_UPLD_EXT' AS name
    UNION ALL
    SELECT 1004 AS id, 'RI_INSP_SND_REVIEW' AS name
    UNION ALL
    SELECT 1005 AS id, 'RI_INSP_APRV' AS name
    UNION ALL
    SELECT 1006 AS id, 'RI_INSP_WRITE_CMNT' AS name
    UNION ALL
    SELECT 1007 AS id, 'RI_INSP_WRITE_CMNT_EXT' AS name
    UNION ALL
    SELECT 1008 AS id, 'RI_INSP_MEDIA_UPLD' AS name
    UNION ALL
    SELECT 1009 AS id, 'RI_INSP_CREATE_INSP' AS name
    UNION ALL
    SELECT 1010 AS id, 'RI_INSP_RJCT' AS name
    UNION ALL
    SELECT 1011 AS id, 'RI_INSP_SLCT_RETN_RSN' AS name
    UNION ALL
    SELECT 1012 AS id, 'RI_INSP_LAND_SCAN' AS name
    UNION ALL
    SELECT 1013 AS id, 'RI_INSP_SLA' AS name
    UNION ALL
    SELECT 1014 AS id, 'RI_INSP_RETN_RSN' AS name
    UNION ALL
    SELECT 1015 AS id, 'RI_INSP_AUDIT' AS name
    UNION ALL
    SELECT 1016 AS id, 'RI_INSP_OMS_CONFIG' AS name
    UNION ALL
    SELECT 1017 AS id, 'RI_INSP_CREATE_USER' AS name
    UNION ALL
    SELECT 1018 AS id, 'RI_INSP_CREATE_USER_ROLE_GRP' AS name
    UNION ALL
    SELECT 1019 AS id, 'RI_INSP_QI_INSTRUCTIONS' AS name
    UNION ALL
    SELECT 1020 AS id, 'RI_INSP_MY_PROFILE' AS name
    UNION ALL
    SELECT 1021 AS id, 'RI_INSP_ACCESS_INSP_PAGE' AS name
    UNION ALL
    SELECT 1022 AS id, 'RI_INSP_ACCESS_DSHBRD_PAGE' AS name
    UNION ALL
    SELECT 1023 AS id, 'RI_INSP_INTGRN_LOG' AS name
    UNION ALL
    SELECT 1024 AS id, 'RI_INSP_INTGRN_LOG_VIEW' AS name
    UNION ALL
    SELECT 1025 AS id, 'RI_INSP_ITEM_CONDN' AS name
    UNION ALL
    SELECT 1026 AS id, 'RI_INSP_USER_GRP' AS name
    UNION ALL
    SELECT 1027 AS id, 'RI_INSP_REVIEWERS' AS name
    UNION ALL
    SELECT 1028 AS id, 'RI_INSP_SSO_CONFIG' AS name
    UNION ALL
    SELECT 1029 AS id, 'RI_INSP_EDIT_INSTRUCTIONS' AS name
    UNION ALL
    SELECT 1030 AS id, 'RI_INSP_PREVIEW_HIDE' AS name
    UNION ALL
    SELECT 1031 AS id, 'RI_INSP_CUSTOMER_DETAILS' AS name
    UNION ALL
    SELECT 1032 AS id, 'RI_INSP_MEDIA_QR_CODE' AS name
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM `role` WHERE id = new_data.id
);


-- Insert Role Group
INSERT INTO role_group (id, role_group_id, name, tenant)
SELECT id,role_group_id, name, tenant
FROM (
    SELECT 1000 AS id, UUID() AS role_group_id, 'ADMIN_ROLES' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1001 AS id, UUID() AS role_group_id, 'WO_ROLES' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1002 AS id, UUID() AS role_group_id, 'SU_ROLES' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1003 AS id, UUID() AS role_group_id, 'CS_ROLES' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1004 AS id, UUID() AS role_group_id, 'MP_ROLES' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1005 AS id, UUID() AS role_group_id, 'ADMIN_ROLES' AS name, 819405 AS tenant
    UNION ALL
    SELECT 1006 AS id, UUID() AS role_group_id, 'WO_ROLES' AS name, 819405 AS tenant
    UNION ALL
    SELECT 1007 AS id, UUID() AS role_group_id, 'SU_ROLES' AS name, 819405 AS tenant
    UNION ALL
    SELECT 1008 AS id, UUID() AS role_group_id, 'CS_ROLES' AS name, 819405 AS tenant
    UNION ALL
    SELECT 1009 AS id, UUID() AS role_group_id, 'MP_ROLES' AS name, 819405 AS tenant
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM role_group WHERE id = new_data.id
);

-- Insert User Group
INSERT INTO user_group (id, name, tenant, comment_visibility, user_group_id,partner_name, is_active)
SELECT id, name, tenant, comment_visibility, user_group_id,partner_name, is_active
FROM (
    SELECT 1000 AS id, 'Warehouse Operator' AS name, 837995 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1001 AS id, 'Supervisor' AS name, 837995 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1002 AS id, 'Harrods' AS name, 837995 AS tenant, 'EXTERNAL' AS comment_visibility, UUID() AS user_group_id, 'Harrods' As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1003 AS id, 'Client Service' AS name, 837995 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1004 AS id, 'Warehouse Operator' AS name, 913265 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1005 AS id, 'Supervisor' AS name, 913265 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1006 AS id, 'Farfetch' AS name, 913265 AS tenant, 'EXTERNAL' AS comment_visibility, UUID() AS user_group_id, 'Farfetch' As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1007 AS id, 'Farfetch' AS name, 837995 AS tenant, 'EXTERNAL' AS comment_visibility, UUID() AS user_group_id, 'Farfetch' As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1008 AS id, 'Harrods' AS name, 913265 AS tenant, 'EXTERNAL' AS comment_visibility, UUID() AS user_group_id, 'Harrods' As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1009 AS id, 'Warehouse Operator' AS name, 819405 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1010 AS id, 'Supervisor' AS name, 819405 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1011 AS id, 'Client Service' AS name, 819405 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1012 AS id, 'Amazon' AS name, 819405 AS tenant, 'EXTERNAL' AS comment_visibility, UUID() AS user_group_id, 'Amazon' As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1013 AS id, 'Admin' AS name, 819405 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1014 AS id, 'Admin' AS name, 837995 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1015 AS id, 'Admin' AS name, 913265 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
    UNION ALL
    SELECT 1016 AS id, 'Client Service' AS name, 913265 AS tenant, 'INTERNAL' AS comment_visibility, UUID() AS user_group_id, NULL As partner_name, 1 AS is_active
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_group WHERE id = new_data.id
);

-- Template Data
INSERT INTO template (id, template_data, template_id, template_name, tenant, user_group)
SELECT id, template_data, template_id, template_name, tenant, user_group
FROM (
    SELECT 1000 AS id, '[{"field":"status","label":"Status icon","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"referenceId","label":"Reference ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"orderId","label":"Order ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"createdAt","label":"Start date","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"sellingChannel","label":"Channel","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"partnerName","label":"Partner","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"assigneeGroup","label":"Assigned to","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"remainingDays","label":"Remaining days","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}}]' AS template_data, 'RI_INSPECTION' AS template_name,
        819405 AS tenant, 1013 AS user_group, UUID() AS template_id) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM template WHERE id = new_data.id
);

INSERT INTO template (id, template_data, template_id, template_name, tenant, user_group)
SELECT id, template_data, template_id, template_name, tenant, user_group
FROM (
    SELECT 1001 AS id, '[{"field":"status","label":"Status icon","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"referenceId","label":"Reference ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"orderId","label":"Order ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"createdAt","label":"Start date","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"sellingChannel","label":"Channel","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"partnerName","label":"Partner","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"assigneeGroup","label":"Assigned to","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"remainingDays","label":"Remaining days","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}}]' AS template_data, 'RI_INSPECTION' AS template_name,
        837995 AS tenant, 1014 AS user_group, UUID() AS template_id) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM template WHERE id = new_data.id
);

INSERT INTO template (id, template_data, template_id, template_name, tenant, user_group)
SELECT id, template_data, template_id, template_name, tenant, user_group
FROM (
    SELECT 1002 AS id, '[{"field":"status","label":"Status icon","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"referenceId","label":"Reference ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"orderId","label":"Order ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"createdAt","label":"Start date","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"sellingChannel","label":"Channel","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"partnerName","label":"Partner","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"assigneeGroup","label":"Assigned to","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"remainingDays","label":"Remaining days","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}}]' AS template_data, 'RI_INSPECTION' AS template_name,
        913265 AS tenant, 1015 AS user_group, UUID() AS template_id) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM template WHERE id = new_data.id
);


INSERT INTO user_group_reviewers (user_group_id, reviewer_group_id)
SELECT new_data.user_group_id, new_data.reviewer_group_id
FROM (
    SELECT 1000 AS user_group_id, 1001 AS reviewer_group_id UNION ALL
    SELECT 1000, 1003 UNION ALL
    SELECT 1001, 1000 UNION ALL
    SELECT 1001, 1003 UNION ALL
    SELECT 1003, 1000 UNION ALL
    SELECT 1003, 1001 UNION ALL
    SELECT 1003, 1002 UNION ALL
    SELECT 1003, 1007 UNION ALL
    SELECT 1002, 1003 UNION ALL
    SELECT 1007, 1003 UNION ALL
    SELECT 1004, 1005 UNION ALL
    SELECT 1004, 1016 UNION ALL
    SELECT 1016, 1004 UNION ALL
    SELECT 1016, 1005 UNION ALL
    SELECT 1016, 1006 UNION ALL
    SELECT 1016, 1008 UNION ALL
    SELECT 1006, 1016 UNION ALL
    SELECT 1008, 1016 UNION ALL
    SELECT 1009, 1010 UNION ALL
    SELECT 1009, 1011 UNION ALL
    SELECT 1010, 1009 UNION ALL
    SELECT 1010, 1011 UNION ALL
    SELECT 1011, 1009 UNION ALL
    SELECT 1011, 1010 UNION ALL
    SELECT 1011, 1012 UNION ALL
    SELECT 1011, 1013 UNION ALL
    SELECT 1012, 1011 UNION ALL
    SELECT 1013, 1011 UNION ALL
    SELECT 1005, 1004 UNION ALL
    SELECT 1005, 1016
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_group_reviewers
    WHERE user_group_id = new_data.user_group_id
    AND reviewer_group_id = new_data.reviewer_group_id
);


INSERT INTO inspection_item_condition (id, condition_type, item_condition_id, name, tenant)
SELECT id, condition_type, item_condition_id, name, tenant
FROM (
    SELECT 1000 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Good Condition' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1001 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Damaged (needs reconditioning)' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1002 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Damaged (needs repair)' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1003 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Damaged (unusable)' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1004 AS id, 'REJECTED' AS condition_type, UUID() AS item_condition_id, 'Worn out' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1005 AS id, 'REJECTED' AS condition_type, UUID() AS item_condition_id, 'Labels not available' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1006 AS id, 'REJECTED' AS condition_type, UUID() AS item_condition_id, 'Damaged' AS name, 837995 AS tenant
    UNION ALL
    SELECT 1007 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Good Condition' AS name, 913265 AS  tenant
    UNION ALL
    SELECT 1008 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Damaged (needs reconditioning)' AS name, 913265 AS tenant
    UNION ALL
    SELECT 1009 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Damaged (needs repair)' AS name,  913265 AS tenant
    UNION ALL
    SELECT 1010 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Damaged (unusable)' AS name, 913265  AS tenant
    UNION ALL
    SELECT 1011 AS id, 'REJECTED' AS condition_type, UUID() AS item_condition_id, 'Worn out' AS name, 913265 AS tenant
    UNION ALL
    SELECT 1012 AS id, 'REJECTED' AS condition_type, UUID() AS item_condition_id, 'Labels not available' AS name, 913265 AS tenant
    UNION ALL
    SELECT 1013 AS id, 'REJECTED' AS condition_type, UUID() AS item_condition_id, 'Damaged' AS name, 913265 AS tenant
    UNION ALL
    SELECT 1014 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Good Condition' AS name, 819405 AS   tenant
    UNION ALL
    SELECT 1015 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Damaged (needs reconditioning)' AS  name, 819405 AS tenant
    UNION ALL
    SELECT 1016 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Damaged (needs repair)' AS name,   819405 AS tenant
    UNION ALL
    SELECT 1017 AS id, 'APPROVED' AS condition_type, UUID() AS item_condition_id, 'Damaged (unusable)' AS name, 819405   AS tenant
    UNION ALL
    SELECT 1018 AS id, 'REJECTED' AS condition_type, UUID() AS item_condition_id, 'Worn out' AS name, 819405 AS tenant
    UNION ALL
    SELECT 1019 AS id, 'REJECTED' AS condition_type, UUID() AS item_condition_id, 'Labels not available' AS name, 819405 AS tenant
    UNION ALL
    SELECT 1020 AS id, 'REJECTED' AS condition_type, UUID() AS item_condition_id, 'Damaged' AS name, 819405 AS tenant
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM inspection_item_condition WHERE id = new_data.id
);


-- Insert User
INSERT INTO user (id, is_active, password, user_name, auth_provider_id, tenant, first_name, last_name, user_id, merchant_id, language,role_group_id)
SELECT id, is_active, password, user_name, auth_provider_id, tenant, first_name, last_name, user_id, merchant_id, language,role_group_id
FROM (
    SELECT 1000 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password,'<EMAIL>' AS user_name, 1000 AS auth_provider_id, 837995 AS tenant, 'db' AS first_name, 'user' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1001 As role_group_id
    UNION ALL
    SELECT 1001 AS id, 1 AS is_active, NULL AS password, '<EMAIL>' AS user_name, 1001 AS auth_provider_id, 837995 AS tenant, 'Sri Rohith' AS first_name, 'Ch' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1001 As role_group_id
    UNION ALL
    SELECT 1002 AS id, 1 AS is_active, NULL AS password, '<EMAIL>' AS user_name, 1001 AS auth_provider_id, 837995 AS tenant, 'Aravind' AS first_name, 'Aliveli' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1001 As role_group_id
    UNION ALL
    SELECT 1003 AS id, 1 AS is_active, NULL AS password, '<EMAIL>' AS user_name, 1001 AS auth_provider_id, 837995 AS tenant, 'Ajay' AS first_name, 'Shankar' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1001 As role_group_id
    UNION ALL
    SELECT 1004 AS id, 1 AS is_active, NULL AS password, '<EMAIL>' AS user_name, 1001 AS auth_provider_id, 837995 AS tenant, NULL AS first_name, NULL AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1001 As role_group_id
    UNION ALL
    SELECT 1005 AS id, 1 AS is_active, NULL AS password, '<EMAIL>' AS user_name, 1001 AS auth_provider_id, 837995 AS tenant, 'Keerthana' AS first_name, 'Gummadi' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1001 As role_group_id
    UNION ALL
    SELECT 1006 AS id, 1 AS is_active, NULL AS password, '<EMAIL>' AS user_name, 1001 AS auth_provider_id, 837995 AS tenant, 'Prudhvi' AS first_name, 'Sajja' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1001 As role_group_id
    UNION ALL
    SELECT 1007 AS id, 1 AS is_active, NULL AS password, '<EMAIL>' AS user_name, 1001 AS auth_provider_id, 837995 AS tenant, 'Siva' AS first_name, 'Sajja' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1001 As role_group_id
    UNION ALL
    SELECT 1008 AS id, 1 AS is_active, NULL AS password, '<EMAIL>' AS user_name, 1001 AS auth_provider_id, 837995 AS tenant, 'Jeevan' AS first_name, 'Peddineni' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1001 As role_group_id
    UNION ALL
    SELECT 1009 AS id, 1 AS is_active, NULL AS password, '<EMAIL>' AS user_name, 1001 AS auth_provider_id, 837995 AS tenant, 'Sukanya' AS first_name, 'Marothy' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1001 As role_group_id
    UNION ALL
    SELECT 1010 AS id, 1 AS is_active, NULL AS password, '<EMAIL>' AS user_name, 1001 AS auth_provider_id, 837995 AS tenant, 'Shilpa' AS first_name, 'Vinodh' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1001 As role_group_id
    UNION ALL
    SELECT 1011 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password, '<EMAIL>' AS user_name, 1000 AS auth_provider_id, 837995 AS tenant, 'Warehouse' AS first_name, 'Operator' AS last_name, UUID() AS user_id, 1001 As merchant_id, 'en' As language,1000 As role_group_id
    UNION ALL
    SELECT 1012 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password, '<EMAIL>' AS user_name, 1000 AS auth_provider_id, 837995 AS tenant, 'Supervisor' AS first_name, '' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1002 As role_group_id
    UNION ALL
    SELECT 1013 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password, '<EMAIL>' AS user_name, 1000 AS auth_provider_id, 837995 AS tenant, 'Marketplace' AS first_name, 'User' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1000 As role_group_id
    UNION ALL
    SELECT 1014 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password, '<EMAIL>' AS user_name, 1000 AS auth_provider_id, 837995 AS tenant, 'Client' AS first_name, 'Service' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1003 As role_group_id
    UNION ALL
    SELECT 1015 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password, '<EMAIL>' AS user_name, 1002 AS auth_provider_id, 913265 AS tenant, 'Penti' AS first_name, 'User' AS last_name, UUID() AS user_id, 1001 As merchant_id, 'en' As language,1000 As role_group_id
    UNION ALL
    SELECT 1016 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password, '<EMAIL>' AS user_name, 1002 AS auth_provider_id, 913265 AS tenant, 'Supervisor' AS first_name, 'User' AS last_name, UUID() AS user_id, 1001 As merchant_id, 'en' As language,1000 As role_group_id
    UNION ALL
    SELECT 1017 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password, '<EMAIL>' AS user_name, 1002 AS auth_provider_id, 837995 AS tenant, 'Farfetch' AS first_name, 'User' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1000 As role_group_id
    UNION ALL
    SELECT 1018 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password, '<EMAIL>' AS user_name, 1002 AS auth_provider_id, 837995 AS tenant, 'Harrods' AS first_name, 'User' AS last_name, UUID() AS user_id, 1000 As merchant_id, 'en' As language,1004 As role_group_id
    UNION ALL
    SELECT 1019 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password, '<EMAIL>' AS user_name, 1002 AS auth_provider_id, 913265 AS tenant, 'Farfetch' AS first_name, 'User' AS last_name, UUID() AS user_id, 1001 As merchant_id, 'en' As language,1004 As role_group_id
    UNION ALL
    SELECT 1020 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password, '<EMAIL>' AS user_name, 1002 AS auth_provider_id, 913265 AS tenant, 'Harrods' AS first_name, 'User' AS last_name, UUID() AS user_id, 1001 As merchant_id, 'en' As language,1000 As role_group_id
    UNION ALL
    SELECT 1021 AS id, 1 AS is_active, '$2a$10$i7d/ZFgOjt8eOd5oScu6P.RTw0SEJrSypLhn6uhGlMM/1jTF6N4HG' AS password, '<EMAIL>' AS user_name, 1000 AS auth_provider_id, 819405 AS tenant, 'Warehouse' AS first_name, 'Operator' AS last_name , UUID() AS user_id, 1002 As merchant_id, 'en' As language,1006 As role_group_id
    UNION ALL
    SELECT 1022 AS id, 1 AS is_active, '$2a$10$i7d/ZFgOjt8eOd5oScu6P.RTw0SEJrSypLhn6uhGlMM/1jTF6N4HG' AS password, '<EMAIL>' AS user_name, 1000 AS auth_provider_id, 819405 AS tenant, 'Supervisor' AS first_name, '' AS last_name , UUID() AS user_id, 1002 As merchant_id, 'en' As language,1007 As role_group_id
    UNION ALL
    SELECT 1023 AS id, 1 AS is_active, '$2a$10$i7d/ZFgOjt8eOd5oScu6P.RTw0SEJrSypLhn6uhGlMM/1jTF6N4HG' AS password, '<EMAIL>' AS user_name, 1000 AS auth_provider_id, 819405 AS tenant, 'Client' AS first_name, 'Service' AS last_name , UUID() AS user_id, 1002 As merchant_id, 'en' As language,1008 As role_group_id
    UNION ALL
    SELECT 1024 AS id, 1 AS is_active, '$2a$10$i7d/ZFgOjt8eOd5oScu6P.RTw0SEJrSypLhn6uhGlMM/1jTF6N4HG' AS password, '<EMAIL>' AS user_name, 1002 AS auth_provider_id, 819405 AS tenant, 'Admin' AS first_name, 'User' AS last_name , UUID() AS user_id, 1002 As merchant_id, 'en' As language,1005 As role_group_id
    UNION ALL
    SELECT 1025 AS id, 1 AS is_active, '$2a$10$i7d/ZFgOjt8eOd5oScu6P.RTw0SEJrSypLhn6uhGlMM/1jTF6N4HG' AS password, '<EMAIL>' AS user_name, 1002 AS auth_provider_id, 819405 AS tenant, 'Amazon' AS first_name, 'User' AS last_name , UUID() AS user_id, 1002 As merchant_id, 'en' As language,1009 As role_group_id
    UNION ALL
    SELECT 1026 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password,'<EMAIL>' AS user_name, 1002 AS auth_provider_id, 837995 AS tenant, 'Admin' AS first_name, 'User' AS last_name , UUID() AS user_id, 1000 As merchant_id, 'en' As language,1000 As role_group_id
    UNION ALL
    SELECT 1027 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password,'<EMAIL>' AS user_name, 1002 AS auth_provider_id, 913265 AS tenant, 'Admin' AS first_name, 'User' AS last_name ,UUID() AS user_id, 1001 As merchant_id, 'en' As language,1000 As role_group_id
    UNION ALL
    SELECT 1028 AS id, 1 AS is_active, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q' AS password, '<EMAIL>' AS user_name, 1002 AS auth_provider_id, 913265 AS tenant, 'Client' AS first_name, 'Service' AS last_name, UUID() AS user_id, 1001 As merchant_id, 'en' As language,1000 As role_group_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user WHERE id = new_data.id
);

-- Insert User Group Relationship
INSERT INTO user_group_rel (group_id, user_id)
SELECT group_id, user_id
FROM (
    SELECT 1000 AS group_id, 1000 AS user_id
    UNION ALL
    SELECT 1000 AS group_id, 1001 AS user_id
    UNION ALL
    SELECT 1000 AS group_id, 1002 AS user_id
    UNION ALL
    SELECT 1001 AS group_id, 1003 AS user_id
    UNION ALL
    SELECT 1000 AS group_id, 1004 AS user_id
    UNION ALL
    SELECT 1000 AS group_id, 1005 AS user_id
    UNION ALL
    SELECT 1000 AS group_id, 1006 AS user_id
    UNION ALL
    SELECT 1000 AS group_id, 1007 AS user_id
    UNION ALL
    SELECT 1000 AS group_id, 1008 AS user_id
    UNION ALL
    SELECT 1000 AS group_id, 1009 AS user_id
    UNION ALL
    SELECT 1000 AS group_id, 1010 AS user_id
    UNION ALL
    SELECT 1000 AS group_id, 1011 AS user_id
    UNION ALL
    SELECT 1001 AS group_id, 1012 AS user_id
    UNION ALL
    SELECT 1002 AS group_id, 1013 AS user_id
    UNION ALL
    SELECT 1003 AS group_id, 1014 AS user_id
    UNION ALL
    SELECT 1004 AS group_id, 1015 AS user_id
    UNION ALL
    SELECT 1004 AS group_id, 1015 AS user_id
    UNION ALL
    SELECT 1007 AS group_id, 1017 AS user_id
    UNION ALL
    SELECT 1002 AS group_id, 1018 AS user_id
    UNION ALL
    SELECT 1005 AS group_id, 1016 AS user_id
    UNION ALL
    SELECT 1006 AS group_id, 1019 AS user_id
    UNION ALL
    SELECT 1008 AS group_id, 1020 AS user_id
    UNION ALL
    SELECT 1009 AS group_id, 1021 AS user_id
    UNION ALL
    SELECT 1010 AS group_id, 1022 AS user_id
    UNION ALL
    SELECT 1011 AS group_id, 1023 AS user_id
    UNION ALL
    SELECT 1013 AS group_id, 1024 AS user_id
    UNION ALL
    SELECT 1012 AS group_id, 1025 AS user_i
    UNION ALL
    SELECT 1014 AS group_id, 1026 AS user_id
    UNION ALL
    SELECT 1015 AS group_id, 1027 AS user_i
    UNION ALL
    SELECT 1016 AS group_id, 1028 AS user_i
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM user_group_rel WHERE group_id = new_data.group_id AND user_id = new_data.user_id);



-- Insert OMS Provider
INSERT INTO oms_provider (id, provider_name, tenant)
SELECT id, provider_name, tenant
FROM (
    SELECT 1000 AS id, 'MAO' AS provider_name, 837995 AS tenant
    UNION ALL
    SELECT 1001 AS id, 'MAO' AS provider_name, 913265 AS tenant
    UNION ALL
    SELECT 1002 AS id, 'MOCK' AS provider_name, 819405 AS tenant
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM oms_provider WHERE id = new_data.id
);

-- Insert OMS Config
INSERT INTO oms_config (config_key, config_value, oms_provider_id)
SELECT config_key, config_value, oms_provider_id
FROM (
    SELECT 'auth_url' AS config_key, 'https://guccs-auth.omni.manh.com' AS config_value, 1000 AS oms_provider_id
    UNION ALL
    SELECT 'base_path' AS config_key, 'https://omni-guccs.omni.manh.com' AS config_value, 1000 AS oms_provider_id
    UNION ALL
    SELECT 'username' AS config_key, 'omnicomponent.1.0.0' AS config_value, 1000 AS oms_provider_id
    UNION ALL
    SELECT 'password' AS config_key, 'b4s8rgTyg55XYNun' AS config_value, 1000 AS oms_provider_id
    UNION ALL
    SELECT 'basic_auth_user' AS config_key, '<EMAIL>' AS config_value, 1000 AS oms_provider_id
    UNION ALL
    SELECT 'basic_auth_password' AS config_key, 'Password4=' AS config_value, 1000 AS oms_provider_id
    UNION ALL
    SELECT 'search_organizations' AS config_key, 'Gucci-US,Gucci-IT' AS config_value, 1000 AS oms_provider_id
    UNION ALL
    SELECT 'product_organizations' AS config_key, 'GCOM' AS config_value, 1000 AS oms_provider_id
    UNION ALL
    SELECT 'base_path' AS config_key, 'http://localhost:8090/api/oms/demo' AS config_value, 1002 AS oms_provider_id
    UNION ALL
    SELECT 'user_name' AS config_key, 'demo' AS config_value, 1002 AS oms_provider_id
    UNION ALL
    SELECT 'password' AS config_key, 'DPas0rd' AS config_value, 1002 AS oms_provider_id
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM oms_config WHERE config_key = new_data.config_key and oms_provider_id = new_data.oms_provider_id
);

-- Insert Search Strategies
INSERT INTO search_strategies (search_strategy_id, search_strategy)
SELECT search_strategy_id, search_strategy
FROM (
    SELECT 1000 AS search_strategy_id, 'RETURN_BY_TRACKING_NUMBER' AS search_strategy
    UNION ALL
    SELECT 1000 AS search_strategy_id, 'ORDER_BY_ID' AS search_strategy
    UNION ALL
    SELECT 1001 AS search_strategy_id, 'RETURN_BY_TRACKING_NUMBER' AS search_strategy
    UNION ALL
    SELECT 1001 AS search_strategy_id, 'ORDER_BY_ID' AS search_strategy
    UNION ALL
    SELECT 1002 AS search_strategy_id, 'RETURN_BY_TRACKING_NUMBER' AS search_strategy
    UNION ALL
    SELECT 1002 AS search_strategy_id, 'ORDER_BY_ID' AS search_strategy
    UNION ALL
    SELECT 1002 AS search_strategy_id, 'ORDER_BY_RETURN_TRACKING' AS search_strategy
    UNION ALL
    SELECT 1002 AS search_strategy_id, 'ORDER_BY_PRODUCT_SERIAL_NUMBER' AS search_strategy
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM search_strategies WHERE search_strategy_id = new_data.search_strategy_id and search_strategy = new_data.search_strategy
);

-- User Roles Groups

--ADMIN Roles
INSERT INTO role_role_groups (role_group_id, role_id)
SELECT new_data.role_group_id, new_data.role_id
FROM (
    SELECT 1000 AS role_group_id, 1022 AS role_id UNION ALL
    SELECT 1000, 1015 UNION ALL
    SELECT 1000, 1017 UNION ALL
    SELECT 1000, 1018 UNION ALL
    SELECT 1000, 1014 UNION ALL
    SELECT 1000, 1020 UNION ALL
    SELECT 1000, 1016 UNION ALL
    SELECT 1000, 1019 UNION ALL
    SELECT 1000, 1023 UNION ALL
    SELECT 1000, 1000 UNION ALL
    SELECT 1000, 1013 UNION ALL
    SELECT 1000, 1024 UNION ALL
    SELECT 1000, 1025 UNION ALL
    SELECT 1000, 1026 UNION ALL
    SELECT 1000, 1027 UNION ALL
    SELECT 1000, 1028 UNION ALL
    SELECT 1000, 1029
) AS new_data
LEFT JOIN role_role_groups existing
  ON existing.role_group_id = new_data.role_group_id
 AND existing.role_id = new_data.role_id
WHERE existing.role_group_id IS NULL;


--WO Roles
INSERT INTO role_role_groups (role_group_id, role_id)
SELECT new_data.role_group_id, new_data.role_id
FROM (
    SELECT 1001 AS role_group_id, 1021 AS role_id UNION ALL
    SELECT 1001, 1005 UNION ALL
    SELECT 1001, 1009 UNION ALL
    SELECT 1001, 1012 UNION ALL
    SELECT 1001, 1008 UNION ALL
    SELECT 1001, 1020 UNION ALL
    SELECT 1001, 1002 UNION ALL
    SELECT 1001, 1000 UNION ALL
    SELECT 1001, 1019 UNION ALL
    SELECT 1001, 1011 UNION ALL
    SELECT 1001, 1004 UNION ALL
    SELECT 1001, 1010 UNION ALL
    SELECT 1001, 1006
) AS new_data
  LEFT JOIN role_role_groups existing
    ON existing.role_group_id = new_data.role_group_id
   AND existing.role_id = new_data.role_id
  WHERE existing.role_group_id IS NULL;

  INSERT INTO role_role_groups (role_group_id, role_id)
  SELECT new_data.role_group_id, new_data.role_id
  FROM (
      SELECT 1006 AS role_group_id, 1021 AS role_id UNION ALL
      SELECT 1006, 1005 UNION ALL
      SELECT 1006, 1009 UNION ALL
      SELECT 1006, 1012 UNION ALL
      SELECT 1006, 1008 UNION ALL
      SELECT 1006, 1020 UNION ALL
      SELECT 1006, 1002 UNION ALL
      SELECT 1006, 1000 UNION ALL
      SELECT 1006, 1019 UNION ALL
      SELECT 1006, 1011 UNION ALL
      SELECT 1006, 1004 UNION ALL
      SELECT 1006, 1010 UNION ALL
      SELECT 1006, 1006
  ) AS new_data
    LEFT JOIN role_role_groups existing
      ON existing.role_group_id = new_data.role_group_id
     AND existing.role_id = new_data.role_id
    WHERE existing.role_group_id IS NULL;

--SU Roles
INSERT INTO role_role_groups (role_group_id, role_id)
SELECT new_data.role_group_id, new_data.role_id
FROM (
    SELECT 1002 AS role_group_id, 1022 AS role_id UNION ALL
    SELECT 1002, 1021 UNION ALL
    SELECT 1002, 1009 UNION ALL
    SELECT 1002, 1008 UNION ALL
    SELECT 1002, 1003 UNION ALL
    SELECT 1002, 1020 UNION ALL
    SELECT 1002, 1019 UNION ALL
    SELECT 1002, 1004
) AS new_data
  LEFT JOIN role_role_groups existing
    ON existing.role_group_id = new_data.role_group_id
   AND existing.role_id = new_data.role_id
  WHERE existing.role_group_id IS NULL;


--CS Roles
INSERT INTO role_role_groups (role_group_id, role_id)
SELECT new_data.role_group_id, new_data.role_id
FROM (
    SELECT 1003 AS role_group_id, 1022 AS role_id UNION ALL
    SELECT 1003, 1021 UNION ALL
    SELECT 1003, 1009 UNION ALL
    SELECT 1003, 1008 UNION ALL
    SELECT 1003, 1003 UNION ALL
    SELECT 1003, 1020 UNION ALL
    SELECT 1003, 1019 UNION ALL
    SELECT 1003, 1004 UNION ALL
    SELECT 1003, 1007
) AS new_data
  LEFT JOIN role_role_groups existing
    ON existing.role_group_id = new_data.role_group_id
   AND existing.role_id = new_data.role_id
  WHERE existing.role_group_id IS NULL;


--MP Roles
INSERT INTO role_role_groups (role_group_id, role_id)
SELECT new_data.role_group_id, new_data.role_id
FROM (
    SELECT 1004 AS role_group_id, 1021 AS role_id UNION ALL
    SELECT 1004, 1008 UNION ALL
    SELECT 1004, 1020 UNION ALL
    SELECT 1004, 1004 UNION ALL
    SELECT 1004, 1006
) AS new_data
  LEFT JOIN role_role_groups existing
    ON existing.role_group_id = new_data.role_group_id
   AND existing.role_id = new_data.role_id
  WHERE existing.role_group_id IS NULL;
