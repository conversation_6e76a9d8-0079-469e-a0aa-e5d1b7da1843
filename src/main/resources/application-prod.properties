spring.application.name=return-insights

server.port=8080

# Flyway migration
flyway.url=jdbc:mysql://${DATA_SOURCE_URL}
flyway.user=${DATA_SOURCE_USERNAME}
flyway.password=${DATA_SOURCE_PASSWORD}

#Database config
spring.datasource.url=jdbc:mysql://${DATA_SOURCE_URL}
spring.datasource.username=${DATA_SOURCE_USERNAME}
spring.datasource.password=${DATA_SOURCE_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Hibernate config
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.show-sql=false

#JWT Config
jwt.secret=mqXGtMAJDRxRdGNKELVF9jn65kF30sJprPoie5IbhUQ=
jwt.accessExpirationMs=86400000
jwt.refreshExpirationMs=172800000

#MinioConfig
minio.access.key=blZbcK0d1aSXQZ7dwvde
minio.secret.key=mBBh0ygIeWe8CDUUzZ98qHpTK2xhK7A5Bomcim6o
minio.secret.api=http://localhost:9000

#Spring Batch Configuration
spring.batch.jdbc.initialize-schema=always
spring.batch.job.enabled=fals