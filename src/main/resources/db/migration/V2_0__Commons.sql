-- Insert Languages (Common for all Merchant)
INSERT INTO supported_language (id, code, name)
SELECT id, code, name
FROM (
    SELECT 1000 AS id, 'en' AS code, 'English' AS name
    UNION ALL
    SELECT 1001 AS id, 'es' AS code, 'Spanish' AS name
    UNION ALL
    SELECT 1002 AS id, 'it' AS code, 'Italian' AS name
    UNION ALL
    SELECT 1003 AS id, 'ar' AS code, 'Arabic' AS name
    UNION ALL
    SELECT 1004 AS id, 'fr' AS code, 'French' AS name
    UNION ALL
    SELECT 1005 AS id, 'de' AS code, 'German' AS name
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM supported_language WHERE id = new_data.id
);

-- Insert Roles
INSERT INTO `role` (id, name)
SELECT id, name
FROM (
    SELECT 1000 AS id, 'RI_INSP_PCKG_SCN' AS name
    UNION ALL
    SELECT 1001 AS id, 'RI_INSP_ODP_EDIT' AS name
    UNION ALL
    SELECT 1002 AS id, 'RI_INSP_MEDIA_UPLD_EXT' AS name
    UNION ALL
    SELECT 1003 AS id, 'RI_INSP_SND_REVIEW' AS name
    UNION ALL
    SELECT 1004 AS id, 'RI_INSP_APRV' AS name
    UNION ALL
    SELECT 1005 AS id, 'RI_INSP_WRITE_CMNT' AS name
    UNION ALL
    SELECT 1006 AS id, 'RI_INSP_WRITE_CMNT_EXT' AS name
    UNION ALL
    SELECT 1007 AS id, 'RI_INSP_MEDIA_UPLD' AS name
    UNION ALL
    SELECT 1008 AS id, 'RI_INSP_CREATE_INSP' AS name
    UNION ALL
    SELECT 1009 AS id, 'RI_INSP_RJCT' AS name
    UNION ALL
    SELECT 1010 AS id, 'RI_INSP_SLCT_RETN_RSN' AS name
    UNION ALL
    SELECT 1011 AS id, 'RI_INSP_LAND_SCAN' AS name
    UNION ALL
    SELECT 1012 AS id, 'RI_INSP_SLA' AS name
    UNION ALL
    SELECT 1013 AS id, 'RI_INSP_RETN_RSN' AS name
    UNION ALL
    SELECT 1014 AS id, 'RI_INSP_AUDIT' AS name
    UNION ALL
    SELECT 1015 AS id, 'RI_INSP_OMS_CONFIG' AS name
    UNION ALL
    SELECT 1016 AS id, 'RI_INSP_CREATE_USER' AS name
    UNION ALL
    SELECT 1017 AS id, 'RI_INSP_CREATE_USER_ROLE_GRP' AS name
    UNION ALL
    SELECT 1018 AS id, 'RI_INSP_QI_INSTRUCTIONS' AS name
    UNION ALL
    SELECT 1019 AS id, 'RI_INSP_MY_PROFILE' AS name
    UNION ALL
    SELECT 1020 AS id, 'RI_INSP_ACCESS_INSP_PAGE' AS name
    UNION ALL
    SELECT 1021 AS id, 'RI_INSP_ACCESS_DSHBRD_PAGE' AS name
    UNION ALL
    SELECT 1022 AS id, 'RI_INSP_INTGRN_LOG' AS name
    UNION ALL
    SELECT 1023 AS id, 'RI_INSP_INTGRN_LOG_VIEW' AS name
    UNION ALL
    SELECT 1024 AS id, 'RI_INSP_ITEM_CONDN' AS name
    UNION ALL
    SELECT 1025 AS id, 'RI_INSP_USER_GRP' AS name
    UNION ALL
    SELECT 1026 AS id, 'RI_INSP_REVIEWERS' AS name
    UNION ALL
    SELECT 1027 AS id, 'RI_INSP_SSO_CONFIG' AS name
    UNION ALL
    SELECT 1028 AS id, 'RI_INSP_EDIT_INSTRUCTIONS' AS name
    UNION ALL
    SELECT 1029 AS id, 'RI_INSP_PREVIEW_HIDE' AS name
    UNION ALL
    SELECT 1030 AS id, 'RI_INSP_CUSTOMER_DETAILS' AS name
    UNION ALL
    SELECT 1031 AS id, 'RI_INSP_MEDIA_QR_CODE' AS name
) AS new_data
WHERE NOT EXISTS (
    SELECT 1 FROM `role` WHERE id = new_data.id
);