ALTER VIEW inspection_view AS
SELECT
    i.inspection_id,
    COALESCE(o.return_order_id, o.order_id) AS order_id,
    i.tenant,
    i.created_by_group as`created_by_group_id`,
    i.assignee_group as`assignee_group_id`,
    aug.name as `assignee_group`,
    i.reference_id,
    i.created_at,
    i.status,
    i.selling_channel,
    GREATEST(DATEDIFF(DATE_ADD(i.created_at, INTERVAL m.sla DAY), CURRENT_DATE), 0) AS remaining_days
FROM
    inspection i
JOIN
    merchant m ON i.tenant = m.tenant
JOIN
	orders o ON o.id = i.order_id
LEFT JOIN
	user_group aug ON aug.id = i.assignee_group;