-- =====================================================
-- New Merchant Setup Script Template
-- =====================================================
-- This script provides a template for setting up a new merchant
-- Replace the placeholder values with actual merchant data

-- Step 1: Insert New Merchant
-- Replace values: merchant_email, merchant_name, tenant_id, reporting_currency, hmac_key
INSERT INTO merchant
(id, merchant_email, merchant_name, tenant, reporting_currency, hmac_key)
VALUES(1000, '<EMAIL>', 'Flexi', 819405, 'EUR', '6uj0IKLBnXnVRN0x4ZOassfeaaaadfrgfg');

-- Step 2: Insert Default Channel
-- Each merchant needs at least one channel for order processing
INSERT INTO channel
(id, channel_id, name, sla, tenant)
VALUES(1000, UUID(), 'Default', 5, 819405);

-- Step 3: Insert Authentication Provider
-- Configure how users will authenticate (DB, GOOGLE, AZURE, OKTA, MANHATTAN)
INSERT INTO auth_provider
(id, auth_url, provider, tenant, merchant_id)
VALUES(1000, '/api/v1/auth/login', 'DB', 819405, 1000);

-- Step 4: Insert Admin User Group
-- Create the primary admin group for the merchant
INSERT INTO user_group(id, name, tenant, comment_visibility, user_group_id,partner_name, is_active)
VALUES
 (1000 ,'Admin', 819405, 'INTERNAL', UUID(), NULL, 1),
 (1001 ,'Warehouse Operator', 819405, 'INTERNAL', UUID(), NULL, 1),
 (1002 ,'Supervisor', 819405, 'INTERNAL', UUID(), NULL, 1),
 (1003 ,'Client Service', 819405, 'INTERNAL', UUID(), NULL, 1),
 (1004 ,'Amazon', 819405, 'EXTERNAL', UUID(), NULL, 1),
 (1005 ,'Zalando', 819405, 'EXTERNAL', UUID(), NULL, 1);

-- Step 5: Insert Default Template Configuration
-- Template for inspection data display and filtering
INSERT INTO template (id, template_data, template_id, template_name, tenant, user_group)
VALUES (1000 , '[{"field":"status","label":"Status icon","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"referenceId","label":"Reference ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"orderId","label":"Order ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"createdAt","label":"Start date","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"sellingChannel","label":"Channel","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"partnerName","label":"Partner","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"assigneeGroup","label":"Assigned to","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"remainingDays","label":"Remaining days","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}}]', 'RI_INSPECTION_FLEXI', UUID(),819405 , 1000);

-- Step 6: Insert Role Group
-- Create admin role group with all permissions
INSERT INTO role_group (id, role_group_id, name, tenant)
VALUES
    (1000, UUID(), 'ADMIN_ROLES', 819405),
    (1001, UUID(), 'WO_ROLES', 819405),
    (1002, UUID(), 'SU_ROLES', 819405),
    (1003, UUID(), 'CS_ROLES', 819405),
    (1004, UUID(), 'MP_ROLES', 819405);

-- Step 7: Insert Admin User
-- Create the primary admin user for the merchant
-- Password: 'password' (hashed with BCrypt)
INSERT INTO user (id, is_active, password, user_name, auth_provider_id, tenant, first_name, last_name, user_id, merchant_id, language,role_group_id)
VALUES
    (1000, 1, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q','<EMAIL>', 1000, 819405, 'siva', 'sajja', UUID(), 1000, 'en',1000),
    (1001, 1, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q','<EMAIL>', 1000, 819405, 'Warehouse', 'Operator', UUID(), 1000, 'en',1001),
    (1002, 1, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q','<EMAIL>', 1000, 819405, 'Supervisor', '', UUID(), 1000, 'en',1002),
    (1003, 1, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q','<EMAIL>', 1000, 819405, 'Client', 'Service', UUID(), 1000, 'en',1003),
    (1004, 1, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q','<EMAIL>', 1000, 819405, 'Amazon', '', UUID(), 1000, 'en',1004),
    (1005, 1, '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q','<EMAIL>', 1000, 819405, 'Zelando', '', UUID(), 1000, 'en',1004);
-- Step 8: Link User to User Group
-- Associate the admin user with the admin group
INSERT INTO user_group_rel (group_id, user_id)
VALUES (1000, 1000),
       (1000, 1001),
       (1000, 1002),
       (1000, 1003),
       (1000, 1004),
       (1000, 1005);

-- Step 9: Insert OMS Provider
-- Configure Order Management System integration
INSERT INTO oms_provider (id, provider_name, tenant)
VALUES (1000, 'MOCK', 819405);

-- Step 10: Insert OMS Configuration
-- Configure OMS connection parameters
INSERT INTO oms_config (config_key, config_value, oms_provider_id)
VALUES
    ('base_path', 'http://89.116.228.165:8090/api/oms/demo', 1000),
    ('user_name', 'demo', 1000),
    ('password', 'DPas0rd', 1000);


-- Step 11: Insert Search Strategies
-- Configure available search methods for orders/returns
INSERT INTO search_strategies (search_strategy_id, search_strategy)
VALUES (1000, 'RETURN_BY_TRACKING_NUMBER'),
    (1000, 'ORDER_BY_ID'),
    (1000, 'ORDER_BY_RETURN_TRACKING'),
    (1000, 'ORDER_BY_PRODUCT_SERIAL_NUMBER');


-- Step 12: Assign Admin Roles
-- Grant all available permissions to the admin role group
-- ADMIN_ROLES (1000) - Full administrative access
INSERT INTO role_role_groups (role_group_id, role_id)
VALUES
    (1000, 1022), -- RI_INSP_ACCESS_DSHBRD_PAGE
    (1000, 1015), -- RI_INSP_AUDIT
    (1000, 1017), -- RI_INSP_CREATE_USER
    (1000, 1018), -- RI_INSP_CREATE_USER_ROLE_GRP
    (1000, 1014), -- RI_INSP_RETN_RSN
    (1000, 1020), -- RI_INSP_MY_PROFILE
    (1000, 1016), -- RI_INSP_OMS_CONFIG
    (1000, 1019), -- RI_INSP_QI_INSTRUCTIONS
    (1000, 1023), -- RI_INSP_INTGRN_LOG
    (1000, 1000), -- RI_INSP_PCKG_SCN
    (1000, 1013), -- RI_INSP_SLA
    (1000, 1024), -- RI_INSP_INTGRN_LOG_VIEW
    (1000, 1025), -- RI_INSP_ITEM_CONDN
    (1000, 1026), -- RI_INSP_USER_GRP
    (1000, 1027), -- RI_INSP_REVIEWERS
    (1000, 1028), -- RI_INSP_SSO_CONFIG
    (1000, 1029), -- RI_INSP_EDIT_INSTRUCTIONS
    (1000, 1021), -- RI_INSP_ACCESS_INSP_PAGE
    (1000, 1009), -- RI_INSP_CREATE_INSP
    (1000, 1005), -- RI_INSP_APRV
    (1000, 1010), -- RI_INSP_RJCT
    (1000, 1004), -- RI_INSP_SND_REVIEW
    (1000, 1006), -- RI_INSP_WRITE_CMNT
    (1000, 1008), -- RI_INSP_MEDIA_UPLD
    (1000, 1011), -- RI_INSP_SLCT_RETN_RSN
    (1000, 1012), -- RI_INSP_LAND_SCAN
    (1000, 1031), -- RI_INSP_CUSTOMER_DETAILS
    (1000, 1032), -- RI_INSP_MEDIA_QR_CODE
    (1000, 1003), -- RI_INSP_MEDIA_UPLD_EXT
    (1000, 1007), -- RI_INSP_WRITE_CMNT_EXT
    (1000, 1002), -- RI_INSP_ODP_EDIT
    (1000, 1030); -- RI_INSP_PREVIEW_HIDE

-- WO_ROLES (1001) - Warehouse Operator permissions
INSERT INTO role_role_groups (role_group_id, role_id)
VALUES
    (1001, 1021), -- RI_INSP_ACCESS_INSP_PAGE
    (1001, 1005), -- RI_INSP_APRV
    (1001, 1009), -- RI_INSP_CREATE_INSP
    (1001, 1012), -- RI_INSP_LAND_SCAN
    (1001, 1008), -- RI_INSP_MEDIA_UPLD
    (1001, 1020), -- RI_INSP_MY_PROFILE
    (1001, 1002), -- RI_INSP_ODP_EDIT
    (1001, 1000), -- RI_INSP_PCKG_SCN
    (1001, 1019), -- RI_INSP_QI_INSTRUCTIONS
    (1001, 1011), -- RI_INSP_SLCT_RETN_RSN
    (1001, 1004), -- RI_INSP_SND_REVIEW
    (1001, 1010), -- RI_INSP_RJCT
    (1001, 1006), -- RI_INSP_WRITE_CMNT
    (1001, 1022), -- RI_INSP_ACCESS_DSHBRD_PAGE
    (1001, 1031), -- RI_INSP_CUSTOMER_DETAILS
    (1001, 1032); -- RI_INSP_MEDIA_QR_CODE

-- SU_ROLES (1002) - Supervisor permissions
INSERT INTO role_role_groups (role_group_id, role_id)
VALUES
    (1002, 1022), -- RI_INSP_ACCESS_DSHBRD_PAGE
    (1002, 1021), -- RI_INSP_ACCESS_INSP_PAGE
    (1002, 1009), -- RI_INSP_CREATE_INSP
    (1002, 1008), -- RI_INSP_MEDIA_UPLD
    (1002, 1004), -- RI_INSP_SND_REVIEW
    (1002, 1020), -- RI_INSP_MY_PROFILE
    (1002, 1019), -- RI_INSP_QI_INSTRUCTIONS
    (1002, 1005), -- RI_INSP_APRV
    (1002, 1010), -- RI_INSP_RJCT
    (1002, 1006), -- RI_INSP_WRITE_CMNT
    (1002, 1000), -- RI_INSP_PCKG_SCN
    (1002, 1011), -- RI_INSP_SLCT_RETN_RSN
    (1002, 1012), -- RI_INSP_LAND_SCAN
    (1002, 1014), -- RI_INSP_RETN_RSN
    (1002, 1027), -- RI_INSP_REVIEWERS
    (1002, 1031), -- RI_INSP_CUSTOMER_DETAILS
    (1002, 1032); -- RI_INSP_MEDIA_QR_CODE

-- CS_ROLES (1003) - Client Services permissions
INSERT INTO role_role_groups (role_group_id, role_id)
VALUES
    (1003, 1022), -- RI_INSP_ACCESS_DSHBRD_PAGE
    (1003, 1021), -- RI_INSP_ACCESS_INSP_PAGE
    (1003, 1009), -- RI_INSP_CREATE_INSP
    (1003, 1008), -- RI_INSP_MEDIA_UPLD
    (1003, 1004), -- RI_INSP_SND_REVIEW
    (1003, 1020), -- RI_INSP_MY_PROFILE
    (1003, 1019), -- RI_INSP_QI_INSTRUCTIONS
    (1003, 1005), -- RI_INSP_APRV
    (1003, 1010), -- RI_INSP_RJCT
    (1003, 1007), -- RI_INSP_WRITE_CMNT_EXT
    (1003, 1006), -- RI_INSP_WRITE_CMNT
    (1003, 1003), -- RI_INSP_MEDIA_UPLD_EXT
    (1003, 1011), -- RI_INSP_SLCT_RETN_RSN
    (1003, 1014), -- RI_INSP_RETN_RSN
    (1003, 1031), -- RI_INSP_CUSTOMER_DETAILS
    (1003, 1032); -- RI_INSP_MEDIA_QR_CODE

-- MP_ROLES (1004) - Marketplace Users permissions
INSERT INTO role_role_groups (role_group_id, role_id)
VALUES
    (1004, 1021), -- RI_INSP_ACCESS_INSP_PAGE
    (1004, 1009), -- RI_INSP_CREATE_INSP
    (1004, 1020), -- RI_INSP_MY_PROFILE
    (1004, 1005), -- RI_INSP_APRV
    (1004, 1006), -- RI_INSP_WRITE_CMNT
    (1004, 1007), -- RI_INSP_WRITE_CMNT_EXT
    (1004, 1008), -- RI_INSP_MEDIA_UPLD
    (1004, 1003), -- RI_INSP_MEDIA_UPLD_EXT
    (1004, 1010), -- RI_INSP_RJCT
    (1004, 1011), -- RI_INSP_SLCT_RETN_RSN
    (1004, 1014), -- RI_INSP_RETN_RSN
    (1004, 1031), -- RI_INSP_CUSTOMER_DETAILS
    (1004, 1022); -- RI_INSP_ACCESS_DSHBRD_PAGE

-- =====================================================
-- IMPORTANT NOTES:
-- =====================================================
-- 1. Update all ID values (2000) to unique values not used by existing merchants
-- 2. Replace tenant ID (123456) with calculated value or unique identifier
-- 3. Update merchant_email, merchant_name, and other merchant-specific values
-- 4. Generate secure HMAC key for webhook security
-- 5. Update OMS configuration based on actual integration requirements
-- 6. Consider adding additional user groups and users as needed
-- 7. Customize template configuration based on merchant requirements
-- =====================================================

