ALTER VIEW inspection_view AS
SELECT
    i.inspection_id,
    COALESCE(o.return_order_id, o.order_id) AS order_id,
    i.tenant,
    i.created_by_group as`created_by_group_id`,
    i.assignee_group as`assignee_group_id`,
    aug.name as `assignee_group`,
    i.reference_id,
    i.created_at,
    i.status,
    i.selling_channel,
    i.partner_name,
    GREATEST(DATEDIFF(DATE_ADD(i.created_at, INTERVAL m.sla DAY), CURRENT_DATE), 0) AS remaining_days
FROM
    inspection i
JOIN
    merchant m ON i.tenant = m.tenant
JOIN
	orders o ON o.id = i.order_id
LEFT JOIN
	user_group aug ON aug.id = i.assignee_group;

create table template (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), template_data json, template_id varchar(36) not null, template_name varchar(255) not null, tenant bigint not null, user_group bigint, primary key (id));

alter table template add constraint UK16u9q7uua8kwemrv69vu4xmx unique (template_id);

alter table template add constraint FKdn7ik2lnq3rsvqrai69gsuu9k foreign key (user_group) references user_group (id);

alter table user_group add column partner_name varchar(50);