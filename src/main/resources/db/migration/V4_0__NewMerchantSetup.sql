-- =====================================================
-- New Merchant Setup Script Template
-- =====================================================
-- This script provides a template for setting up a new merchant
-- Replace the placeholder values with actual merchant data

-- Step 1: Insert New Merchant
-- Replace values: merchant_email, merchant_name, tenant_id, reporting_currency, hmac_key
INSERT INTO merchant
(id, merchant_email, merchant_name, tenant, reporting_currency, hmac_key)
VALUES(
    2000, -- Unique merchant ID (increment from existing merchants)
    '<EMAIL>', -- Merchant admin email
    'NewMerchant', -- Merchant name (used for tenant ID calculation)
    123456, -- Tenant ID (calculated based on merchant name or custom)
    'USD', -- Reporting currency (ISO 3-letter code)
    'your-secure-hmac-key-here' -- HMAC key for webhook security
);

-- Step 2: Insert Default Channel
-- Each merchant needs at least one channel for order processing
INSERT INTO channel
(id, channel_id, name, sla, tenant)
VALUES(
    2000, -- Unique channel ID
    UUID(), -- Auto-generated channel UUID
    'Default', -- Channel name
    5, -- Service Level Agreement (days)
    123456 -- Same tenant ID as merchant
);

-- Step 3: Insert Authentication Provider
-- Configure how users will authenticate (DB, GOOGLE, AZURE, OKTA, MANHATTAN)
INSERT INTO auth_provider
(id, auth_url, provider, tenant, merchant_id)
VALUES(
    2000, -- Unique auth provider ID
    '/api/v1/auth/login', -- Authentication URL endpoint
    'DB', -- Provider type (DB for database authentication)
    123456, -- Tenant ID
    2000 -- Merchant ID reference
);

-- Step 4: Insert Admin User Group
-- Create the primary admin group for the merchant
INSERT INTO user_group
(id, name, tenant, comment_visibility, user_group_id, partner_name, is_active)
VALUES (
    2000, -- Unique user group ID
    'Admin', -- Group name
    123456, -- Tenant ID
    'INTERNAL', -- Comment visibility (INTERNAL/EXTERNAL)
    UUID(), -- Auto-generated user group UUID
    NULL, -- Partner name (optional)
    1 -- Active status
);

-- Step 5: Insert Default Template Configuration
-- Template for inspection data display and filtering
INSERT INTO template (id, template_data, template_id, template_name, tenant, user_group)
VALUES (
    2000, -- Unique template ID
    '[{"field":"status","label":"Status icon","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"referenceId","label":"Reference ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"orderId","label":"Order ID","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"createdAt","label":"Start date","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"sellingChannel","label":"Channel","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"partnerName","label":"Partner","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"assigneeGroup","label":"Assigned to","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}},{"field":"remainingDays","label":"Remaining days","filter":{"enabled":true,"criteria":["eq","neq"]},"sort":{"enabled":true,"criteria":["asc","desc"]}}]',
    'RI_INSPECTION', -- Template type
    UUID(), -- Auto-generated template UUID
    123456, -- Tenant ID
    2000 -- User group ID reference
);

-- Step 6: Insert Role Group
-- Create admin role group with all permissions
INSERT INTO role_group (id, role_group_id, name, tenant)
VALUES (
    2000, -- Unique role group ID
    UUID(), -- Auto-generated role group UUID
    'ADMIN_ROLES', -- Role group name
    123456 -- Tenant ID
);

-- Step 7: Insert Admin User
-- Create the primary admin user for the merchant
-- Password: 'password' (hashed with BCrypt)
INSERT INTO user (id, is_active, password, user_name, auth_provider_id, tenant, first_name, last_name, user_id, merchant_id, language, role_group_id)
VALUES (
    2000, -- Unique user ID
    1, -- Active status
    '$2a$10$XIjz9lkMaicP3xtiZbxqtu.P0TGtswuZxzZ0xjvbuWSFqSu1DWM6q', -- BCrypt hashed password
    '<EMAIL>', -- Username (usually email)
    2000, -- Auth provider ID reference
    123456, -- Tenant ID
    'Admin', -- First name
    'User', -- Last name
    UUID(), -- Auto-generated user UUID
    2000, -- Merchant ID reference
    'en', -- Language preference
    2000 -- Role group ID reference
);

-- Step 8: Link User to User Group
-- Associate the admin user with the admin group
INSERT INTO user_group_rel (group_id, user_id)
VALUES (2000, 2000);

-- Step 9: Insert OMS Provider
-- Configure Order Management System integration
INSERT INTO oms_provider (id, provider_name, tenant)
VALUES (
    2000, -- Unique OMS provider ID
    'MOCK', -- Provider name (MOCK for testing, MAO for production)
    123456 -- Tenant ID
);

-- Step 10: Insert OMS Configuration
-- Configure OMS connection parameters
INSERT INTO oms_config (config_key, config_value, oms_provider_id)
VALUES
    ('user_name', 'demo_user', 2000), -- OMS username
    ('password', 'demo_password', 2000), -- OMS password
    ('base_url', 'https://api.newmerchant.com', 2000), -- OMS base URL (optional)
    ('timeout', '30000', 2000); -- Connection timeout in milliseconds (optional)

-- Step 11: Insert Search Strategies
-- Configure available search methods for orders/returns
INSERT INTO search_strategies (search_strategy_id, search_strategy)
VALUES 
    (2000, 'RETURN_BY_TRACKING_NUMBER'),
    (2000, 'ORDER_BY_ID'),
    (2000, 'ORDER_BY_RETURN_TRACKING'),
    (2000, 'ORDER_BY_PRODUCT_SERIAL_NUMBER');

-- Step 12: Assign Admin Roles
-- Grant all available permissions to the admin role group
INSERT INTO role_role_groups (role_group_id, role_id)
VALUES
    (2000, 1021), -- RI_INSP_ACCESS_DSHBRD_PAGE
    (2000, 1014), -- RI_INSP_AUDIT
    (2000, 1016), -- RI_INSP_CREATE_USER
    (2000, 1017), -- RI_INSP_CREATE_USER_ROLE_GRP
    (2000, 1013), -- RI_INSP_RETN_RSN
    (2000, 1019), -- RI_INSP_MY_PROFILE
    (2000, 1015), -- RI_INSP_OMS_CONFIG
    (2000, 1018), -- RI_INSP_QI_INSTRUCTIONS
    (2000, 1022), -- RI_INSP_INTGRN_LOG
    (2000, 1000), -- RI_INSP_PCKG_SCN
    (2000, 1012), -- RI_INSP_SLA
    (2000, 1023), -- RI_INSP_INTGRN_LOG_VIEW
    (2000, 1024), -- RI_INSP_ITEM_CONDN
    (2000, 1025), -- RI_INSP_USER_GRP
    (2000, 1026), -- RI_INSP_REVIEWERS
    (2000, 1027), -- RI_INSP_SSO_CONFIG
    (2000, 1028); -- RI_INSP_EDIT_INSTRUCTIONS

-- =====================================================
-- IMPORTANT NOTES:
-- =====================================================
-- 1. Update all ID values (2000) to unique values not used by existing merchants
-- 2. Replace tenant ID (123456) with calculated value or unique identifier
-- 3. Update merchant_email, merchant_name, and other merchant-specific values
-- 4. Generate secure HMAC key for webhook security
-- 5. Update OMS configuration based on actual integration requirements
-- 6. Consider adding additional user groups and users as needed
-- 7. Customize template configuration based on merchant requirements
-- =====================================================
