create table audit_events (id bigint not null auto_increment, created_at datetime(6), details json, entity_id varchar(36), event_type enum ('INSPECTION_ITEM_APPROVED','INSPECTION_ITEM_CREATE','INSPECTION_ITEM_DELETE','INSPECTION_ITEM_REJECTED','INSPECTION_ITEM_SEND_FOR_REVIEW'), tenant bigint not null, unique_id varchar(100), user_group_id varchar(36), user_id varchar(36), primary key (id)) engine=InnoDB;
create table auth_provider (id bigint not null auto_increment, auth_url varchar(255) not null, authorization_grant_type varchar(255), authorization_uri varchar(255), client_authentication_method varchar(255), client_id varchar(255), client_secret varchar(255), domain varchar(255), jwk_set_uri varchar(255), name_attr varchar(255), provider enum ('AZURE','DB','GOOGLE','MANHATT<PERSON>','OKTA') not null, redirect_uri varchar(255), registration_id varchar(255), scopes varchar(255), tenant bigint not null, tenant_id varchar(255), token_uri varchar(255), user_info_uri varchar(255), merchant_id bigint not null, primary key (id)) engine=InnoDB;
create table channel (id bigint not null auto_increment, channel_id varchar(36) not null, name varchar(255) not null, sla integer, tenant bigint not null, primary key (id)) engine=InnoDB;
create table comment (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), comment_id varchar(36) not null, comment_type enum ('APPROVE','GENERAL','REJECT','REVIEW'), content mediumtext not null, tenant bigint not null, visibility enum ('EXTERNAL','INTERNAL') not null, user_id bigint not null, inspection_item_id bigint not null, primary key (id)) engine=InnoDB;;
create table comment_aud (id bigint not null, rev integer not null, revtype tinyint, updated_at datetime(6), updated_by varchar(255), comment_id varchar(36), content mediumtext, tenant bigint, primary key (rev, id)) engine=InnoDB;
create table event_config (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), config json, config_name varchar(255) not null, destination enum ('AZUREQUEUE','GOOGLEPUBSUB','KAFKA','SQS','WEBHOOK') not null, event_type varchar(255) not null, mapping_script text, retry_count integer, tenant bigint not null, primary key (id)) engine=InnoDB;
create table event_queue (id bigint not null auto_increment, config_id bigint not null, event_id varchar(36) not null, event_time datetime(6) not null, event_type enum ('INSP_COMPLETED','INSP_CREATED','INSP_ITEM_APPROVED','INSP_ITEM_COMMENT_ADDED','INSP_ITEM_COMMENT_DELETED','INSP_ITEM_COMMENT_UPDATED','INSP_ITEM_REJECTED','INSP_ITEM_RETURN_REASON_UPDATED','INSP_SEND_FOR_REVIEW') not null, payload json, performed_by_group varchar(255) not null, performed_by_user varchar(255) not null, reference_type enum ('COMMENT','INSPECTION','INSPECTION_ITEM') not null, tenant bigint not null, unique_id varchar(255) not null, primary key (id)) engine=InnoDB;
create table event_queue_req (id bigint not null auto_increment, config_id bigint not null, event_id varchar(255) not null, event_queue_id bigint not null, event_type varchar(255) not null, original_payload LONGTEXT, payload LONGTEXT, request_time datetime(6) not null, target varchar(255) not null, tenant bigint not null, unique_id varchar(255) not null, primary key (id)) engine=InnoDB;
create table event_queue_res (id bigint not null auto_increment, body mediumtext, error_message mediumtext, event_id varchar(255) not null, response_time datetime(6) not null, status enum ('FAILED','PENDING','SENT') not null, status_code integer, tenant bigint not null, request_id bigint not null, primary key (id)) engine=InnoDB;
create table inspection (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), assigned_date datetime(6), assignee_group_name varchar(255), completed_by varchar(255), completed_date datetime(6), customer_email varchar(255), customer_first_name varchar(255), customer_last_name varchar(255), inspection_id varchar(36) not null, partner_name varchar(50), reference_id varchar(50) not null, selling_channel varchar(50), status enum ('COMPLETED','IN_PROGRESS') not null, tenant bigint not null, assignee bigint not null, assignee_group bigint, created_by_group bigint, last_assigned_group bigint, order_id bigint not null, primary key (id)) engine=InnoDB;
create table inspection_aud (id bigint not null, rev integer not null, revtype tinyint, updated_at datetime(6), updated_by varchar(255), assignee_group_name varchar(255), inspection_id varchar(36), reference_id varchar(50), status enum ('COMPLETED','IN_PROGRESS'), tenant bigint, primary key (rev, id)) engine=InnoDB;
create table inspection_item (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), city varchar(255), completed_by varchar(255), completed_date datetime(6), country varchar(255), expected_item_condition varchar(255), expected_quantity Decimal(14,4) not null, inspection_item_id varchar(36) not null, item_condition varchar(255), item_status enum ('APPROVED','REJECTED','REVIEW'), order_line_id varchar(255) not null, received_quantity Decimal(14,4) not null, region varchar(255), return_reason varchar(255), tenant bigint not null, inspection_id bigint not null, product_id bigint not null, primary key (id)) engine=InnoDB;
create table inspection_item_aud (id bigint not null, rev integer not null, revtype tinyint, updated_at datetime(6), updated_by varchar(255), inspection_item_id varchar(36), item_status enum ('APPROVED','REJECTED','REVIEW'), return_reason varchar(255), tenant bigint, primary key (rev, id)) engine=InnoDB;
create table inspection_item_condition (id bigint not null auto_increment, condition_type enum ('APPROVED','REJECTED') not null, item_condition_id varchar(36) not null, name varchar(100) not null, tenant bigint not null, primary key (id)) engine=InnoDB;
create table instruction (id bigint not null auto_increment, content longtext not null, language_code varchar(10) not null, tenant bigint not null, product_instruction_id bigint not null, primary key (id)) engine=InnoDB;
create table media (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), data longblob, file_name varchar(255) not null, media_id varchar(36) not null, media_type varchar(255) not null, tenant bigint not null, url varchar(255), comment_id bigint, inspection_item_id bigint, primary key (id)) engine=InnoDB;
create table media_aud (id bigint not null, rev integer not null, revtype tinyint, updated_at datetime(6), updated_by varchar(255), media_id varchar(36), tenant bigint, url varchar(255), primary key (rev, id)) engine=InnoDB;
create table merchant (id bigint not null auto_increment, hmac_key varchar(255), merchant_email varchar(30), merchant_name varchar(50) not null, reporting_currency varchar(3), tenant bigint not null, primary key (id)) engine=InnoDB;
create table merchant_product_price (id bigint not null auto_increment, currency_code varchar(3) not null, sku varchar(255) not null, tenant bigint not null, total_unit Decimal(14,4) not null, unit_price decimal(19,6) not null, primary key (id)) engine=InnoDB;
create table notification_channel (id bigint not null auto_increment, channel enum ('EMAIL','SLACK','TEAMS') not null, config json, tenant bigint not null, primary key (id)) engine=InnoDB;
create table notification_config (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), destination mediumtext not null, frequency varchar(255) not null, notification_type enum ('ASSIGN_TO_ME','INTEGRATION_FAILURE') not null, tenant bigint not null, user_group_id varchar(255), channel_id bigint not null, primary key (id)) engine=InnoDB;
create table notification_response (id bigint not null auto_increment, config_id bigint not null, content mediumtext not null, send_date datetime(6), tenant_id bigint not null, primary key (id)) engine=InnoDB;
create table oms_config (id bigint not null auto_increment, config_key varchar(255) not null, config_value varchar(255) not null, oms_provider_id bigint not null, primary key (id)) engine=InnoDB;
create table oms_provider (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), provider_name varchar(255) not null, tenant bigint not null, primary key (id)) engine=InnoDB;
create table orders (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), order_id varchar(100), return_order_id varchar(100), return_tracking_id varchar(100), tenant bigint not null, primary key (id)) engine=InnoDB;
create table product (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), currency_code varchar(3), department_name varchar(255), description mediumtext not null, product_class varchar(255), product_image mediumtext not null, product_name varchar(255) not null, product_size varchar(255), product_style varchar(255), product_sub_class varchar(255), properties json, return_reason varchar(255), sku varchar(50) not null, tenant bigint not null, unit_price decimal(19,6), primary key (id)) engine=InnoDB;
create table product_instruction (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), product_class varchar(255) not null, product_instruction_id varchar(36) not null, product_sub_class varchar(255), tenant bigint not null, primary key (id)) engine=InnoDB;
create table return_reason (id bigint not null auto_increment, reason varchar(255) not null, return_reason_id varchar(36) not null, tenant bigint not null, primary key (id)) engine=InnoDB;
create table revinfo (id integer not null, timestamp bigint not null, unique_id varchar(255), primary key (id)) engine=InnoDB;
create table revinfo_seq (next_val bigint) engine=InnoDB;
insert into revinfo_seq values ( 1 );
create table role (id bigint not null auto_increment, name varchar(30) not null, primary key (id)) engine=InnoDB;
create table role_group (id bigint not null auto_increment, name varchar(30) not null, role_group_id varchar(36) not null, tenant bigint not null, primary key (id)) engine=InnoDB;
create table role_role_groups (role_group_id bigint not null, role_id bigint not null, primary key (role_group_id, role_id)) engine=InnoDB;
create table search_strategies (search_strategy_id bigint not null, search_strategy varchar(255)) engine=InnoDB;
create table supported_language (id bigint not null auto_increment, code varchar(10) not null, name varchar(10) not null, primary key (id)) engine=InnoDB;
create table temp_media (id bigint not null auto_increment, created_at datetime(6), file_name varchar(255) not null, inspection_item_id varchar(255) not null, media_type varchar(255) not null, tenant bigint not null, url varchar(255) not null, user_id varchar(255) not null, primary key (id)) engine=InnoDB;
create table template (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), template_data json, template_id varchar(36) not null, template_name varchar(255) not null, tenant bigint not null, user_group bigint, primary key (id)) engine=InnoDB;
create table user (id bigint not null auto_increment, created_at datetime(6), created_by varchar(255), updated_at datetime(6), updated_by varchar(255), first_name varchar(30), is_active bit not null, language varchar(50) not null, last_name varchar(30), password varchar(255), tenant bigint not null, time_zone varchar(50), user_id varchar(36) not null, user_name varchar(50) not null, auth_provider_id bigint, merchant_id bigint not null, profile_image_id bigint, role_group_id bigint not null, primary key (id)) engine=InnoDB;
create table user_group (id bigint not null auto_increment, is_active bit not null, name varchar(30) not null, partner_name varchar(50), tenant bigint not null, user_group_id varchar(36) not null, comment_visibility enum ('EXTERNAL','INTERNAL') not null, primary key (id)) engine=InnoDB;
create table user_group_rel (id bigint not null auto_increment, group_id bigint not null, user_id bigint not null, primary key (id)) engine=InnoDB;
create table user_group_reviewers (user_group_id bigint not null, reviewer_group_id bigint not null, primary key (user_group_id, reviewer_group_id)) engine=InnoDB;
alter table auth_provider add constraint UKd0bfnllxvil1eidsy4p1oarvu unique (registration_id);
alter table channel add constraint UKsvv1df27bn76qb2qxh1xoke3b unique (name, tenant);
alter table channel add constraint UKfu15fl3mnrr2ciq0fsnibf74p unique (channel_id);
create index idx_comment_comment_id on comment (comment_id);
alter table comment add constraint UK9k154dwhlnk39nh50ix50swor unique (comment_id);
alter table event_queue add constraint UK8i3jlg9pr8tev046q5xlk28qp unique (event_id);
create index idx_inspection_inspection_id on inspection (inspection_id);
alter table inspection add constraint UK3rmedrrlxd5a5dmcaabofe3j2 unique (inspection_id);
create index idx_inspection_item_inspection_item_id on inspection_item (inspection_item_id);
alter table inspection_item add constraint UK6tenokd1j8vuy9b9qxi1fure unique (inspection_item_id);
alter table inspection_item_condition add constraint UKg48wrsmtqc2t04qenwo7qyjtj unique (name, tenant);
alter table inspection_item_condition add constraint UKiepso0f2vejhml23l7ytmtqc9 unique (item_condition_id);
alter table instruction add constraint UKflnckic2xff1yujckt3klu4q3 unique (language_code, product_instruction_id);
create index idx_media_media_id on media (media_id);
alter table media add constraint UKb5hg8ssy6nck9i2ju7y5i6s25 unique (media_id);
alter table merchant add constraint UK9d99mmka6ug1kl85hatmdji3m unique (merchant_name);
alter table merchant add constraint UKq80yfeex1w2aywi5ticxcmu32 unique (tenant);
create index idx_merchant_product_price_sku on merchant_product_price (sku);
create index idx_merchant_product_price_tenant on merchant_product_price (tenant);
alter table merchant_product_price add constraint uk_sku_tenant unique (sku, tenant);
alter table oms_config add constraint UKbq6di768yeyhuy3eb3oaoy7jh unique (oms_provider_id, config_key);
alter table oms_provider add constraint unique_provider unique (provider_name, tenant);
create index order_id_idx01 on orders (order_id);
create index return_tracking_id_idx on orders (return_tracking_id);
create index return_order_id_idx on orders (return_order_id);
alter table product_instruction add constraint UKodrjx3yb2lncm5h5uximbr3ju unique (product_class, product_sub_class, tenant);
alter table product_instruction add constraint UKrir95568y6d44gmhrcckimlx5 unique (product_instruction_id);
alter table return_reason add constraint UKla9oopoki3y50ido3pfll6uvn unique (reason, tenant);
alter table return_reason add constraint UKobajhhya0incg1qqy3kmr4ehp unique (return_reason_id);
alter table role add constraint UK8sewwnpamngi6b1dwaa88askk unique (name);
alter table role_group add constraint UK4hgj0apw2h5i7n5xfhb7y3477 unique (name, tenant);
alter table role_group add constraint UK89811uilhlui0alcwlytl2fwh unique (role_group_id);
alter table template add constraint UK16u9q7uua8kwemrv69vu4xmx unique (template_id);
create index user_idx01 on user (user_name);
alter table user add constraint UK6e2ponyubhjd0etuhspf7uik2 unique (user_name, tenant);
alter table user add constraint UKa3imlf41l37utmxiquukk8ajc unique (user_id);
create index idx_user_group_id on user_group (user_group_id);
alter table user_group add constraint UKe42n11wvf1dlurmhep08aq1iu unique (name, tenant);
alter table user_group add constraint UK26hdon2ljjyltcehgikpq48r4 unique (user_group_id);
alter table auth_provider add constraint FKmpetiugqmydr0bcysh39un25a foreign key (merchant_id) references merchant (id);
alter table comment add constraint FK8kcum44fvpupyw6f5baccx25c foreign key (user_id) references user (id);
alter table comment add constraint FK8pdxv1assjgff4sp0sj1x7q82 foreign key (inspection_item_id) references inspection_item (id);
alter table comment_aud add constraint FKo1q5p3hhua10tousqu4m2hn3n foreign key (rev) references revinfo (id);
alter table event_queue_res add constraint FKk2ey68ea8sp3f5rw2hx4adf9w foreign key (request_id) references event_queue_req (id);
alter table inspection add constraint FK56vwis2sxg5my6gbg1i2pv9nj foreign key (assignee) references user (id);
alter table inspection add constraint FKqarbx02gt42exf1wa5a0nk9ri foreign key (assignee_group) references user_group (id);
alter table inspection add constraint FK41n3gkf0h0lk8sptprrk5gey5 foreign key (created_by_group) references user_group (id);
alter table inspection add constraint FKjhrnuppqphheeg1qd3e7lhut9 foreign key (last_assigned_group) references user_group (id);
alter table inspection add constraint FKk8fye4r86q8k73jrck0u5kvx6 foreign key (order_id) references orders (id);
alter table inspection_aud add constraint FK8t8c9gvr7p8na1u1cdae7dbrg foreign key (rev) references revinfo (id);
alter table inspection_item add constraint FKhgla6gy5xnb27nyun7ybmclv foreign key (inspection_id) references inspection (id);
alter table inspection_item add constraint FKg98niaiq7vr6yyit0in7u1oo0 foreign key (product_id) references product (id);
alter table inspection_item_aud add constraint FK83reboq4cmww3be3ad6qq6wmr foreign key (rev) references revinfo (id);
alter table instruction add constraint FKe0qwpqm2xlit02ebjra4s43wi foreign key (product_instruction_id) references product_instruction (id);
alter table media add constraint FKpo2buh2muj0nd20xop2n2ag7s foreign key (comment_id) references comment (id);
alter table media add constraint FKid5dx2jubdusx8ql2owst0t3l foreign key (inspection_item_id) references inspection_item (id);
alter table media_aud add constraint FK69pjf53ckwya5efbjt05kx4ye foreign key (rev) references revinfo (id);
alter table notification_config add constraint FKg5cn23417fsjqc6y6h2dnl9qh foreign key (channel_id) references notification_channel (id);
alter table oms_config add constraint FKg1doygfd11ouow16rs9yti4wj foreign key (oms_provider_id) references oms_provider (id);
alter table role_role_groups add constraint FKmn4hoxdtfjn2aei1a1c0f4rrs foreign key (role_id) references role (id);
alter table role_role_groups add constraint FK3tx7a45ycn93ecnycxakt0l15 foreign key (role_group_id) references role_group (id);
alter table search_strategies add constraint FKd0jrmf0xqadrbsnuwnajtrlxa foreign key (search_strategy_id) references oms_provider (id);
alter table template add constraint FKdn7ik2lnq3rsvqrai69gsuu9k foreign key (user_group) references user_group (id);
alter table user add constraint FK81juwa426meeq9nb7l90i259b foreign key (auth_provider_id) references auth_provider (id);
alter table user add constraint FKkee5al1v1e34tu1bqhgs5s1jm foreign key (merchant_id) references merchant (id);
alter table user add constraint FK7f31epj84qwkvaqdpn0yanvm4 foreign key (profile_image_id) references media (id);
alter table user add constraint FKnndcge1bphjyi5f6b66n42150 foreign key (role_group_id) references role_group (id);
alter table user_group_rel add constraint FKt7vuoac67docof3681ymj4ts3 foreign key (group_id) references user_group (id);
alter table user_group_rel add constraint FKehhbimrhdoc6wfy0ghnxf6cte foreign key (user_id) references user (id);
alter table user_group_reviewers add constraint FKerpkjk0rmko7bu7aldvi3p9yx foreign key (reviewer_group_id) references user_group (id);
alter table user_group_reviewers add constraint FKfgkttrtrfjw7pww3ov9pw238o foreign key (user_group_id) references user_group (id);

-- Batch Tables

-- BATCH_JOB_EXECUTION_SEQ definition

CREATE TABLE BATCH_JOB_EXECUTION_SEQ (
  ID bigint NOT NULL,
  UNIQUE_KEY char(1) NOT NULL,
  UNIQUE KEY UNIQUE_KEY_UN (UNIQUE_KEY)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- BATCH_JOB_INSTANCE definition

CREATE TABLE BATCH_JOB_INSTANCE (
  JOB_INSTANCE_ID bigint NOT NULL,
  VERSION bigint DEFAULT NULL,
  JOB_NAME varchar(100) NOT NULL,
  JOB_KEY varchar(32) NOT NULL,
  PRIMARY KEY (JOB_INSTANCE_ID),
  UNIQUE KEY JOB_INST_UN (JOB_NAME,JOB_KEY)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- BATCH_JOB_SEQ definition

CREATE TABLE BATCH_JOB_SEQ (
  ID bigint NOT NULL,
  UNIQUE_KEY char(1) NOT NULL,
  UNIQUE KEY UNIQUE_KEY_UN (UNIQUE_KEY)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- BATCH_STEP_EXECUTION_SEQ definition

CREATE TABLE BATCH_STEP_EXECUTION_SEQ (
  ID bigint NOT NULL,
  UNIQUE_KEY char(1) NOT NULL,
  UNIQUE KEY UNIQUE_KEY_UN (UNIQUE_KEY)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- BATCH_JOB_EXECUTION definition

CREATE TABLE BATCH_JOB_EXECUTION (
  JOB_EXECUTION_ID bigint NOT NULL,
  VERSION bigint DEFAULT NULL,
  JOB_INSTANCE_ID bigint NOT NULL,
  CREATE_TIME datetime(6) NOT NULL,
  START_TIME datetime(6) DEFAULT NULL,
  END_TIME datetime(6) DEFAULT NULL,
  STATUS varchar(10) DEFAULT NULL,
  EXIT_CODE varchar(2500) DEFAULT NULL,
  EXIT_MESSAGE varchar(2500) DEFAULT NULL,
  LAST_UPDATED datetime(6) DEFAULT NULL,
  PRIMARY KEY (JOB_EXECUTION_ID),
  KEY JOB_INST_EXEC_FK (JOB_INSTANCE_ID),
  CONSTRAINT JOB_INST_EXEC_FK FOREIGN KEY (JOB_INSTANCE_ID) REFERENCES BATCH_JOB_INSTANCE (JOB_INSTANCE_ID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- BATCH_JOB_EXECUTION_CONTEXT definition

CREATE TABLE `BATCH_JOB_EXECUTION_CONTEXT` (
  JOB_EXECUTION_ID bigint NOT NULL,
  SHORT_CONTEXT varchar(2500) NOT NULL,
  SERIALIZED_CONTEXT text,
  PRIMARY KEY (JOB_EXECUTION_ID),
  CONSTRAINT JOB_EXEC_CTX_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES BATCH_JOB_EXECUTION (JOB_EXECUTION_ID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- BATCH_JOB_EXECUTION_PARAMS definition

CREATE TABLE BATCH_JOB_EXECUTION_PARAMS (
  JOB_EXECUTION_ID bigint NOT NULL,
  PARAMETER_NAME varchar(100) NOT NULL,
  PARAMETER_TYPE varchar(100) NOT NULL,
  PARAMETER_VALUE varchar(2500) DEFAULT NULL,
  IDENTIFYING char(1) NOT NULL,
  KEY JOB_EXEC_PARAMS_FK (JOB_EXECUTION_ID),
  CONSTRAINT JOB_EXEC_PARAMS_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES BATCH_JOB_EXECUTION (JOB_EXECUTION_ID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- BATCH_STEP_EXECUTION definition

CREATE TABLE BATCH_STEP_EXECUTION (
  STEP_EXECUTION_ID bigint NOT NULL,
  VERSION bigint NOT NULL,
  STEP_NAME varchar(100) NOT NULL,
  JOB_EXECUTION_ID bigint NOT NULL,
  CREATE_TIME datetime(6) NOT NULL,
  START_TIME datetime(6) DEFAULT NULL,
  END_TIME datetime(6) DEFAULT NULL,
  STATUS varchar(10) DEFAULT NULL,
  COMMIT_COUNT bigint DEFAULT NULL,
  READ_COUNT bigint DEFAULT NULL,
  FILTER_COUNT bigint DEFAULT NULL,
  WRITE_COUNT bigint DEFAULT NULL,
  READ_SKIP_COUNT bigint DEFAULT NULL,
  WRITE_SKIP_COUNT bigint DEFAULT NULL,
  PROCESS_SKIP_COUNT bigint DEFAULT NULL,
  ROLLBACK_COUNT bigint DEFAULT NULL,
  EXIT_CODE varchar(2500) DEFAULT NULL,
  EXIT_MESSAGE varchar(2500) DEFAULT NULL,
  LAST_UPDATED datetime(6) DEFAULT NULL,
  PRIMARY KEY (STEP_EXECUTION_ID),
  KEY JOB_EXEC_STEP_FK (JOB_EXECUTION_ID),
  CONSTRAINT JOB_EXEC_STEP_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES BATCH_JOB_EXECUTION (JOB_EXECUTION_ID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- BATCH_STEP_EXECUTION_CONTEXT definition

CREATE TABLE BATCH_STEP_EXECUTION_CONTEXT (
  STEP_EXECUTION_ID bigint NOT NULL,
  SHORT_CONTEXT varchar(2500) NOT NULL,
  SERIALIZED_CONTEXT text,
  PRIMARY KEY (STEP_EXECUTION_ID),
  CONSTRAINT STEP_EXEC_CTX_FK FOREIGN KEY (STEP_EXECUTION_ID) REFERENCES BATCH_STEP_EXECUTION (STEP_EXECUTION_ID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


-- Dashboard views
-- Inspection view
CREATE OR REPLACE VIEW inspection_view AS
SELECT
    i.inspection_id,
    COALESCE(o.return_order_id, o.order_id) AS order_id,
    i.tenant,
    i.created_by_group as`created_by_group_id`,
    i.assignee_group as`assignee_group_id`,
    aug.name as `assignee_group`,
    i.reference_id,
    i.created_at,
    i.status,
    i.selling_channel,
    i.partner_name,
    GREATEST(DATEDIFF(DATE_ADD(i.created_at, INTERVAL COALESCE(c1.sla, c2.sla) DAY), CURRENT_DATE), 0) AS remaining_days
FROM
    inspection i
LEFT JOIN channel c1
    ON LOWER(i.selling_channel) = LOWER(c1.name)
    AND i.tenant = c1.tenant
LEFT JOIN channel c2
    ON LOWER(c2.name) = 'default'
    AND i.tenant = c2.tenant
JOIN
	orders o ON o.id = i.order_id
LEFT JOIN
	user_group aug ON aug.id = i.assignee_group;

-- Event Queue Processing
CREATE OR REPLACE VIEW event_queue_view AS
SELECT
    eq.id,
    eq.event_id,
    eq.event_time,
    eq.event_type,
    eq.payload,
    eq.performed_by_group,
    eq.performed_by_user,
    eq.unique_id,
    eq.reference_type,
    eq.tenant,
    ec.id as config_id,
    ec.destination,
    ec.mapping_script,
    ec.config,
    m.hmac_key,
    COUNT(eqr.id) AS retry_count,
    MAX(eqr.request_time) AS last_attempt_time,
    TIMESTAMPDIFF(MINUTE, MAX(eqr.request_time), NOW()) AS minutes_since_last_retry,
    DATE_ADD(MAX(eqr.request_time), INTERVAL POW(2, COUNT(eqr.id)) MINUTE) AS next_retry_time
FROM event_queue eq
JOIN event_config ec ON ec.id = eq.config_id
JOIN merchant m on m.tenant = ec.tenant
LEFT JOIN event_queue_req eqr ON eqr.event_queue_id = eq.id
LEFT JOIN event_queue_res eqs ON eqs.request_id = eqr.id
WHERE eq.event_time >= NOW() - INTERVAL 1 HOUR
GROUP BY eq.id, ec.retry_count, ec.destination, ec.config
HAVING COUNT(eqr.id) <= ec.retry_count
   AND SUM(CASE WHEN eqs.status = 'SENT' THEN 1 ELSE 0 END) = 0
   AND (
       COUNT(eqr.id) = 0
       OR NOW() >= DATE_ADD(MAX(eqr.request_time), INTERVAL POW(2, COUNT(eqr.id)) MINUTE)
   )
ORDER BY eq.event_time;


-- Return Reasons View
CREATE OR REPLACE VIEW return_reason_view AS
SELECT
    ROW_NUMBER() OVER () AS id,
    DATE(ii.created_at) as created_at,
    p.sku,
	MAX(COALESCE(p.product_size,'Unknown')) as product_size,
    ii.tenant,
    CASE
        WHEN LOWER(ii.return_reason) IN ('change mind', 'change of mind') THEN 'CHANGE OF MIND'
        ELSE COALESCE(ii.return_reason, 'OTHER')
    END AS common_return_reason,
    COALESCE(p.product_style,'Unknown') as product_style,
    MAX(COALESCE(p.product_name,'Unknown')) as product_name,
    COALESCE(p.product_class,'Unknown') as product_class,
    MAX(COALESCE(p.department_name,'Unknown')) as department_name,
    COALESCE(p.product_sub_class,'Unknown') as product_sub_class,
    COUNT(ii.id) AS return_count,
    SUM(COALESCE(mp.unit_price,0)) AS price
FROM inspection_item ii
JOIN product p ON ii.product_id = p.id
LEFT JOIN merchant_product_price mp on mp.sku = p.sku and mp.tenant = ii.tenant
GROUP BY ii.tenant, DATE(ii.created_at), common_return_reason, p.sku, p.product_style,p.product_class,p.product_sub_class
ORDER BY return_count DESC;

-- Item Condition View
CREATE OR REPLACE VIEW item_condition_view AS
SELECT
    ROW_NUMBER() OVER () AS id,
    DATE(ii.created_at) as created_at,
    COALESCE(i.selling_channel,'Unknown') as selling_channel,
    ii.tenant,
    ii.item_condition,
    ii.item_status,
    p.product_class,
    MAX(p.department_name) as department_name,
    COUNT(ii.id) AS return_count,
    SUM(COALESCE(mp.unit_price,0)) AS price
FROM inspection i
JOIN inspection_item ii on ii.inspection_id = i.id
JOIN product p ON ii.product_id = p.id
LEFT JOIN merchant_product_price mp on mp.sku = p.sku and mp.tenant = ii.tenant
WHERE ii.item_condition IS NOT NULL
GROUP BY ii.tenant, DATE(ii.created_at),ii.item_condition,COALESCE(i.selling_channel,'Unknown'),ii.item_status,p.product_class
ORDER BY return_count DESC;


-- Return top product view
CREATE OR REPLACE VIEW top_product_return_view AS
SELECT
    ROW_NUMBER() OVER () AS id,
    DATE(ii.created_at) as created_at,
    ii.tenant,
    COALESCE(p.product_style,'Unknown') as product_style,
    MAX(COALESCE(p.product_name,'Unknown')) as product_name,
    p.sku,
    MAX(COALESCE(p.product_size,'Unknown')) as product_size,
    COALESCE(ii.return_reason,'OTHER') as return_reason,
    COALESCE(i.selling_channel,'Unknown') as selling_channel,
    COUNT(ii.id) AS return_count,
    SUM(COALESCE(mp.unit_price,0)) AS price
FROM inspection_item ii
JOIN inspection i on i.id = ii.inspection_id
JOIN product p ON ii.product_id = p.id
LEFT JOIN merchant_product_price mp on mp.sku = p.sku and mp.tenant = ii.tenant
GROUP BY ii.tenant, DATE(ii.created_at), ii.return_reason, p.sku, p.product_style,i.selling_channel
ORDER BY return_count DESC;


-- Inspection Time Tracker view
CREATE OR REPLACE VIEW inspection_time_tracker_view AS
SELECT
    ROW_NUMBER() OVER () AS id,
    i.tenant,
    DATE(i.completed_date) AS completed_date,
    COUNT(DISTINCT i.id) AS return_count,
    DATEDIFF(i.completed_date, i.created_at) AS days
FROM
    inspection i
WHERE
    i.completed_date IS NOT NULL
GROUP BY
    i.tenant, DATE(i.completed_date), days
ORDER BY return_count DESC;

-- Inspection Completed View
CREATE OR REPLACE VIEW inspection_completed_view AS
SELECT
    ROW_NUMBER() OVER () AS id,
    i.completed_by,
    DATE(i.completed_date) as completed_date,
    i.tenant,
    COUNT(i.id) AS completed_count
FROM inspection i
GROUP BY i.tenant, DATE(i.completed_date),i.completed_by
ORDER BY completed_count DESC;